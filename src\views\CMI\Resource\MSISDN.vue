<template>
  <div class="msisdn-management">
    <ContentWrap>
      <div class="page-header">
        <h2>MSISDN管理</h2>
        <p>管理移动用户号码资源的分配和使用情况</p>
      </div>

      <!-- 搜索区域 -->
      <div class="search-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="MSISDN">
            <el-input
              v-model="searchForm.msisdn"
              placeholder="请输入MSISDN"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <el-option label="可用" value="available" />
              <el-option label="已分配" value="assigned" />
              <el-option label="已使用" value="used" />
              <el-option label="已回收" value="recycled" />
            </el-select>
          </el-form-item>
          <el-form-item label="运营商">
            <el-select
              v-model="searchForm.operator"
              placeholder="请选择运营商"
              clearable
              style="width: 150px"
            >
              <el-option label="中国移动" value="CMCC" />
              <el-option label="中国联通" value="CUCC" />
              <el-option label="中国电信" value="CTCC" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <Icon icon="vi-ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button @click="handleReset">
              <Icon icon="vi-ep:refresh" class="mr-5px" />
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <el-button type="primary" @click="handleAdd">
          <Icon icon="vi-ep:plus" class="mr-5px" />
          新增MSISDN
        </el-button>
        <el-button type="success" @click="handleBatchImport">
          <Icon icon="vi-ep:upload" class="mr-5px" />
          批量导入
        </el-button>
        <el-button type="warning" @click="handleBatchAssign" :disabled="!selectedIds.length">
          <Icon icon="vi-ep:connection" class="mr-5px" />
          批量分配
        </el-button>
        <el-button type="info" @click="handleExport">
          <Icon icon="vi-ep:download" class="mr-5px" />
          导出数据
        </el-button>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <el-table
          v-loading="loading"
          :data="tableData"
          @selection-change="handleSelectionChange"
          stripe
          border
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="msisdn" label="MSISDN" min-width="140" />
          <el-table-column prop="operator" label="运营商" width="100">
            <template #default="{ row }">
              <el-tag :type="getOperatorType(row.operator)">
                {{ getOperatorText(row.operator) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="assignedTo" label="分配给" min-width="120" />
          <el-table-column prop="assignTime" label="分配时间" width="160" />
          <el-table-column prop="lastUsedTime" label="最后使用时间" width="160" />
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button
                v-if="row.status === 'available'"
                type="success"
                size="small"
                @click="handleAssign(row)"
              >
                分配
              </el-button>
              <el-button
                v-if="row.status === 'assigned'"
                type="warning"
                size="small"
                @click="handleRecycle(row)"
              >
                回收
              </el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </ContentWrap>

    <!-- 编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="MSISDN" prop="msisdn">
          <el-input v-model="formData.msisdn" placeholder="请输入MSISDN" />
        </el-form-item>
        <el-form-item label="运营商" prop="operator">
          <el-select v-model="formData.operator" placeholder="请选择运营商" style="width: 100%">
            <el-option label="中国移动" value="CMCC" />
            <el-option label="中国联通" value="CUCC" />
            <el-option label="中国电信" value="CTCC" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="可用" value="available" />
            <el-option label="已分配" value="assigned" />
            <el-option label="已使用" value="used" />
            <el-option label="已回收" value="recycled" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

// 搜索表单
const searchForm = reactive({
  msisdn: '',
  status: '',
  operator: ''
})

// 表格数据
const loading = ref(false)
const tableData = ref([
  {
    id: 1,
    msisdn: '13800138001',
    operator: 'CMCC',
    status: 'available',
    assignedTo: '',
    assignTime: '',
    lastUsedTime: '',
    createTime: '2024-01-01 10:00:00',
    remark: '测试号码'
  },
  {
    id: 2,
    msisdn: '13800138002',
    operator: 'CUCC',
    status: 'assigned',
    assignedTo: '张三',
    assignTime: '2024-01-02 10:00:00',
    lastUsedTime: '2024-01-15 15:30:00',
    createTime: '2024-01-01 10:00:00',
    remark: '已分配给用户'
  }
])

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 选中的行
const selectedIds = ref<number[]>([])

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const submitLoading = ref(false)

// 表单数据
const formData = reactive({
  id: '',
  msisdn: '',
  operator: '',
  status: 'available',
  remark: ''
})

// 表单验证规则
const formRules = {
  msisdn: [{ required: true, message: '请输入MSISDN', trigger: 'blur' }],
  operator: [{ required: true, message: '请选择运营商', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 运营商类型
const getOperatorType = (operator: string) => {
  switch (operator) {
    case 'CMCC': return 'success'
    case 'CUCC': return 'primary'
    case 'CTCC': return 'warning'
    default: return 'info'
  }
}

// 运营商文本
const getOperatorText = (operator: string) => {
  switch (operator) {
    case 'CMCC': return '中国移动'
    case 'CUCC': return '中国联通'
    case 'CTCC': return '中国电信'
    default: return '未知'
  }
}

// 状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'available': return 'success'
    case 'assigned': return 'primary'
    case 'used': return 'warning'
    case 'recycled': return 'info'
    default: return 'info'
  }
}

// 状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'available': return '可用'
    case 'assigned': return '已分配'
    case 'used': return '已使用'
    case 'recycled': return '已回收'
    default: return '未知'
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    msisdn: '',
    status: '',
    operator: ''
  })
  handleSearch()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增MSISDN'
  isEdit.value = false
  dialogVisible.value = true
  resetFormData()
}

// 编辑
const handleEdit = (row: any) => {
  dialogTitle.value = '编辑MSISDN'
  isEdit.value = true
  dialogVisible.value = true
  Object.assign(formData, row)
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除该MSISDN吗？', '提示', {
      type: 'warning'
    })
    ElMessage.success('删除成功')
    loadTableData()
  } catch (error) {
    // 用户取消删除
  }
}

// 分配
const handleAssign = (row: any) => {
  ElMessage.info('分配功能开发中...')
}

// 回收
const handleRecycle = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要回收该MSISDN吗？', '提示', {
      type: 'warning'
    })
    ElMessage.success('回收成功')
    loadTableData()
  } catch (error) {
    // 用户取消操作
  }
}

// 批量导入
const handleBatchImport = () => {
  ElMessage.info('批量导入功能开发中...')
}

// 批量分配
const handleBatchAssign = () => {
  ElMessage.info('批量分配功能开发中...')
}

// 导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedIds.value = selection.map(item => item.id)
}

// 提交表单
const handleSubmit = async () => {
  submitLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
    dialogVisible.value = false
    loadTableData()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    id: '',
    msisdn: '',
    operator: '',
    status: 'available',
    remark: ''
  })
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  loadTableData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadTableData()
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    pagination.total = tableData.value.length
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadTableData()
})
</script>

<style scoped>
.msisdn-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.page-header p {
  color: #606266;
  margin: 0;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-section {
  margin-bottom: 20px;
}

.table-section {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}
</style>
