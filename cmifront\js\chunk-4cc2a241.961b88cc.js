(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4cc2a241"],{"00b4":function(t,e,a){"use strict";a("ac1f");var i=a("23e7"),s=a("c65b"),o=a("1626"),n=a("825a"),l=a("577e"),r=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),c=/./.test;i({target:"RegExp",proto:!0,forced:!r},{test:function(t){var e=n(this),a=l(t),i=e.exec;if(!o(i))return s(c,e,a);var r=s(i,e,a);return null!==r&&(n(r),!0)}})},"108d":function(t,e,a){},"129f":function(t,e,a){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},"44a7":function(t,e,a){"use strict";a("108d")},"466d":function(t,e,a){"use strict";var i=a("c65b"),s=a("d784"),o=a("825a"),n=a("7234"),l=a("50c4"),r=a("577e"),c=a("1d80"),d=a("dc4a"),u=a("8aa5"),p=a("14c3");s("match",(function(t,e,a){return[function(e){var a=c(this),s=n(e)?void 0:d(e,t);return s?i(s,e,a):new RegExp(e)[t](r(a))},function(t){var i=o(this),s=r(t),n=a(e,i,s);if(n.done)return n.value;if(!i.global)return p(i,s);var c=i.unicode;i.lastIndex=0;var d,f=[],h=0;while(null!==(d=p(i,s))){var m=r(d[0]);f[h]=m,""===m&&(i.lastIndex=u(s,l(i.lastIndex),c)),h++}return 0===h?null:f}]}))},6881:function(t,e,a){"use strict";a.r(e);var i=a("ade3"),s=(a("b0c0"),a("ac1f"),a("841c"),a("498a"),function(){var t=this,e=t._self._c;return e("div",[e("Card",[e("div",{staticClass:"search_head_i"},[e("div",{staticClass:"search_box1"},[e("span",{staticClass:"search_box_label"},[t._v("任务名称:")]),e("Input",{staticClass:"searchCondition",attrs:{placeholder:"请输入任务名称",clearable:""},model:{value:t.searchObj.taskName,callback:function(e){t.$set(t.searchObj,"taskName","string"===typeof e?e.trim():e)},expression:"searchObj.taskName"}})],1),t._v("   \n        "),e("div",{staticClass:"search_box1"},[e("span",{staticClass:"search_box_label"},[t._v("任务类型:")]),e("Select",{staticClass:"searchCondition",attrs:{filterable:"",placeholder:"请选择任务类型",clearable:""},model:{value:t.searchObj.taskType,callback:function(e){t.$set(t.searchObj,"taskType",e)},expression:"searchObj.taskType"}},[e("Option",{attrs:{value:"0"}},[t._v("删除OTA数据")]),e("Option",{attrs:{value:"1"}},[t._v("新增OTA数据")])],1)],1),t._v("   \n        "),e("div",{staticClass:"search_box1"},[e("span",{staticClass:"search_box_label"},[t._v("任务状态:")]),e("Select",{staticClass:"searchCondition",attrs:{filterable:"",placeholder:"请选择任务状态",clearable:""},model:{value:t.searchObj.status,callback:function(e){t.$set(t.searchObj,"status",e)},expression:"searchObj.status"}},[e("Option",{attrs:{value:"0"}},[t._v("待开始")]),e("Option",{attrs:{value:"1"}},[t._v("处理中")]),e("Option",{attrs:{value:"2"}},[t._v("已完成")]),e("Option",{attrs:{value:"3"}},[t._v("失败")])],1)],1),t._v("   \n\t\t\t\t"),e("div",{staticClass:"search_box1"},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.search()}}},[t._v("\n            搜索\n          ")]),t._v("      \n\t\t\t\t\t"),e("Button",{directives:[{name:"has",rawName:"v-has",value:"addTask",expression:"'addTask'"}],attrs:{type:"warning",icon:"md-add"},on:{click:function(e){return t.addTask()}}},[t._v("新建任务")])],1)]),e("div",{staticStyle:{"margin-top":"20px"}},[e("Table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{columns:t.columns,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"originFilePath",fn:function(a){var i=a.row;a.index;return[i.originFilePath?e("Button",{directives:[{name:"has",rawName:"v-has",value:"originFilePath",expression:"'originFilePath'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"info",ghost:"",size:"small"},on:{click:function(e){return t.exportfile(i.id,"1")}}},[t._v("\n              点击下载\n            ")]):t._e()]}},{key:"successedFilePath",fn:function(a){var i=a.row;a.index;return[i.successedFilePath?e("Button",{directives:[{name:"has",rawName:"v-has",value:"successedFilePath",expression:"'successedFilePath'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",ghost:"",size:"small"},on:{click:function(e){return t.exportfile(i.id,"2")}}},[t._v("\n            点击下载\n            ")]):t._e()]}},{key:"failedFilePath",fn:function(a){var i=a.row;a.index;return[i.failedFilePath?e("Button",{directives:[{name:"has",rawName:"v-has",value:"failedFilePath",expression:"'failedFilePath'"}],attrs:{type:"error",ghost:"",size:"small"},on:{click:function(e){return t.exportfile(i.id,"3")}}},[t._v("\n            点击下载\n            ")]):t._e()]}}])}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Table",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"modelTable",attrs:{columns:t.modelColumns,data:t.modelData}}),e("Modal",{attrs:{title:"新建OTA数据管理任务","mask-closable":!1,width:"600px"},on:{"on-cancel":t.cancelModal},model:{value:t.addTaskModel,callback:function(e){t.addTaskModel=e},expression:"addTaskModel"}},[e("Form",{ref:"formobj",staticStyle:{"font-weight":"bold"},attrs:{model:t.formobj,rules:t.formobjRule,"label-width":120,"label-height":100,inline:""}},[e("FormItem",{attrs:{label:"任务名称",prop:"taskName"}},[e("Input",{staticStyle:{width:"400px"},attrs:{maxlength:"255",placeholder:"请输入任务名称",clearable:""},model:{value:t.formobj.taskName,callback:function(e){t.$set(t.formobj,"taskName","string"===typeof e?e.trim():e)},expression:"formobj.taskName"}})],1),e("FormItem",{attrs:{label:"任务类型",prop:"taskType"}},[e("RadioGroup",{on:{"on-change":function(e){return t.changeType(t.formobj.taskType)}},model:{value:t.formobj.taskType,callback:function(e){t.$set(t.formobj,"taskType",e)},expression:"formobj.taskType"}},[e("Radio",{attrs:{label:"0"}},[t._v("删除OTA数据")]),t._v("\n                                \n              "),e("Radio",{attrs:{label:"1"}},[t._v("新增OTA数据")])],1)],1),t.formobj.taskType?e("FormItem",{staticStyle:{width:"520px"},attrs:{label:"文件",prop:"file"}},[e("Upload",{attrs:Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])({multiple:"",type:"drag",action:"//jsonplaceholder.typicode.com/posts/"},"action",t.uploadUrl),"on-success",t.fileSuccess),"on-error",t.handleError),"before-upload",t.handleBeforeUpload),"on-progress",t.fileUploading)},[e("div",{staticStyle:{padding:"20px 0"}},[e("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),e("p",[t._v("点击或拖拽文件上传")])],1)]),t.file?e("ul",{staticClass:"ivu-upload-list",staticStyle:{width:"100%"}},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("Icon",{attrs:{type:"ios-folder"}}),t._v(t._s(t.file.name))],1),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeFile}})])]):t._e(),e("div",{staticStyle:{width:"100%"}},[e("Button",{attrs:{type:"primary",icon:"ios-download"},on:{click:t.downloadFile}},[t._v("下载模板文件")]),e("Alert",{staticStyle:{float:"right",padding:"8px 10px 8px 10px"},attrs:{type:"warning"}},[t._v(t._s(t.message))])],1)],1):t._e()],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("返回")]),t._v("       \n\t\t\t  \t"),e("Button",{attrs:{type:"primary",loading:t.uploadLoading},on:{click:t.handleUpload}},[t._v("确定")])],1)],1),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v("导出提示")]),e("FormItem",{attrs:{label:"你本次导出任务ID为:"}},[e("span",{staticStyle:{width:"100px"}},[t._v(t._s(t.taskId))])]),e("FormItem",{attrs:{label:"你本次导出的文件名为:"}},[e("span",[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v("请前往下载管理-下载列表查看及下载。")])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v("立即前往")])],1)]),e("a",{ref:"downloadLink",staticStyle:{display:"none"}})],1)],1)}),o=[],n=(a("d9e2"),a("14d9"),a("a9e3"),a("d3b7"),a("00b4"),a("3ca3"),a("466d"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("66df")),l="/rms/api/v1",r=function(t){return n["a"].request({url:l+"/ota2/list",params:t,method:"get"})},c=function(t){return n["a"].request({url:l+"/ota2/add",data:t,method:"post"})},d=function(t){return n["a"].request({url:l+"/ota2/delete",data:t,method:"post"})},u=function(t){return n["a"].request({url:l+"/ota2/download",params:t,method:"get"})},p=function(t){return n["a"].request({url:l+"/ota2/downloadTemplate",params:t,method:"get",responseType:"blob"})},f={data:function(){var t=this,e=function(e,a,i){t.uploadList&&0===t.uploadList.length?i(new Error("请上传文件")):i()};return{total:0,currentPage:1,page:1,searchloading:!1,loading:!1,uploadLoading:!1,downloadFileLoading:!1,addTaskModel:!1,exportModal:!1,searchObj:{taskName:"",taskType:"",status:""},columns:[{title:"任务名称",key:"taskName",minWidth:150,align:"center",tooltip:!0},{title:"任务执行时间",key:"createTime",minWidth:160,align:"center",tooltip:!0},{title:"任务结束时间",key:"updateTime",minWidth:160,align:"center",tooltip:!0},{title:"任务类型",key:"taskType",minWidth:150,align:"center",tooltip:!0,render:function(t,e){var a=e.row,i="",s="";switch(a.taskType){case"0":i="删除OTA数据",s="#ff2679";break;case"1":i="新增OTA数据",s="#38aa19";break;default:i="",s=""}return t("label",{style:{color:s}},i)}},{title:"任务状态",key:"status",minWidth:150,align:"center",tooltip:!0,render:function(t,e){var a=e.row,i="",s="";switch(a.status){case 0:i="待开始",s="#06b5ff";break;case 1:i="处理中",s="#ffb70f";break;case 2:i="已完成",s="#07ff8f";break;case 3:i="失败",s="#ff2167";break;default:i="",s=""}return t("label",{style:{color:s}},i)}},{title:"号码总数",key:"totalCount",minWidth:150,align:"center",tooltip:!0},{title:"成功数量",key:"successCount",minWidth:150,align:"center",tooltip:!0},{title:"失败数量",key:"failCount",minWidth:150,align:"center",tooltip:!0},{title:"失败原因",key:"description",minWidth:150,align:"center",tooltip:!0},{title:"源文件",slot:"originFilePath",width:100,align:"center",fixed:"right"},{title:"成功文件",slot:"successedFilePath",width:100,align:"center",fixed:"right"},{title:"失败文件",slot:"failedFilePath",width:100,align:"center",fixed:"right"}],data:[],modelColumns:[{title:"iccid",key:"iccid"}],modelData:[{iccid:""}],formobj:{taskName:"",type:"",file:""},file:null,taskId:"",taskName:"",message:"",uploadUrl:"",uploadList:[],formobjRule:{taskName:[{required:!0,message:"请输入任务名称",trigger:"blur"}],taskType:[{required:!0,message:"请选择任务类型"}],file:[{required:!0,validator:e,trigger:"change"}]}}},mounted:function(){this.goPageFirst(1)},methods:{goPageFirst:function(t){var e=this;this.loading=!0;var a=this;r({pageSize:10,pageNumber:t,taskName:this.searchObj.taskName,taskType:this.searchObj.taskType,status:this.searchObj.status}).then((function(i){"0000"==i.code&&(a.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t,e.total=Number(i.count),e.data=i.data)})).catch((function(t){console.log(t)})).finally((function(){a.loading=!1,e.searchloading=!1}))},search:function(){this.searchloading=!0,this.goPageFirst(1)},goPage:function(t){this.goPageFirst(t)},exportfile:function(t,e){var a=this;u({taskId:t,fileType:e,userId:this.$store.state.user.userId}).then((function(t){a.exportModal=!0,a.taskId=t.data.id,a.taskName=t.data.fileName})).catch()},addTask:function(){this.addTaskModel=!0},handleUpload:function(){var t=this;this.$refs["formobj"].validate((function(e){if(e){var a=new FormData;a.append("taskName",t.formobj.taskName),a.append("taskType",t.formobj.taskType),a.append("file",t.file),t.uploadLoading=!0;var i="1"==t.formobj.taskType?c:d;i(a).then((function(e){if("0000"!==e.code)throw e;t.$Notice.success({title:"操作提醒",desc:"任务已开始，请稍后查看！"}),t.addTaskModel=!1,t.goPageFirst(1),t.file="",t.$refs["formobj"].resetFields()})).catch((function(t){})).finally((function(){t.uploadLoading=!1}))}}))},downloadFile:function(){var t=this;"1"==this.formobj.taskType?(this.downloadFileLoading=!0,p({templateType:"add"}).then((function(e){var a=e.data,i=decodeURIComponent(escape(e.headers["content-disposition"].match(/=(.*)$/)[1]));if("download"in document.createElement("a")){var s=t.$refs.downloadLink,o=URL.createObjectURL(a);s.download=i,s.href=o,s.click(),URL.revokeObjectURL(o)}else navigator.msSaveBlob(a,i)})).catch((function(t){return console.error(t)})).finally((function(){t.downloadFileLoading=!1}))):"0"==this.formobj.taskType&&this.$refs.modelTable.exportCsv({filename:"DeleteCards",type:"csv",columns:this.modelColumns,data:this.modelData})},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModal=!1},fileSuccess:function(t,e,a){this.message="请先下载模板文件，并按格式填写后上传"},handleError:function(t,e){var a=this;setTimeout((function(){a.uploading=!1,a.$Notice.warning({title:"错误提示",desc:"上传失败！"})}),3e3)},handleBeforeUpload:function(t,e){var a=!1;return"1"===this.formobj.taskType?/^.+(\.zip)$/i.test(t.name)?a=!0:this.$Notice.warning({title:"文件格式不正确",desc:"文件 "+t.name+" 格式不正确，请上传.zip文件。"}):"0"===this.formobj.taskType&&(/^.+(\.csv)$/i.test(t.name)?a=!0:this.$Notice.warning({title:"文件格式不正确",desc:"文件 "+t.name+" 格式不正确，请上传.csv文件。"})),a&&(this.file=t,this.uploadList=e),!1},fileUploading:function(t,e,a){this.message="文件上传中、待进度条消失后再操作"},removeFile:function(){this.file=""},changeType:function(t){this.file=null,this.uploadList=[],"1"==t?this.message="填写好模板文件后，打包成zip压缩包进行上传":"0"==t&&(this.message="请上传.csv文件")},cancelModal:function(){this.file=null,this.$refs["formobj"].resetFields(),this.addTaskModel=!1,this.exportModal=!1}}},h=f,m=(a("44a7"),a("2877")),v=Object(m["a"])(h,s,o,!1,null,null,null);e["default"]=v.exports},"841c":function(t,e,a){"use strict";var i=a("c65b"),s=a("d784"),o=a("825a"),n=a("7234"),l=a("1d80"),r=a("129f"),c=a("577e"),d=a("dc4a"),u=a("14c3");s("search",(function(t,e,a){return[function(e){var a=l(this),s=n(e)?void 0:d(e,t);return s?i(s,e,a):new RegExp(e)[t](c(a))},function(t){var i=o(this),s=c(t),n=a(e,i,s);if(n.done)return n.value;var l=i.lastIndex;r(l,0)||(i.lastIndex=0);var d=u(i,s);return r(i.lastIndex,l)||(i.lastIndex=l),null===d?-1:d.index}]}))}}]);