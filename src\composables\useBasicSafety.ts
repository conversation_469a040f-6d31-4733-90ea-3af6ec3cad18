import { ref, onMounted, onUnmounted, nextTick } from 'vue'

/**
 * 基础组件安全性管理
 * 简化版本，专注于解决DOM操作相关的错误
 */
export function useBasicSafety() {
  // 组件状态
  const isMounted = ref(false)
  const isDestroyed = ref(false)

  /**
   * 安全的DOM操作包装器
   */
  const safeDOMOperation = (operation: () => void) => {
    if (isDestroyed.value) {
      console.warn('🚫 组件已销毁，跳过DOM操作')
      return
    }
    
    if (!isMounted.value) {
      console.warn('🚫 组件未挂载，跳过DOM操作')
      return
    }

    try {
      operation()
    } catch (error) {
      console.error('❌ DOM操作失败:', error)
    }
  }

  /**
   * 安全的异步操作包装器
   */
  const safeAsyncOperation = async <T>(
    operation: () => Promise<T>,
    fallback?: T
  ): Promise<T | undefined> => {
    if (isDestroyed.value) {
      console.warn('🚫 组件已销毁，跳过异步操作')
      return fallback
    }

    try {
      const result = await operation()
      
      // 操作完成后再次检查组件状态
      if (isDestroyed.value) {
        console.warn('🚫 组件在异步操作过程中被销毁')
        return fallback
      }
      
      return result
    } catch (error) {
      console.error('❌ 异步操作失败:', error)
      return fallback
    }
  }

  /**
   * 创建安全的ref
   */
  const createSafeRef = <T = any>(initialValue?: T) => {
    const refValue = ref<T | null>(initialValue || null)
    
    // 在组件卸载时清理引用
    onUnmounted(() => {
      refValue.value = null
    })
    
    return refValue
  }

  // 生命周期管理
  onMounted(async () => {
    console.log('🎉 组件开始挂载')
    
    // 等待DOM完全渲染
    await nextTick()
    
    isMounted.value = true
    isDestroyed.value = false
    
    console.log('✅ 组件挂载完成')
  })

  onUnmounted(() => {
    console.log('🔄 组件开始卸载')
    
    isDestroyed.value = true
    isMounted.value = false
    
    console.log('✅ 组件卸载完成')
  })

  return {
    // 状态
    isMounted,
    isDestroyed,
    
    // 方法
    safeDOMOperation,
    safeAsyncOperation,
    createSafeRef
  }
}
