/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { OriginalPOITransaction } from './originalPOITransaction';
import { StoredValueAccountId } from './storedValueAccountId';
import { StoredValueTransactionType } from './storedValueTransactionType';
export declare class StoredValueData {
    'Currency': string;
    'EanUpc'?: string;
    'ItemAmount': number;
    'OriginalPOITransaction'?: OriginalPOITransaction;
    'ProductCode'?: string;
    'StoredValueAccountID'?: StoredValueAccountId;
    'StoredValueProvider'?: string;
    'StoredValueTransactionType': StoredValueTransactionType;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
