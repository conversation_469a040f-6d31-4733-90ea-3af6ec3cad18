"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.POICapabilitiesType = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var POICapabilitiesType;
(function (POICapabilitiesType) {
    POICapabilitiesType[POICapabilitiesType["CashHandling"] = 'CashHandling'] = "CashHandling";
    POICapabilitiesType[POICapabilitiesType["CashierDisplay"] = 'CashierDisplay'] = "CashierDisplay";
    POICapabilitiesType[POICapabilitiesType["CashierError"] = 'CashierError'] = "CashierError";
    POICapabilitiesType[POICapabilitiesType["CashierInput"] = 'CashierInput'] = "CashierInput";
    POICapabilitiesType[POICapabilitiesType["CustomerDisplay"] = 'CustomerDisplay'] = "CustomerDisplay";
    POICapabilitiesType[POICapabilitiesType["CustomerError"] = 'CustomerError'] = "CustomerError";
    POICapabilitiesType[POICapabilitiesType["CustomerInput"] = 'CustomerInput'] = "CustomerInput";
    POICapabilitiesType[POICapabilitiesType["EmvContactless"] = 'EMVContactless'] = "EmvContactless";
    POICapabilitiesType[POICapabilitiesType["Icc"] = 'ICC'] = "Icc";
    POICapabilitiesType[POICapabilitiesType["MagStripe"] = 'MagStripe'] = "MagStripe";
    POICapabilitiesType[POICapabilitiesType["PrinterDocument"] = 'PrinterDocument'] = "PrinterDocument";
    POICapabilitiesType[POICapabilitiesType["PrinterReceipt"] = 'PrinterReceipt'] = "PrinterReceipt";
    POICapabilitiesType[POICapabilitiesType["PrinterVoucher"] = 'PrinterVoucher'] = "PrinterVoucher";
})(POICapabilitiesType = exports.POICapabilitiesType || (exports.POICapabilitiesType = {}));
//# sourceMappingURL=pOICapabilitiesType.js.map