var u=Object.defineProperty,S=Object.defineProperties;var b=Object.getOwnPropertyDescriptors;var f=Object.getOwnPropertySymbols;var p=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable;var h=(t,n,e)=>n in t?u(t,n,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[n]=e,a=(t,n)=>{for(var e in n||(n={}))p.call(n,e)&&h(t,e,n[e]);if(f)for(var e of f(n))m.call(n,e)&&h(t,e,n[e]);return t},s=(t,n)=>S(t,b(n));import{t as C,f as g,e as v}from"./tree-Dgaev6Pi.js";import{V as T}from"./vue-chunks-Co0GcRjL.js";const q=t=>{const n=T({searchSchema:[],tableColumns:[],formSchema:[],detailSchema:[]}),e=x(t);n.searchSchema=e;const l=D(t);n.tableColumns=l||[];const i=F(t);n.formSchema=i;const o=M(t);return n.detailSchema=o,{allSchemas:n}},x=t=>{var l,i,o;const n=[],e=t.length;for(let r=0;r<e;r++){const c=t[r];if(((l=c.search)==null?void 0:l.hidden)===!0)continue;const d=s(a({component:((i=c==null?void 0:c.search)==null?void 0:i.component)||"Input"},c.search),{field:c.field,label:((o=c.search)==null?void 0:o.label)||c.label});n.push(d)}return n},D=t=>{const n=C(t,{conversion:e=>{var l;if(!((l=e==null?void 0:e.table)!=null&&l.hidden))return a(a({},e),e.table)}});return g(n,e=>(e.children===void 0&&delete e.children,!!e.field))},F=t=>{var l,i;const n=[],e=t.length;for(let o=0;o<e;o++){const r=t[o],c=s(a({component:((l=r==null?void 0:r.form)==null?void 0:l.component)||"Input"},r.form),{field:r.field,label:((i=r.form)==null?void 0:i.label)||r.label});n.push(c)}return n},M=t=>{const n=[];return v(t,e=>{var l,i;if(!((l=e==null?void 0:e.detail)!=null&&l.hidden)){const o=s(a({},e.detail),{field:e.field,label:((i=e.detail)==null?void 0:i.label)||e.label});delete o.hidden,n.push(o)}}),n};export{q as u};
