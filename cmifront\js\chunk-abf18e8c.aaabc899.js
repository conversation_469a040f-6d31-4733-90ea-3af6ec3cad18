(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-abf18e8c"],{"07ac":function(e,t,a){"use strict";var r=a("23e7"),n=a("6f53").values;r({target:"Object",stat:!0},{values:function(e){return n(e)}})},"209e":function(e,t,a){},"4ec9":function(e,t,a){"use strict";a("6f48")},6035:function(e,t,a){"use strict";a("ef11")},"614b":function(e,t,a){"use strict";a.r(t);a("4de4"),a("d3b7"),a("498a");var r=function(){var e=this,t=e._self._c;return t("Card",{staticClass:"card-wrapper"},[t("Form",{ref:"formValidate",staticClass:"form-wrapper",attrs:{model:e.formValidate,rules:e.ruleValidate,"label-width":80}},[t("div",{staticClass:"form-section"},[t("div",{staticClass:"section-header"},[e._v("基本信息")]),t("div",{staticClass:"section-content"},[t("Row",{attrs:{gutter:24}},[t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"规则名称",prop:"groupName"}},[t("Input",{staticClass:"uniform-input",attrs:{placeholder:"请输入规则名称",maxlength:200},model:{value:e.formValidate.groupName,callback:function(t){e.$set(e.formValidate,"groupName",t)},expression:"formValidate.groupName"}})],1)],1),t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"国家",prop:"mcc"}},[t("Select",{staticClass:"uniform-select",attrs:{placeholder:"请选择国家",filterable:"",clearable:"",disabled:e.isEdit},model:{value:e.formValidate.mcc,callback:function(t){e.$set(e.formValidate,"mcc",t)},expression:"formValidate.mcc"}},e._l(e.countryList,(function(a,r){return t("Option",{key:r,staticClass:"select-option",staticStyle:{width:"300px"},attrs:{value:String(a.mcc),label:a.countryEn}},[e._v("\n                  "+e._s(a.countryEn)+"\n                ")])})),1)],1)],1),t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"是否生效",prop:"status"}},[t("i-switch",{model:{value:e.formValidate.status,callback:function(t){e.$set(e.formValidate,"status",t)},expression:"formValidate.status"}})],1)],1)],1)],1),t("div",{staticClass:"section-header"},[e._v("卡池信息")]),t("div",{staticClass:"section-content"},[t("div",{staticClass:"card-pool-table"},[t("Table",{attrs:{columns:e.cardPoolColumns,border:"",data:e.formValidate.details},scopedSlots:e._u([{key:"supplier",fn:function(a){var r=a.row,n=a.index;return[r.isDefault?t("div",{staticClass:"supplier-select-wrapper"},[e._v("-")]):t("div",{staticClass:"supplier-select-wrapper"},[t("FormItem",{attrs:{prop:"details."+n+".supplierId",rules:[{required:!0,message:"供应商不能为空",trigger:"change"}],"label-width":0}},[t("Select",{staticClass:"supplier-select-select",attrs:{placeholder:"请选择供应商",filterable:"",clearable:"",transfer:""},on:{"on-change":function(t){return e.handleSupplierChange(t,n)}},model:{value:r.supplierId,callback:function(t){e.$set(r,"supplierId",t)},expression:"row.supplierId"}},e._l(e.supplierList,(function(a,r){return t("Option",{key:r,attrs:{value:a.supplierId}},[e._v(e._s(a.supplierName))])})),1)],1)],1)]}},{key:"cardPool",fn:function(a){var r=a.row,n=a.index;return[r.isDefault?t("div",{staticStyle:{"font-weight":"bold"}},[e._v("默认")]):t("div",{staticClass:"pool-container"},[t("div",{staticClass:"pool-row"},[t("div",{staticClass:"pool-select"},[t("FormItem",{attrs:{prop:"details."+n+".cardPools",rules:e.cardPoolRules,"label-width":0}},[t("Select",{staticClass:"pool-select-select",attrs:{placeholder:"请选择卡池名称",multiple:"",filterable:"",clearable:"",transfer:""},on:{"on-change":function(t){return e.handleCardPoolChange(t,n)},"on-open-change":function(t){return e.handleCardPoolSelectOpen(t,n)}},model:{value:r.selectedPoolIds,callback:function(t){e.$set(r,"selectedPoolIds",t)},expression:"row.selectedPoolIds"}},e._l(r.availableCardPools,(function(a,r){return t("Option",{key:r,staticClass:"select-option",attrs:{value:a.poolId,label:a.poolName}},[e._v("\n                          "+e._s(a.poolName)+"\n                        ")])})),1)],1)],1),r.selectedPoolIds&&r.selectedPoolIds.length>0?t("div",{staticClass:"pool-percentages"},e._l(r.selectedPoolIds,(function(a,l){return t("div",{key:l,staticClass:"percentage-item"},[t("InputNumber",{staticClass:"percentage-input",attrs:{min:0,max:100,size:"small"},on:{"on-change":function(t){return e.handleRateChange(t,n,l)}},model:{value:r.selectedRates[l],callback:function(t){e.$set(r.selectedRates,l,t)},expression:"row.selectedRates[pIndex]"}}),t("span",{staticClass:"percentage-symbol"},[e._v("%")])],1)})),0):e._e()])])]}},{key:"remarkCn",fn:function(a){var r=a.row,n=a.index;return[t("FormItem",{attrs:{prop:"details."+n+".remarkCn",rules:[{required:!0,message:r.isDefault?"默认项中文备注不能为空":"中文备注不能为空",trigger:"blur"},{validator:function(e,t,a){t&&""===t.trim()?a("中文备注不能为空"):a()},trigger:"blur"}],"label-width":0}},[t("Input",{attrs:{type:"textarea",maxlength:1e3,rows:2,placeholder:"请输入中文备注"},model:{value:e.formValidate.details[n].remarkCn,callback:function(t){e.$set(e.formValidate.details[n],"remarkCn",t)},expression:"formValidate.details[index].remarkCn"}})],1)]}},{key:"remarkEn",fn:function(a){var r=a.row,n=a.index;return[t("FormItem",{attrs:{prop:"details."+n+".remarkEn",rules:[{required:!0,message:r.isDefault?"默认项英文备注不能为空":"英文备注不能为空",trigger:"blur"},{validator:function(e,t,a){t&&""===t.trim()?a("英文备注不能为空"):a()},trigger:"blur"}],"label-width":0}},[t("Input",{attrs:{type:"textarea",maxlength:1e3,rows:2,placeholder:"请输入英文备注"},model:{value:e.formValidate.details[n].remarkEn,callback:function(t){e.$set(e.formValidate.details[n],"remarkEn",t)},expression:"formValidate.details[index].remarkEn"}})],1)]}},{key:"action",fn:function(a){var r=a.row,n=a.index;return[!r.isDefault&&e.formValidate.details.filter((function(e){return!e.isDefault})).length>=2?t("Button",{attrs:{type:"error",size:"small"},on:{click:function(t){return e.removeCardPoolRow(n)}}},[e._v("\n                删除\n              ")]):e._e()]}}])}),t("div",{staticClass:"add-row"},[t("Button",{attrs:{type:"primary"},on:{click:e.addCardPoolRow}},[e._v("\n               添加\n            ")])],1)],1)]),t("div",{staticClass:"section-header"},[e._v("渠道商信息")]),t("div",{staticClass:"section-content channel-section"},[t("Row",[t("Col",{attrs:{span:"24"}},[t("DualTableSelect",{ref:"dualTableSelect",attrs:{"source-columns":e.channelColumns,"source-data":e.channelList,"selected-columns":e.selectedChannelColumns,"selected-data":e.selectedChannelData,total:e.totalChannels,current:e.currentPage,"page-size":e.pageSize,loading:e.loading,"check-all":e.computedCheckAll,indeterminate:e.computedIndeterminate,"search-placeholder":"请输入渠道商名称搜索"},on:{"on-search":e.handleSearch,"on-check-all":e.handleCheckAll,"on-page-change":e.onPageChange,"on-select":e.onSelect,"on-select-cancel":e.onSelectCancel,"on-select-all":e.onSelectAll,"on-select-all-cancel":e.onSelectAllCancel,"on-remove":e.handleRemoveChannel},model:{value:e.formValidate.corps,callback:function(t){e.$set(e.formValidate,"corps",t)},expression:"formValidate.corps"}},[t("template",{slot:"selected-header"},[t("div",[t("div",{staticClass:"custom-selected-header",staticStyle:{height:"32px","line-height":"32px"}},[e._v("\n                    已选渠道商\n                  ")])])])],2)],1)],1)],1),t("div",{staticClass:"form-actions"},[t("Button",{directives:[{name:"has",rawName:"v-has",value:"detailSubmit",expression:"'detailSubmit'"}],attrs:{type:"primary",loading:e.isSubmitting},on:{click:e.handleSubmit}},[e._v("确认")]),t("Button",{staticStyle:{"margin-left":"8px"},on:{click:e.handleCancel}},[e._v("返回")])],1)])])],1)},n=[],l=a("2909"),s=a("5530"),i=a("53ca"),o=a("c7eb"),c=a("1da1"),u=(a("d9e2"),a("99af"),a("cb29"),a("7db0"),a("c740"),a("caad"),a("a15b"),a("d81d"),a("14d9"),a("a434"),a("b0c0"),a("4ec9"),a("a9e3"),a("b64b"),a("07ac"),a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),a("2532"),a("3ca3"),a("159b"),a("ddb0"),a("66df")),d="/cms",h="/pms/api/v1",p="/oms/api/v1",f="/rms/api/v1",m=function(e){return u["a"].request({url:p+"/country/queryCounrtyList",method:"get",data:e})},g=function(e){return u["a"].request({url:d+"/channel/pagelist",method:"post",data:e})},v=function(e){return u["a"].request({url:h+"/cardPool/getCardPoolinfoBymcc",method:"get",params:e})};function b(e){return u["a"].request({url:h+"/cardpoolSpecial/addSpecial",method:"post",data:e})}function C(e){return u["a"].request({url:h+"/cardpoolSpecial/editSpecial",method:"post",data:e})}function k(e){return u["a"].request({url:h+"/cardpoolSpecial/pagelist",method:"post",data:e})}var I=function(e){return u["a"].request({url:f+"/supplier/query",method:"get",params:e})},S=a("d1c9"),w={name:"SpecialCountryRuleUpdate",components:{DualTableSelect:S["a"]},data:function(){var e=this;return{isEdit:!1,formValidate:{groupName:"",countryEn:"",mcc:"",status:0,details:[this.getDefaultDetail(),this.getEmptyDetail()],corps:[]},ruleValidate:{groupName:[{required:!0,message:"规则名称不能为空",trigger:"blur"},{type:"string",max:200,message:"规则名称最多支持200字符",trigger:"blur"},{validator:function(e,t,a){t&&""===t.trim()?a("规则名称不能为空"):a()},trigger:"blur"}],mcc:[{required:!0,message:"请选择国家",trigger:"change"}]},countryList:[],cardPoolList:[],channelList:[],selectedChannelData:[],supplierList:[],searchChannel:"",currentPage:1,pageSize:10,totalChannels:0,checkAll:!1,indeterminate:!1,loading:!1,cardPoolColumns:[{title:"供应商",slot:"supplier",width:230,align:"center"},{title:"卡池名称及分配比例",slot:"cardPool",width:430,align:"center"},{title:"备注中文",slot:"remarkCn",minWidth:350},{title:"备注英文",slot:"remarkEn",minWidth:350},{title:"操作",slot:"action",align:"center",width:80}],channelColumns:[{type:"selection",width:60,align:"center"},{title:"渠道商简称",key:"corpName",align:"center",width:200,ellipsis:!0,tooltip:!0},{title:"渠道商公司名称",key:"companyName",align:"center",ellipsis:!0,tooltip:!0},{title:"国家卡池关联组",key:"relationGroupNames",align:"center",width:280,ellipsis:!0,tooltip:!0,render:function(t,a){return e.renderRelationGroupNames(t,a)}}],selectedChannelColumns:[{title:"渠道商简称",key:"corpName",align:"center",width:200,ellipsis:!0,tooltip:!0},{title:"渠道商公司名称",key:"companyName",align:"center",ellipsis:!0,tooltip:!0},{title:"国家卡池关联组",key:"relationGroupNames",align:"center",width:280,ellipsis:!0,tooltip:!0,render:function(t,a){return e.renderRelationGroupNames(t,a)}},{title:"操作",slot:"action",width:80,align:"center"}],cardPoolRules:{type:"array",required:!0,message:"请选择卡池",trigger:"change"},debounceTimer:null,cardPoolCache:{},isLoadingCardPools:!1,isSubmitting:!1,currentSearchAllIds:[]}},watch:{"formValidate.mcc":{handler:function(e,t){var a=this;e?(t&&e!==t?this.formValidate.details.forEach((function(e,t){a.$set(e,"selectedPoolIds",[]),a.$set(e,"selectedRates",[]),a.$set(e,"cardPools",[]),a.$set(e,"availableCardPools",[])})):!t&&e&&this.isEdit,t||!e||this.isEdit||this.formValidate.details&&0!==this.formValidate.details.length||(this.formValidate.details=[this.getDefaultDetail(),this.getEmptyDetail()])):this.formValidate.details.forEach((function(e,t){a.$set(e,"selectedPoolIds",[]),a.$set(e,"selectedRates",[]),a.$set(e,"cardPools",[])}))}},countryList:{handler:function(e){if(this.isEdit&&this.formValidate.mcc&&e.length>0){var t=this.convertMccToString(this.formValidate.mcc),a=e.find((function(e){return String(e.mcc)===t}));a&&(this.$set(this.formValidate,"mcc",String(a.mcc)),this.$forceUpdate())}},immediate:!0}},created:function(){this.isEdit="specialCountryRuleEdit"===this.$route.name,this.init()},methods:{init:function(){var e=this;return Object(c["a"])(Object(o["a"])().mark((function t(){var a;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getSupplierList();case 2:return t.next=4,e.getCountryList();case 4:if(!e.isEdit){t.next=13;break}if(a=e.$route.query.ruleId||e.$route.params.ruleId,!a){t.next=11;break}return t.next=9,e.getDetail(a);case 9:t.next=13;break;case 11:e.$Message.error("缺少规则ID"),e.$router.push("/specialCountryRule");case 13:if(e.isEdit){t.next=16;break}return t.next=16,e.getChannels();case 16:case"end":return t.stop()}}),t)})))()},getDefaultDetail:function(){return{detailId:"",cardPools:[{poolId:"default",rate:100,poolName:"默认",supplierId:null}],remarkCn:"",remarkEn:"",selectedPoolIds:["default"],selectedRates:[100],isDefault:!0,supplierId:null,availableCardPools:[{poolId:"default",rate:100,poolName:"默认",supplierId:null}]}},getEmptyDetail:function(){return{detailId:"",cardPools:[],remarkCn:"",remarkEn:"",selectedPoolIds:[],selectedRates:[],isDefault:!1,supplierId:"",availableCardPools:[]}},renderRelationGroupNames:function(e,t){var a=t.row.relationGroupNames?"object"===Object(i["a"])(t.row.relationGroupNames)?Object.values(t.row.relationGroupNames):[t.row.relationGroupNames]:[],r=a.filter(Boolean).join("、");return r&&r.length>0?e("Tooltip",{props:{content:r,maxWidth:300,transfer:!0}},[e("span",{style:{display:"inline-block",width:"240px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},r)]):e("div","-")},getCountryList:function(){var e=this;return Object(c["a"])(Object(o["a"])().mark((function t(){var a;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,m();case 3:a=t.sent,"0000"===a.code&&(e.countryList=a.data),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),e.$Message.error("获取国家列表失败");case 10:case"end":return t.stop()}}),t,null,[[0,7]])})))()},getDetail:function(e){var t=this;return Object(c["a"])(Object(o["a"])().mark((function a(){var r,n;return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,k({groupId:e,current:-1,size:-1});case 3:if(r=a.sent,!("0000"===r.code&&r.data&&r.data.length>0)){a.next=13;break}if(n=r.data[0],0!==t.countryList.length){a.next=9;break}return a.next=9,t.getCountryList();case 9:return a.next=11,t.handleDetailData(n);case 11:a.next=14;break;case 13:t.$Message.error("获取详情失败：数据为空");case 14:a.next=19;break;case 16:a.prev=16,a.t0=a["catch"](0),t.$Message.error("获取详情失败："+(a.t0.message||"未知错误"));case 19:case"end":return a.stop()}}),a,null,[[0,16]])})))()},getCardPools:function(e,t){var a=arguments,r=this;return Object(c["a"])(Object(o["a"])().mark((function t(){var n,l,s,i,c;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n=a.length>2&&void 0!==a[2]?a[2]:{},r.formValidate.mcc){t.next=3;break}return t.abrupt("return");case 3:if(l="".concat(r.formValidate.mcc,"_").concat(e||""),s=function(t){r.formValidate.details.forEach((function(a,n){a.supplierId!==e||a.isDefault||(r.$set(r.formValidate.details[n],"availableCardPools",t),r.$nextTick((function(){if(a.selectedPoolIds&&a.selectedPoolIds.length>0&&a.availableCardPools&&a.availableCardPools.length>0){var e=a.selectedPoolIds.filter((function(e){return a.availableCardPools.some((function(t){return t.poolId===e}))}));r.$set(a,"selectedPoolIds",e)}})))}))},!r.cardPoolCache[l]){t.next=8;break}return s(r.cardPoolCache[l]),t.abrupt("return");case 8:if(r.isLoadingCardPools!==l){t.next=10;break}return t.abrupt("return");case 10:return r.isLoadingCardPools=l,i={mcc:r.formValidate.mcc,supplierId:e||""},t.prev=12,t.next=15,v(i);case 15:c=t.sent,"0000"===c.code?(r.cardPoolCache[l]=c.data,s(c.data),r.formValidate.details.forEach((function(t,a){if(t.supplierId===e&&!t.isDefault&&(!t.selectedPoolIds||0===t.selectedPoolIds.length)&&t.cardPools&&t.cardPools.length>0){var n=t.cardPools.map((function(e){return e.poolId}));r.$set(t,"selectedPoolIds",n);var l=t.cardPools.map((function(e){return e.rate}));r.$set(t,"selectedRates",l)}}))):n.silent?console.warn("获取卡池列表失败",c):r.$Message.error("获取卡池列表失败"),t.next=22;break;case 19:t.prev=19,t.t0=t["catch"](12),n.silent?console.warn("获取卡池列表失败",t.t0):r.$Message.error("获取卡池列表失败");case 22:return t.prev=22,r.isLoadingCardPools===l&&(r.isLoadingCardPools=!1),t.finish(22);case 25:case"end":return t.stop()}}),t,null,[[12,19,22,25]])})))()},getChannels:function(){var e=this;return Object(c["a"])(Object(o["a"])().mark((function t(){var a;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.prev=1,t.next=4,g({corpName:e.searchChannel,current:e.currentPage,size:e.pageSize,cooperationMode:"2"});case 4:if(a=t.sent,"0000"!==a.code){t.next=9;break}return e.handleChannelData(a),t.next=9,e.updateCurrentSearchAllIds();case 9:t.next=14;break;case 11:t.prev=11,t.t0=t["catch"](1),e.$Message.error("获取渠道商列表失败");case 14:return t.prev=14,e.loading=!1,t.finish(14);case 17:case"end":return t.stop()}}),t,null,[[1,11,14,17]])})))()},handleDetailData:function(e){var t=this;return Object(c["a"])(Object(o["a"])().mark((function a(){var r;return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return r=t.convertMccToString(e.mcc),t.formValidate={groupId:e.groupId,groupName:e.groupName,countryEn:e.countryEn,mcc:r,status:"1"==e.status,corps:e.corps||[],details:{}},t.$nextTick((function(){t.formValidate.mcc&&t.$set(t.formValidate,"mcc",r)})),a.next=5,t.processDetails(e.details);case 5:t.formValidate.details=a.sent,t.getSelectedChannelData(e.corps);case 7:case"end":return a.stop()}}),a)})))()},processDetails:function(e){var t=this;return Object(c["a"])(Object(o["a"])().mark((function a(){var r,n,l;return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return r=null,n=[],e&&e.length>0&&e.forEach((function(e){var t,a,l,s=null===(t=e.cardPools)||void 0===t?void 0:t.some((function(e){return"default"===e.poolId})),i=[],o=[];null===(a=e.cardPools)||void 0===a||a.forEach((function(e){"default"!==e.poolId&&(i.push(e.poolId),o.push(e.rate))}));var c={detailId:e.detailId||"",cardPools:e.cardPools||[],remarkCn:e.remarkCn||"",remarkEn:e.remarkEn||"",selectedPoolIds:i,selectedRates:o,supplierId:s?null:(null===(l=e.cardPools)||void 0===l||null===(l=l[0])||void 0===l?void 0:l.supplierId)||"",isDefault:s,availableCardPools:[]};s?r=c:n.push(c)})),r||(r=t.getDefaultDetail()),t.$set(t.formValidate,"details",[r].concat(n)),l=t.formValidate.details.map((function(e,a){return!e.isDefault&&e.supplierId?t.getCardPools(e.supplierId,a,{silent:!0}):Promise.resolve()})),a.next=8,Promise.all(l);case 8:return a.abrupt("return",[r].concat(n));case 9:case"end":return a.stop()}}),a)})))()},handleChannelData:function(e){var t=this;this.channelList=e.data.map((function(e){return Object(s["a"])(Object(s["a"])({},e),{},{_checked:t.formValidate.corps.includes(e.corpId)})})),this.totalChannels=JSON.parse(e.count),this.currentSearchAllIds=(e.data||[]).map((function(e){return e.corpId})),this.updateCheckAllStatus()},handleCardPoolChange:function(e,t){var a=this.formValidate.details[t];a.selectedPoolIds=e;var r=new Array(e.length).fill(0);e.forEach((function(e,t){var n=a.selectedPoolIds.indexOf(e);-1!==n&&n<a.selectedRates.length&&(r[t]=a.selectedRates[n])})),this.$set(a,"selectedRates",r),this.updateCardPoolsStructure(t)},updateCardPoolsStructure:function(e){var t=this.formValidate.details[e],a=t.selectedPoolIds.map((function(e,a){return{poolId:e,rate:t.selectedRates[a]||0}}));this.$set(t,"cardPools",a)},handleSearch:function(e){this.searchChannel=e,this.currentPage=1,this.getChannels()},handleCheckAll:function(e){var t=this;this.debounceTimer&&clearTimeout(this.debounceTimer),this.debounceTimer=setTimeout(Object(c["a"])(Object(o["a"])().mark((function a(){var r,n,s,i,c,u;return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.loading=!0,a.prev=1,a.next=4,g({corpName:t.searchChannel,current:-1,size:-1,cooperationMode:"2"});case 4:if(r=a.sent,"0000"===r.code){a.next=7;break}throw new Error(r.msg||"获取渠道商列表失败");case 7:n=r.data||[],s=n.map((function(e){return e.corpId})),e?(i=Object(l["a"])(t.formValidate.corps),c=Object(l["a"])(t.selectedChannelData),u=new Map(c.map((function(e){return[e.corpId,e]}))),n.forEach((function(e){i.includes(e.corpId)||(i.push(e.corpId),u.has(e.corpId)||(c.push({corpId:e.corpId,corpName:e.corpName,companyName:e.companyName,relationGroupNames:e.relationGroupNames||{}}),u.set(e.corpId,e)))})),t.formValidate.corps=i,t.selectedChannelData=c,t.updateSelectionStatus(!0)):(t.formValidate.corps=t.formValidate.corps.filter((function(e){return!s.includes(e)})),t.selectedChannelData=t.selectedChannelData.filter((function(e){return!s.includes(e.corpId)})),t.updateSelectionStatus(!1)),a.next=15;break;case 12:a.prev=12,a.t0=a["catch"](1),t.$Message.error("处理全选/取消全选操作失败: "+a.t0.message);case 15:return a.prev=15,t.loading=!1,t.updateCheckAllStatus(),a.finish(15);case 19:case"end":return a.stop()}}),a,null,[[1,12,15,19]])}))),300)},updateSelectionStatus:function(e){var t,a=this;this.checkAll=e,this.indeterminate=!1,(null===(t=this.channelList)||void 0===t?void 0:t.length)>0&&(this.channelList.forEach((function(t){t._checked=e})),this.$nextTick((function(){var t;null!==(t=a.$refs.dualTableSelect)&&void 0!==t&&t.$refs.sourceTable&&a.$refs.dualTableSelect.$refs.sourceTable.selectAll(e),a.$forceUpdate()})))},handleSubmit:function(){var e=this;return Object(c["a"])(Object(o["a"])().mark((function t(){return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$refs.formValidate.validate(function(){var t=Object(c["a"])(Object(o["a"])().mark((function t(a){var r,n;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a&&e.validateRates()){t.next=2;break}return t.abrupt("return");case 2:if(!e.isSubmitting){t.next=4;break}return t.abrupt("return");case 4:return e.isSubmitting=!0,t.prev=5,r=e.prepareSubmitData(),t.next=9,e.submitData(r);case 9:n=t.sent,"0000"===(null===n||void 0===n?void 0:n.code)&&(e.$Message.success("操作成功"),e.$router.push("/specialCountryRule")),t.next=16;break;case 13:t.prev=13,t.t0=t["catch"](5),e.$Message.error("操作失败");case 16:return t.prev=16,e.isSubmitting=!1,t.finish(16);case 19:case"end":return t.stop()}}),t,null,[[5,13,16,19]])})));return function(e){return t.apply(this,arguments)}}());case 1:case"end":return t.stop()}}),t)})))()},validateRates:function(){var e=this,t=!0;return this.formValidate.details.forEach((function(a,r){var n;if(e.updateCardPoolsStructure(r),(null===(n=a.selectedPoolIds)||void 0===n?void 0:n.length)>0){var l=a.selectedRates.some((function(e){return Number(e)<=0}));if(l)return t=!1,void e.$Message.error("第".concat(r+1,"行的卡池分配比例不能为负数或0"));var s=a.selectedRates.reduce((function(e,t){return e+(Number(t)||0)}),0);Math.abs(s-100)>.01&&(t=!1,e.$Message.error("第".concat(r+1,"行的卡池分配比例之和必须为100%，当前为").concat(s,"%")))}})),t},prepareSubmitData:function(){var e,t=this;return this.formValidate.details.forEach((function(e,a){t.updateCardPoolsStructure(a)})),Object(s["a"])(Object(s["a"])({},this.isEdit?{groupId:this.formValidate.groupId}:{}),{},{groupName:this.formValidate.groupName?this.formValidate.groupName.trim():"",countryEn:(null===(e=this.countryList.find((function(e){return e.mcc===t.formValidate.mcc})))||void 0===e?void 0:e.countryEn)||"",mcc:this.formValidate.mcc,status:this.formValidate.status?1:0,corps:this.formValidate.corps,details:this.formValidate.details.map((function(e){return e.isDefault?{detailId:e.detailId||"",cardPools:[{poolId:"default",rate:100}],remarkCn:e.remarkCn?e.remarkCn.trim():"",remarkEn:e.remarkEn?e.remarkEn.trim():"",supplierId:e.supplierId||""}:{detailId:e.detailId||"",cardPools:e.cardPools||[],remarkCn:e.remarkCn?e.remarkCn.trim():"",remarkEn:e.remarkEn?e.remarkEn.trim():"",supplierId:e.supplierId||""}}))})},submitData:function(e){var t=this;return Object(c["a"])(Object(o["a"])().mark((function a(){return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!t.isEdit){a.next=6;break}return a.next=3,C(e);case 3:a.t0=a.sent,a.next=9;break;case 6:return a.next=8,b(e);case 8:a.t0=a.sent;case 9:return a.abrupt("return",a.t0);case 10:case"end":return a.stop()}}),a)})))()},handleReset:function(){this.$refs.formValidate.resetFields(),this.formValidate.corps=[],this.selectedChannelData=[],this.checkAll=!1,this.indeterminate=!1,this.getChannels()},handleCancel:function(){this.$router.push("/specialCountryRule")},handleRateChange:function(e,t,a){var r=this.formValidate.details[t];this.$set(r.selectedRates,a,e),this.updateCardPoolsStructure(t)},removeCardPoolRow:function(e){var t=this;this.formValidate.details.length>1&&!this.formValidate.details[e].isDefault&&(this.formValidate.details.forEach((function(a,r){r!==e&&t.updateCardPoolsStructure(r)})),this.formValidate.details.splice(e,1))},addCardPoolRow:function(){var e=this;this.formValidate.details.forEach((function(t,a){e.updateCardPoolsStructure(a)}));var t=this.getEmptyDetail();this.formValidate.details.push(t)},onPageChange:function(e){this.currentPage=e,this.getChannels()},onSelect:function(e,t){this.updateSelectedForCurrentPage(e)},onSelectCancel:function(e,t){this.updateSelectedForCurrentPage(e)},onSelectAll:function(e){this.updateSelectedForCurrentPage(e)},onSelectAllCancel:function(e){this.updateSelectedForCurrentPage(e)},updateSelectedForCurrentPage:function(e){var t=this.channelList.map((function(e){return e.corpId})),a=this.formValidate.corps.filter((function(e){return!t.includes(e)})),r=e.map((function(e){return e.corpId}));this.formValidate.corps=[].concat(Object(l["a"])(a),Object(l["a"])(r));var n=this.selectedChannelData.filter((function(e){return!t.includes(e.corpId)}));this.selectedChannelData=[].concat(Object(l["a"])(n),Object(l["a"])(e)),this.isAllSelected=this.formValidate.corps.length===this.totalChannels,this.updateCheckAllStatus()},updateCheckAllStatus:function(){var e=this;if(this.isAllSelected)return this.checkAll=!0,void(this.indeterminate=!1);this.channelList.filter((function(t){return e.formValidate.corps.includes(t.corpId)})).length;var t=this.formValidate.corps.length;0===this.totalChannels||0===t?(this.checkAll=!1,this.indeterminate=!1):t===this.totalChannels?(this.checkAll=!0,this.indeterminate=!1,this.isAllSelected=!0):(this.checkAll=!1,this.indeterminate=!0)},handleRemoveChannel:function(e){var t=this.formValidate.corps.indexOf(e.corpId);if(t>-1){this.formValidate.corps.splice(t,1);var a=this.selectedChannelData.findIndex((function(t){return t.corpId===e.corpId}));a>-1&&this.selectedChannelData.splice(a,1);for(var r=0;r<this.channelList.length;r++)this.channelList[r].corpId===e.corpId?this.$set(this.channelList[r],"_checked",!1):this.$set(this.channelList[r],"_checked",this.formValidate.corps.includes(this.channelList[r].corpId))}this.isAllSelected=this.formValidate.corps.length===this.totalChannels,this.updateCheckAllStatus()},getPoolLabel:function(e){var t=this.cardPoolList.find((function(t){return t.poolId===e}));return t?t.poolName:""},getSelectedChannelData:function(e){var t=this;if(console.log("corpIds",e),!e||0===e.length)return console.log("没有已选渠道商，直接获取渠道商列表"),void this.getChannels();this.loading=!0,g({cooperationMode:"2",corpIds:e,current:-1,size:-1}).then((function(e){"0000"===e.code&&e.data?(t.selectedChannelData=e.data.map((function(e){return{corpId:e.corpId,corpName:e.corpName,companyName:e.companyName,relationGroupNames:e.relationGroupNames||{}}})),t.isEdit&&t.getChannels(),t.updateCheckAllStatus()):t.$Message.error(e.msg||"获取已选渠道商失败")})).catch((function(e){t.$Message.error("获取已选渠道商失败"),console.error("获取已选渠道商失败:",e)})).finally((function(){t.loading=!1}))},convertMccToString:function(e){return null===e||void 0===e?"":String(e)},updateCurrentSearchAllIds:function(){var e=this;return Object(c["a"])(Object(o["a"])().mark((function t(){var a;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,g({corpName:e.searchChannel,current:-1,size:-1,cooperationMode:"2"});case 2:a=t.sent,"0000"===a.code?e.currentSearchAllIds=(a.data||[]).map((function(e){return e.corpId})):e.currentSearchAllIds=[];case 4:case"end":return t.stop()}}),t)})))()},getSupplierList:function(){var e=this;return Object(c["a"])(Object(o["a"])().mark((function t(){var a;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,I();case 3:a=t.sent,"0000"===a.code&&(e.supplierList=a.data||[]),t.next=10;break;case 7:t.prev=7,t.t0=t["catch"](0),e.$Message.error("获取供应商列表失败");case 10:case"end":return t.stop()}}),t,null,[[0,7]])})))()},handleSupplierChange:function(e,t){var a=this.formValidate.details[t];this.$set(a,"supplierId",e),this.$set(a,"selectedPoolIds",[]),this.$set(a,"selectedRates",[]),this.$set(a,"cardPools",[]),this.$set(a,"availableCardPools",[])},handleCardPoolSelectOpen:function(e,t){if(e){var a=this.formValidate.details[t];a.supplierId&&this.getCardPools(a.supplierId,t)}}},computed:{computedCheckAll:function(){if(!this.currentSearchAllIds||0===this.currentSearchAllIds.length)return!1;var e=new Set(this.formValidate.corps);return this.currentSearchAllIds.every((function(t){return e.has(t)}))},computedIndeterminate:function(){if(!this.currentSearchAllIds||0===this.currentSearchAllIds.length)return!1;var e=new Set(this.formValidate.corps),t=this.currentSearchAllIds.filter((function(t){return e.has(t)})).length;return t>0&&t<this.currentSearchAllIds.length}}},y=w,P=(a("7bea"),a("6035"),a("2877")),x=Object(P["a"])(y,r,n,!1,null,"12b4ba6c",null);t["default"]=x.exports},"6f48":function(e,t,a){"use strict";var r=a("6d61"),n=a("6566");r("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),n)},"6f53":function(e,t,a){"use strict";var r=a("83ab"),n=a("d039"),l=a("e330"),s=a("e163"),i=a("df75"),o=a("fc6a"),c=a("d1e7").f,u=l(c),d=l([].push),h=r&&n((function(){var e=Object.create(null);return e[2]=2,!u(e,2)})),p=function(e){return function(t){var a,n=o(t),l=i(n),c=h&&null===s(n),p=l.length,f=0,m=[];while(p>f)a=l[f++],r&&!(c?a in n:u(n,a))||d(m,e?[a,n[a]]:n[a]);return m}};e.exports={entries:p(!0),values:p(!1)}},"7bea":function(e,t,a){"use strict";a("9c7a")},"81d5":function(e,t,a){"use strict";var r=a("7b0b"),n=a("23cb"),l=a("07fa");e.exports=function(e){var t=r(this),a=l(t),s=arguments.length,i=n(s>1?arguments[1]:void 0,a),o=s>2?arguments[2]:void 0,c=void 0===o?a:n(o,a);while(c>i)t[i++]=e;return t}},"9c7a":function(e,t,a){},c337:function(e,t,a){"use strict";a("209e")},cb29:function(e,t,a){"use strict";var r=a("23e7"),n=a("81d5"),l=a("44d2");r({target:"Array",proto:!0},{fill:n}),l("fill")},d1c9:function(e,t,a){"use strict";a("b64b");var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"dual-table-container",style:{height:e.containerHeight}},[t("div",{staticClass:"source-table"},[t("div",{staticClass:"table-header"},[e._t("source-header",(function(){return[t("div",{staticStyle:{height:"32px","line-height":"32px","margin-right":"10px"}},[e._v("渠道商简称")]),e.showDefaultSearch?t("Input",{staticStyle:{width:"200px","margin-right":"8px"},attrs:{placeholder:e.searchPlaceholder,clearable:""},model:{value:e.searchKeyword,callback:function(t){e.searchKeyword=t},expression:"searchKeyword"}}):e._e(),e.showDefaultSearch?t("Button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]):e._e(),e.showCheckAll?t("Checkbox",{staticStyle:{"margin-left":"auto"},attrs:{indeterminate:e.indeterminate,value:e.checkAll},nativeOn:{click:function(t){return t.preventDefault(),e.handleCheckAll.apply(null,arguments)}}},[e._v("全选")]):e._e()]})),e._t("all-check-box")],2),t("div",{staticClass:"table-content"},[t("Table",{ref:"sourceTable",attrs:{columns:e.sourceColumns,data:e.sourceData,loading:e.loading,height:e.tableHeight,ellipsis:!0,border:""},on:{"on-select":e.handleSelect,"on-select-cancel":e.handleSelectCancel,"on-select-all":e.handleSelectAll,"on-select-all-cancel":e.handleSelectAllCancel},scopedSlots:e._u([e._l(Object.keys(e.$slots),(function(t){return{key:t,fn:function(a){return[e._t(t,null,null,a)]}}}))],null,!0)},[t("template",{slot:"noDataText"},[t("span",[e._v(e._s(e.loading?"加载中...":"暂无数据"))])])],2),e.showPagination?t("Page",{staticStyle:{"margin-top":"10px","text-align":"right"},attrs:{total:e.total,current:e.current,"page-size":e.pageSize,size:"small","show-total":"","show-elevator":""},on:{"on-change":e.handlePageChange}}):e._e()],1)]),t("div",{staticClass:"selected-table"},[t("div",{staticClass:"table-header"},[e._t("selected-header",(function(){return[t("div",{staticClass:"selected-title"},[e._v("已选数据")])]}))],2),t("div",{staticClass:"table-content"},[t("Table",{attrs:{columns:e.selectedColumns,data:e.selectedData,height:e.tableHeight,ellipsis:!0,border:""},scopedSlots:e._u([e._l(Object.keys(e.$slots),(function(t){return{key:t,fn:function(a){return[e._t(t,null,null,a)]}}})),{key:"action",fn:function(a){var r=a.row;return[t("Button",{attrs:{type:"error",size:"small"},on:{click:function(t){return e.handleRemove(r)}}},[t("Icon",{attrs:{type:"md-close"}})],1)]}}],null,!0)})],1)])])},n=[],l=(a("a9e3"),{name:"DualTableSelect",props:{sourceColumns:{type:Array,required:!0},sourceData:{type:Array,default:function(){return[]}},selectedColumns:{type:Array,required:!0},containerHeight:{type:String,default:""},tableHeight:{type:[Number,String],default:520},showDefaultSearch:{type:Boolean,default:!0},searchPlaceholder:{type:String,default:"请输入关键词搜索"},showCheckAll:{type:Boolean,default:!0},showPagination:{type:Boolean,default:!0},total:{type:Number,default:0},current:{type:Number,default:1},pageSize:{type:Number,default:10},loading:{type:Boolean,default:!1},value:{type:Array,default:function(){return[]}},selectedData:{type:Array,default:function(){return[]}},indeterminate:{type:Boolean,default:!1},checkAll:{type:Boolean,default:!1}},data:function(){return{searchKeyword:""}},methods:{handleSearch:function(){this.$emit("on-search",this.searchKeyword)},handleCheckAll:function(){this.$emit("on-check-all",!this.checkAll)},handlePageChange:function(e){this.$emit("on-page-change",e)},handleSelect:function(e,t){this.$emit("on-select",e,t)},handleSelectCancel:function(e,t){this.$emit("on-select-cancel",e,t)},handleSelectAll:function(e){this.$emit("on-select-all",e)},handleSelectAllCancel:function(e){this.$emit("on-select-all-cancel",e)},handleRemove:function(e){this.$emit("on-remove",e)},reset:function(){this.searchKeyword=""}}}),s=l,i=(a("c337"),a("2877")),o=Object(i["a"])(s,r,n,!1,null,"5355fcb0",null);t["a"]=o.exports},ef11:function(e,t,a){}}]);