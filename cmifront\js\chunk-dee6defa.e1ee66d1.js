(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-dee6defa"],{"00b4":function(t,e,a){"use strict";a("ac1f");var i=a("23e7"),r=a("c65b"),o=a("1626"),n=a("825a"),s=a("577e"),l=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),c=/./.test;i({target:"RegExp",proto:!0,forced:!l},{test:function(t){var e=n(this),a=s(t),i=e.exec;if(!o(i))return r(c,e,a);var l=r(i,e,a);return null!==l&&(n(l),!0)}})},"129f":function(t,e,a){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},"2b6c":function(t,e,a){"use strict";a.d(e,"f",(function(){return o})),a.d(e,"h",(function(){return n})),a.d(e,"a",(function(){return s})),a.d(e,"e",(function(){return l})),a.d(e,"c",(function(){return c})),a.d(e,"d",(function(){return d})),a.d(e,"i",(function(){return u})),a.d(e,"b",(function(){return p})),a.d(e,"g",(function(){return m}));var i=a("66df"),r="/pms/api/v1/cardPool",o=function(t){return i["a"].request({url:r+"/getList",data:t,method:"POST"})},n=function(t){return i["a"].request({url:r+"/queryList",params:t,method:"GET"})},s=function(t){return i["a"].request({url:r+"/add",data:t,method:"POST"})},l=function(t){return i["a"].request({url:r+"/export/".concat(t),method:"POST",responseType:"blob"})},c=function(t){return i["a"].request({url:r+"/copy/".concat(t),method:"POST"})},d=function(t){return i["a"].request({url:r+"/".concat(t),method:"delete"})},u=function(t){return i["a"].request({url:r+"/update",data:t,method:"POST"})},p=function(t){return i["a"].request({url:r+"/getRateList",data:t,method:"POST"})},m=function(t){return i["a"].request({url:r+"/getCardPoolinfoBymccNew",params:t,method:"get"})}},4075:function(t,e,a){},"466d":function(t,e,a){"use strict";var i=a("c65b"),r=a("d784"),o=a("825a"),n=a("7234"),s=a("50c4"),l=a("577e"),c=a("1d80"),d=a("dc4a"),u=a("8aa5"),p=a("14c3");r("match",(function(t,e,a){return[function(e){var a=c(this),r=n(e)?void 0:d(e,t);return r?i(r,e,a):new RegExp(e)[t](l(a))},function(t){var i=o(this),r=l(t),n=a(e,i,r);if(n.done)return n.value;if(!i.global)return p(i,r);var c=i.unicode;i.lastIndex=0;var d,m=[],h=0;while(null!==(d=p(i,r))){var f=l(d[0]);m[h]=f,""===f&&(i.lastIndex=u(r,s(i.lastIndex),c)),h++}return 0===h?null:m}]}))},6262:function(t,e,a){"use strict";a.r(e);a("ac1f"),a("841c");var i=function(){var t=this,e=t._self._c;return e("div",[e("Card",[e("div",{staticClass:"search_head_i"},[e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("VIMSI")]),e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入VIMSI",clearable:""},model:{value:t.searchCondition.vimsi,callback:function(e){t.$set(t.searchCondition,"vimsi",e)},expression:"searchCondition.vimsi"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("MSISDN")]),e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入MSISDN",clearable:""},model:{value:t.searchCondition.msisdn,callback:function(e){t.$set(t.searchCondition,"msisdn",e)},expression:"searchCondition.msisdn"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("制卡IMSI")]),e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入制卡IMSI",clearable:""},model:{value:t.searchCondition.madeImsi,callback:function(e){t.$set(t.searchCondition,"madeImsi",e)},expression:"searchCondition.madeImsi"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("VIMSI状态")]),e("Select",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请选择VIMSI状态"},model:{value:t.searchCondition.status,callback:function(e){t.$set(t.searchCondition,"status",e)},expression:"searchCondition.status"}},t._l(t.statuseList,(function(a,i){return e("Option",{key:i,attrs:{value:a.value}},[t._v(t._s(a.label)+"\n\t\t\t\t\t")])})),1)],1),e("div",{staticStyle:{width:"430px",padding:"0 5px",display:"flex","justify-content":"flex-start","align-items":"center","margin-bottom":"20px"}},[e("span",{staticClass:"search_box_label2"},[t._v("是否需要GTP PROXY指定号码")]),e("Select",{staticStyle:{width:"200px"},attrs:{placeholder:"是否需要GTP PROXY指定号码",clearable:""},model:{value:t.searchCondition.reqGtpProxy,callback:function(e){t.$set(t.searchCondition,"reqGtpProxy",e)},expression:"searchCondition.reqGtpProxy"}},[e("Option",{attrs:{value:1}},[t._v("是")]),e("Option",{attrs:{value:2}},[t._v("否")])],1)],1)]),e("div",{staticClass:"search_head_i"},[e("div",{staticClass:"search_box"},[e("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{margin:"0 15px"},attrs:{type:"primary",loading:t.loading},on:{click:function(e){return t.search()}}},[e("Icon",{attrs:{type:"ios-search"}}),t._v(" 搜索\n\t\t\t\t")],1),e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{margin:"0 15px"},attrs:{type:"info"},on:{click:t.vimsiImport}},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("Icon",{attrs:{type:"md-add"}}),t._v(" VIMSI导入\n\t\t\t\t\t")],1)]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"batchDelete",expression:"'batchDelete'"}],staticStyle:{margin:"0 15px"},attrs:{type:"error"},on:{click:t.removeBatch}},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("Icon",{attrs:{type:"ios-trash"}}),t._v(" 批量删除\n\t\t\t\t\t")],1)]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"importRecord",expression:"'importRecord'"}],staticStyle:{margin:"0 15px"},attrs:{type:"success"},on:{click:function(e){return t.recordView()}}},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("Icon",{attrs:{type:"md-add"}}),t._v(" 导入记录查看\n\t\t\t\t\t")],1)])],1)]),e("div",{staticStyle:{"margin-top":"20px"}},[e("Table",{attrs:{columns:t.columns,data:t.tableData,ellipsis:!0,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(a){var i=a.row;a.index;return["1"==i.status?e("Button",{directives:[{name:"has",rawName:"v-has",value:"stop",expression:"'stop'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.stop(i.id)}}},[t._v("暂停")]):e("Button",{directives:[{name:"has",rawName:"v-has",value:"stop",expression:"'stop'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",size:"small",disabled:""}},[t._v("暂停")]),"4"==i.status||"5"==i.status?e("Button",{directives:[{name:"has",rawName:"v-has",value:"active",expression:"'active'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"success",size:"small"},on:{click:function(e){return t.active(i.id)}}},[t._v("激活")]):e("Button",{directives:[{name:"has",rawName:"v-has",value:"active",expression:"'active'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"success",size:"small",disabled:""}},[t._v("激活")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"error",size:"small"},on:{click:function(e){return t.remove(i.id,i.status)}}},[t._v("删除")])]}}])})],1),e("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[e("Page",{staticStyle:{margin:"10px 0"},attrs:{total:t.total,current:t.currentPage,"page-size":t.pageSize,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1)]),e("Modal",{attrs:{title:"VIMSI导入","footer-hide":!0,"mask-closable":!1,width:"530px"},model:{value:t.importFlag,callback:function(e){t.importFlag=e},expression:"importFlag"}},[e("div",{staticClass:"modal_content"},[e("Form",{ref:"formValidate",staticStyle:{"font-weight":"bold"},attrs:{model:t.formValidate,rules:t.ruleValidate,"label-width":130,"label-height":100,inline:""}},[e("FormItem",{staticStyle:{width:"420px"},attrs:{label:"VIMSI数量",prop:"vimsiNum"}},[e("Input",{attrs:{placeholder:"请输入VIMSI数量",maxlength:11,clearable:""},model:{value:t.formValidate.vimsiNum,callback:function(e){t.$set(t.formValidate,"vimsiNum",e)},expression:"formValidate.vimsiNum"}})],1),e("FormItem",{staticStyle:{width:"420px"},attrs:{label:"供应商",prop:"supplierId"}},[e("Select",{attrs:{filterable:!0,clearable:"",placeholder:"请选择供应商",disabled:t.cardPoodIntoFlag},on:{"on-change":t.queryCardPoolList},model:{value:t.formValidate.supplierId,callback:function(e){t.$set(t.formValidate,"supplierId",e)},expression:"formValidate.supplierId"}},t._l(t.providers,(function(a,i){return e("Option",{key:i,attrs:{value:a.supplierId}},[t._v(t._s(a.supplierName))])})),1)],1),e("FormItem",{staticStyle:{width:"420px"},attrs:{label:"是否需要GTP PROXY指定号码",prop:"reqGtpProxy"}},[e("Select",{attrs:{placeholder:"是否需要GTP PROXY指定号码",clearable:""},model:{value:t.formValidate.reqGtpProxy,callback:function(e){t.$set(t.formValidate,"reqGtpProxy",e)},expression:"formValidate.reqGtpProxy"}},[e("Option",{attrs:{value:1}},[t._v("是")]),e("Option",{attrs:{value:2}},[t._v("否")])],1)],1),e("FormItem",{staticStyle:{width:"420px"},attrs:{label:"卡池",prop:"cardPool"}},[e("Select",{attrs:{filterable:!0,clearable:"",placeholder:"请选择卡池",disabled:""==t.formValidate.supplierId||void 0==t.formValidate.supplierId||t.cardPoodIntoFlag},on:{"on-change":t.cardPoolSelect},model:{value:t.formValidate.cardPool,callback:function(e){t.$set(t.formValidate,"cardPool",e)},expression:"formValidate.cardPool"}},t._l(t.cardPoolList,(function(a,i){return e("Option",{key:i,staticStyle:{width:"270px",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap","text-align":"left"},attrs:{value:a.poolId}},[t._v(t._s(a.poolName))])})),1)],1),e("FormItem",{staticStyle:{width:"420px"},attrs:{label:"K4SNO",prop:"K4SNO"}},[e("Input",{attrs:{placeholder:"请输入K4SNO",maxlength:30,clearable:""},model:{value:t.formValidate.K4SNO,callback:function(e){t.$set(t.formValidate,"K4SNO",e)},expression:"formValidate.K4SNO"}})],1),e("FormItem",{staticStyle:{width:"420px"},attrs:{label:"OPSNO",prop:"OPSNO"}},[e("Input",{attrs:{placeholder:"请输入OPSNO",maxlength:30,clearable:""},model:{value:t.formValidate.OPSNO,callback:function(e){t.$set(t.formValidate,"OPSNO",e)},expression:"formValidate.OPSNO"}})],1),e("FormItem",{staticStyle:{width:"420px"},attrs:{label:"KI",prop:"ki"}},[e("Select",{attrs:{clearable:"",placeholder:"请选择是否需要KI"},model:{value:t.formValidate.ki,callback:function(e){t.$set(t.formValidate,"ki",e)},expression:"formValidate.ki"}},[e("Option",{attrs:{value:"1"}},[t._v("是")]),e("Option",{attrs:{value:"2"}},[t._v("否")])],1)],1),e("FormItem",{staticStyle:{width:"420px"},attrs:{label:"ICCID"}},[e("Select",{attrs:{clearable:"",placeholder:"请选择是否需要ICCID"},model:{value:t.formValidate.iccid,callback:function(e){t.$set(t.formValidate,"iccid",e)},expression:"formValidate.iccid"}},[e("Option",{attrs:{value:"1"}},[t._v("是")]),e("Option",{attrs:{value:"2"}},[t._v("否")])],1)],1),e("FormItem",{staticStyle:{width:"420px"},attrs:{label:"OPC"}},[e("Select",{attrs:{clearable:"",placeholder:"请选择是否需要OPC"},model:{value:t.formValidate.opc,callback:function(e){t.$set(t.formValidate,"opc",e)},expression:"formValidate.opc"}},[e("Option",{attrs:{value:"1"}},[t._v("是")]),e("Option",{attrs:{value:"2"}},[t._v("否")])],1)],1),e("FormItem",{staticStyle:{width:"420px"},attrs:{label:"签约业务ID",prop:"businessId"}},[e("Input",{attrs:{placeholder:"请输入签约业务ID",maxlength:50,clearable:""},model:{value:t.formValidate.businessId,callback:function(e){t.$set(t.formValidate,"businessId",e)},expression:"formValidate.businessId"}})],1)],1),e("div",{staticStyle:{"text-align":"center",margin:"4px 0"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"},{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:t.submitFlag},on:{click:t.submit}},[t._v("提交")]),e("Button",{staticStyle:{"margin-left":"8px"},on:{click:function(e){t.importFlag=!1}}},[t._v("取消")])],1)],1)]),e("Modal",{attrs:{title:"VIMSI批量删除","footer-hide":!0,"mask-closable":!1,width:"530px"},model:{value:t.batchDelFlag,callback:function(e){t.batchDelFlag=e},expression:"batchDelFlag"}},[e("div",{staticClass:"modal_content"},[e("Form",{ref:"formBatch",staticStyle:{"font-weight":"bold"},attrs:{model:t.formBatch,rules:t.ruleBatch,"label-width":130,"label-height":100,inline:""}},[e("FormItem",{staticStyle:{width:"420px"},attrs:{label:"VIMSI起始号码",prop:"beginNum"}},[e("Input",{attrs:{placeholder:"请输入VIMSI起始号码",clearable:""},model:{value:t.formBatch.beginNum,callback:function(e){t.$set(t.formBatch,"beginNum",e)},expression:"formBatch.beginNum"}})],1),e("FormItem",{staticStyle:{width:"420px"},attrs:{label:"VIMSI结束号码",prop:"endNum"}},[e("Input",{attrs:{placeholder:"请输入VIMSI结束号码",clearable:""},model:{value:t.formBatch.endNum,callback:function(e){t.$set(t.formBatch,"endNum",e)},expression:"formBatch.endNum"}})],1)],1),e("div",{staticStyle:{"text-align":"center",margin:"4px 0"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"batchDelete",expression:"'batchDelete'"},{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:t.deleteloading},on:{click:t.batchSubmit}},[t._v("提交")]),e("Button",{staticStyle:{"margin-left":"8px"},on:{click:function(e){t.batchDelFlag=!1}}},[t._v("取消")])],1)],1)]),e("Modal",{attrs:{title:"导入记录查看","mask-closable":!1,"footer-hide":!0,width:"1285px",loading:t.recordLoading},model:{value:t.recoedViewFlag,callback:function(e){t.recoedViewFlag=e},expression:"recoedViewFlag"}},[e("div",{staticClass:"search_head"},[e("Button",{attrs:{icon:"ios-arrow-back"},on:{click:t.back}},[t._v("返回")]),t._v("    \n\t\t")],1),e("Table",{attrs:{columns:t.taskColumns,data:t.taskData,ellipsis:!0,loading:t.taskloading},scopedSlots:t._u([{key:"successFileUrl",fn:function(a){var i=a.row;a.index;return["1"===i.taskStatus||0===i.successNum?e("Button",{directives:[{name:"has",rawName:"v-has",value:"download",expression:"'download'"}],attrs:{disabled:"",type:"success"},on:{click:function(e){return t.exportfile(i,1)}}},[t._v("点击下载")]):e("Button",{directives:[{name:"has",rawName:"v-has",value:"download",expression:"'download'"}],attrs:{type:"success"},on:{click:function(e){return t.exportfile(i,1)}}},[t._v("点击下载")])]}},{key:"failFileUrl",fn:function(a){var i=a.row;a.index;return["1"===i.taskStatus||0===i.failNum?e("Button",{directives:[{name:"has",rawName:"v-has",value:"download",expression:"'download'"}],attrs:{disabled:"",type:"error"},on:{click:function(e){return t.exportfile(i,2)}}},[t._v("点击下载")]):e("Button",{directives:[{name:"has",rawName:"v-has",value:"download",expression:"'download'"}],attrs:{type:"error"},on:{click:function(e){return t.exportfile(i,2)}}},[t._v("点击下载")])]}},{key:"detail",fn:function(a){var i=a.row;a.index;return["1"===i.taskStatus?e("Button",{directives:[{name:"has",rawName:"v-has",value:"info",expression:"'info'"}],attrs:{type:"info",disabled:""},on:{click:function(e){return t.recordInfo(i)}}},[t._v("点击查看")]):e("Button",{directives:[{name:"has",rawName:"v-has",value:"info",expression:"'info'"}],attrs:{type:"info"},on:{click:function(e){return t.recordInfo(i)}}},[t._v("点击查看")])]}}])}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.recordTotal,current:t.currentRecordPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentRecordPage=e},"on-change":t.goRecordPage}})],1),e("Modal",{staticStyle:{padding:"30px"},attrs:{title:"详情","mask-closable":!1,"footer-hide":!0,width:"600px"},model:{value:t.infoFlag,callback:function(e){t.infoFlag=e},expression:"infoFlag"}},[e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",[t._v("VIMSI数量:")]),t._v("  \n\t\t\t"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.info.importNum))]),t._v("  \n\t\t")]),e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",[t._v("供应商:")]),t._v("  \n\t\t\t"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.info.supplierName))]),t._v("  \n\t\t")]),e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",[t._v("卡池:")]),t._v("  \n\t\t\t"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.info.cardpoolName))]),t._v("  \n\t\t")]),e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",[t._v("K4SNO:")]),t._v("  \n\t\t\t"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.info.k4sno))]),t._v("  \n\t\t")]),e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",[t._v("OPSNO:")]),t._v("  \n\t\t\t"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.info.opsno))]),t._v("  \n\t\t")]),e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",[t._v("是否需要KI:")]),t._v("  \n\t\t\t"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s("1"==t.info.needKi?"是":"2"==t.info.needKi?"否":""))]),t._v("  \n\t\t")]),e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",[t._v("是否需要ICCID:")]),t._v("  \n\t\t\t"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s("1"==t.info.needIccid?"是":"2"==t.info.needIccid?"否":""))]),t._v("  \n\t\t")]),e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",[t._v("是否需要OPC:")]),t._v("  \n\t\t\t"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s("1"==t.info.needOpc?"是":"2"==t.info.needOpc?"否":""))]),t._v("  \n\t\t")]),e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",[t._v("签约业务ID:")]),t._v("  \n\t\t\t"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.info.signId))]),t._v("  \n\t\t")]),e("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"center"}},[e("Button",{attrs:{icon:"ios-arrow-back",size:"large"},on:{click:t.reback}},[t._v("返回")])],1)]),e("a",{ref:"downloadLink",staticStyle:{display:"none"}})],1)},r=[],o=a("ade3"),n=(a("4de4"),a("14d9"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("00b4"),a("3ca3"),a("466d"),a("5319"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("99af"),a("66df")),s="/pms/vcard",l="/pms/api/v1",c=function(t){return n["a"].request({url:s+"/getList",data:t,method:"POST"})},d=function(t){return n["a"].request({url:s+"/insertpool",data:t,method:"POST"})},u=function(t,e,a){return n["a"].request({url:s+"/deleteBatch?poolId=".concat(t,"&startImsi=").concat(e,"&endImsi=").concat(a),method:"DELETE"})},p=function(t,e){return n["a"].request({url:s+"/".concat(t,"/").concat(e),method:"PUT"})},m=function(t){return n["a"].request({url:s+"/".concat(t),method:"delete"})},h=function(t){return n["a"].request({url:l+"/card/getVcardImportTaskList",params:t,method:"get"})},f=function(t){return n["a"].request({url:l+"/card/getResultFile",params:t,method:"get",responseType:"blob"})},v=a("2b6c"),g=a("a550"),b=a("f121"),I=b["a"].keytype,S={components:{},data:function(){var t=this,e=function(t,e,a){var i=/^[0-9]\d*$/;return i.test(e)};return Object(o["a"])(Object(o["a"])(Object(o["a"])(Object(o["a"])({deleteloading:!1,cardPoodIntoFlag:!1,submitFlag:!1,providers:[],cardPoolList:[],searchCondition:{vimsi:"",msisdn:"",madeImsi:"",status:"",reqGtpProxy:""},importFlag:!1,formValidate:{vimsiNum:"",supplierId:"",reqGtpProxy:"",cardPool:"",iccid:"",K4SNO:"",OPSNO:"",ki:"",opc:"",businessId:""},formValidateSave:"",batchDelFlag:!1,formBatch:{beginNum:"",endNum:""},formBatchSave:"",recoedViewFlag:!1,infoFlag:!1,ruleValidate:{vimsiNum:[{required:!0,type:"string",message:"请输入导入VIMSI数量"},{validator:e,message:"VIMSI数量格式错误"},{validator:function(t,e,a){return 0!=Number(e)},message:"VIMSI数量不能为0"},{validator:function(t,e,a){return Number(**********)>=Number(e)},message:"VIMSI数量数值过大"}],supplierId:[{required:!0,message:"请选择供应商",trigger:"change"}],reqGtpProxy:[{required:!0,message:"请选是否需要GTP PROXY指定号码"}],cardPool:[{required:!0,message:"请选择卡池",trigger:"change"}],K4SNO:[{validator:function(t,e,a){var i=/^[0-9]\d*$/;return i.test(e)||""==e},message:"K4SNO格式错误(仅支持数字)"}],OPSNO:[{validator:function(t,e,a){var i=/^[0-9]\d*$/;return i.test(e)||""==e},message:"OPSNO格式错误(仅支持数字)"}],ki:[{validator:function(e,a,i){return"1"!=t.cardPoolType&&""!=a&&void 0!=a||"1"==t.cardPoolType},message:"终端卡池此项必填"}],iccid:[{required:!0,message:"请选择是否需要ICCID",trigger:"change"}],opc:[{required:!0,message:"请选择是否需要OPC",trigger:"change"}],businessId:[{validator:function(e,a,i){return"2"==t.cardPoolType&&"1"==t.isExpireReset&&""!=a&&void 0!=a||"2"!=t.cardPoolType||"2"==t.cardPoolType&&"2"==t.isExpireReset},message:"到期后重置卡池此项必填"}]},ruleBatch:{beginNum:[{required:!0,type:"string",message:"请输入VIMSI起始号码"},{validator:e,message:"VIMSI号码格式错误"}],endNum:[{required:!0,type:"string",message:"请输入VIMSI结束号码"},{validator:e,message:"VIMSI号码格式错误"}]},tableData:[],loading:!1,currentPage:1,total:0,pageSize:10,columns:[],statuseList:[{label:"待分配",value:"1"},{label:"已分配",value:"2"},{label:"暂停态",value:"4"},{label:"已冻结",value:"5"}],cardPoolType:"1",cardPoolStatus:"1",isExpireReset:"2",taskColumns:[{title:"导入时间",key:"createTime",align:"center",width:"150px"},{title:"完成时间",key:"finishTime",align:"center",width:"150px"},{title:"处理状态",key:"taskStatus",align:"center",width:"120px",render:function(t,e){var a=e.row,i="1"===a.taskStatus?"处理中":"2"===a.taskStatus?"已完成":"";return t("label",i)}},{title:"导入号码总数量",key:"importNum",align:"center",width:"120px"},{title:"导入成功数量",key:"successNum",align:"center",width:"120px"},{title:"导入失败数量",key:"failNum",align:"center",width:"120px"},{title:"下载导入成功号码列表",slot:"successFileUrl",align:"center",width:"160px"},{title:"下载导入失败号码列表",slot:"failFileUrl",align:"center",width:"160px"},{title:"详情",slot:"detail",align:"center",width:"150px"}],taskData:[],recordTotal:0,currentRecordPage:1},"pageSize",10),"recordLoading",!1),"taskloading",!1),"info",{})},methods:{goPageFirst:function(t){var e=this;this.currentPage=t,this.loading=!0;var a={poolId:this.searchCondition.poolId,imsi:this.searchCondition.vimsi.replace(/\s/g,""),msisdn:this.searchCondition.msisdn.replace(/\s/g,""),madeImsi:this.searchCondition.madeImsi.replace(/\s/g,""),reqGtpProxy:this.searchCondition.reqGtpProxy,status:this.searchCondition.status,page:t,pageSize:this.pageSize};c(a).then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.total=a.total,e.tableData=a.data})).catch((function(t){})).finally((function(){e.loading=!1}))},goPage:function(t){this.goPageFirst(t)},search:function(){this.goPageFirst(1)},init:function(){this.formValidateSave=JSON.stringify(this.formValidate),this.formBatchSave=JSON.stringify(this.formBatch),this.columns=[{title:"VIMSI",key:"imsi",align:"center",minWidth:130,tooltip:!0},{title:"MSISDN",key:"msisdn",align:"center",minWidth:130,tooltip:!0},{title:"制卡IMSI",key:"madeImsi",align:"center",minWidth:130,tooltip:!0},{title:"卡池ID",key:"poolId",align:"center",tooltip:!0,minWidth:200},{title:"卡池名称",key:"poolName",align:"center",tooltip:!0,minWidth:120},{title:"VIMSI状态",key:"status",align:"center",minWidth:100,tooltip:!0,render:function(t,e){var a=e.row,i="1"==a.status?"#2d8cf0":"2"==a.status?"#808695":"3"==a.status?"#19be6b":"4"==a.status?"#ff9900":"5"==a.status?"#ed4014":"#515a6e",r="1"==a.status?"待分配":"2"==a.status?"已分配":"3"==a.status?"使用中":"4"==a.status?"暂停态":"5"==a.status?"已冻结":"未知";return t("label",{style:{color:i}},r)}},{title:"是否需要GTP PROXY指定号码",key:"reqGtpProxy",align:"center",minWidth:210,tooltip:!0,render:function(t,e){var a=e.row,i="1"==a.reqGtpProxy?"是":"2"==a.reqGtpProxy?"否":"";return t("label",i)}}];var t=["delete","active","stop"],e=this.$route.meta.permTypes,a=t.filter((function(t){return e.indexOf(t)>-1}));if(a.length>0){var i=50+60*a.length;this.columns.push({title:"操作",slot:"action",width:i,align:"center",fixed:"right"})}this.goPageFirst(1)},vimsiImport:function(){var t=this;this.$refs["formValidate"].resetFields();var e=JSON.parse(this.formValidateSave);if(this.getProviderList(),this.formValidate=Object.assign({},e),this.cardPoodIntoFlag){var a=this.searchCondition.supplierId,i=this.searchCondition.poolId;this.formValidate.supplierId=a,this.queryCardPoolList(a),setTimeout((function(){t.formValidate.cardPool=i,t.cardPoolSelect(i)}),1e3)}this.importFlag=!0},submit:function(){var t=this;this.$refs["formValidate"].validate((function(e){if(e){var a=t.formValidate,i={vimsiNum:Number(a.vimsiNum),reqGtpProxy:a.reqGtpProxy,cardPool:a.cardPool,k4sno:a.K4SNO,opsno:a.OPSNO,keytype:I,isNeedICCID:"1"==a.iccid,isNeedKI:"1"==a.ki,isNeedOPC:"1"==a.opc,supplierId:a.supplierId,upccBusinessId:a.businessId,poolStatus:t.cardPoolStatus};t.submitFlag=!0,d(i).then((function(e){if(!e||"0000"!=e.code)throw t.submitFlag=!1,e;setTimeout((function(){t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.importFlag=!1,t.submitFlag=!1,t.goPageFirst(t.currentPage)}),1500)})).catch((function(e){t.submitFlag=!1}))}}))},batchSubmit:function(){var t=this;this.$refs["formBatch"].validate((function(e){if(e){t.deleteloading=!0;var a=void 0==t.searchCondition.poolId?"":t.searchCondition.poolId;t.formBatch.beginNum,t.formBatch.endNum;u(a,t.formBatch.beginNum,t.formBatch.endNum).then((function(e){if(!e||"0000"!=e.code)throw e;setTimeout((function(){t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.deleteloading=!1,t.batchDelFlag=!1,t.goPageFirst(1)}),1500)})).catch((function(t){})).finally((function(){t.deleteloading=!1,t.batchDelFlag=!1}))}}))},removeBatch:function(){this.$refs["formBatch"].resetFields(),this.formBatch=Object.assign({},JSON.parse(this.formBatchSave)),this.batchDelFlag=!0},stop:function(t){var e=this;this.$Modal.confirm({title:"确认暂停？",onOk:function(){p(t,"4").then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.goPageFirst(e.currentPage)})).catch((function(t){}))}})},active:function(t){var e=this;this.$Modal.confirm({title:"确认激活？",onOk:function(){p(t,"1").then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.goPageFirst(e.currentPage)})).catch((function(t){}))}})},remove:function(t,e){var a=this;if("2"==e)return this.$Notice.error({title:"操作提示",desc:"已分配状态不能删除"}),!1;this.$Modal.confirm({title:"确认删除？",onOk:function(){m(t).then((function(t){if(!t||"0000"!=t.code)throw t;a.$Notice.success({title:"操作提示",desc:"操作成功"}),1==a.tableData.length&&a.currentPage>1?a.goPageFirst(a.currentPage-1):a.goPageFirst(a.currentPage)})).catch((function(t){}))}})},getProviderList:function(){var t=this;Object(g["i"])().then((function(e){if(!e||"0000"!=e.code)throw e;t.providers=e.data})).catch((function(t){})).finally((function(){}))},queryCardPoolList:function(t){var e=this;this.formValidate.cardPool="",void 0!=t&&Object(v["h"])({supplierId:t,isSupportHimsi:"2"}).then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.cardPoolList=a})).catch((function(t){})).finally((function(){e.loading=!1}))},cardPoolSelect:function(t){if(void 0!=t)for(var e=this.cardPoolList,a=0;a<e.length;a++)t==e[a].poolId&&(this.isExpireReset=e[a].isExpireReset,this.cardPoolType=e[a].usageType,this.cardPoolStatus=e[a].status);else this.isExpireReset="2",this.cardPoolType="1",this.cardPoolStatus="1";this.$refs["formValidate"].validateField("ki"),this.$refs["formValidate"].validateField("businessId")},recordView:function(){this.recoedViewFlag=!0,this.goRecodePageFirst(1)},back:function(){this.recoedViewFlag=!1},goRecodePageFirst:function(t){var e=this;this.loading=!0;var a=this;h({pageSize:this.pageSize,pageNum:t}).then((function(i){if("0000"==i.code){a.loading=!1,e.recordLoading=!1;var r=i.data;e.currentRecordPage=t,e.recordTotal=r.total,e.taskData=r.records}})).catch((function(t){console.log(t)})).finally((function(){a.loading=!1,e.recordLoading=!1}))},goRecordPage:function(t){this.goRecodePageFirst(t)},exportfile:function(t,e){var a=this;this.taskloading=!0;f({id:t.id,type:e}).then((function(t){var e=t.data,i=decodeURI(t.headers["content-disposition"].match(/=(.*)$/)[1]);if(console.log(i),"download"in document.createElement("a")){var r=a.$refs.downloadLink,o=URL.createObjectURL(e);r.download=i,r.href=o,r.click(),URL.revokeObjectURL(o)}else navigator.msSaveBlob(e,i)})).finally((function(){a.taskloading=!1}))},recordInfo:function(t){this.info={importNum:t.importNum,supplierName:t.supplierName,cardpoolName:t.cardpoolName,k4sno:t.k4sno,opsno:t.opsno,needKi:t.needKi,needIccid:t.needIccid,needOpc:t.needOpc,signId:t.signId},this.infoFlag=!0},reback:function(){this.infoFlag=!1}},mounted:function(){try{var t=JSON.parse(decodeURIComponent(this.$route.query.p));this.cardPoodIntoFlag=!0,this.searchCondition.poolId=t.p,this.searchCondition.supplierId=t.s}catch(e){}this.init()},watch:{importFlag:function(t,e){}}},y=S,x=(a("6350"),a("2877")),w=Object(x["a"])(y,i,r,!1,null,"8c61b130",null);e["default"]=w.exports},6350:function(t,e,a){"use strict";a("4075")},"841c":function(t,e,a){"use strict";var i=a("c65b"),r=a("d784"),o=a("825a"),n=a("7234"),s=a("1d80"),l=a("129f"),c=a("577e"),d=a("dc4a"),u=a("14c3");r("search",(function(t,e,a){return[function(e){var a=s(this),r=n(e)?void 0:d(e,t);return r?i(r,e,a):new RegExp(e)[t](c(a))},function(t){var i=o(this),r=c(t),n=a(e,i,r);if(n.done)return n.value;var s=i.lastIndex;l(s,0)||(i.lastIndex=0);var d=u(i,r);return l(i.lastIndex,s)||(i.lastIndex=s),null===d?-1:d.index}]}))},a550:function(t,e,a){"use strict";a.d(e,"a",(function(){return o})),a.d(e,"b",(function(){return n})),a.d(e,"g",(function(){return s})),a.d(e,"j",(function(){return l})),a.d(e,"c",(function(){return c})),a.d(e,"k",(function(){return d})),a.d(e,"d",(function(){return u})),a.d(e,"i",(function(){return p})),a.d(e,"e",(function(){return m})),a.d(e,"h",(function(){return h})),a.d(e,"f",(function(){return f}));var i=a("66df"),r="/rms/api/v1",o=function(t){return i["a"].request({url:r+"/IMSITRIAD/query",params:t,method:"get"})},n=function(t){return i["a"].request({url:r+"/IMSITRIAD/add",data:t,method:"post"})},s=function(t){return i["a"].request({url:r+"/IMSITRIAD/excelAdd",data:t,method:"post",contentType:"multipart/form-data"})},l=function(t){return i["a"].request({url:r+"/IMSITRIAD/update",data:t,method:"put"})},c=function(t){return i["a"].request({url:r+"/IMSITRIAD/delete",data:t,method:"DELETE"})},d=function(t){return i["a"].request({url:r+"/IMSITRIAD/updateSingleStatus",params:t,method:"put"})},u=function(t){return i["a"].request({url:r+"/IMSITRIAD/deleteSingle",params:t,method:"DELETE"})},p=function(t){return i["a"].request({url:r+"/supplier/query",params:t,method:"get"})},m=function(t){return i["a"].request({url:"/rms/IMSITRIAD/template.csv",params:t,method:"get",responseType:"blob"})},h=function(t){return i["a"].request({url:r+"/CMHKIMSI/selectTask",params:t,method:"get"})},f=function(t){return i["a"].request({url:r+"/CMHKIMSI/fileDownLoad",params:t,method:"get",responseType:"blob"})}}}]);