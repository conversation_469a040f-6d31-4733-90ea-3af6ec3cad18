"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.POIProfile = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
class POIProfile {
    static getAttributeTypeMap() {
        return POIProfile.attributeTypeMap;
    }
}
exports.POIProfile = POIProfile;
POIProfile.discriminator = undefined;
POIProfile.attributeTypeMap = [
    {
        "name": "GenericProfile",
        "baseName": "GenericProfile",
        "type": "POIProfile.GenericProfileEnum"
    },
    {
        "name": "ServiceProfiles",
        "baseName": "ServiceProfiles",
        "type": "Array<POIProfile.ServiceProfilesEnum>"
    }
];
(function (POIProfile) {
    let GenericProfileEnum;
    (function (GenericProfileEnum) {
        GenericProfileEnum[GenericProfileEnum["Basic"] = 'Basic'] = "Basic";
        GenericProfileEnum[GenericProfileEnum["Extended"] = 'Extended'] = "Extended";
        GenericProfileEnum[GenericProfileEnum["Standard"] = 'Standard'] = "Standard";
    })(GenericProfileEnum = POIProfile.GenericProfileEnum || (POIProfile.GenericProfileEnum = {}));
    let ServiceProfilesEnum;
    (function (ServiceProfilesEnum) {
        ServiceProfilesEnum[ServiceProfilesEnum["Batch"] = 'Batch'] = "Batch";
        ServiceProfilesEnum[ServiceProfilesEnum["CardReader"] = 'CardReader'] = "CardReader";
        ServiceProfilesEnum[ServiceProfilesEnum["Communication"] = 'Communication'] = "Communication";
        ServiceProfilesEnum[ServiceProfilesEnum["Loyalty"] = 'Loyalty'] = "Loyalty";
        ServiceProfilesEnum[ServiceProfilesEnum["OneTimeRes"] = 'OneTimeRes'] = "OneTimeRes";
        ServiceProfilesEnum[ServiceProfilesEnum["Pin"] = 'PIN'] = "Pin";
        ServiceProfilesEnum[ServiceProfilesEnum["Reservation"] = 'Reservation'] = "Reservation";
        ServiceProfilesEnum[ServiceProfilesEnum["Sound"] = 'Sound'] = "Sound";
        ServiceProfilesEnum[ServiceProfilesEnum["StoredValue"] = 'StoredValue'] = "StoredValue";
        ServiceProfilesEnum[ServiceProfilesEnum["Synchro"] = 'Synchro'] = "Synchro";
    })(ServiceProfilesEnum = POIProfile.ServiceProfilesEnum || (POIProfile.ServiceProfilesEnum = {}));
})(POIProfile = exports.POIProfile || (exports.POIProfile = {}));
//# sourceMappingURL=pOIProfile.js.map