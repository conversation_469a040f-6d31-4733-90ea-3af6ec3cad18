/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SaleSoftware } from './saleSoftware';
import { SaleTerminalData } from './saleTerminalData';
export declare class LoginRequest {
    'CustomerOrderReq'?: Array<LoginRequest.CustomerOrderReqEnum>;
    'DateTime': {
        [key: string]: any;
    };
    'OperatorID'?: string;
    'OperatorLanguage': string;
    'POISerialNumber'?: string;
    'SaleSoftware': SaleSoftware;
    'SaleTerminalData'?: SaleTerminalData;
    'ShiftNumber'?: string;
    'TokenRequestedType'?: LoginRequest.TokenRequestedTypeEnum;
    'TrainingModeFlag'?: boolean;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace LoginRequest {
    enum CustomerOrderReqEnum {
        Both,
        Closed,
        Open
    }
    enum TokenRequestedTypeEnum {
        Customer,
        Transaction
    }
}
