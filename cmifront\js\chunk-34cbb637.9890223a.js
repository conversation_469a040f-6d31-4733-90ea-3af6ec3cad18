(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-34cbb637"],{"00b4":function(t,e,a){"use strict";a("ac1f");var n=a("23e7"),i=a("c65b"),l=a("1626"),s=a("825a"),c=a("577e"),o=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),r=/./.test;n({target:"RegExp",proto:!0,forced:!o},{test:function(t){var e=s(this),a=c(t),n=e.exec;if(!l(n))return i(r,e,a);var o=i(n,e,a);return null!==o&&(s(o),!0)}})},"0230":function(t,e,a){"use strict";a.r(e);a("caad"),a("ac1f"),a("841c");var n=function(){var t=this,e=t._self._c;return e("Card",{staticStyle:{width:"100%",padding:"16px"}},[e("div",{staticClass:"search_head_i"},[e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("加油包名称:")]),e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入加油包名称",clearable:""},model:{value:t.searchObj.gaspackname,callback:function(e){t.$set(t.searchObj,"gaspackname",e)},expression:"searchObj.gaspackname"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("加油包ID:")]),e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入加油包ID",clearable:""},model:{value:t.searchObj.gaspacknameid,callback:function(e){t.$set(t.searchObj,"gaspacknameid",e)},expression:"searchObj.gaspacknameid"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("是否允许订购:")]),e("Select",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{filterable:"",clearable:!0,placeholder:"请选择使用状态"},model:{value:t.searchObj.allowsub,callback:function(e){t.$set(t.searchObj,"allowsub",e)},expression:"searchObj.allowsub"}},[e("Option",{attrs:{value:1}},[t._v("是")]),e("Option",{attrs:{value:2}},[t._v("否")])],1)],1),e("div",{staticClass:"search_box"},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.search()}}},[t._v("搜索")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{margin:"0 2px","margin-left":"20px"},attrs:{type:"success",icon:"md-add"},on:{click:function(e){return t.addfuelpack()}}},[t._v("新建加油包")])],1)]),e("Table",{staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:t.columns,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"showpackage",fn:function(a){var n=a.row;a.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],staticStyle:{"margin-right":"3px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.showpackage(n)}}},[t._v("点击查看")])]}},{key:"action",fn:function(a){var n=a.row;a.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"3px"},attrs:{disabled:[5].includes(+n.authStatus),type:"info",size:"small"},on:{click:function(e){return t.Update(n)}}},[t._v("编辑")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],staticStyle:{"margin-right":"3px"},attrs:{disabled:[4,5].includes(+n.authStatus),type:"error",size:"small"},on:{click:function(e){return t.Delete(n.id)}}},[t._v("删除")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"copy",expression:"'copy'"}],staticStyle:{"margin-right":"3px"},attrs:{type:"warning",size:"small"},on:{click:function(e){return t.Copy(n)}}},[t._v("复制")])]}},{key:"authaction",fn:function(a){var n=a.row;a.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"pass",expression:"'pass'"}],staticStyle:{"margin-right":"3px"},attrs:{disabled:![1,4,5].includes(+n.authStatus),type:"success",size:"small"},on:{click:function(e){return t.Operation(!0,n.id)}}},[t._v("通过")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"fail",expression:"'fail'"}],staticStyle:{"margin-right":"3px"},attrs:{disabled:![1,4,5].includes(+n.authStatus),type:"error",size:"small"},on:{click:function(e){return t.Operation(!1,n.id)}}},[t._v("不通过")])]}}])}),e("div",{staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Modal",{attrs:{title:"查看关联套餐","mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.showModal,callback:function(e){t.showModal=e},expression:"showModal"}},[e("Table",{staticStyle:{width:"100%"},attrs:{columns:t.Unitedcolumns,data:t.Uniteddata,loading:t.Unitedloading}}),e("div",{staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.Unitedtotal,current:t.UnitedcurrentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.UnitedcurrentPage=e},"on-change":t.UnitedgoPage}})],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("返回")])],1)],1),e("Modal",{attrs:{title:t.title,"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.fuelPackModal,callback:function(e){t.fuelPackModal=e},expression:"fuelPackModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{ref:"fuelPackObj",staticStyle:{"align-items":"center","justify-content":"center"},attrs:{model:t.fuelPackObj,rules:t.rule,"label-position":"left","label-width":150}},[e("FormItem",{attrs:{label:"加油包名称(简中)",prop:"nameCn"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入加油包名称(简中)"},model:{value:t.fuelPackObj.nameCn,callback:function(e){t.$set(t.fuelPackObj,"nameCn",e)},expression:"fuelPackObj.nameCn"}})],1),e("FormItem",{attrs:{label:"加油包名称(繁中)",prop:"nameTw"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入加油包名称(繁中)"},model:{value:t.fuelPackObj.nameTw,callback:function(e){t.$set(t.fuelPackObj,"nameTw",e)},expression:"fuelPackObj.nameTw"}})],1),e("FormItem",{attrs:{label:"加油包名称(英文)",prop:"nameEn"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入加油包名称(英文)"},model:{value:t.fuelPackObj.nameEn,callback:function(e){t.$set(t.fuelPackObj,"nameEn",e)},expression:"fuelPackObj.nameEn"}})],1),e("FormItem",{attrs:{label:"加油包单价(人民币)",prop:"cny"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入加油包名称(人民币)"},model:{value:t.fuelPackObj.cny,callback:function(e){t.$set(t.fuelPackObj,"cny",e)},expression:"fuelPackObj.cny"}})],1),e("FormItem",{attrs:{label:"加油包单价(港币)",prop:"hkd"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入加油包名称(港币)"},model:{value:t.fuelPackObj.hkd,callback:function(e){t.$set(t.fuelPackObj,"hkd",e)},expression:"fuelPackObj.hkd"}})],1),e("FormItem",{attrs:{label:"加油包单价(美元)",prop:"usd"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入加油包名称(美元)"},model:{value:t.fuelPackObj.usd,callback:function(e){t.$set(t.fuelPackObj,"usd",e)},expression:"fuelPackObj.usd"}})],1),e("FormItem",{attrs:{label:"加油包流量单位",prop:"flowUnit"}},[e("Select",{staticStyle:{"margin-right":"10px",width:"200px"},attrs:{filterable:"",clearable:!0,placeholder:"请选择使用状态"},model:{value:t.fuelPackObj.flowUnit,callback:function(e){t.$set(t.fuelPackObj,"flowUnit",e)},expression:"fuelPackObj.flowUnit"}},[e("Option",{attrs:{value:"2"}},[t._v("GB")]),e("Option",{attrs:{value:"1"}},[t._v("MB")])],1)],1),e("FormItem",{attrs:{label:"加油包流量值",prop:"flowValue"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入加油包流量值"},model:{value:t.fuelPackObj.flowValue,callback:function(e){t.$set(t.fuelPackObj,"flowValue",e)},expression:"fuelPackObj.flowValue"}})],1),e("FormItem",{attrs:{label:"是否允许订购",prop:"allowSub"}},[e("Select",{staticStyle:{"margin-right":"10px",width:"200px"},attrs:{filterable:"",clearable:!0,placeholder:"请选择使用状态"},model:{value:t.fuelPackObj.allowSub,callback:function(e){t.$set(t.fuelPackObj,"allowSub",e)},expression:"fuelPackObj.allowSub"}},[e("Option",{attrs:{value:"1"}},[t._v("是")]),e("Option",{attrs:{value:"2"}},[t._v("否")])],1)],1)],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary",loading:t.submitloading},on:{click:t.submit}},[t._v("确定")])],1)])],1)},i=[],l=(a("d81d"),a("14d9"),a("d3b7"),a("00b4"),a("25f0"),a("66df")),s="/pms",c=function(t){return l["a"].request({url:s+"/refuelPackage/select",data:t,method:"post"})},o=function(t){return l["a"].request({url:s+"/refuelPackage/add",data:t,method:"post"})},r=function(t){return l["a"].request({url:s+"/refuelPackage/update",data:t,method:"post"})},u=function(t){return l["a"].request({url:s+"/refuelPackage/delete",params:t,method:"post"})},d=function(t){return l["a"].request({url:s+"/refuelPackage/audit",params:t,method:"post"})},f=function(t){return l["a"].request({url:s+"/refuelPackage/selectRefuekPackage",params:t,method:"post"})},h={data:function(){return{searchObj:{gaspackname:"",gaspacknameid:"",allowsub:""},fuelPackObj:{nameCn:"",nameTw:"",nameEn:"",cny:"",hkd:"",usd:"",flowUnit:"",flowValue:"",allowSub:""},refuelId:"",total:0,currentPage:1,page:0,Unitedtotal:0,UnitedcurrentPage:1,Unitedpage:0,showModal:!1,fuelPackModal:!1,loading:!1,Unitedloading:!1,searchloading:!1,submitloading:!1,title:"",columns:[{title:"加油包ID",key:"id",minWidth:200,align:"center",tooltip:!0},{title:"加油包名称(简体中文)",key:"nameCn",minWidth:200,align:"center",tooltip:!0},{title:"加油包名称(繁体中文)",key:"nameTw",minWidth:200,align:"center",tooltip:!0},{title:"加油包名称(英文)",key:"nameEn",minWidth:200,align:"center",tooltip:!0},{title:"加油包价格(人民币)",key:"cny",minWidth:200,align:"center",tooltip:!0},{title:"加油包价格(港币)",key:"hkd",minWidth:200,align:"center",tooltip:!0},{title:"加油包价格(美元)",key:"usd",minWidth:200,align:"center",tooltip:!0},{title:"加油包流量单位",key:"flowUnit",minWidth:150,align:"center",tooltip:!0,render:function(t,e){var a=e.row,n="1"===a.flowUnit?"MB":"2"===a.flowUnit?"GB":"";return t("label",n)}},{title:"加油包流量值",key:"flowValue",minWidth:140,align:"center",tooltip:!0},{title:"是否允许订购",key:"allowSub",minWidth:140,align:"center",tooltip:!0,render:function(t,e){var a=e.row,n="1"===a.allowSub?"是":"2"===a.allowSub?"否":"";return t("label",n)}},{title:"关联套餐",slot:"showpackage",minWidth:120,align:"center",tooltip:!0,fixed:"right"},{title:"操作",slot:"action",minWidth:200,align:"center",tooltip:!0,fixed:"right"},{title:"审批状态",key:"authStatus",minWidth:150,align:"center",tooltip:!0,fixed:"right",render:function(t,e){var a=e.row,n="1"==a.authStatus?"#2b85e4":"2"==a.authStatus?"#19be6b":"3"==a.authStatus?"#ff0000":"4"==a.authStatus?"#ffa554":"5"==a.authStatus?"#ff0000":"",i="1"===a.authStatus?"新建待审批":"2"===a.authStatus?"通过":"3"===a.authStatus?"新建审批不通过":"4"===a.authStatus?"修改待审批":"5"===a.authStatus?"删除待审批":"";return t("label",{style:{color:n}},i)}},{title:"审批操作",slot:"authaction",minWidth:200,align:"center",tooltip:!0,fixed:"right"}],Unitedcolumns:[{title:"套餐名称",key:"nameCn",minWidth:200,align:"center",tooltip:!0},{title:"套餐ID",key:"id",minWidth:200,align:"center",tooltip:!0}],Uniteddata:[],data:[],rule:{nameCn:[{required:!0,type:"string",message:"加油包名称(简中)不能为空"}],nameTw:[{required:!0,type:"string",message:"加油包名称(繁中)不能为空"}],nameEn:[{required:!0,type:"string",message:"加油包名称(英文)不能为空"},{validator:function(t,e,a){var n=/^[0-9a-zA-Z\/\(\)\,\.\:\"\<\>\|\?\!\=\-\+\~\^\\\'\;\#\$\%\&\*\`\@\_\]\[\'\s]+$/;return n.test(e)||""==e},message:"Please enter English"}],cny:[{required:!0,type:"string",message:"加油包单价(人民币)不能为空"},{validator:function(t,e,a){var n=/^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;return n.test(e)},message:"最高支持8位整数和2位小数正数或零"}],hkd:[{required:!0,type:"string",message:"加油包单价(港币)不能为空"},{validator:function(t,e,a){var n=/^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;return n.test(e)},message:"最高支持8位整数和2位小数正数或零"}],usd:[{required:!0,type:"string",message:"加油包单价(美元)不能为空"},{validator:function(t,e,a){var n=/^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;return n.test(e)},message:"最高支持8位整数和2位小数正数或零"}],flowUnit:[{required:!0,message:"请选择加油包流量单位"}],flowValue:[{required:!0,type:"string",message:"加油包流量值不能为空"},{validator:function(t,e,a){var n=/^[1-9]\d*$/;return n.test(e)},message:"请输入正整数"}],allowSub:[{required:!0,message:"请选择是否允许订购"}]}}},mounted:function(){this.goPageFirst(1)},methods:{goPageFirst:function(t){var e=this;this.loading=!0;var a=this;c({pageNo:t,pageSize:10,allowSub:this.searchObj.allowsub,id:this.searchObj.gaspacknameid,nameCn:this.searchObj.gaspackname}).then((function(n){if("0000"==n.code){a.loading=!1;var i=[];n.data.records.map((function(t,e){t.authObj?i.push(t.authObj):i.push(t)})),e.searchloading=!1,e.page=t,e.currentPage=t,e.total=n.data.total,e.data=i}})).catch((function(t){console.error(t)})).finally((function(){a.loading=!1,e.searchloading=!1}))},goPage:function(t){this.goPageFirst(t)},UnitedgoPageFirst:function(t){var e=this;this.Unitedloading=!0;var a=this;f({pageNo:t,pageSize:10,refuelId:this.refuelId}).then((function(n){"0000"==n.code&&(a.Unitedloading=!1,e.Unitedpage=t,e.UnitedcurrentPage=t,e.Unitedtotal=n.count,e.Uniteddata=n.data)})).catch((function(t){console.error(t)})).finally((function(){a.Unitedloading=!1}))},UnitedgoPage:function(t){this.UnitedgoPageFirst(t)},search:function(){this.searchloading=!0,this.goPageFirst(1)},cancelModal:function(){this.showModal=!1,this.fuelPackModal=!1,this.$refs["fuelPackObj"].resetFields()},addfuelpack:function(){this.title="新建加油包",this.fuelPackModal=!0},showpackage:function(t){this.showModal=!0,this.refuelId=t.id,this.UnitedgoPageFirst(1)},Update:function(t){this.title="修改加油包",this.fuelPackModal=!0,this.fuelPackObj.id=Object.assign({},t).id,this.fuelPackObj.nameCn=Object.assign({},t).nameCn,this.fuelPackObj.nameTw=Object.assign({},t).nameTw,this.fuelPackObj.nameEn=Object.assign({},t).nameEn,this.fuelPackObj.cny=Object.assign({},t).cny.toString(),this.fuelPackObj.hkd=Object.assign({},t).hkd.toString(),this.fuelPackObj.usd=Object.assign({},t).usd.toString(),this.fuelPackObj.flowValue=Object.assign({},t).flowValue.toString(),this.fuelPackObj.flowUnit=Object.assign({},t).flowUnit,this.fuelPackObj.allowSub=Object.assign({},t).allowSub},Delete:function(t){var e=this;this.$Modal.confirm({title:"确认删除？",onOk:function(){u({id:t}).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.goPageFirst(1)})).catch((function(t){}))}})},Copy:function(t){this.title="复制加油包",this.fuelPackModal=!0,this.fuelPackObj.id=Object.assign({},t).id,this.fuelPackObj.nameCn=Object.assign({},t).nameCn,this.fuelPackObj.nameTw=Object.assign({},t).nameTw,this.fuelPackObj.nameEn=Object.assign({},t).nameEn,this.fuelPackObj.cny=Object.assign({},t).cny.toString(),this.fuelPackObj.hkd=Object.assign({},t).hkd.toString(),this.fuelPackObj.usd=Object.assign({},t).usd.toString(),this.fuelPackObj.flowValue=Object.assign({},t).flowValue.toString(),this.fuelPackObj.flowUnit=Object.assign({},t).flowUnit,this.fuelPackObj.allowSub=Object.assign({},t).allowSub},Operation:function(t,e){var a=this,n=!0===t?"确认审批通过?":"确认审批不通过?";this.$Modal.confirm({title:n,onOk:function(){d({authStatus:t,id:e}).then((function(t){if(!t||"0000"!=t.code)throw t;a.goPageFirst(1),a.$Notice.success({title:"操作提示",desc:"操作成功"})})).catch((function(t){return!1}))}})},submit:function(){var t=this;this.$refs["fuelPackObj"].validate((function(e){e&&(t.submitloading=!0,"新建加油包"===t.title||"复制加油包"===t.title?o(t.fuelPackObj).then((function(e){"0000"==e.code&&(t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.fuelPackModal=!1,t.$refs["fuelPackObj"].resetFields(),t.goPageFirst(1))})).catch((function(t){console.error(t)})).finally((function(){t.submitloading=!1})):"修改加油包"===t.title&&r(t.fuelPackObj).then((function(e){"0000"==e.code&&(t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.fuelPackModal=!1,t.$refs["fuelPackObj"].resetFields(),t.goPageFirst(1))})).catch((function(t){console.error(t)})).finally((function(){t.submitloading=!1})))}))}}},p=h,g=(a("2960"),a("2877")),b=Object(g["a"])(p,n,i,!1,null,null,null);e["default"]=b.exports},"129f":function(t,e,a){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},2960:function(t,e,a){"use strict";a("522b")},"522b":function(t,e,a){},"841c":function(t,e,a){"use strict";var n=a("c65b"),i=a("d784"),l=a("825a"),s=a("7234"),c=a("1d80"),o=a("129f"),r=a("577e"),u=a("dc4a"),d=a("14c3");i("search",(function(t,e,a){return[function(e){var a=c(this),i=s(e)?void 0:u(e,t);return i?n(i,e,a):new RegExp(e)[t](r(a))},function(t){var n=l(this),i=r(t),s=a(e,n,i);if(s.done)return s.value;var c=n.lastIndex;o(c,0)||(n.lastIndex=0);var u=d(n,i);return o(n.lastIndex,c)||(n.lastIndex=c),null===u?-1:u.index}]}))}}]);