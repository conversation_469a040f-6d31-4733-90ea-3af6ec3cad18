# CMI-Web 到 Vue-Element-Plus-Admin 迁移方案

## 项目概述

本文档详细描述了将 CMI-Web 项目的核心功能迁移到 Vue-Element-Plus-Admin 项目的完整方案。

### 技术栈对比

| 功能模块 | CMI-Web | Vue-Element-Plus-Admin | 迁移策略 |
|---------|---------|----------------------|----------|
| 框架版本 | Vue 2.x | Vue 3.x | 升级语法，使用 Composition API |
| UI 组件库 | iView | Element Plus | 替换组件，保持功能一致 |
| 路由 | Vue Router 3.x | Vue Router 4.x | 升级路由配置语法 |
| 状态管理 | Vuex | Pinia | 重构状态管理逻辑 |
| 构建工具 | Webpack | Vite | 适配新的构建配置 |
| 语言 | JavaScript | TypeScript | 添加类型定义 |

## 迁移计划

### 阶段一：基础架构准备（1-2天）

#### 1.1 环境配置更新

**目标文件：** `vite.config.ts`

```typescript
// 添加 CMI 相关的代理配置
export default defineConfig({
  server: {
    proxy: {
      '/cmiweb': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/cmiweb/, '')
      }
    }
  }
})
```

#### 1.2 项目配置迁移

**目标文件：** `src/config/app.ts`

```typescript
interface AppConfig {
  title: string
  cookieExpires: number
  useI18n: boolean
  baseUrl: {
    dev: string
    pro: string
  }
  homeName: string
  keytype: string
}

export const appConfig: AppConfig = {
  title: 'CMI',
  cookieExpires: 1,
  useI18n: true,
  baseUrl: {
    dev: '/cmiweb',
    pro: '/api/'
  },
  homeName: 'home',
  keytype: 'ClearKey'
}
```

### 阶段二：用户登录系统迁移（2-3天）

#### 2.1 登录页面迁移

**源文件分析：** CMI-Web 登录组件
**目标文件：** `src/views/Login/Login.vue`

**迁移要点：**
- iView 组件替换为 Element Plus 组件
- Vue 2 语法升级为 Vue 3 Composition API
- 添加 TypeScript 类型定义

```vue
<template>
  <div class="login-container">
    <el-form
      ref="loginFormRef"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
    >
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          :placeholder="t('login.username')"
          prefix-icon="User"
        />
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          :placeholder="t('login.password')"
          prefix-icon="Lock"
          @keyup.enter="handleLogin"
        />
      </el-form-item>
      <el-button
        type="primary"
        class="login-btn"
        :loading="loading"
        @click="handleLogin"
      >
        {{ t('login.login') }}
      </el-button>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/store/modules/user'
import type { FormInstance, FormRules } from 'element-plus'

interface LoginForm {
  username: string
  password: string
}

const { t } = useI18n()
const router = useRouter()
const userStore = useUserStore()

const loginFormRef = ref<FormInstance>()
const loading = ref(false)

const loginForm = reactive<LoginForm>({
  username: '',
  password: ''
})

const loginRules: FormRules = {
  username: [
    { required: true, message: t('login.usernameRequired'), trigger: 'blur' }
  ],
  password: [
    { required: true, message: t('login.passwordRequired'), trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        await userStore.login(loginForm)
        router.push('/')
      } catch (error) {
        console.error('Login failed:', error)
      } finally {
        loading.value = false
      }
    }
  })
}
</script>
```

#### 2.2 用户状态管理迁移

**目标文件：** `src/store/modules/user.ts`

```typescript
import { defineStore } from 'pinia'
import { store } from '@/store'
import { loginApi, getUserInfoApi } from '@/api/user'
import { setToken, getToken, removeToken } from '@/utils/auth'

interface UserInfo {
  id: string
  username: string
  email: string
  roles: string[]
  access: string[]
  userBtnPriv: Array<{
    url: string
    priv: string[]
  }>
}

interface UserState {
  token: string
  userInfo: UserInfo | null
  roleRouters: any[]
  hasGetInfo: boolean
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    token: getToken() || '',
    userInfo: null,
    roleRouters: [],
    hasGetInfo: false
  }),

  getters: {
    getToken(): string {
      return this.token
    },
    getUserInfo(): UserInfo | null {
      return this.userInfo
    },
    getRoleRouters(): any[] {
      return this.roleRouters
    }
  },

  actions: {
    setToken(token: string) {
      this.token = token
      setToken(token)
    },

    setUserInfo(userInfo: UserInfo) {
      this.userInfo = userInfo
      this.hasGetInfo = true
    },

    async login(loginForm: { username: string; password: string }) {
      try {
        const response = await loginApi(loginForm)
        const { token } = response.data
        this.setToken(token)
        return response
      } catch (error) {
        throw error
      }
    },

    async getUserInfo() {
      try {
        const response = await getUserInfoApi()
        const userInfo = response.data
        this.setUserInfo(userInfo)
        return userInfo
      } catch (error) {
        throw error
      }
    },

    async logout() {
      this.token = ''
      this.userInfo = null
      this.hasGetInfo = false
      removeToken()
    }
  }
})

export const useUserStoreWithOut = () => {
  return useUserStore(store)
}
```

#### 2.3 Token 管理工具迁移

**目标文件：** `src/utils/auth.ts`

```typescript
import Cookies from 'js-cookie'
import { appConfig } from '@/config/app'

const TokenKey = 'CMI_TOKEN'
const UserIdKey = 'CMI_USER_ID'

export function getToken(): string | undefined {
  return Cookies.get(TokenKey)
}

export function setToken(token: string): void {
  Cookies.set(TokenKey, token, { expires: appConfig.cookieExpires })
}

export function removeToken(): void {
  Cookies.remove(TokenKey)
}

export function getUserId(): string | undefined {
  return Cookies.get(UserIdKey)
}

export function setUserId(userId: string): void {
  Cookies.set(UserIdKey, userId, { expires: appConfig.cookieExpires })
}

export function removeUserId(): void {
  Cookies.remove(UserIdKey)
}
```

### 阶段三：菜单系统迁移（2-3天）

#### 3.1 菜单数据结构定义

**目标文件：** `src/types/menu.ts`

```typescript
export interface MenuItem {
  id: string
  name: string
  path: string
  component?: string
  meta: {
    title: string
    icon?: string
    hidden?: boolean
    alwaysShow?: boolean
    roles?: string[]
    noCache?: boolean
  }
  children?: MenuItem[]
}

export interface MenuState {
  menuList: MenuItem[]
  isCollapse: boolean
  activeMenu: string
}
```

#### 3.2 菜单状态管理

**目标文件：** `src/store/modules/menu.ts`

```typescript
import { defineStore } from 'pinia'
import { getMenuListApi } from '@/api/menu'
import type { MenuItem, MenuState } from '@/types/menu'

export const useMenuStore = defineStore('menu', {
  state: (): MenuState => ({
    menuList: [],
    isCollapse: false,
    activeMenu: ''
  }),

  getters: {
    getMenuList(): MenuItem[] {
      return this.menuList
    },
    getIsCollapse(): boolean {
      return this.isCollapse
    }
  },

  actions: {
    setMenuList(menuList: MenuItem[]) {
      this.menuList = menuList
    },

    setCollapse(isCollapse: boolean) {
      this.isCollapse = isCollapse
    },

    setActiveMenu(path: string) {
      this.activeMenu = path
    },

    async fetchMenuList() {
      try {
        const response = await getMenuListApi()
        this.setMenuList(response.data)
        return response.data
      } catch (error) {
        console.error('Failed to fetch menu list:', error)
        throw error
      }
    }
  }
})
```

#### 3.3 菜单组件迁移

**目标文件：** `src/components/Menu/src/Menu.vue`

```vue
<template>
  <el-menu
    :default-active="activeMenu"
    :collapse="isCollapse"
    :unique-opened="true"
    class="sidebar-menu"
    @select="handleMenuSelect"
  >
    <menu-item
      v-for="menu in menuList"
      :key="menu.id"
      :menu="menu"
    />
  </el-menu>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useMenuStore } from '@/store/modules/menu'
import MenuItem from './MenuItem.vue'

const router = useRouter()
const menuStore = useMenuStore()

const menuList = computed(() => menuStore.getMenuList)
const isCollapse = computed(() => menuStore.getIsCollapse)
const activeMenu = computed(() => menuStore.activeMenu)

const handleMenuSelect = (path: string) => {
  menuStore.setActiveMenu(path)
  router.push(path)
}
</script>
```

### 阶段四：国际化系统迁移（1-2天）

#### 4.1 语言文件迁移

**目标目录：** `src/locales/`

**文件结构：**
```
src/locales/
├── zh-CN/
│   ├── common.ts
│   ├── login.ts
│   ├── menu.ts
│   └── index.ts
├── en/
│   ├── common.ts
│   ├── login.ts
│   ├── menu.ts
│   └── index.ts
└── index.ts
```

**目标文件：** `src/locales/zh-CN/login.ts`

```typescript
export default {
  login: '登录',
  username: '用户名',
  password: '密码',
  usernameRequired: '请输入用户名',
  passwordRequired: '请输入密码',
  loginSuccess: '登录成功',
  loginFailed: '登录失败',
  logout: '退出登录'
}
```

#### 4.2 国际化配置

**目标文件：** `src/locales/index.ts`

```typescript
import { createI18n } from 'vue-i18n'
import zhCN from './zh-CN'
import en from './en'

const messages = {
  'zh-CN': zhCN,
  'en': en
}

export const i18n = createI18n({
  legacy: false,
  locale: 'zh-CN',
  fallbackLocale: 'en',
  messages,
  globalInjection: true
})

export default i18n
```

### 阶段五：路由系统迁移（2-3天）

#### 5.1 路由配置迁移

**目标文件：** `src/router/modules/cmi.ts`

```typescript
import type { RouteRecordRaw } from 'vue-router'

const cmiRoutes: RouteRecordRaw[] = [
  {
    path: '/cmi',
    name: 'CMI',
    component: () => import('@/layout/Layout.vue'),
    meta: {
      title: 'CMI系统',
      icon: 'ep:home-filled'
    },
    children: [
      {
        path: 'dashboard',
        name: 'CMIDashboard',
        component: () => import('@/views/CMI/Dashboard/index.vue'),
        meta: {
          title: '仪表板',
          icon: 'ep:data-analysis'
        }
      }
      // 更多 CMI 相关路由...
    ]
  }
]

export default cmiRoutes
```

#### 5.2 路由守卫迁移

**目标文件：** `src/permission.ts` (更新现有文件)

```typescript
// 在现有路由守卫中添加 CMI 特定逻辑
router.beforeEach(async (to, from, next) => {
  start()
  loadStart()
  
  const permissionStore = usePermissionStoreWithOut()
  const appStore = useAppStoreWithOut()
  const userStore = useUserStoreWithOut()
  
  // CMI 特定的权限检查逻辑
  if (to.path.startsWith('/cmi')) {
    // 检查 CMI 相关权限
    const hasPermission = await checkCMIPermission(to)
    if (!hasPermission) {
      next({ name: 'error_401' })
      return
    }
  }
  
  // 原有逻辑保持不变...
  if (userStore.getUserInfo) {
    if (to.path === '/login') {
      next({ path: '/' })
    } else {
      // 现有逻辑...
    }
  }
})

// CMI 权限检查函数
async function checkCMIPermission(route: RouteLocationNormalized): Promise<boolean> {
  const userStore = useUserStoreWithOut()
  const userBtnPriv = userStore.getUserInfo?.userBtnPriv || []
  
  const matchedPriv = userBtnPriv.find(item => item.url === route.path)
  return matchedPriv ? matchedPriv.priv.includes('view') : false
}
```

### 阶段六：权限管理系统迁移（2-3天）

#### 6.1 权限状态管理

**目标文件：** `src/store/modules/permission.ts` (扩展现有文件)

```typescript
// 在现有 permission store 中添加 CMI 权限逻辑
export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    // 现有状态...
    cmiPermissions: [],
    buttonPermissions: new Map()
  }),

  actions: {
    // 现有方法...
    
    setCMIPermissions(permissions: any[]) {
      this.cmiPermissions = permissions
    },

    setButtonPermissions(permissions: Map<string, string[]>) {
      this.buttonPermissions = permissions
    },

    hasButtonPermission(route: string, action: string): boolean {
      const permissions = this.buttonPermissions.get(route) || []
      return permissions.includes(action)
    },

    async generateCMIRoutes(userPermissions: any[]) {
      // 根据用户权限生成 CMI 路由
      const accessedRoutes = filterCMIRoutes(cmiRoutes, userPermissions)
      this.addRouters = this.addRouters.concat(accessedRoutes)
      return accessedRoutes
    }
  }
})

function filterCMIRoutes(routes: RouteRecordRaw[], permissions: any[]): RouteRecordRaw[] {
  const res: RouteRecordRaw[] = []
  
  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(permissions, tmp)) {
      if (tmp.children) {
        tmp.children = filterCMIRoutes(tmp.children, permissions)
      }
      res.push(tmp)
    }
  })
  
  return res
}
```

#### 6.2 权限指令

**目标文件：** `src/directives/permission.ts`

```typescript
import type { App, Directive } from 'vue'
import { usePermissionStoreWithOut } from '@/store/modules/permission'

// 按钮权限指令
const permissionDirective: Directive = {
  mounted(el, binding) {
    const { value } = binding
    const permissionStore = usePermissionStoreWithOut()
    
    if (value && value instanceof Array && value.length > 0) {
      const route = value[0]
      const action = value[1]
      
      const hasPermission = permissionStore.hasButtonPermission(route, action)
      
      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
  }
}

export function setupPermissionDirective(app: App) {
  app.directive('permission', permissionDirective)
}
```

### 阶段七：API 接口迁移（1-2天）

#### 7.1 API 配置

**目标文件：** `src/api/cmi/index.ts`

```typescript
import request from '@/utils/request'

// 用户相关 API
export const loginApi = (data: { username: string; password: string }) => {
  return request.post('/auth/login', data)
}

export const getUserInfoApi = () => {
  return request.get('/auth/userinfo')
}

export const logoutApi = () => {
  return request.post('/auth/logout')
}

// 菜单相关 API
export const getMenuListApi = () => {
  return request.get('/menu/list')
}

// 权限相关 API
export const getUserPermissionsApi = () => {
  return request.get('/auth/permissions')
}
```

#### 7.2 请求拦截器更新

**目标文件：** `src/utils/request.ts` (更新现有文件)

```typescript
// 在现有请求拦截器中添加 CMI 特定逻辑
request.interceptors.request.use(
  (config) => {
    // 现有逻辑...
    
    // CMI 特定的请求头
    if (config.url?.startsWith('/cmiweb')) {
      config.headers['X-CMI-Version'] = '1.0'
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)
```

## 测试验证方法

### 功能测试清单

#### 1. 登录系统测试
- [ ] 用户名密码验证
- [ ] Token 存储和读取
- [ ] 登录状态保持
- [ ] 自动登出功能

#### 2. 菜单系统测试
- [ ] 菜单数据加载
- [ ] 菜单权限过滤
- [ ] 菜单展开收起
- [ ] 菜单路由跳转

#### 3. 国际化测试
- [ ] 语言切换功能
- [ ] 翻译文本显示
- [ ] 语言状态保持
- [ ] 动态语言加载

#### 4. 路由系统测试
- [ ] 路由权限验证
- [ ] 动态路由生成
- [ ] 路由守卫功能
- [ ] 页面访问控制

#### 5. 权限管理测试
- [ ] 角色权限验证
- [ ] 按钮权限控制
- [ ] 页面权限过滤
- [ ] 权限指令功能

### 自动化测试

**目标文件：** `tests/unit/cmi.spec.ts`

```typescript
import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import Login from '@/views/Login/Login.vue'

describe('CMI Login Component', () => {
  it('should render login form correctly', () => {
    const wrapper = mount(Login, {
      global: {
        plugins: [createPinia()]
      }
    })
    
    expect(wrapper.find('.login-form').exists()).toBe(true)
    expect(wrapper.find('input[placeholder="用户名"]').exists()).toBe(true)
    expect(wrapper.find('input[placeholder="密码"]').exists()).toBe(true)
  })
  
  it('should validate form inputs', async () => {
    const wrapper = mount(Login, {
      global: {
        plugins: [createPinia()]
      }
    })
    
    const loginBtn = wrapper.find('.login-btn')
    await loginBtn.trigger('click')
    
    // 验证表单验证逻辑
    expect(wrapper.text()).toContain('请输入用户名')
  })
})
```

## 注意事项

### 1. 兼容性问题
- **Vue 2 到 Vue 3 语法差异**：注意 `$emit`、`$refs` 等 API 变化
- **组件库差异**：iView 到 Element Plus 的组件属性和事件名称变化
- **路由版本差异**：Vue Router 4.x 的新语法和配置方式

### 2. 性能优化
- **代码分割**：使用动态导入分割 CMI 相关代码
- **懒加载**：菜单和路由的懒加载实现
- **缓存策略**：用户信息和权限数据的缓存机制

### 3. 安全考虑
- **Token 安全**：Token 的安全存储和传输
- **权限验证**：前后端权限验证的一致性
- **XSS 防护**：用户输入的安全处理

### 4. 代码规范
- **TypeScript 类型**：为所有 CMI 相关代码添加类型定义
- **ESLint 规则**：遵循项目的代码规范
- **组件命名**：使用统一的组件命名规范

## 迁移时间表

| 阶段 | 任务 | 预计时间 | 负责人 |
|------|------|----------|--------|
| 1 | 基础架构准备 | 1-2天 | 开发者 |
| 2 | 用户登录系统迁移 | 2-3天 | 开发者 |
| 3 | 菜单系统迁移 | 2-3天 | 开发者 |
| 4 | 国际化系统迁移 | 1-2天 | 开发者 |
| 5 | 路由系统迁移 | 2-3天 | 开发者 |
| 6 | 权限管理系统迁移 | 2-3天 | 开发者 |
| 7 | API 接口迁移 | 1-2天 | 开发者 |
| 8 | 测试和优化 | 2-3天 | 开发者 |

**总计：** 13-21天

## 风险评估

### 高风险项
1. **权限系统复杂性**：CMI 的权限系统可能比较复杂，需要仔细分析和测试
2. **数据结构差异**：两个项目的数据结构可能存在差异，需要适配

### 中风险项
1. **组件兼容性**：UI 组件的迁移可能需要调整样式和交互
2. **API 接口变化**：后端接口可能需要相应调整

### 低风险项
1. **国际化迁移**：相对简单，主要是文件迁移和配置
2. **基础配置**：项目配置的迁移风险较低

## 后续维护

### 1. 文档更新
- 更新项目 README
- 编写 CMI 功能使用文档
- 维护 API 接口文档

### 2. 代码维护
- 定期更新依赖包
- 优化性能和用户体验
- 修复发现的 Bug

### 3. 功能扩展
- 根据业务需求添加新功能
- 优化现有功能的用户体验
- 集成更多 CMI 相关模块

---

**文档版本：** 1.0  
**创建日期：** 2024年12月  
**最后更新：** 2024年12月  
**维护者：** 开发团队