<template>
  <!-- 押金/账户管理 -->
  <ContentWrap>
    <!-- 加载状态 -->
    <div v-if="!isComponentMounted" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 主要内容 -->
    <el-card v-else style="z-index: auto">
      <el-form ref="formRef" :model="formInline" inline style="margin: 50px 0; font-weight: bold">
        <el-form-item label="合作模式" :label-width="120">
          <el-input v-model="formInline.cooperationMode" readonly style="width: 250px" />
        </el-form-item>
        <el-form-item label="渠道模式" :label-width="100">
          <el-input v-model="formInline.channelType" readonly style="width: 250px" />
        </el-form-item>
        <el-form-item label="币种" :label-width="80">
          <el-input v-model="formInline.currencyCode" readonly style="width: 250px" />
        </el-form-item>
      </el-form>

      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="deposit" label="押金账户" min-width="200" align="center" />
        <el-table-column label="营销账户" min-width="200" align="center">
          <template #default="scope">
            <div class="cell-content" v-if="scope">
              <div style="margin: 10px 0">{{ scope.row.marketingAmount || 0 }}</div>
              <el-button
                v-if="hasPermission('marketingAccountDetails')"
                type="info"
                size="small"
                plain
                @click="handleDetailClick(scope.row, 'marketing')"
                style="margin: 0 0 10px 0"
              >
                营销账户详情
              </el-button>
            </div>
            <div v-else class="cell-content">
              <div style="margin: 10px 0">-</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="信用账户" min-width="200" align="center">
          <template #default="scope">
            <div class="cell-content" v-if="scope">
              <div style="margin: 10px 0">{{ scope.row.creditAmount || 0 }}</div>
            </div>
            <div v-else class="cell-content">
              <div style="margin: 10px 0">-</div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div style="margin: 50px 0; font-weight: bold; display: flex; justify-content: center">
        <el-button
          v-if="hasPermission('Packagedetails')"
          :disabled="cooperationMode === '3'"
          size="large"
          type="success"
          style="width: 150px; margin-right: 100px"
          @click="details(1)"
        >
          可购套餐
        </el-button>
        <el-button
          v-if="hasPermission('streamdetails')"
          :disabled="['2', '3'].includes(cooperationMode)"
          size="large"
          type="warning"
          style="min-width: 150px; margin-right: 100px"
          @click="details(2)"
        >
          流量详情
        </el-button>
        <el-button
          v-if="hasPermission('recharge')"
          :disabled="cooperationMode === '3'"
          size="large"
          type="danger"
          style="width: 150px"
          @click="recharge"
        >
          充值
        </el-button>
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { useRouter } from 'vue-router'
import { useComponentSafety } from '@/composables/useComponentSafety'
import { getAccountManagement, getChannelCooperationMode } from '@/api/cmi/channel'
import { useUserStore } from '@/store/modules/user'

const hasPermission = (permission: string): boolean => true

const router = useRouter()
const userStore = useUserStore()

// 字段映射函数
const mapChannelType = (type: string): string => {
  const typeMap: Record<string, string> = {
    '1': '直销',
    '2': '代理',
    '3': '混合'
  }
  return typeMap[type] || '未知'
}

const mapCurrencyCode = (code: string): string => {
  const currencyMap: Record<string, string> = {
    '840': 'USD',
    '156': 'CNY',
    '978': 'EUR'
  }
  return currencyMap[code] || code
}

const mapCooperationMode = (modes: string[]): string => {
  if (!modes || modes.length === 0) return '未知'

  const modeMap: Record<string, string> = {
    '1': '预付费',
    '2': '后付费',
    '3': '混合'
  }
  return modeMap[modes[0]] || '未知'
}

const {
  isComponentMounted,
  isLoading,
  safeNavigate,
  safeLoadData,
  createSafeRef,
  initComponentSafety
} = useComponentSafety('充值管理')

const formRef = createSafeRef()
const loading = isLoading

const formInline = reactive({
  cooperationMode: '', // 合作模式
  channelType: '',     // 渠道模式
  currencyCode: ''     // 币种
})

// 原始合作模式代码，用于按钮状态判断
const cooperationMode = ref('')

const tableData = ref([
  {
    deposit: '账户余额', // 显示标签
    marketingAmount: 0,
    creditAmount: 0
  }
])

const handleDetailClick = (row: any, type: string) => {
  if (type === 'marketing') {
    const corpId = userStore.getCorpId
    safeNavigate(router, {
      path: '/newcmi/channel/deposit/marketing-account',
      query: {
        corpId: corpId,
        amount: row.marketingAmount
      }
    }, '跳转到营销账户详情页面失败')
  }
}

const details = (type: number) => {
  const corpId = userStore.getCorpId
  if (type === 1) {
    safeNavigate(router, {
      path: '/newcmi/channel/deposit/meal-list',
      query: { corpId }
    }, '跳转到可购套餐页面失败')
  } else if (type === 2) {
    safeNavigate(router, {
      path: '/newcmi/channel/deposit/stream-list',
      query: { corpId }
    }, '跳转到流量详情页面失败')
  }
}

const recharge = () => {
  const corpId = userStore.getCorpId
  safeNavigate(router, {
    path: '/newcmi/channel/deposit/offline-payment',
    query: { corpId }
  }, '跳转到充值页面失败')
}

// 获取账户管理数据
const getAccountData = () => {
  return safeLoadData(async () => {
    try {
      console.log('📊 [充值管理] 开始获取账户数据')

      const corpId = userStore.getCorpId
      if (!corpId) {
        throw new Error('未获取到企业ID')
      }

      console.log('🔍 [充值管理] 企业ID:', corpId)

      // 并行获取账户管理信息和合作模式
      const [accountData, cooperationModes] = await Promise.all([
        getAccountManagement({ corpId }),
        getChannelCooperationMode({ corpId })
      ])

      console.log('✅ [充值管理] API响应:', { accountData, cooperationModes })

      // 检查账户管理数据（API已经处理过，直接返回对象）
      if (accountData && typeof accountData === 'object') {
        // 获取原始合作模式代码用于按钮状态判断
        const rawCooperationModes = Array.isArray(cooperationModes) ? cooperationModes : []
        cooperationMode.value = rawCooperationModes.length > 0 ? rawCooperationModes[0] : '1'

        // 更新表单数据 - 使用映射函数转换
        formInline.cooperationMode = mapCooperationMode(rawCooperationModes)
        formInline.channelType = mapChannelType(accountData.channelType || '1')
        formInline.currencyCode = mapCurrencyCode(accountData.currencyCode || '156')

        // 更新表格数据
        tableData.value = [
          {
            deposit: '账户余额',
            marketingAmount: accountData.marketingAmount ?? 0,
            creditAmount: accountData.creditAmount ?? 0
          }
        ]

        console.log('✅ [充值管理] 数据更新完成:', {
          formInline: { ...formInline },
          tableData: tableData.value,
          originalData: { accountData, cooperationModes }
        })

      } else {
        console.warn('⚠️ [充值管理] API返回数据格式异常，使用默认值')
        setDefaultData()
      }

    } catch (error) {
      console.error('❌ [充值管理] API调用失败:', error)
      setDefaultData()
      ElMessage.warning('获取账户数据失败，显示默认数据')
    }
  }, '获取账户数据失败')
}

// 设置默认数据
const setDefaultData = () => {
  console.log('🔧 [充值管理] 设置默认数据')

  formInline.cooperationMode = '预付费'
  formInline.channelType = '直销'
  formInline.currencyCode = 'CNY'

  tableData.value = [
    {
      deposit: '账户余额',
      marketingAmount: 0,
      creditAmount: 0
    }
  ]
}

initComponentSafety(async () => {
  await getAccountData()
})
</script>

<style scoped>
.loading-container {
  padding: 20px;
}

.cell-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 确保表格在加载时不会闪烁 */
:deep(.el-table) {
  min-height: 200px;
}
</style>
