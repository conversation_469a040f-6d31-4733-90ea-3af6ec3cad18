<template>
  <!-- 押金/账户管理 -->
  <ContentWrap>
    <!-- 加载状态 -->
    <div v-if="!isComponentMounted" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 主要内容 -->
    <el-card v-else style="z-index: auto">
      <el-form ref="formRef" :model="formInline" inline style="margin: 50px 0; font-weight: bold">
        <el-form-item label="合作模式" :label-width="120">
          <el-input v-model="formInline.cooperationMode" readonly style="width: 250px" />
        </el-form-item>
        <el-form-item label="渠道模式" :label-width="100">
          <el-input v-model="formInline.channelType" readonly style="width: 250px" />
        </el-form-item>
        <el-form-item label="币种" :label-width="80">
          <el-input v-model="formInline.currencyCode" readonly style="width: 250px" />
        </el-form-item>
      </el-form>

      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="deposit" label="" min-width="200" align="center" />
        <el-table-column label="营销账户" min-width="200" align="center">
          <template #default="{ row }">
            <div class="cell-content" v-if="row">
              <div style="margin: 10px 0">{{ row.marketingAmount || 0 }}</div>
              <el-button
                v-if="hasPermission('marketingAccountDetails')"
                type="info"
                size="small"
                plain
                @click="handleDetailClick(row, 'marketing')"
                style="margin: 0 0 10px 0"
              >
                营销账户详情
              </el-button>
            </div>
            <div v-else class="cell-content">
              <div style="margin: 10px 0">-</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="信用账户" min-width="200" align="center">
          <template #default="{ row }">
            <div class="cell-content" v-if="row">
              <div style="margin: 10px 0">{{ row.creditAmount || 0 }}</div>
            </div>
            <div v-else class="cell-content">
              <div style="margin: 10px 0">-</div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div style="margin: 50px 0; font-weight: bold; display: flex; justify-content: center">
        <el-button
          v-if="hasPermission('Packagedetails')"
          :disabled="cooperationMode === '3'"
          size="large"
          type="success"
          style="width: 150px; margin-right: 100px"
          @click="details(1)"
        >
          可购套餐
        </el-button>
        <el-button
          v-if="hasPermission('streamdetails')"
          :disabled="['2', '3'].includes(cooperationMode)"
          size="large"
          type="warning"
          style="min-width: 150px; margin-right: 100px"
          @click="details(2)"
        >
          流量详情
        </el-button>
        <el-button
          v-if="hasPermission('recharge')"
          :disabled="cooperationMode === '3'"
          size="large"
          type="danger"
          style="width: 150px"
          @click="recharge"
        >
          充值
        </el-button>
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { useRouter } from 'vue-router'

// 权限检查函数 - 临时返回true以确保页面正常显示
const hasPermission = (permission: string): boolean => {
  console.log(`🔍 [权限检查] ${permission}: 允许访问`)
  return true // 临时返回true，后续需要实现真实的权限检查逻辑
}

const router = useRouter()

// 组件状态管理
const isComponentMounted = ref(false)
const isComponentDestroyed = ref(false)

// 响应式数据
const loading = ref(false)
const corpId = ref('')
const cooperationMode = ref('')

const formInline = reactive({
  cooperationMode: '',
  channelType: '',
  currencyCode: ''
})

const tableData = ref<any[]>([
  {
    deposit: '账户余额',
    marketingAmount: 0,
    creditAmount: 0
  }
])

// 方法
const handleDetailClick = (row: any, type: string) => {
  if (isComponentDestroyed.value) {
    console.warn('🚫 [充值管理] 组件已销毁，取消操作')
    return
  }

  if (type === 'marketing') {
    try {
      console.log('🔗 [充值管理] 跳转到营销账户详情页面')
      // 跳转到营销账户详情页面
      router.push({
        path: '/newcmi/channel/deposit/marketing-account',
        query: { corpId: corpId.value }
      })
    } catch (error) {
      console.error('❌ [充值管理] 路由跳转失败:', error)
      ElMessage.error('页面跳转失败')
    }
  }
}

const details = (type: number) => {
  if (isComponentDestroyed.value) {
    console.warn('🚫 [充值管理] 组件已销毁，取消操作')
    return
  }

  try {
    if (type === 1) {
      console.log('🔗 [充值管理] 跳转到可购套餐页面')
      // 跳转到可购套餐页面
      router.push({
        path: '/newcmi/channel/deposit/meal-list',
        query: { corpId: corpId.value }
      })
    } else if (type === 2) {
      console.log('🔗 [充值管理] 跳转到流量详情页面')
      // 跳转到流量详情页面
      router.push({
        path: '/newcmi/channel/deposit/stream-list',
        query: { corpId: corpId.value }
      })
    }
  } catch (error) {
    console.error('❌ [充值管理] 路由跳转失败:', error)
    ElMessage.error('页面跳转失败')
  }
}

const recharge = () => {
  if (isComponentDestroyed.value) {
    console.warn('🚫 [充值管理] 组件已销毁，取消操作')
    return
  }

  try {
    console.log('🔗 [充值管理] 跳转到充值页面')
    // 跳转到充值页面
    router.push({
      path: '/newcmi/channel/deposit/offline-payment',
      query: { corpId: corpId.value }
    })
  } catch (error) {
    console.error('❌ [充值管理] 路由跳转失败:', error)
    ElMessage.error('页面跳转失败')
  }
}

// 获取账户管理数据
const getAccountData = async () => {
  if (isComponentDestroyed.value) {
    console.warn('🚫 [充值管理] 组件已销毁，取消数据加载')
    return
  }

  try {
    loading.value = true
    console.log('📊 [充值管理] 开始加载账户数据')

    // 使用 nextTick 确保DOM已更新
    await nextTick()

    if (isComponentDestroyed.value) {
      console.warn('🚫 [充值管理] 组件在数据加载过程中被销毁')
      return
    }

    // TODO: 实现API调用
    // const response = await getAccountManagement()
    // 模拟数据
    formInline.cooperationMode = '预付费'
    formInline.channelType = '直销'
    formInline.currencyCode = 'CNY'

    tableData.value = [
      {
        deposit: '账户余额',
        marketingAmount: 10000.0,
        creditAmount: 5000.0
      }
    ]

    console.log('✅ [充值管理] 账户数据加载完成')
  } catch (error) {
    console.error('❌ [充值管理] 数据加载失败:', error)
    if (!isComponentDestroyed.value) {
      ElMessage.error('获取账户数据失败')
    }
  } finally {
    if (!isComponentDestroyed.value) {
      loading.value = false
    }
  }
}

// 生命周期
onMounted(async () => {
  console.log('🎉 [充值管理] 组件开始挂载')

  try {
    // 标记组件已挂载
    isComponentMounted.value = true
    isComponentDestroyed.value = false

    // 等待DOM完全渲染
    await nextTick()

    console.log('✅ [充值管理] 组件挂载完成，开始加载数据')

    // 延迟一小段时间确保所有DOM元素都已准备好
    setTimeout(() => {
      if (!isComponentDestroyed.value) {
        getAccountData()
      }
    }, 50)

  } catch (error) {
    console.error('❌ [充值管理] 组件挂载失败:', error)
  }
})

onUnmounted(() => {
  console.log('🔄 [充值管理] 组件开始卸载')

  // 标记组件已销毁
  isComponentDestroyed.value = true
  isComponentMounted.value = false

  // 清理加载状态
  loading.value = false

  console.log('✅ [充值管理] 组件卸载完成')
})
</script>

<style scoped>
.loading-container {
  padding: 20px;
}

.cell-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 确保表格在加载时不会闪烁 */
:deep(.el-table) {
  min-height: 200px;
}
</style>
