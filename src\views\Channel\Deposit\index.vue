<template>
  <ContentWrap title="充值管理" message="管理渠道账户充值和余额信息">
    <!-- 账户信息表单 -->
    <div class="mb-20px">
      <ElForm ref="formRef" :model="formData" inline label-width="120px">
        <ElFormItem label="合作模式">
          <ElInput v-model="formData.cooperationMode" readonly style="width: 250px" />
        </ElFormItem>
        <ElFormItem label="渠道模式">
          <ElInput v-model="formData.channelType" readonly style="width: 250px" />
        </ElFormItem>
        <ElFormItem label="币种">
          <ElInput v-model="formData.currencyCode" readonly style="width: 250px" />
        </ElFormItem>
      </ElForm>
    </div>

    <!-- 账户余额表格 -->
    <div class="mb-20px">
      <ElTable :data="tableData" border v-loading="loading" style="width: 100%">
        <ElTableColumn prop="deposit" label="押金账户" min-width="200" align="center" />
        <ElTableColumn label="营销账户" min-width="200" align="center">
          <template #default="scope">
            <div class="cell-content">
              <div style="margin-bottom: 10px">{{ formatAmount(scope.row.marketingAmount) }}</div>
              <ElButton
                v-if="checkPermission(['marketingAccountDetails'])"
                type="info"
                size="small"
                plain
                @click="handleDetailClick(scope.row, 'marketing')"
              >
                营销账户详情
              </ElButton>
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn label="信用账户" min-width="200" align="center">
          <template #default="scope">
            <div class="cell-content">
              <div>{{ formatAmount(scope.row.creditAmount) }}</div>
            </div>
          </template>
        </ElTableColumn>
      </ElTable>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <ElButton
        v-if="checkPermission(['Packagedetails'])"
        :disabled="cooperationModeCode === '3'"
        size="large"
        type="success"
        style="width: 150px"
        @click="handleNavigation('packages')"
      >
        可购套餐
      </ElButton>
      <ElButton
        v-if="checkPermission(['streamdetails'])"
        :disabled="['2', '3'].includes(cooperationModeCode)"
        size="large"
        type="warning"
        style="min-width: 150px"
        @click="handleNavigation('streams')"
      >
        流量详情
      </ElButton>
      <ElButton
        v-if="checkPermission(['recharge'])"
        :disabled="cooperationModeCode === '3'"
        size="large"
        type="danger"
        style="width: 150px"
        @click="handleNavigation('recharge')"
      >
        充值
      </ElButton>
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, toRefs } from 'vue'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { useRouter } from 'vue-router'
import { hasPermi } from '@/components/Permission'
import { getAccountManagement, getChannelCooperationMode } from '@/api/cmi/channel'
import { useUserStore } from '@/store/modules/user'

defineOptions({
  name: 'ChannelDepositManagement'
})

const { push } = useRouter()
const userStore = useUserStore()

// 权限检查函数
const checkPermission = (permissions: string[]): boolean => {
  // 临时返回true，实际应该使用用户权限进行检查
  return true
}

// 响应式数据
const loading = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
  cooperationMode: '', // 合作模式
  channelType: '', // 渠道模式
  currencyCode: '' // 币种
})

// 原始合作模式代码，用于按钮状态判断
const cooperationModeCode = ref('')

// 表格数据
const tableData = ref([
  {
    deposit: '账户余额',
    marketingAmount: 0,
    creditAmount: 0
  }
])

// 字段映射函数
const mapChannelType = (type: string): string => {
  const typeMap: Record<string, string> = {
    '1': '直销',
    '2': '代理',
    '3': '混合'
  }
  return typeMap[type] || '未知'
}

const mapCurrencyCode = (code: string): string => {
  const currencyMap: Record<string, string> = {
    '840': 'USD',
    '156': 'CNY',
    '978': 'EUR'
  }
  return currencyMap[code] || code
}

const mapCooperationMode = (modes: string[]): string => {
  if (!modes || modes.length === 0) return '未知'

  const modeMap: Record<string, string> = {
    '1': '预付费',
    '2': '后付费',
    '3': '混合'
  }
  return modeMap[modes[0]] || '未知'
}

// 格式化金额显示
const formatAmount = (amount: number | null | undefined): string => {
  if (amount === null || amount === undefined) return '0.00'
  return Number(amount).toFixed(2)
}

// 处理详情点击
const handleDetailClick = (row: any, type: string) => {
  if (type === 'marketing') {
    const corpId = userStore.getCorpId
    push({
      path: '/newcmi/channel/deposit/marketing-account',
      query: {
        corpId,
        amount: row.marketingAmount
      }
    })
  }
}

// 统一的导航处理
const handleNavigation = (type: string) => {
  const corpId = userStore.getCorpId

  const routeMap: Record<string, string> = {
    packages: '/newcmi/channel/deposit/meal-list',
    streams: '/newcmi/channel/deposit/stream-list',
    recharge: '/newcmi/channel/deposit/offline-payment'
  }

  const path = routeMap[type]
  if (path) {
    push({
      path,
      query: { corpId }
    })
  }
}

// 获取账户管理数据
const getAccountData = async () => {
  try {
    loading.value = true
    console.log('📊 [充值管理] 开始获取账户数据')

    const corpId = userStore.getCorpId
    if (!corpId) {
      throw new Error('未获取到企业ID')
    }

    console.log('🔍 [充值管理] 企业ID:', corpId)

    // 并行获取账户管理信息和合作模式
    const [accountData, cooperationModes] = await Promise.all([
      getAccountManagement({ corpId }),
      getChannelCooperationMode({ corpId })
    ])

    console.log('✅ [充值管理] API响应:', { accountData, cooperationModes })

    // 检查账户管理数据（API已经处理过，直接返回对象）
    if (accountData && typeof accountData === 'object') {
      // 获取原始合作模式代码用于按钮状态判断
      const rawCooperationModes = Array.isArray(cooperationModes) ? cooperationModes : []
      cooperationModeCode.value = rawCooperationModes.length > 0 ? rawCooperationModes[0] : '1'

      // 更新表单数据 - 使用映射函数转换
      formData.cooperationMode = mapCooperationMode(rawCooperationModes)
      formData.channelType = mapChannelType(accountData.channelType || '1')
      formData.currencyCode = mapCurrencyCode(accountData.currencyCode || '156')

      // 更新表格数据
      tableData.value = [
        {
          deposit: '账户余额',
          marketingAmount: accountData.marketingAmount ?? 0,
          creditAmount: accountData.creditAmount ?? 0
        }
      ]

      console.log('✅ [充值管理] 数据更新完成:', {
        formData: { ...formData },
        tableData: tableData.value,
        originalData: { accountData, cooperationModes }
      })
    } else {
      console.warn('⚠️ [充值管理] API返回数据格式异常，使用默认值')
      setDefaultData()
    }
  } catch (error) {
    console.error('❌ [充值管理] API调用失败:', error)
    setDefaultData()
    ElMessage.warning('获取账户数据失败，显示默认数据')
  } finally {
    loading.value = false
  }
}

// 设置默认数据
const setDefaultData = () => {
  console.log('🔧 [充值管理] 设置默认数据')

  cooperationModeCode.value = '1' // 原始代码
  formData.cooperationMode = '预付费'
  formData.channelType = '直销'
  formData.currencyCode = 'CNY'

  tableData.value = [
    {
      deposit: '账户余额',
      marketingAmount: 0,
      creditAmount: 0
    }
  ]
}

// 组件挂载时获取数据
onMounted(async () => {
  await getAccountData()
})
</script>

<style scoped>
/* 使用框架的设计系统 */
.deposit-form {
  margin-bottom: 20px;
}

.deposit-table {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 100px;
  margin-top: 50px;
}

.cell-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 0;
}

/* 确保表格在加载时不会闪烁 */
:deep(.el-table) {
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 20px;
    align-items: center;
  }

  :deep(.el-form--inline .el-form-item) {
    display: block;
    margin-bottom: 20px;
  }
}
</style>
