<template>
  <!-- 押金/账户管理 -->
  <ContentWrap>
    <!-- 加载状态 -->
    <div v-if="!isComponentMounted" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 主要内容 -->
    <el-card v-else style="z-index: auto">
      <el-form ref="formRef" :model="formInline" inline style="margin: 50px 0; font-weight: bold">
        <el-form-item label="合作模式" :label-width="120">
          <el-input v-model="formInline.cooperationMode" readonly style="width: 250px" />
        </el-form-item>
        <el-form-item label="渠道模式" :label-width="100">
          <el-input v-model="formInline.channelType" readonly style="width: 250px" />
        </el-form-item>
        <el-form-item label="币种" :label-width="80">
          <el-input v-model="formInline.currencyCode" readonly style="width: 250px" />
        </el-form-item>
      </el-form>

      <el-table :data="tableData" border style="width: 100%" v-loading="loading">
        <el-table-column prop="deposit" label="" min-width="200" align="center" />
        <el-table-column label="营销账户" min-width="200" align="center">
          <template #default="scope">
            <div class="cell-content" v-if="scope">
              <div style="margin: 10px 0">{{ scope.row.marketingAmount || 0 }}</div>
              <el-button
                v-if="hasPermission('marketingAccountDetails')"
                type="info"
                size="small"
                plain
                @click="handleDetailClick(scope.row, 'marketing')"
                style="margin: 0 0 10px 0"
              >
                营销账户详情
              </el-button>
            </div>
            <div v-else class="cell-content">
              <div style="margin: 10px 0">-</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="信用账户" min-width="200" align="center">
          <template #default="scope">
            <div class="cell-content" v-if="scope">
              <div style="margin: 10px 0">{{ scope.row.creditAmount || 0 }}</div>
            </div>
            <div v-else class="cell-content">
              <div style="margin: 10px 0">-</div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div style="margin: 50px 0; font-weight: bold; display: flex; justify-content: center">
        <el-button
          v-if="hasPermission('Packagedetails')"
          :disabled="cooperationMode === '3'"
          size="large"
          type="success"
          style="width: 150px; margin-right: 100px"
          @click="details(1)"
        >
          可购套餐
        </el-button>
        <el-button
          v-if="hasPermission('streamdetails')"
          :disabled="['2', '3'].includes(cooperationMode)"
          size="large"
          type="warning"
          style="min-width: 150px; margin-right: 100px"
          @click="details(2)"
        >
          流量详情
        </el-button>
        <el-button
          v-if="hasPermission('recharge')"
          :disabled="cooperationMode === '3'"
          size="large"
          type="danger"
          style="width: 150px"
          @click="recharge"
        >
          充值
        </el-button>
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { useRouter } from 'vue-router'
import { useComponentSafety } from '@/composables/useComponentSafety'
import { getAccountManagement, getChannelCooperationMode } from '@/api/cmi/channel'
import { useUserStore } from '@/store/modules/user'

// 权限检查函数 - 临时返回true以确保页面正常显示
const hasPermission = (permission: string): boolean => {
  console.log(`🔍 [权限检查] ${permission}: 允许访问`)
  return true // 临时返回true，后续需要实现真实的权限检查逻辑
}

const router = useRouter()
const userStore = useUserStore()

// 使用组件安全性管理
const {
  isComponentMounted,
  isLoading,
  safeNavigate,
  safeLoadData,
  createSafeRef,
  initComponentSafety
} = useComponentSafety('充值管理')

// DOM引用
const formRef = createSafeRef()

// 响应式数据
const loading = isLoading
const cooperationMode = ref('')

const formInline = reactive({
  cooperationMode: '',
  channelType: '',
  currencyCode: ''
})

const tableData = ref<any[]>([
  {
    deposit: '账户余额',
    marketingAmount: 0,
    creditAmount: 0
  }
])

// 方法
const handleDetailClick = (row: any, type: string) => {
  console.log('🔗 [充值管理] 处理详情点击:', { row, type })

  if (type === 'marketing') {
    const corpId = userStore.getCorpId
    safeNavigate(router, {
      path: '/newcmi/channel/deposit/marketing-account',
      query: {
        corpId: corpId,
        amount: row.marketingAmount
      }
    }, '跳转到营销账户详情页面失败')
  }
}

const details = (type: number) => {
  console.log('🔗 [充值管理] 处理详情跳转:', { type })

  const corpId = userStore.getCorpId

  if (type === 1) {
    safeNavigate(router, {
      path: '/newcmi/channel/deposit/meal-list',
      query: { corpId }
    }, '跳转到可购套餐页面失败')
  } else if (type === 2) {
    safeNavigate(router, {
      path: '/newcmi/channel/deposit/stream-list',
      query: { corpId }
    }, '跳转到流量详情页面失败')
  }
}

const recharge = () => {
  console.log('🔗 [充值管理] 处理充值跳转')

  const corpId = userStore.getCorpId
  safeNavigate(router, {
    path: '/newcmi/channel/deposit/offline-payment',
    query: { corpId }
  }, '跳转到充值页面失败')
}

// 获取账户管理数据
const getAccountData = () => {
  return safeLoadData(async () => {
    try {
      console.log('📊 [充值管理] 开始获取账户数据')

      // 获取用户信息
      const corpId = userStore.getCorpId
      if (!corpId) {
        throw new Error('未获取到企业ID')
      }

      // 并行获取账户管理信息和合作模式
      const [accountResponse, cooperationResponse] = await Promise.all([
        getAccountManagement({ corpId }),
        getChannelCooperationMode({ corpId })
      ])

      console.log('✅ [充值管理] API响应:', { accountResponse, cooperationResponse })

      // 处理账户管理数据
      if (accountResponse?.data) {
        const accountData = accountResponse.data

        // 更新表单数据
        formInline.cooperationMode = cooperationResponse?.data?.cooperationMode || '预付费'
        formInline.channelType = accountData.channelType || '直销'
        formInline.currencyCode = accountData.currencyCode || 'CNY'

        // 更新表格数据
        tableData.value = [
          {
            deposit: '账户余额',
            marketingAmount: accountData.marketingAmount || 0,
            creditAmount: accountData.creditAmount || 0
          }
        ]

        console.log('✅ [充值管理] 数据更新完成:', {
          formInline: formInline,
          tableData: tableData.value
        })
      } else {
        // 如果API返回空数据，使用默认值
        console.warn('⚠️ [充值管理] API返回空数据，使用默认值')
        formInline.cooperationMode = '预付费'
        formInline.channelType = '直销'
        formInline.currencyCode = 'CNY'

        tableData.value = [
          {
            deposit: '账户余额',
            marketingAmount: 0,
            creditAmount: 0
          }
        ]
      }

      return true
    } catch (error) {
      console.error('❌ [充值管理] API调用失败:', error)

      // 发生错误时使用默认数据，确保页面能正常显示
      formInline.cooperationMode = '预付费'
      formInline.channelType = '直销'
      formInline.currencyCode = 'CNY'

      tableData.value = [
        {
          deposit: '账户余额',
          marketingAmount: 0,
          creditAmount: 0
        }
      ]

      // 显示错误信息但不阻止页面显示
      ElMessage.warning('获取账户数据失败，显示默认数据')
      return true
    }
  }, '获取账户数据失败')
}

// 在组件挂载后自动加载数据
initComponentSafety(async () => {
  await getAccountData()
})
</script>

<style scoped>
.loading-container {
  padding: 20px;
}

.cell-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 确保表格在加载时不会闪烁 */
:deep(.el-table) {
  min-height: 200px;
}
</style>
