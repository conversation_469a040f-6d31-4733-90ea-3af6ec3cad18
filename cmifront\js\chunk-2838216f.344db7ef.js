(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2838216f"],{"00b4":function(e,t,a){"use strict";a("ac1f");var r=a("23e7"),i=a("c65b"),o=a("1626"),s=a("825a"),l=a("577e"),n=function(){var e=!1,t=/[ac]/;return t.exec=function(){return e=!0,/./.exec.apply(this,arguments)},!0===t.test("abc")&&e}(),c=/./.test;r({target:"RegExp",proto:!0,forced:!n},{test:function(e){var t=s(this),a=l(e),r=t.exec;if(!o(r))return i(c,t,a);var n=i(r,t,a);return null!==n&&(s(n),!0)}})},"129f":function(e,t,a){"use strict";e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!==e&&t!==t}},"2d70":function(e,t,a){"use strict";a.d(t,"j",(function(){return o})),a.d(t,"k",(function(){return s})),a.d(t,"d",(function(){return l})),a.d(t,"p",(function(){return n})),a.d(t,"q",(function(){return c})),a.d(t,"m",(function(){return d})),a.d(t,"c",(function(){return u})),a.d(t,"s",(function(){return p})),a.d(t,"t",(function(){return m})),a.d(t,"g",(function(){return h})),a.d(t,"e",(function(){return f})),a.d(t,"n",(function(){return g})),a.d(t,"a",(function(){return b})),a.d(t,"h",(function(){return v})),a.d(t,"o",(function(){return y})),a.d(t,"l",(function(){return x})),a.d(t,"b",(function(){return w})),a.d(t,"r",(function(){return I})),a.d(t,"f",(function(){return k})),a.d(t,"i",(function(){return C}));var r=a("66df"),i="/cms/api/v1/personalOrder",o=function(e){return r["a"].request({url:i+"/pages",params:e,method:"get"})},s=function(e){return r["a"].request({url:i+"/getPackagePurchaseRecord",params:e,method:"get"})},l=function(e){return r["a"].request({url:i+"/deliver/batch",data:e,method:"post",contentType:"multipart/form-data"})},n=function(e){return r["a"].request({url:i+"/unbind/".concat(e),method:"POST"})},c=function(e){return r["a"].request({url:i+"/big/unbind/".concat(e),method:"POST"})},d=function(e){return r["a"].request({url:i+"/recover/".concat(e),method:"POST"})},u=function(e,t){return r["a"].request({url:i+"/deliver/".concat(t),data:e,method:"post"})},p=function(e){return r["a"].request({url:i+"/unsubscribe/".concat(e),method:"POST"})},m=function(e){return r["a"].request({url:i+"/unsubscribeForBigOrder",data:e,method:"POST"})},h=function(e){return r["a"].request({url:i+"/audit",data:e,method:"post"})},f=function(e){return r["a"].request({url:i+"/pages/export",params:e,method:"GET",responseType:"blob"})},g=function(e){return r["a"].request({url:"cms/order/compensation",data:e,method:"POST"})},b=function(e){return r["a"].request({url:i+"/bigOrder/deliver",data:e,method:"POST",contentType:"multipart/form-data"})},v=function(e){return r["a"].request({url:i+"/orderDetailsByOrderId",params:e,method:"get"})},y=function(e){return r["a"].request({url:i+"/unbind/child/".concat(e),method:"POST"})},x=function(e){return r["a"].request({url:i+"/recover/child/".concat(e),method:"POST"})},w=function(e,t){return r["a"].request({url:i+"/deliver/child/".concat(t),data:e,method:"post"})},I=function(e){return r["a"].request({url:i+"/detail/unsubscribe/".concat(e),method:"POST"})},k=function(e){return r["a"].request({url:i+"/detail/audit",data:e,method:"post"})},C=function(e){return r["a"].request({url:i+"/detail/orderDetail",params:e,method:"get"})}},"45c1":function(e,t,a){"use strict";a("bb47")},"4f3c":function(e,t,a){"use strict";a("b84c")},"6d60":function(e,t,a){"use strict";a.r(t);a("caad"),a("b0c0");var r=function(){var e=this,t=e._self._c;return t("div",[t("Card",[t("div",{staticClass:"search_head_i"},[t("div",{staticClass:"search_box"},[t("span",{staticClass:"search_box_label"},[e._v("订单编号")]),t("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入订单编号",clearable:""},model:{value:e.searchCondition.orderUniqueId,callback:function(t){e.$set(e.searchCondition,"orderUniqueId",t)},expression:"searchCondition.orderUniqueId"}})],1),t("div",{staticClass:"search_box"},[t("span",{staticClass:"search_box_label"},[e._v("订单状态")]),t("Select",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请选择订单状态"},model:{value:e.searchCondition.orderStatus,callback:function(t){e.$set(e.searchCondition,"orderStatus",t)},expression:"searchCondition.orderStatus"}},e._l(e.statusList,(function(a,r){return t("Option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.label))])})),1)],1),t("div",{staticClass:"search_box"},[t("span",{staticClass:"search_box_label"},[e._v("第三方订单号")]),t("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入第三方订单号",clearable:""},model:{value:e.searchCondition.thirdOrderId,callback:function(t){e.$set(e.searchCondition,"thirdOrderId",t)},expression:"searchCondition.thirdOrderId"}})],1),t("div",{staticClass:"search_box"},[t("span",{staticClass:"search_box_label"},[e._v("商品名称")]),t("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入商品名称",clearable:""},model:{value:e.searchCondition.orderName,callback:function(t){e.$set(e.searchCondition,"orderName",t)},expression:"searchCondition.orderName"}})],1),t("div",{staticClass:"search_box"},[t("span",{staticClass:"search_box_label"},[e._v("订单生成时间")]),t("DatePicker",{staticStyle:{width:"200px"},attrs:{editable:!1,type:"daterange",placeholder:"请选择订单生成时间",clearable:""},on:{"on-change":e.handleDateChange,"on-clear":e.hanldeDateClear},model:{value:e.timeRangeArray,callback:function(t){e.timeRangeArray=t},expression:"timeRangeArray"}})],1),t("div",{staticClass:"search_box"},[t("span",{staticClass:"search_box_label"},[e._v("用户名称")]),t("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入用户名称",clearable:""},model:{value:e.searchCondition.orderUserName,callback:function(t){e.$set(e.searchCondition,"orderUserName",t)},expression:"searchCondition.orderUserName"}})],1),t("div",{staticClass:"search_box"},[t("span",{staticClass:"search_box_label"},[e._v("是否大额订单")]),t("Select",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请选择是否大额订单"},model:{value:e.searchCondition.isBigOrder,callback:function(t){e.$set(e.searchCondition,"isBigOrder",t)},expression:"searchCondition.isBigOrder"}},[t("Option",{attrs:{value:"1"}},[e._v("是")]),t("Option",{attrs:{value:"2"}},[e._v("否")])],1)],1),t("div",{staticClass:"search_box"},[t("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{margin:"0 4px"},attrs:{type:"primary",loading:e.loading},on:{click:e.searchByCondition}},[t("Icon",{attrs:{type:"ios-search"}}),e._v(" 搜索\n          ")],1),t("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],staticStyle:{margin:"0 4px"},attrs:{type:"success",icon:"md-cloud-download",loading:e.exportLoading},on:{click:e.exportData}},[e._v("导出")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"shipBatch",expression:"'shipBatch'"}],staticStyle:{margin:"0 4px"},attrs:{type:"info"},on:{click:e.showBatchModal}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"md-add"}}),e._v(" 批量发货")],1)]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"apply",expression:"'apply'"}],staticStyle:{margin:"0 4px"},attrs:{type:"warning",disabled:"1"==e.isSuperManger},on:{click:e.viewPermission}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"md-unlock"}}),e._v(" 申请查看权限")],1)])],1)]),t("div",{staticStyle:{"margin-top":"20px"}},[t("Table",{attrs:{columns:e.columns,data:e.tableData,ellipsis:!0,loading:e.loading},scopedSlots:e._u([{key:"orderInfo",fn:function(a){var r=a.row;a.index;return[t("a",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],staticStyle:{color:"#55aaff"},on:{click:function(t){return e.showOrderInfo(r.orderId)}}},[e._v("查看详情")])]}},{key:"h5OrderInfo",fn:function(a){var r=a.row;a.index;return["1"!=r.orderUserType?t("a",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],attrs:{disabled:""}},[e._v("H5订单信息")]):t("a",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],on:{click:function(t){return e.showH5OrderInfo(r.orderId)}}},[e._v("H5订单信息")])]}},{key:"unsubscribeCheck",fn:function(a){var r=a.row;a.index;return["4"==r.orderStatus?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"5px"},attrs:{ghost:"",type:"success",size:"small"},on:{click:function(t){return e.examine(r.orderId,"2")}}},[e._v("通过")]):t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"5px"},attrs:{ghost:"",type:"success",size:"small",disabled:""}},[e._v("通过")]),"4"==r.orderStatus?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"5px"},attrs:{ghost:"",type:"error",size:"small"},on:{click:function(t){return e.examine(r.orderId,"3")}}},[e._v("不通过")]):t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"5px"},attrs:{ghost:"",type:"error",size:"small",disabled:""}},[e._v("不通过")])]}},{key:"action",fn:function(a){var r=a.row;a.index;return["2"==r.orderStatus&&"3"==r.orderType&&"2"!=r.cardForm&&!r.bigOrder||["2","6"].includes(r.orderStatus)&&"3"==r.orderType&&r.bigOrder?t("Button",{directives:[{name:"has",rawName:"v-has",value:"untie",expression:"'untie'"}],staticStyle:{"margin-right":"5px"},attrs:{ghost:"",size:"small",type:"warning"},on:{click:function(t){return e.unbundling(r)}}},[e._v("解绑")]):t("Button",{directives:[{name:"has",rawName:"v-has",value:"untie",expression:"'untie'"}],staticStyle:{"margin-right":"5px"},attrs:{ghost:"",size:"small",type:"warning",disabled:""}},[e._v("解绑")]),"1"==r.orderStatus&&"3"==r.orderType&&!r.bigOrder||["1","6","9"].includes(r.orderStatus)&&"3"==r.orderType&&r.bigOrder?t("Button",{directives:[{name:"has",rawName:"v-has",value:"ship",expression:"'ship'"}],staticStyle:{"margin-right":"5px"},attrs:{ghost:"",size:"small",type:"info"},on:{click:function(t){return e.deliveryAllMoadl(r)}}},[e._v("发货")]):t("Button",{directives:[{name:"has",rawName:"v-has",value:"ship",expression:"'ship'"}],staticStyle:{"margin-right":"5px"},attrs:{ghost:"",size:"small",type:"info",disabled:""}},[e._v("发货")]),"2"==r.orderStatus&&"7"==r.orderType&&!r.bigOrder||"2"==r.orderStatus&&"2"==r.orderType&&!r.bigOrder||("1"==r.orderStatus||"2"==r.orderStatus)&&"3"==r.orderType&&!r.bigOrder||["1","2","5","9"].includes(r.orderStatus)&&"3"==r.orderType&&r.bigOrder?t("Button",{directives:[{name:"has",rawName:"v-has",value:"unsubscribe",expression:"'unsubscribe'"}],staticStyle:{"margin-right":"5px"},attrs:{ghost:"",size:"small",type:"error"},on:{click:function(t){return e.unsubscribeAll(r)}}},[e._v("退订")]):t("Button",{directives:[{name:"has",rawName:"v-has",value:"unsubscribe",expression:"'unsubscribe'"}],staticStyle:{"margin-right":"5px"},attrs:{ghost:"",size:"small",type:"error",disabled:""}},[e._v("退订")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"reorder",expression:"'reorder'"}],staticStyle:{"margin-right":"5px"},attrs:{size:"small",ghost:"",type:"primary",disabled:!("11"==r.orderStatus&&"3"==r.orderType&&r.bigOrder)},on:{click:function(t){return e.reorderAgain(r.orderUniqueId)}}},[e._v("再次订购")])]}}])})],1),t("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[t("Page",{staticStyle:{margin:"10px 0"},attrs:{total:e.total,current:e.currentPage,"page-size":e.pageSize,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1)]),t("Modal",{attrs:{title:"请上传批量发货文件","mask-closable":!1},on:{"on-cancel":e.cancelUpload},model:{value:e.batchModal,callback:function(t){e.batchModal=t},expression:"batchModal"}},[t("div",[t("Upload",{attrs:{type:"drag",action:"","on-success":e.fileSuccess,"before-upload":e.handleBeforeUpload,"on-progress":e.fileUploading}},[t("div",{staticStyle:{padding:"20px 0"}},[t("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),t("p",[e._v("点击或拖拽文件上传")])],1)]),e.file?t("ul",{staticClass:"ivu-upload-list"},[t("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[t("span",[t("i",{staticClass:"ivu-icon ivu-icon-ios-stats"}),e._v(e._s(e.file.name))]),t("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:e.removeFile}})])]):e._e()],1),t("div",{staticStyle:{width:"100%",padding:"10px 0","text-align":"center"}},[t("Alert",{attrs:{type:"warning"}},[e._v(e._s(e.message))])],1),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{staticStyle:{"margin-left":"8px"},on:{click:e.cancelUpload}},[e._v("取消")]),t("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:e.uploading},on:{click:e.deliveryBatch}},[e._v("确定")])],1)]),t("Modal",{attrs:{title:"请填写发货信息","mask-closable":!1,width:"530px"},on:{"on-cancel":e.deliverCancel},model:{value:e.deliverFlag,callback:function(t){e.deliverFlag=t},expression:"deliverFlag"}},[t("div",{staticClass:"modal_content"},[t("Form",{ref:"formValidate",staticStyle:{"font-weight":"bold"},attrs:{model:e.formValidate,rules:e.ruleValidate,"label-width":120,"label-height":100,inline:""}},[t("FormItem",{staticStyle:{width:"420px"},attrs:{label:"货运单号",prop:"logistic"}},[t("Input",{attrs:{placeholder:"请输入货运单号",maxlength:100,clearable:""},model:{value:e.formValidate.logistic,callback:function(t){e.$set(e.formValidate,"logistic",t)},expression:"formValidate.logistic"}})],1),e.bigOrder?e._e():t("FormItem",{staticStyle:{width:"420px"},attrs:{label:"ICCID",prop:"iccidList"}},[t("Input",{attrs:{wrap:"hard",autosize:{minRows:1,maxRows:3},rows:2,maxlength:4e3,clearable:"",type:"textarea",placeholder:"请输入ICCID,多个以'|'分隔"},model:{value:e.formValidate.iccidList,callback:function(t){e.$set(e.formValidate,"iccidList",t)},expression:"formValidate.iccidList"}})],1),e.bigOrder?t("FormItem",{staticStyle:{"font-size":"14px","font-weight":"bold"},attrs:{label:"上传ICCID文件",prop:"file"}},[t("div",[t("Upload",{staticStyle:{width:"290px"},attrs:{type:"drag",action:"","on-success":e.fileSuccess,"before-upload":e.handleBigOrderBeforeUpload,"on-progress":e.fileUploading}},[t("div",{staticStyle:{padding:"20px 0"}},[t("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),t("p",[e._v("点击或拖拽文件上传")])],1)]),e.file?t("ul",{staticClass:"ivu-upload-list"},[t("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[t("span",[t("i",{staticClass:"ivu-icon ivu-icon-ios-stats"}),e._v(e._s(e.file.name))]),t("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:e.removeFile}})])]):e._e()],1),t("div",{staticStyle:{width:"300px",display:"flex","flex-wrap":"wrap"}},[t("Alert",{staticStyle:{float:"right",padding:"8px 10px 8px 10px"},attrs:{type:"warning"}},[e._v(e._s(e.deliverMessage))]),t("Button",{attrs:{type:"primary",icon:"ios-download"},on:{click:e.downloadTempFile}},[e._v("下载模板文件")])],1)]):e._e(),t("FormItem",{staticStyle:{width:"420px"},attrs:{label:"物流公司",prop:"logisticCompany"}},[t("Input",{attrs:{placeholder:"请输入物流公司",maxlength:100,clearable:""},model:{value:e.formValidate.logisticCompany,callback:function(t){e.$set(e.formValidate,"logisticCompany",t)},expression:"formValidate.logisticCompany"}})],1),t("FormItem",{staticStyle:{width:"420px"},attrs:{label:"地址",prop:"address"}},[t("Input",{attrs:{wrap:"hard",autosize:{minRows:1,maxRows:3},rows:1,maxlength:255,clearable:"",type:"textarea",placeholder:"请输入国家/地区"},model:{value:e.formValidate.address.country,callback:function(t){e.$set(e.formValidate.address,"country",t)},expression:"formValidate.address.country"}}),t("Input",{attrs:{wrap:"hard",autosize:{minRows:1,maxRows:3},rows:1,maxlength:255,clearable:"",type:"textarea",placeholder:"请输入省份"},model:{value:e.formValidate.address.province,callback:function(t){e.$set(e.formValidate.address,"province",t)},expression:"formValidate.address.province"}}),t("Input",{attrs:{wrap:"hard",autosize:{minRows:1,maxRows:3},rows:1,maxlength:255,clearable:"",type:"textarea",placeholder:"请输入城市地址"},model:{value:e.formValidate.address.city,callback:function(t){e.$set(e.formValidate.address,"city",t)},expression:"formValidate.address.city"}}),t("Input",{attrs:{wrap:"hard",autosize:{minRows:1,maxRows:3},rows:1,maxlength:255,clearable:"",type:"textarea",placeholder:"请输入邮寄地址"},model:{value:e.formValidate.address.mailing,callback:function(t){e.$set(e.formValidate.address,"mailing",t)},expression:"formValidate.address.mailing"}})],1),t("FormItem",{staticStyle:{width:"420px"},attrs:{label:"邮政编码",prop:"postCode"}},[t("Input",{attrs:{placeholder:"请输入邮政编码",maxlength:20,clearable:""},model:{value:e.formValidate.postCode,callback:function(t){e.$set(e.formValidate,"postCode",t)},expression:"formValidate.postCode"}})],1)],1)],1),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:e.deliverCancel}},[e._v("取消")]),t("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:e.submitFlag},on:{click:e.deliverySubmit}},[e._v("确定")])],1)]),t("Modal",{staticStyle:{"z-index":"100"},attrs:{title:"订单详情",fullscreen:!1,"mask-closable":!1,width:"80%","footer-hide":!0},on:{"on-cancel":e.orderInfoModalCancel},model:{value:e.orderInfoModal,callback:function(t){e.orderInfoModal=t},expression:"orderInfoModal"}},[e.orderInfoModal?t("orderInfo",{attrs:{id:e.orderId}}):e._e()],1),t("Modal",{attrs:{title:"H5订单详情",fullscreen:!1,"mask-closable":!1,width:"60%","footer-hide":!0},on:{"on-cancel":e.h5orderInfoCancel},model:{value:e.h5orderInfoModal,callback:function(t){e.h5orderInfoModal=t},expression:"h5orderInfoModal"}},[e.h5orderInfoModal?t("h5orderInfo",{attrs:{ordersObj:e.h5Obj}}):e._e()],1),t("Modal",{staticStyle:{"z-index":"100"},attrs:{title:"大额订单详情",fullscreen:!1,"mask-closable":!1,width:"40%","footer-hide":!0},on:{"on-cancel":e.largeOrderModalCancel},model:{value:e.largeOrderModal,callback:function(t){e.largeOrderModal=t},expression:"largeOrderModal"}},[e.largeOrderModal?t("largeOrderInfo",{attrs:{id:e.orderId}}):e._e()],1),t("Modal",{attrs:{title:"金锁模式申请",fullscreen:!1,"mask-closable":!1,width:"500px","footer-hide":!0},on:{"on-cancel":e.lockApplicationCancel},model:{value:e.lockApplicationModal,callback:function(t){e.lockApplicationModal=t},expression:"lockApplicationModal"}},[e.lockApplicationModal?t("lockApplication",{attrs:{page:1,type:1},on:{lockApplicationCancel:e.lockApplicationCancel,goPageFirst:function(t){return e.goPageFirst(1)}}}):e._e()],1),t("Table",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"modelTable",attrs:{columns:e.modelColumns,data:e.modelData}})],1)},i=[],o=a("3835"),s=(a("d9e2"),a("99af"),a("4de4"),a("14d9"),a("d3b7"),a("ac1f"),a("00b4"),a("3ca3"),a("5319"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("841c"),a("498a"),function(){var e=this,t=e._self._c;return t("div",[t("div",{staticStyle:{padding:"5px 0"}},[t("div",{staticClass:"search_head_i"},[t("div",{staticClass:"search_box"},[t("span",{staticClass:"search_box_label"},[e._v("ICCID")]),t("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入ICCID",clearable:""},model:{value:e.iccid,callback:function(t){e.iccid="string"===typeof t?t.trim():t},expression:"iccid"}})],1),t("div",{staticClass:"search_box"},[t("span",{staticClass:"search_box_label"},[e._v("订单状态")]),t("Select",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请选择订单状态"},model:{value:e.status,callback:function(t){e.status=t},expression:"status"}},e._l(e.statusList,(function(a,r){return t("Option",{key:r,attrs:{value:a.value}},[e._v(e._s(a.label))])})),1)],1),t("div",{staticClass:"search_box"},[t("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{margin:"0 4px"},attrs:{type:"primary",loading:e.loading},on:{click:e.search}},[t("Icon",{attrs:{type:"ios-search"}}),e._v(" 搜索\n          ")],1)],1)]),t("Table",{ref:"selection",attrs:{columns:e.columnsModal,data:e.tableDataModal,ellipsis:!0,loading:e.loadingModal,"max-height":"500"},scopedSlots:e._u([{key:"action",fn:function(a){var r=a.row;a.index;return["2"==r.orderStatus&&"3"==r.orderType&&"2"!=r.cardForm?t("Button",{directives:[{name:"has",rawName:"v-has",value:"untie",expression:"'untie'"}],staticStyle:{"margin-right":"5px"},attrs:{size:"small",type:"warning"},on:{click:function(t){return e.unbundlingModal(r.id)}}},[e._v("解绑")]):t("Button",{directives:[{name:"has",rawName:"v-has",value:"untie",expression:"'untie'"}],staticStyle:{"margin-right":"5px"},attrs:{size:"small",type:"warning",disabled:""}},[e._v("解绑")]),"1"==r.orderStatus&&"3"==r.orderType?t("Button",{directives:[{name:"has",rawName:"v-has",value:"ship",expression:"'ship'"}],staticStyle:{"margin-right":"5px"},attrs:{size:"small",type:"info"},on:{click:function(t){return e.deliveryModal(r)}}},[e._v("发货")]):t("Button",{directives:[{name:"has",rawName:"v-has",value:"ship",expression:"'ship'"}],staticStyle:{"margin-right":"5px"},attrs:{size:"small",type:"info",disabled:""}},[e._v("发货")]),"2"==r.orderStatus&&"2"==r.orderType||("1"==r.orderStatus||"2"==r.orderStatus)&&"3"==r.orderType&&"7"!=r.orderType?t("Button",{directives:[{name:"has",rawName:"v-has",value:"unsubscribe",expression:"'unsubscribe'"}],staticStyle:{"margin-right":"5px"},attrs:{size:"small",type:"error"},on:{click:function(t){return e.unsubscribeModal(r.id)}}},[e._v("退订")]):t("Button",{directives:[{name:"has",rawName:"v-has",value:"unsubscribe",expression:"'unsubscribe'"}],staticStyle:{"margin-right":"5px"},attrs:{size:"small",type:"error",disabled:""}},[e._v("退订")]),"4"==r.orderStatus&&"7"!=r.orderType?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.examineModal(r.id,"2")}}},[e._v("通过")]):t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small",disabled:""}},[e._v("通过")]),"4"==r.orderStatus&&"7"!=r.orderType?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.examineModal(r.id,"3")}}},[e._v("不通过")]):t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small",disabled:""}},[e._v("不通过")])]}}])})],1),t("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[t("Page",{staticStyle:{margin:"10px 0"},attrs:{total:e.totalModal,current:e.currentPageModal,"page-size":e.pageSizeModal,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPageModal=t},"on-change":e.goPageModal}})],1),t("Modal",{attrs:{title:"请填写发货信息","mask-closable":!1,width:"530px"},on:{"on-cancel":e.deliverCancelModal},model:{value:e.deliverModal,callback:function(t){e.deliverModal=t},expression:"deliverModal"}},[t("div",{staticClass:"modal_content"},[t("Form",{ref:"formValidateModal",staticStyle:{"font-weight":"bold"},attrs:{model:e.formValidateModal,rules:e.ruleValidateModal,"label-width":130,"label-height":100,inline:""}},[t("FormItem",{staticStyle:{width:"420px"},attrs:{label:"货运单号",prop:"logistic"}},[t("Input",{attrs:{placeholder:"请输入货运单号",maxlength:100,clearable:""},model:{value:e.formValidateModal.logistic,callback:function(t){e.$set(e.formValidateModal,"logistic",t)},expression:"formValidateModal.logistic"}})],1),t("FormItem",{staticStyle:{width:"420px"},attrs:{label:"ICCID",prop:"iccidList"}},[t("Input",{attrs:{wrap:"hard",autosize:{minRows:1,maxRows:3},rows:2,maxlength:4e3,clearable:"",type:"textarea",placeholder:"请输入ICCID"},model:{value:e.formValidateModal.iccidList,callback:function(t){e.$set(e.formValidateModal,"iccidList",t)},expression:"formValidateModal.iccidList"}})],1),t("FormItem",{staticStyle:{width:"420px"},attrs:{label:"物流公司",prop:"logisticCompany"}},[t("Input",{attrs:{placeholder:"请输入物流公司",maxlength:100,clearable:""},model:{value:e.formValidateModal.logisticCompany,callback:function(t){e.$set(e.formValidateModal,"logisticCompany",t)},expression:"formValidateModal.logisticCompany"}})],1),t("FormItem",{staticStyle:{width:"420px"},attrs:{label:"地址",prop:"address"}},[t("Input",{attrs:{wrap:"hard",autosize:{minRows:1,maxRows:3},rows:1,maxlength:255,clearable:"",type:"textarea",placeholder:"请输入国家/地区"},model:{value:e.formValidateModal.address.country,callback:function(t){e.$set(e.formValidateModal.address,"country",t)},expression:"formValidateModal.address.country"}}),t("Input",{attrs:{wrap:"hard",autosize:{minRows:1,maxRows:3},rows:1,maxlength:255,clearable:"",type:"textarea",placeholder:"请输入省份"},model:{value:e.formValidateModal.address.province,callback:function(t){e.$set(e.formValidateModal.address,"province",t)},expression:"formValidateModal.address.province"}}),t("Input",{attrs:{wrap:"hard",autosize:{minRows:1,maxRows:3},rows:1,maxlength:255,clearable:"",type:"textarea",placeholder:"请输入城市地址"},model:{value:e.formValidateModal.address.city,callback:function(t){e.$set(e.formValidateModal.address,"city",t)},expression:"formValidateModal.address.city"}}),t("Input",{attrs:{wrap:"hard",autosize:{minRows:1,maxRows:3},rows:1,maxlength:255,clearable:"",type:"textarea",placeholder:"请输入邮寄地址"},model:{value:e.formValidateModal.address.mailing,callback:function(t){e.$set(e.formValidateModal.address,"mailing",t)},expression:"formValidateModal.address.mailing"}})],1),t("FormItem",{staticStyle:{width:"420px"},attrs:{label:"邮政编码",prop:"postCode"}},[t("Input",{attrs:{placeholder:"请输入邮政编码",maxlength:20,clearable:""},model:{value:e.formValidateModal.postCode,callback:function(t){e.$set(e.formValidateModal,"postCode",t)},expression:"formValidateModal.postCode"}})],1)],1)],1),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:e.deliverCancelModal}},[e._v("取消")]),t("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:e.submitFlagModal},on:{click:e.deliverySubmitModal}},[e._v("确定")])],1)])],1)}),l=[],n=a("2d70"),c=(a("c70b"),{props:{id:String},data:function(){return{iccid:"",status:"",columnsModal:[],tableDataModal:[],loadingModal:!1,totalModal:0,currentPageModal:1,pageSizeModal:10,deliverModal:!1,formValidateModal:{orderId:"",logistic:"",iccidList:"",logisticCompany:"",address:{address:"",country:"",province:"",city:"",mailing:""},postCode:""},ruleValidateModal:{logistic:[{required:!0,type:"string",message:"请输入货运单号"}],iccidList:[{required:!0,type:"string",message:"请输入ICCID"}],logisticCompany:[{required:!0,type:"string",message:"请输入物流公司"}],address:[{validator:function(e,t,a){return null!=t.country&&""!=t.country},message:"请输入国家/地区"},{validator:function(e,t,a){return null!=t.mailing&&""!=t.mailing},message:"请输入邮寄地址"}]},submitFlagModal:!1,statusList:[{value:"1",label:"待发货"},{value:"2",label:"已完成"},{value:"3",label:"已退订/已回滚"},{value:"4",label:"激活退订待审批"}]}},methods:{init:function(){this.columnsModal=[{title:"订单编号",key:"id",minWidth:170,tooltip:!0,align:"center"},{title:"ICCID",key:"iccid",tooltip:!0,align:"center",minWidth:150},{title:"物流编号",key:"logistic",tooltip:!0,align:"center",minWidth:150},{title:"订购类型",key:"orderType",align:"center",minWidth:150,render:function(e,t){var a=t.row,r="",i="";switch(a.orderType){case"1":r="卡",i="#3943ff";break;case"2":r="套餐",i="#00aa00";break;case"3":r="卡+套餐",i="#1d9dff";break;case"4":r="终端线下卡池套餐",i="#4f4f4f";break;case"5":r="流量池套餐",i="#ff3056";break;case"6":r="终端厂商套餐",i="#9822ff";break;case"7":r="加油包",i="#ff10ac";break;default:r="未知类型"}return e("label",{style:{color:i}},r)}},{title:"订单状态",key:"orderStatus",align:"center",minWidth:150,render:function(e,t){var a=t.row,r="",i="";switch(a.orderStatus){case"1":r="待发货",i="#aaaa10";break;case"2":r="已完成",i="#00aa00";break;case"3":r="已退订/已回滚",i="#130bff";break;case"4":r="激活退订待审批",i="#ff10ac";break;default:r="未知状态"}return e("label",{style:{color:i}},r)}},{title:"币种",key:"currencyCode",align:"center",minWidth:100,render:function(e,t){var a=t.row,r="";switch(a.currencyCode){case"156":r="人民币";break;case"840":r="美元";break;case"344":r="港币";break;default:r="未知"}return e("label",r)}},{title:"金额",key:"amount",align:"center",minWidth:100}],this.columnsModal.push({title:"操作",width:370,fixed:"right",slot:"action",align:"center"}),this.goPageFirstModal(1)},search:function(){this.goPageFirstModal(1)},goPageFirstModal:function(e){var t=this;this.currentPageModal=e,this.loadingModal=!0;var a={orderId:this.id,pageNumber:e,pageSize:this.pageSizeModal,iccid:this.iccid,status:this.status,onlyIccid:!1};Object(n["h"])(a).then((function(e){if(!e||"0000"!=e.code)throw e;var a=e.data;t.totalModal=a.totalCount,t.tableDataModal=a.records})).catch((function(e){})).finally((function(){t.loadingModal=!1}))},goPageModal:function(e){this.goPageFirstModal(e)},unbundlingModal:function(e){var t=this;this.$Modal.confirm({title:"确认解绑？",onOk:function(){Object(n["o"])(e).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.goPageFirstModal(t.currentPageModal)})).catch((function(e){}))}})},recyclingModal:function(e){var t=this;this.$Modal.confirm({title:"确认回收？",onOk:function(){Object(n["l"])(e).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.goPageFirstModal(t.currentPageModal)})).catch((function(e){}))}})},deliveryModal:function(e){this.$refs["formValidateModal"].resetFields();var t={address:"",country:"",province:"",city:"",mailing:""};if(null!=e.address){var a=e.address.split("|");t={address:"",country:a[0],province:a[1],city:a[2],mailing:a[3]}}this.formValidateModal={orderId:e.id,logistic:e.logistic,iccidList:e.iccid,logisticCompany:e.logisticCompany,address:t,postCode:e.postCode},this.deliverModal=!0},unsubscribeModal:function(e){var t=this;this.$Modal.confirm({title:"确认退订？",onOk:function(){Object(n["r"])(e).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.goPageFirstModal(t.currentPageModal)})).catch((function(e){}))}})},deliverCancelModal:function(){this.$refs["formValidateModal"].resetFields(),this.deliverModal=!1},deliverySubmitModal:function(){var e=this;this.$refs.formValidateModal.validate((function(t){if(t){e.submitFlagModal=!0;var a=Object.assign({},e.formValidateModal),r=a.address.country.concat("|",a.address.province,"|",a.address.city,"|",a.address.mailing),i={address:r,iccidList:a.iccidList,logistic:a.logistic,logisticCompany:a.logisticCompany,orderId:a.orderId,postCode:a.postCode};Object(n["b"])(i,i.orderId).then((function(t){if("0000"!==t.code)throw t;t.data;e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.deliverModal=!1,e.goPageFirstModal(1)})).catch((function(e){console.log(e)})).finally((function(){e.submitFlagModal=!1,e.deliverModal=!1}))}}))},examineModal:function(e,t){var a=this;this.$Modal.confirm({title:"2"==t?"确认执行通过操作？":"确认执行不通过操作？",onOk:function(){Object(n["f"])({id:e,status:t}).then((function(e){if(!e||"0000"!=e.code)throw e;a.$Notice.success({title:"操作提示",desc:"操作成功"}),a.goPageFirstModal(a.currentPageModal)})).catch((function(e){}))}})}},mounted:function(){this.init()},watch:{}}),d=c,u=(a("b0fb"),a("2877")),p=Object(u["a"])(d,s,l,!1,null,"b762776c",null),m=p.exports,h=function(){var e=this,t=e._self._c;return t("div",{staticStyle:{height:"650px","overflow-y":"auto"}},[t("Form",{ref:"searchForm",attrs:{"label-width":120}},[t("Row",[t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"订单ID"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.uuid,callback:function(t){e.$set(e.orders,"uuid",t)},expression:"orders.uuid"}})],1),t("FormItem",{attrs:{label:"订单编号"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.orderNumber,callback:function(t){e.$set(e.orders,"orderNumber",t)},expression:"orders.orderNumber"}})],1),t("FormItem",{attrs:{label:"交易币种"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.orderCurrency,callback:function(t){e.$set(e.orders,"orderCurrency",t)},expression:"orders.orderCurrency"}})],1),t("FormItem",{attrs:{label:"订单状态"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.orderType,callback:function(t){e.$set(e.orders,"orderType",t)},expression:"orders.orderType"}})],1),t("FormItem",{attrs:{label:"支付时间"}},[t("DatePicker",{staticStyle:{width:"200px"},attrs:{type:"datetime",readonly:""},model:{value:e.orders.payTime,callback:function(t){e.$set(e.orders,"payTime",t)},expression:"orders.payTime"}})],1),t("FormItem",{attrs:{label:"订单生成时间"}},[t("DatePicker",{staticStyle:{width:"200px"},attrs:{type:"datetime",readonly:""},model:{value:e.orders.orderTime,callback:function(t){e.$set(e.orders,"orderTime",t)},expression:"orders.orderTime"}})],1),t("FormItem",{attrs:{label:"是否包含硬卡"}},[t("Select",{staticStyle:{width:"200px"},attrs:{disabled:""},model:{value:e.orders.cardType,callback:function(t){e.$set(e.orders,"cardType",t)},expression:"orders.cardType"}},[t("Option",{attrs:{value:"0"}},[e._v("不包含")]),t("Option",{attrs:{value:"1"}},[e._v("包含")]),t("Option",{attrs:{value:"2"}},[e._v("esim")])],1)],1),t("FormItem",{attrs:{label:"支付方式"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.methods,callback:function(t){e.$set(e.orders,"methods",t)},expression:"orders.methods"}})],1),t("FormItem",{attrs:{label:"银行卡号"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.cardNo,callback:function(t){e.$set(e.orders,"cardNo",t)},expression:"orders.cardNo"}})],1),t("FormItem",{attrs:{label:"购买渠道"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.buyType,callback:function(t){e.$set(e.orders,"buyType",t)},expression:"orders.buyType"}})],1),t("FormItem",{attrs:{label:"套餐ID"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.productId,callback:function(t){e.$set(e.orders,"productId",t)},expression:"orders.productId"}})],1),t("FormItem",{attrs:{label:"套餐名字"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.productName,callback:function(t){e.$set(e.orders,"productName",t)},expression:"orders.productName"}})],1),t("FormItem",{attrs:{label:"套餐价格"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.productAmount,callback:function(t){e.$set(e.orders,"productAmount",t)},expression:"orders.productAmount"}})],1),t("FormItem",{attrs:{label:"套餐图片"}},[null!=e.orders.productPic&&""!=e.orders.productPic?t("Button",{staticClass:"inputSty",staticStyle:{width:"200px"},attrs:{type:"dashed",long:""},on:{click:function(t){e.pictureShowFlag=!0}}},[e._v("查看图片")]):t("Button",{staticClass:"inputSty",staticStyle:{width:"200px"},attrs:{type:"dashed",long:"",disabled:""}},[e._v("图片不存在")])],1),t("FormItem",{attrs:{label:"套餐介绍"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.productDetial,callback:function(t){e.$set(e.orders,"productDetial",t)},expression:"orders.productDetial"}})],1),t("FormItem",{attrs:{label:"eSim"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.iccd,callback:function(t){e.$set(e.orders,"iccd",t)},expression:"orders.iccd"}})],1),t("FormItem",{attrs:{label:"物流编号"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.logisticsNo,callback:function(t){e.$set(e.orders,"logisticsNo",t)},expression:"orders.logisticsNo"}})],1)],1),t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"物流公司"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.logisticsCom,callback:function(t){e.$set(e.orders,"logisticsCom",t)},expression:"orders.logisticsCom"}})],1),t("FormItem",{attrs:{label:"套餐数量"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.productNum,callback:function(t){e.$set(e.orders,"productNum",t)},expression:"orders.productNum"}})],1),t("FormItem",{attrs:{label:"收货人ID"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.personId,callback:function(t){e.$set(e.orders,"personId",t)},expression:"orders.personId"}})],1),t("FormItem",{attrs:{label:"支付订单ID"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.payId,callback:function(t){e.$set(e.orders,"payId",t)},expression:"orders.payId"}})],1),t("FormItem",{attrs:{label:"订单状态描述"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.payResult,callback:function(t){e.$set(e.orders,"payResult",t)},expression:"orders.payResult"}})],1),t("FormItem",{attrs:{label:"登录帐号"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.loginUser,callback:function(t){e.$set(e.orders,"loginUser",t)},expression:"orders.loginUser"}})],1),t("FormItem",{attrs:{label:"登录信息"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.isTemporary,callback:function(t){e.$set(e.orders,"isTemporary",t)},expression:"orders.isTemporary"}})],1),t("FormItem",{attrs:{label:"是否激活"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.isActivate,callback:function(t){e.$set(e.orders,"isActivate",t)},expression:"orders.isActivate"}})],1),t("FormItem",{attrs:{label:"同步错误信息"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.snyErro,callback:function(t){e.$set(e.orders,"snyErro",t)},expression:"orders.snyErro"}})],1),t("FormItem",{attrs:{label:"同步成功回传订单号"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.synOrderid,callback:function(t){e.$set(e.orders,"synOrderid",t)},expression:"orders.synOrderid"}})],1),t("FormItem",{attrs:{label:"退款失败原因"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.backPayreason,callback:function(t){e.$set(e.orders,"backPayreason",t)},expression:"orders.backPayreason"}})],1),t("FormItem",{attrs:{label:"订单提交语言"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.orderLanguage,callback:function(t){e.$set(e.orders,"orderLanguage",t)},expression:"orders.orderLanguage"}})],1),t("FormItem",{attrs:{label:"退款时间"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.backDate,callback:function(t){e.$set(e.orders,"backDate",t)},expression:"orders.backDate"}})],1),t("FormItem",{attrs:{label:"结算币种"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.orderPayType,callback:function(t){e.$set(e.orders,"orderPayType",t)},expression:"orders.orderPayType"}})],1),t("FormItem",{attrs:{label:"商品原价"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.oldPrice,callback:function(t){e.$set(e.orders,"oldPrice",t)},expression:"orders.oldPrice"}})],1),t("FormItem",{attrs:{label:"商品折扣"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.acount,callback:function(t){e.$set(e.orders,"acount",t)},expression:"orders.acount"}})],1),t("FormItem",{attrs:{label:"电话头"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.orders.billingPhoneTel,callback:function(t){e.$set(e.orders,"billingPhoneTel",t)},expression:"orders.billingPhoneTel"}})],1)],1),t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"唯一ID"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.consignee.uuid,callback:function(t){e.$set(e.consignee,"uuid",t)},expression:"consignee.uuid"}})],1),t("FormItem",{attrs:{label:"是否设置为注册账号"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.consignee.lsLogonNo,callback:function(t){e.$set(e.consignee,"lsLogonNo",t)},expression:"consignee.lsLogonNo"}})],1),t("FormItem",{attrs:{label:"消费者的账单国家"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.consignee.billingCountry,callback:function(t){e.$set(e.consignee,"billingCountry",t)},expression:"consignee.billingCountry"}})],1),t("FormItem",{attrs:{label:"消费者的电话"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.consignee.billingPhone,callback:function(t){e.$set(e.consignee,"billingPhone",t)},expression:"consignee.billingPhone"}})],1),t("FormItem",{attrs:{label:"省份"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.consignee.billingProvince,callback:function(t){e.$set(e.consignee,"billingProvince",t)},expression:"consignee.billingProvince"}})],1),t("FormItem",{attrs:{label:"消费者的城市"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.consignee.billingCity,callback:function(t){e.$set(e.consignee,"billingCity",t)},expression:"consignee.billingCity"}})],1),t("FormItem",{attrs:{label:"消费者的详细地址"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.consignee.billingAddress,callback:function(t){e.$set(e.consignee,"billingAddress",t)},expression:"consignee.billingAddress"}})],1),t("FormItem",{attrs:{label:"消费者的姓"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.consignee.billingFirstName,callback:function(t){e.$set(e.consignee,"billingFirstName",t)},expression:"consignee.billingFirstName"}})],1),t("FormItem",{attrs:{label:"消费者的邮箱"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.consignee.billingEmail,callback:function(t){e.$set(e.consignee,"billingEmail",t)},expression:"consignee.billingEmail"}})],1)],1)],1)],1),t("Modal",{attrs:{title:"封面预览","footer-hide":!0,width:"532px"},model:{value:e.pictureShowFlag,callback:function(t){e.pictureShowFlag=t},expression:"pictureShowFlag"}},[t("div",{staticStyle:{display:"flex","justify-content":"center","align-items":"center",width:"500px"}},[t("img",{staticStyle:{"object-fit":"contain"},attrs:{src:e.orders.productPic,width:"100%"}})])])],1)},f=[],g={props:{ordersObj:Object},data:function(){return{pictureShowFlag:!1,orders:{uuid:"",orderNumber:"",orderCurrency:"",orderType:"",payTime:"",orderTime:"",cardType:"",methods:"",cardNo:"",buyType:"",productId:"",productName:"",productAmount:"",productPic:"",productDetial:"",iccd:"",logisticsNo:"",logisticsCom:"",productNum:"",personId:"",payId:"",payResult:"",loginUser:"",isTemporary:"",isActivate:"",snyErro:"",synOrderid:"",backPayreason:"",orderLanguage:"",backDate:"",orderPayType:"",oldPrice:"",acount:"",billingPhoneTel:"",consignee:null},consignee:{uuid:"",lsLogonNo:"",billingCountry:"",billingPhone:"",billingProvince:"",billingCity:"",billingAddress:"",billingFirstName:"",billingEmail:""}}},methods:{init:function(){},getH5OrderInfoById:function(){var e=this;Object(n["i"])({orderId:this.oId}).then((function(t){if(!t||"0000"!=t.code)throw e.$Notice.error({title:"操作提示",desc:"H5订单详情获取失败"}),t;var a=t.obj;null!=a.orders&&a.orders.length>0?(e.orders=a.orders[0],e.consignee=e.orders.consignee):e.$Notice.error({title:"操作提示",desc:"H5订单详情获取失败"})})).catch((function(e){})).finally((function(){}))}},mounted:function(){null!=this.ordersObj&&(this.orders=this.ordersObj,this.consignee=this.orders.consignee)},watch:{}},b=g,v=Object(u["a"])(b,h,f,!1,null,"0b2c1d81",null),y=v.exports,x=a("d245"),w=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticStyle:{padding:"5px 0"}},[t("div",{staticClass:"search_head_i"},[t("div",{staticClass:"search_box"},[t("span",{staticClass:"search_box_label"},[e._v("ICCID")]),t("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入ICCID",clearable:""},model:{value:e.iccid,callback:function(t){e.iccid="string"===typeof t?t.trim():t},expression:"iccid"}})],1),t("div",{staticClass:"search_box"},[t("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{margin:"0 4px"},attrs:{type:"primary",loading:e.searchLoading},on:{click:e.search}},[t("Icon",{attrs:{type:"ios-search"}}),e._v(" 搜索\n        ")],1)],1)]),t("Table",{ref:"selection",attrs:{columns:e.columns,data:e.tableData,ellipsis:!0,loading:e.loading}})],1),t("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[t("Page",{staticStyle:{margin:"10px 0"},attrs:{total:e.total,current:e.currentPage,"page-size":e.pageSize,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1)])},I=[],k={props:{id:String},data:function(){return{iccid:"",columns:[{title:"ICCID",key:"iccid",tooltip:!0,minWidth:200,align:"center"}],tableData:[],searchLoading:!1,loading:!1,total:0,currentPage:1,pageSize:10}},methods:{search:function(){this.searchLoading=!0,this.goPageFirst(1)},goPageFirst:function(e){var t=this;this.currentPage=e,this.loading=!0;var a={orderId:this.id,pageNumber:e,pageSize:this.pageSize,iccid:this.iccid,onlyIccid:!0};Object(n["h"])(a).then((function(e){if(!e||"0000"!=e.code)throw e;var a=e.data;t.total=a.totalCount,t.tableData=a.records})).catch((function(e){})).finally((function(){t.loading=!1,t.searchLoading=!1}))},goPage:function(e){this.goPageFirst(e)}},mounted:function(){this.goPageFirst(1)}},C=k,S=(a("4f3c"),Object(u["a"])(C,w,I,!1,null,"b8975a98",null)),M=S.exports,O=(a("c70b"),{components:{orderInfo:m,h5orderInfo:y,lockApplication:x["a"],largeOrderInfo:M},data:function(){var e=this,t=function(t,a,r){e.uploadList&&0===e.uploadList.length?r(new Error("请上传文件")):r()};return{statusList:[{value:"1",label:"待发货"},{value:"2",label:"已完成"},{value:"3",label:"已退订"},{value:"4",label:"激活退订待审批"},{value:"5",label:"部分退订"},{value:"6",label:"部分发货"},{value:"9",label:"复合状态"},{value:"10",label:"订单生成中"},{value:"11",label:"订单处理失败"},{value:"12",label:"发货处理中"},{value:"13",label:"退订处理中"},{value:"14",label:"解绑中"}],timeRangeArray:[],searchCondition:{orderUniqueId:"",orderStatus:"",thirdOrderId:"",orderName:"",startTime:"",endTime:"",orderUserName:"",isBigOrder:null},loading:!1,exportLoading:!1,currentPage:1,pageSize:10,total:0,columns:[],tableData:[],batchModal:!1,file:null,downloading:!1,uploading:!1,message:"文件请使用导出模板,仅支持csv格式文件,大小不能超过5MB",deliverMessage:"仅支持csv格式文件,大小不能超过5MB",deliverFlag:!1,submitFlag:!1,formValidate:{orderId:"",logistic:"",iccidList:"",logisticCompany:"",address:{address:"",country:"",province:"",city:"",mailing:""},postCode:"",file:""},ruleValidate:{logistic:[{required:!0,type:"string",message:"请输入货运单号"}],iccidList:[{required:!0,type:"string",message:"请输入ICCID"}],logisticCompany:[{required:!0,type:"string",message:"请输入物流公司"}],address:[{validator:function(e,t,a){return null!=t.country&&""!=t.country},message:"请输入国家/地区"},{validator:function(e,t,a){return null!=t.mailing&&""!=t.mailing},message:"请输入邮寄地址"}],file:[{required:!0,validator:t,trigger:"change"}]},orderId:"",orderInfoModal:!1,h5orderInfoModal:!1,lockApplicationModal:!1,largeOrderModal:!1,h5Obj:null,modelColumns:[{title:"ICCID",key:"iccid"}],modelData:[],uploadList:[],isSuperManger:"",bigOrder:!1}},computed:{},methods:{init:function(){var e=this;this.columns=[{title:"订单详情",width:100,slot:"orderInfo",align:"center"},{title:"商品名称",tooltip:!0,width:150,key:"orderName",align:"center"},{title:"订单编号",key:"orderUniqueId",tooltip:!0,width:180,align:"center"},{title:"订购类型",key:"orderType",tooltip:!0,width:140,align:"center",render:function(e,t){var a=t.row,r="",i="";switch(a.orderType){case"1":r="卡",i="#3943ff";break;case"2":r="套餐",i="#00aa00";break;case"3":r="卡+套餐",i="#1d9dff";break;case"4":r="终端线下卡池套餐",i="#4f4f4f";break;case"5":r="流量池套餐",i="#ff3056";break;case"6":r="终端厂商套餐",i="#9822ff";break;case"7":r="加油包",i="#ff10ac";break;default:r="未知类型"}return e("label",{style:{color:i}},r)}},{title:"订购状态",width:140,key:"orderStatus",align:"center",render:function(e,t){var a=t.row,r="",i="";switch(a.orderStatus){case"1":r="待发货",i="#aaaa10";break;case"2":r="已完成",i="#00aa00";break;case"3":r="已退订",i="#130bff";break;case"4":r="激活退订待审批",i="#ff10ac";break;case"5":r="部分退订",i="#a407ff";break;case"6":r="部分发货",i="#07ffeb";break;case"9":r="复合状态",i="#ff862f";break;case"10":r="订单生成中",i="#309bff";break;case"11":r="订单处理失败",i="#ff0b1b";break;case"12":r="发货处理中",i="#ffcb81";break;case"13":r="退订处理中",i="#9cff5a";break;case"14":r="解绑中",i="#55557f";break;default:r="未知状态"}return e("label",{style:{color:i}},r)}},{title:"订单生成时间",key:"createTime",tooltip:!0,width:150,align:"center"},{title:"订单更新时间",key:"updateTime",tooltip:!0,width:150,align:"center"},{title:"购买数量",width:100,key:"count",align:"center"},{title:"币种",key:"currencyCode",width:100,align:"center",render:function(e,t){var a=t.row,r="";switch(a.currencyCode){case"156":r="人民币";break;case"840":r="美元";break;case"344":r="港币";break;default:r="未知"}return e("label",r)}},{title:"金额",key:"amount",width:100,align:"center"},{title:"ICCID",key:"iccid",minWidth:180,align:"center",tooltip:!0,render:function(t,a){var r=a.row;return r.bigOrder?"1"==r.orderStatus?t("label",""):t("div",[t("a",{on:{click:function(){e.showIccid(r.orderId)}}},"查看详情")]):t("label",r.iccid)}},{title:"第三方订单号",key:"thirdOrderId",width:150,tooltip:!0,align:"center"},{title:"联系人号码",key:"phoneNumber",tooltip:!0,width:150,align:"center"},{title:"收件人",key:"addressee",tooltip:!0,width:150,align:"center"},{title:"收件地址",key:"address",tooltip:!0,width:250,align:"center"},{title:"物流公司",key:"logisticCompany",tooltip:!0,width:150,align:"center"},{title:"货运单号",key:"logistic",tooltip:!0,width:150,align:"center"},{title:"用户",key:"orderUserName",tooltip:!0,width:150,align:"center"}];var t=["untie","unsubscribe","ship","reorder"],a=["check"],r=this.$route.meta.permTypes,i=t.filter((function(e){return r.indexOf(e)>-1})),o=a.filter((function(e){return r.indexOf(e)>-1}));if(i.length>0){var s=0;1==i.length?s=70:2==i.length?s=140:3==i.length?s=175:4==i.length&&(s=240),this.columns.push({title:"操作",slot:"action",width:s,fixed:"right",align:"center"})}o.length>0&&this.columns.push({title:"退订审批",width:140,fixed:"right",slot:"unsubscribeCheck",align:"center"}),this.columns.push({title:"H5订单信息详情",width:125,slot:"h5OrderInfo",fixed:"right",align:"center"}),this.goPageFirst(1)},goPage:function(e){this.goPageFirst(e)},searchByCondition:function(){this.goPageFirst(1)},goPageFirst:function(e){var t=this;this.currentPage=e,this.loading=!0;var a={orderUniqueId:this.searchCondition.orderUniqueId.replace(/\s/g,""),orderStatus:this.searchCondition.orderStatus,thirdOrderId:this.searchCondition.thirdOrderId.replace(/\s/g,""),orderName:this.searchCondition.orderName.replace(/\s/g,""),startTime:this.searchCondition.startTime,endTime:this.searchCondition.endTime,orderUserName:this.searchCondition.orderUserName.replace(/\s/g,""),isBigOrder:this.searchCondition.isBigOrder,orderUserType:"1",pageNumber:e,pageSize:this.pageSize};Object(n["j"])(a).then((function(e){if(!e||"0000"!=e.code)throw e;var a=e.data;t.total=a.totalCount,t.tableData=a.records})).catch((function(e){})).finally((function(){t.loading=!1}))},exportData:function(){var e=this,t={orderUniqueId:this.searchCondition.orderUniqueId.replace(/\s/g,""),orderStatus:this.searchCondition.orderStatus,thirdOrderId:this.searchCondition.thirdOrderId.replace(/\s/g,""),orderName:this.searchCondition.orderName.replace(/\s/g,""),startTime:this.searchCondition.startTime,endTime:this.searchCondition.endTime,orderUserName:this.searchCondition.orderUserName.replace(/\s/g,""),isBigOrder:this.searchCondition.isBigOrder,orderUserType:"1",pageNumber:1,pageSize:-1};this.exportLoading=!0,Object(n["e"])(t).then((function(e){var t=e.data,a=new Blob([t]),r="总订单导出列表.csv";if("download"in document.createElement("a")){var i=document.createElement("a");i.download=r,i.style.display="none",i.href=URL.createObjectURL(a),document.body.appendChild(i),i.click(),URL.revokeObjectURL(i.href),document.body.removeChild(i)}else navigator.msSaveBlob(a,r)})).catch((function(e){console.log(e)})).finally((function(){e.exportLoading=!1}))},unbundling:function(e){var t=this,a=e.bigOrder?n["q"]:n["p"];this.$Modal.confirm({title:"确认解绑？",onOk:function(){a(e.orderId).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.goPageFirst(t.currentPage)})).catch((function(e){}))}})},recycling:function(e){var t=this;this.$Modal.confirm({title:"确认回收？",onOk:function(){Object(n["m"])(e).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.goPageFirst(t.currentPage)})).catch((function(e){}))}})},deliveryAllMoadl:function(e){this.bigOrder=e.bigOrder,this.$refs["formValidate"].resetFields();var t={address:"",country:"",province:"",city:"",mailing:""};if(null!=e.address){var a=e.address.split("|");t={address:"",country:a[0],province:a[1],city:a[2],mailing:a[3]}}this.formValidate={orderId:e.orderId,logistic:e.logistic,iccidList:e.iccid,logisticCompany:e.logisticCompany,address:t,postCode:e.postCode,orderUniqueId:e.orderUniqueId},this.deliverFlag=!0},unsubscribeAll:function(e){var t=this;this.$Modal.confirm({title:"确认全部退订？",onOk:function(){e.bigOrder?Object(n["t"])({orderId:e.orderUniqueId,interWeb:!0}).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.goPageFirst(t.currentPage)})).catch((function(e){})):Object(n["s"])(e.orderId).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.goPageFirst(t.currentPage)})).catch((function(e){}))}})},reorderAgain:function(e){var t=this;this.$Modal.confirm({title:"确认再次订购？",onOk:function(){Object(n["n"])({orderUniqueId:e}).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.goPageFirst(t.currentPage)})).catch((function(e){}))}})},examine:function(e,t){var a=this;this.$Modal.confirm({title:"2"==t?"确认执行通过操作？":"确认执行不通过操作？",onOk:function(){Object(n["g"])({id:e,status:t}).then((function(e){if(!e||"0000"!=e.code)throw e;a.$Notice.success({title:"操作提示",desc:"操作成功"}),a.goPageFirst(a.currentPage)})).catch((function(e){}))}})},showBatchModal:function(){this.file=null,this.batchModal=!0},viewPermission:function(){this.lockApplicationModal=!0},handleBeforeUpload:function(e,t){return/^.+(\.csv)$/.test(e.name)?e.size>5242880?this.$Notice.warning({title:"文件大小超过限制",desc:"文件超过了最大限制范围5MB"}):(this.file=e,this.uploadList=t):this.$Notice.warning({title:"文件格式错误",desc:"文件格式错误，请上传csv格式文件"}),!1},handleBigOrderBeforeUpload:function(e,t){return/^.+(\.csv)$/.test(e.name)?(this.file=e,this.uploadList=t):this.$Notice.warning({title:"文件格式错误",desc:"文件格式错误，请上传csv格式文件"}),!1},fileUploading:function(e,t,a){this.message="文件上传中,待进度条消失后再操作",this.deliverMessage="文件上传中,待进度条消失后再操作"},fileSuccess:function(e,t,a){this.message="请先下载模板文件,并按格式填写后上传",this.deliverMessage="请先下载模板文件,并按格式填写后上传"},removeFile:function(){this.file=""},downloadTempFile:function(){this.$refs.modelTable.exportCsv({filename:"iccid",columns:this.modelColumns,data:this.modelData})},cancelUpload:function(){this.batchModal=!1},deliverCancel:function(){this.$refs["formValidate"].resetFields(),this.deliverFlag=!1,this.file=null,this.uploadList=[]},deliverySubmit:function(){var e=this;this.$refs.formValidate.validate((function(t){if(t){e.submitFlag=!0;var a,r=Object.assign({},e.formValidate),i=r.address.country.concat("|",r.address.province,"|",r.address.city,"|",r.address.mailing);e.bigOrder?(a=new FormData,a.append("file",e.file),a.append("address",i),a.append("logistic",r.logistic),a.append("logisticCompany",r.logisticCompany),a.append("orderId",r.orderId),a.append("postCode",r.postCode),a.append("orderUniqueId",r.orderUniqueId),Object(n["a"])(a).then((function(t){if("0000"!==t.code)throw t;t.data;e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.deliverFlag=!1,e.goPageFirst(1)})).catch((function(e){console.log(e)})).finally((function(){e.submitFlag=!1,e.deliverFlag=!1,e.deliverCancel()}))):(a={address:i,iccidList:r.iccidList,logistic:r.logistic,logisticCompany:r.logisticCompany,orderId:r.orderId,postCode:r.postCode},Object(n["c"])(a,r.orderId).then((function(t){if("0000"!==t.code)throw t;t.data;e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.deliverFlag=!1,e.goPageFirst(1)})).catch((function(e){console.log(e)})).finally((function(){e.submitFlag=!1,e.deliverFlag=!1,e.deliverCancel()})))}}))},deliveryBatch:function(){var e=this;if(!this.file)return this.$Message.warning("请选择需要上传的文件"),!1;this.uploading=!0;var t=new FormData;t.append("file",this.file),Object(n["d"])(t).then((function(t){if("0000"!==t.code)throw t;t.data;e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.cancelUpload(),e.goPageFirst(1)})).catch((function(e){console.log(e)})).finally((function(){e.uploading=!1}))},showOrderInfo:function(e){this.orderId=e,this.orderInfoModal=!0},showIccid:function(e){this.orderId=e,this.largeOrderModal=!0},showH5OrderInfo:function(e){this.getH5OrderInfoById(e)},getH5OrderInfoById:function(e){var t=this;Object(n["i"])({orderId:e}).then((function(e){if(!e||"0000"!=e.code)throw e;var a=e.data;null!=a.orders&&a.orders.length>0?(t.h5Obj=a.orders[0],t.h5orderInfoModal=!0):t.h5orderInfoModal=!0})).catch((function(e){})).finally((function(){}))},hanldeDateClear:function(){this.searchCondition.startTime="",this.searchCondition.endTime=""},handleDateChange:function(e){var t=this.timeRangeArray[0]||"",a=this.timeRangeArray[1]||"";if(""!=t&&""!=a){var r=Object(o["a"])(e,2);this.searchCondition.startTime=r[0],this.searchCondition.endTime=r[1]}},h5orderInfoCancel:function(){this.h5Obj=null,this.h5orderInfoModal=!1},lockApplicationCancel:function(){this.lockApplicationModal=!1},orderInfoModalCancel:function(){this.orderInfoModal=!1,this.goPageFirst(this.currentPage)},largeOrderModalCancel:function(){this.largeOrderModal=!1}},mounted:function(){this.isSuperManger=this.$store.state.user.roleId,this.init()},watch:{}}),_=O,F=(a("fa7c"),a("45c1"),Object(u["a"])(_,r,i,!1,null,"1be22368",null));t["default"]=F.exports},"841c":function(e,t,a){"use strict";var r=a("c65b"),i=a("d784"),o=a("825a"),s=a("7234"),l=a("1d80"),n=a("129f"),c=a("577e"),d=a("dc4a"),u=a("14c3");i("search",(function(e,t,a){return[function(t){var a=l(this),i=s(t)?void 0:d(t,e);return i?r(i,t,a):new RegExp(t)[e](c(a))},function(e){var r=o(this),i=c(e),s=a(t,r,i);if(s.done)return s.value;var l=r.lastIndex;n(l,0)||(r.lastIndex=0);var d=u(r,i);return n(r.lastIndex,l)||(r.lastIndex=l),null===d?-1:d.index}]}))},a8e9:function(e,t,a){},b0fb:function(e,t,a){"use strict";a("a8e9")},b84c:function(e,t,a){},bb47:function(e,t,a){},cc16:function(e,t,a){},d245:function(e,t,a){"use strict";var r=function(){var e=this,t=e._self._c;return t("div",[t("Form",{ref:"formObj",attrs:{model:e.formObj,"label-width":90,rules:e.ruleAddValidate}},[t("FormItem",{attrs:{label:"申请原因",prop:"replyReason"}},[t("Input",{staticStyle:{width:"350px"},attrs:{type:"textarea",rows:3,maxlength:300,clearable:"",placeholder:"请输入原因"},model:{value:e.formObj.replyReason,callback:function(t){e.$set(e.formObj,"replyReason",t)},expression:"formObj.replyReason"}})],1),t("FormItem",{attrs:{label:"浏览时间",prop:"replyTime"}},[t("Select",{staticStyle:{width:"350px"},model:{value:e.formObj.replyTime,callback:function(t){e.$set(e.formObj,"replyTime",t)},expression:"formObj.replyTime"}},e._l(e.timeList,(function(a,r){return t("Option",{key:a,attrs:{value:a}},[e._v(e._s(a+"小时"))])})),1)],1),t("FormItem",{attrs:{label:"申请页面",prop:"pageNumber"}},[t("Select",{staticStyle:{width:"350px"},model:{value:e.formObj.pageNumber,callback:function(t){e.$set(e.formObj,"pageNumber",t)},expression:"formObj.pageNumber"}},[1==e.page?t("Option",{attrs:{value:"1"}},[e._v("个人订单管理")]):t("Option",{attrs:{value:"2"}},[e._v("认证信息")])],1)],1)],1),t("div",{staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:e.cancelModal}},[e._v("取消")]),e._v("      \n\t  "),t("Button",{directives:[{name:"has",rawName:"v-has",value:"submit",expression:"'submit'"},{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:e.submitLoading},on:{click:e.submit}},[e._v("确定")])],1)],1)},i=[],o=(a("a9e3"),a("d3b7"),a("66df")),s="/sys/api/user",l=function(e){return o["a"].request({url:s+"/privilege/replyPrivilege",data:e,method:"post"})},n=function(e){return o["a"].request({url:s+"/privilege/getAvailableTime",data:e,method:"get"})},c={props:{page:{type:Number,default:""},type:{type:Number,default:""}},data:function(){return{submitLoading:!1,timeList:[],formObj:{replyReason:"",replyTime:"",pageNumber:""},ruleAddValidate:{replyReason:[{required:!0,type:"string",message:"原因不能为空"}],replyTime:[{required:!0,message:"时间不能为空"}],pageNumber:[{required:!0,type:"string",message:"页面不能为空"}]}}},mounted:function(){this.getTime(),1==this.page?this.formObj.pageNumber="1":this.formObj.pageNumber="2"},methods:{cancelModal:function(){this.$emit("lockApplicationCancel")},submit:function(){var e=this;this.$refs["formObj"].validate((function(t){t&&(e.submitLoading=!0,l({replyReason:e.formObj.replyReason,replyTime:e.formObj.replyTime,pageNumber:e.formObj.pageNumber}).then((function(t){if("0000"!==t.code)throw t;t.data;e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.submitLoading=!1,e.$emit("lockApplicationCancel"),e.$emit("goPageFirst")})).catch((function(e){console.log(e)})).finally((function(){e.submitLoading=!1})))}))},getTime:function(){var e=this;n().then((function(t){"0000"===t.code&&(e.timeList=t.data)})).catch((function(e){console.log(e)}))}}},d=c,u=a("2877"),p=Object(u["a"])(d,r,i,!1,null,null,null);t["a"]=p.exports},fa7c:function(e,t,a){"use strict";a("cc16")}}]);