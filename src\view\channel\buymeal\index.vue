<template>
	<!-- 套餐购买 -->
	<ElCard class="buymeal-card">
		<ElTabs v-model="activeTab" class="buymeal-tabs">
			<ElTabPane 
				v-if="checkPermission(['buy'])" 
				label="手动批量" 
				name="manual"
			>
				<!-- 选择套餐 -->
				<div class="meal-selection">
					<ElForm 
						ref="mealFormRef" 
						:model="mealForm" 
						:rules="mealRules" 
						inline
						class="search-form"
					>
						<ElFormItem label="套餐名称" label-width="100px">
							<ElInput 
								v-model="mealForm.mealname" 
								placeholder="请输入套餐名称" 
								clearable 
								style="width: 150px" 
							/>
						</ElFormItem>
						<ElFormItem label="国家" label-width="80px">
							<ElSelect 
								v-model="mealForm.localId" 
								filterable 
								placeholder="请选择国家" 
								clearable 
								style="width: 150px"
							>
								<ElOption 
									v-for="item in localList" 
									:key="item.id"
									:value="item.mcc" 
									:label="item.countryEn"
								/>
							</ElSelect>
						</ElFormItem>
						<ElFormItem>
							<ElButton 
								v-if="checkPermission(['search'])" 
								:disabled="cooperationMode === '3'" 
								type="primary" 
								:loading="searchLoading" 
								@click="search"
								:icon="Search"
							>
								搜索
							</ElButton>
						</ElFormItem>
					</ElForm>
					
					<!-- 卡号输入 -->
					<ElForm 
						v-show="cardFlag" 
						:model="mealForm" 
						class="card-form"
					>
						<ElFormItem label="卡号" prop="cardnumber" label-width="100px">
							<ElInput 
								v-model="mealForm.cardnumber" 
								placeholder="请输入卡号" 
								clearable 
								style="width: 250px" 
							/>
						</ElFormItem>
					</ElForm>

					<!-- 套餐表格 -->
					<div class="meal-table">
						<ElTable 
							:data="tableData" 
							:loading="loading" 
							border 
							stripe
							style="width: 100%"
							@selection-change="handleSelectionChange"
						>
							<ElTableColumn type="selection" width="55" />
							<ElTableColumn 
								prop="packageName"
								label="套餐名称"
								min-width="150"
								align="center"
							/>
							<ElTableColumn 
								prop="packageId"
								label="套餐ID"
								min-width="120"
								align="center"
							/>
							<ElTableColumn 
								prop="countryName"
								label="国家"
								min-width="120"
								align="center"
							/>
							<ElTableColumn 
								prop="price"
								label="价格"
								min-width="100"
								align="center"
							>
								<template #default="{ row }">
									¥{{ row.price }}
								</template>
							</ElTableColumn>
							<ElTableColumn 
								prop="validity"
								label="有效期"
								min-width="100"
								align="center"
							/>
							<ElTableColumn 
								prop="dataAllowance"
								label="流量"
								min-width="100"
								align="center"
							/>
						</ElTable>
					</div>
					
					<!-- 分页 -->
					<div class="pagination-container">
						<ElPagination
							:current-page="currentPage"
							:page-size="pageSize"
							:total="total"
							:page-sizes="[10, 20, 50, 100]"
							layout="total, sizes, prev, pager, next, jumper"
							@current-change="goPage"
							@size-change="handleSizeChange"
						/>
					</div>
				</div>
				
				<!-- 选中的套餐和卡号列表 -->
				<div class="selected-section">
					<ElForm 
						ref="formRef" 
						:model="form" 
						:rules="formRules" 
						class="selected-form"
					>
						<ElFormItem 
							v-show="mealFlag" 
							prop="chose_meal"
							label="选择的套餐"
							label-width="120px"
						>
							<ElInput 
								v-model="choseMeal" 
								readonly 
								style="width: 250px" 
							/>
						</ElFormItem>
						
						<!-- 卡号列表 -->
						<ElFormItem 
							v-for="(item, index) in form.cardList" 
							:key="index"
							v-if="item.index !== 0"
							:label="`卡号${index + 1}`"
							label-width="120px"
						>
							<div class="card-item">
								<span>{{ item.number }}</span>
								<ElButton 
									type="danger" 
									size="small" 
									:icon="Close" 
									circle 
									@click="removeCard(index)"
									style="margin-left: 10px"
								/>
							</div>
						</ElFormItem>
						
						<!-- 操作按钮 -->
						<ElFormItem label-width="120px">
							<div class="action-buttons">
								<ElButton 
									v-if="checkPermission(['addCard'])"
									type="primary" 
									@click="addCard"
									:icon="Plus"
								>
									添加卡号
								</ElButton>
								<ElButton 
									v-if="checkPermission(['purchase'])"
									type="success" 
									:loading="purchaseLoading"
									@click="purchase"
									:disabled="!canPurchase"
								>
									购买套餐
								</ElButton>
								<ElButton 
									type="default" 
									@click="reset"
								>
									重置
								</ElButton>
							</div>
						</ElFormItem>
					</ElForm>
				</div>
			</ElTabPane>
			
			<ElTabPane 
				v-if="checkPermission(['batch'])" 
				label="批量导入" 
				name="batch"
			>
				<div class="batch-import">
					<ElUpload
						class="upload-demo"
						drag
						:action="uploadUrl"
						:before-upload="beforeUpload"
						:on-success="handleUploadSuccess"
						:on-error="handleUploadError"
						:file-list="fileList"
					>
						<ElIcon class="el-icon--upload"><UploadFilled /></ElIcon>
						<div class="el-upload__text">
							将文件拖到此处，或<em>点击上传</em>
						</div>
						<template #tip>
							<div class="el-upload__tip">
								只能上传 xlsx/xls 文件，且不超过 10MB
							</div>
						</template>
					</ElUpload>
				</div>
			</ElTabPane>
		</ElTabs>
	</ElCard>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElCard, ElTabs, ElTabPane, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton, ElTable, ElTableColumn, ElPagination, ElUpload, ElIcon } from 'element-plus'
import { Search, Plus, Close, UploadFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

defineOptions({
  name: 'ChannelBuyMeal'
})

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const purchaseLoading = ref(false)
const activeTab = ref('manual')

const cooperationMode = ref('')
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const cardFlag = ref(false)
const mealFlag = ref(false)
const choseMeal = ref('')

const mealForm = reactive({
  mealname: '',
  mealnameEn: '',
  localId: '',
  cardnumber: ''
})

const form = reactive({
  cardList: [{ index: 0, number: '' }]
})

const tableData = ref([])
const localList = ref([])
const selectedMeals = ref([])
const fileList = ref([])

const uploadUrl = ref('/api/upload')

// 表单验证规则
const mealRules = reactive({
  cardnumber: [
    { required: true, message: '请输入卡号', trigger: 'blur' }
  ]
})

const formRules = reactive({
  chose_meal: [
    { required: true, message: '请选择套餐', trigger: 'change' }
  ]
})

// 权限检查函数
const checkPermission = (permissions: string[]): boolean => {
  return true
}

// 计算属性
const canPurchase = computed(() => {
  return selectedMeals.value.length > 0 && form.cardList.length > 1
})

// 方法
const search = () => {
  searchLoading.value = true
  currentPage.value = 1
  loadData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

const goPage = (page: number) => {
  currentPage.value = page
  loadData()
}

const handleSelectionChange = (selection: any[]) => {
  selectedMeals.value = selection
  if (selection.length > 0) {
    mealFlag.value = true
    choseMeal.value = selection.map(item => item.packageName).join(', ')
  } else {
    mealFlag.value = false
    choseMeal.value = ''
  }
}

const addCard = () => {
  if (mealForm.cardnumber.trim()) {
    const newCard = {
      index: form.cardList.length,
      number: mealForm.cardnumber.trim()
    }
    form.cardList.push(newCard)
    mealForm.cardnumber = ''
    cardFlag.value = true
  } else {
    ElMessage.warning('请输入卡号')
  }
}

const removeCard = (index: number) => {
  form.cardList.splice(index, 1)
  if (form.cardList.length <= 1) {
    cardFlag.value = false
  }
}

const purchase = async () => {
  if (!canPurchase.value) {
    ElMessage.warning('请选择套餐并添加卡号')
    return
  }

  purchaseLoading.value = true
  try {
    console.log('购买套餐:', {
      meals: selectedMeals.value,
      cards: form.cardList.filter(item => item.index !== 0)
    })

    // 模拟购买过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('购买成功')
    reset()
  } catch (error) {
    console.error('购买失败:', error)
    ElMessage.error('购买失败')
  } finally {
    purchaseLoading.value = false
  }
}

const reset = () => {
  mealForm.mealname = ''
  mealForm.mealnameEn = ''
  mealForm.localId = ''
  mealForm.cardnumber = ''
  form.cardList = [{ index: 0, number: '' }]
  selectedMeals.value = []
  choseMeal.value = ''
  mealFlag.value = false
  cardFlag.value = false
}

// 文件上传相关方法
const beforeUpload = (file: File) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}

const handleUploadSuccess = (response: any, file: File) => {
  ElMessage.success('文件上传成功')
  console.log('上传成功:', response, file)
}

const handleUploadError = (error: any) => {
  ElMessage.error('文件上传失败')
  console.error('上传失败:', error)
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    console.log('搜索套餐:', mealForm)

    // 模拟数据
    tableData.value = [
      {
        packageName: '国际流量套餐',
        packageId: 'PKG001',
        countryName: '中国',
        price: '99.00',
        validity: '30天',
        dataAllowance: '1GB'
      },
      {
        packageName: '欧洲漫游套餐',
        packageId: 'PKG002',
        countryName: '德国',
        price: '199.00',
        validity: '15天',
        dataAllowance: '2GB'
      }
    ]
    total.value = 2
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
    searchLoading.value = false
  }
}

// 组件挂载时初始化
onMounted(() => {
  cooperationMode.value = sessionStorage.getItem('cooperationMode') || ''

  // 模拟国家列表
  localList.value = [
    { id: 1, mcc: '460', countryEn: '中国' },
    { id: 2, mcc: '310', countryEn: '美国' },
    { id: 3, mcc: '262', countryEn: '德国' },
    { id: 4, mcc: '440', countryEn: '日本' }
  ]
})
</script>

<style scoped>
.buymeal-card {
  margin: 20px;
}

.buymeal-tabs {
  margin-top: 20px;
}

.meal-selection {
  margin-bottom: 30px;
}

.search-form {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 15px;
  margin-right: 20px;
}

.card-form {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.meal-table {
  margin-bottom: 20px;
}

.pagination-container {
  text-align: right;
  margin-bottom: 30px;
}

.selected-section {
  background-color: #f0f2f5;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
}

.selected-form :deep(.el-form-item) {
  margin-bottom: 15px;
}

.card-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  width: fit-content;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.batch-import {
  padding: 40px;
  text-align: center;
}

.upload-demo {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form :deep(.el-form-item) {
    margin-bottom: 15px;
    margin-right: 0;
  }

  .action-buttons {
    flex-direction: column;
  }

  .pagination-container {
    text-align: center;
  }

  .card-item {
    width: 100%;
    justify-content: space-between;
  }
}
</style>
