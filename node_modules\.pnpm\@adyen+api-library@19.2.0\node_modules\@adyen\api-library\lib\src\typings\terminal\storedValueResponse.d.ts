/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { POIData } from './pOIData';
import { Response } from './response';
import { SaleData } from './saleData';
import { StoredValueResult } from './storedValueResult';
export declare class StoredValueResponse {
    'POIData': POIData;
    'Response': Response;
    'SaleData': SaleData;
    'StoredValueResult'?: Array<StoredValueResult>;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
