(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d71dc250"],{2556:function(t,e,a){"use strict";a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i})),a.d(e,"d",(function(){return c})),a.d(e,"e",(function(){return u})),a.d(e,"f",(function(){return l}));var o=a("66df"),n="/cms",r=function(t){return o["a"].request({url:n+"/esim/getList",data:t,method:"post"})},s=function(t){return o["a"].request({url:n+"/esim/infoExport",data:t,method:"post"})},i=function(t){return o["a"].request({url:n+"/esim/uploadQrCode",params:t,method:"get",responseType:"blob"})},c=function(t){return o["a"].request({url:n+"/esim/qrCodeExport",data:t,method:"post"})},u=function(t){return o["a"].request({url:n+"/esim/getEsimInfo",params:t,method:"get"})},l=function(t){return o["a"].request({url:n+"/esim/getQrCode",params:t,method:"get",responseType:"blob"})}},"64ef":function(t,e,a){"use strict";a("ada4")},ada4:function(t,e,a){},e472:function(t,e,a){"use strict";a.d(e,"d",(function(){return s})),a.d(e,"a",(function(){return i})),a.d(e,"e",(function(){return c})),a.d(e,"c",(function(){return u})),a.d(e,"b",(function(){return l}));var o=a("66df"),n="/rms/api/v1",r="/pms",s=function(t){return o["a"].request({url:n+"/supplier/selectSupplier",params:t,method:"get"})},i=function(t){return o["a"].request({url:n+"/supplier/saveSupplier",data:t,method:"post"})},c=function(t){return o["a"].request({url:n+"/supplier/updateSupplier",data:t,method:"post"})},u=function(t){return o["a"].request({url:n+"/supplier/queryShorten",data:t,method:"get"})},l=function(t){return o["a"].request({url:r+"/pms-realname/getMccList",data:t,method:"get"})}},f30e:function(t,e,a){"use strict";a.r(e);a("ac1f"),a("841c");var o=function(){var t=this,e=t._self._c;return e("Card",[e("div",{staticClass:"search_head_i"},[e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v(t._s(t.$t("deposit.mealname"))+":")]),e("Input",{staticStyle:{width:"200px"},attrs:{clearable:!0,placeholder:t.$t("support.input_mealname")},model:{value:t.searchObj.packageName,callback:function(e){t.$set(t.searchObj,"packageName",e)},expression:"searchObj.packageName"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v(t._s(t.$t("support.pakageId"))+":")]),e("Input",{staticStyle:{width:"200px"},attrs:{clearable:!0,placeholder:t.$t("support.inputPackageID")},model:{value:t.searchObj.packageId,callback:function(e){t.$set(t.searchObj,"packageId",e)},expression:"searchObj.packageId"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v(t._s(t.$t("buymeal.Country"))+":")]),e("Select",{staticStyle:{width:"200px"},attrs:{filterable:"",placeholder:t.$t("buymeal.selectCountry"),clearable:!0},model:{value:t.searchObj.country,callback:function(e){t.$set(t.searchObj,"country",e)},expression:"searchObj.country"}},t._l(t.countryList,(function(a){return e("Option",{key:a.id,attrs:{value:a.mcc}},[t._v(t._s(a.countryEn))])})),1)],1),e("div",{staticClass:"search_box"},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{disabled:"1"==t.cooperationMode||"3"==t.cooperationMode,type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.search()}}},[t._v(t._s(t.$t("common.search")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{margin:"0 20px"},attrs:{disabled:!1===t.showFalg||"1"==t.cooperationMode||"3"==t.cooperationMode,type:"info"},on:{click:t.add}},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("Icon",{attrs:{type:"md-add"}}),t._v(" "+t._s(t.$t("support.create"))+"\n\t\t\t\t")],1)]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"refuel",expression:"'refuel'"}],attrs:{type:"warning",disabled:"1"==t.cooperationMode||"3"==t.cooperationMode,icon:"md-briefcase"},on:{click:function(e){return t.fuelPackManagement()}}},[t._v(t._s(t.$t("channelfuelPack_mngr")))])],1)]),e("Table",{staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:t.columns,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(a){var o=a.row;a.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"info",expression:"'info'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"warning",ghost:""},on:{click:function(e){return t.showdetail(o)}}},[t._v(t._s(t.$t("stock.details")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"10px"},attrs:{disabled:!1===t.showFalg,type:"primary",ghost:""},on:{click:function(e){return t.update(o)}}},[t._v(t._s(t.$t("support.edit2")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"copy",expression:"'copy'"}],staticStyle:{"margin-right":"10px"},attrs:{disabled:!1===t.showFalg,type:"warning",ghost:""},on:{click:function(e){return t.copy(o)}}},[t._v(t._s(t.$t("support.copy")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],staticStyle:{"margin-right":"10px"},attrs:{disabled:"4"===o.auditStatus||!1===t.showFalg,type:"error",ghost:""},on:{click:function(e){return t.delItem(o)}}},[t._v(t._s(t.$t("address.Delete")))])]}}])}),e("div",{staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("PackageModel",{ref:"packagemodel",attrs:{title:t.title,typeFlag:t.typeFlag,corpId:t.corpId},on:{goPageFirst:t.goPageFirst}})],1)},n=[],r=(a("d81d"),a("14d9"),a("4e82"),a("e9c4"),a("4ec9"),a("b64b"),a("d3b7"),a("3ca3"),a("ddb0"),a("ad00")),s=a("90fe"),i=a("e472"),c=a("d2d0"),u=a("6dfa"),l=(a("2556"),{components:{PackageModel:r["a"]},data:function(){var t=this;return{cooperationMode:"",total:0,currentPage:1,page:0,name:"",corpId:"",loading:!1,searchloading:!1,title:"",supplierId:"",typeFlag:"",countryList:[],supplierList:[],searchObj:{packageName:"",packageId:"",country:"",poolId:"",supplierId:""},data:[],columns:[{title:this.$t("support.pakageId"),key:"id",minWidth:120,align:"center",tooltip:!0},{title:this.$t("deposit.mealname"),key:"nameCn",minWidth:130,align:"center",tooltip:!0},{title:this.$t("support.DataRestrictionType"),key:"flowLimitType",align:"center",tooltip:!0,minWidth:135,render:function(e,a){var o=a.row,n="1"==o.flowLimitType?t.$t("support.DataRestrictionCycle"):"2"==o.flowLimitType?t.$t("support.DataRestrictionSingle"):"";return e("label",n)}},{title:this.$t("support.failureReason"),key:"noPassMessage",minWidth:150,align:"center",tooltip:!0,render:function(t,e){var a=e.row,o=a.noPassMessage?a.noPassMessage:"";return o.length>10?(o=o.substring(0,10)+"...",t("div",[t("Tooltip",{props:{placement:"bottom",transfer:!0},style:{cursor:"pointer"}},[o,t("label",{slot:"content",style:{whiteSpace:"normal",wordBreak:"break-all"}},a.noPassMessage)])])):(o=o,t("label",o))}},{title:this.$t("support.operate"),slot:"action",minWidth:330,align:"center",fixed:"right"},{title:this.$t("support.approvalStatus"),key:"auditStatus",minWidth:130,align:"center",fixed:"right",tooltip:!0,render:function(e,a){var o=a.row,n="1"==o.auditStatus?"#2b85e4":"2"==o.auditStatus?"#19be6b":"3"==o.auditStatus?"#ff0000":"4"==o.auditStatus?"#ffa554":"5"==o.auditStatus?"#ff0000":"",r="1"==o.auditStatus?t.$t("support.newApproval"):"2"==o.auditStatus?t.$t("support.approve"):"3"==o.auditStatus?t.$t("support.notApprove"):"4"==o.auditStatus?t.$t("support.modificationApproval"):"5"==o.auditStatus?t.$t("support.deleteApproval"):"";return e("label",{style:{color:n}},r)}}],showFalg:!1}},mounted:function(){this.cooperationMode=sessionStorage.getItem("cooperationMode"),this.cooperationMode&&"2"!=this.cooperationMode||(this.searchcorpid(),this.getLocalList(),this.getsupplier())},methods:{goPageFirst:function(t){var e=this;this.loading=!0;var a=this;Object(c["e"])({page:t,pageSize:10,groupName:this.groupName,corpId:this.corpId,packageNameCn:this.searchObj.packageName,packageId:this.searchObj.packageId,mcc:this.searchObj.country,isNeedAuth:!0,list:!0,isNeedMcc:!0,cooperationMode:this.cooperationMode}).then((function(o){if("0000"==o.code){var n=[];o.data.data.map((function(t,e){t.authObj?n.push(t.authObj):n.push(t)})),a.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t,e.total=o.data.total,e.data=n}})).catch((function(t){console.error(t)})).finally((function(){e.loading=!1,e.searchloading=!1}))},search:function(){this.searchloading=!0,this.goPageFirst(1)},goPage:function(t){this.goPageFirst(t)},add:function(){this.title=this.$t("support.createPackage"),this.typeFlag="add",this.$refs.packagemodel.show()},fuelPackManagement:function(){this.$router.push({path:"/fuelPackManagement",query:{showFalg:this.showFalg}})},showdetail:function(t){this.title=this.$t("packageInfo"),this.typeFlag="info",this.$refs.packagemodel.show(JSON.parse(JSON.stringify(t)))},update:function(t){this.title=this.$t("packageUpdate"),this.typeFlag="update";var e="true";this.$refs.packagemodel.show(JSON.parse(JSON.stringify(t)),e)},copy:function(t){this.title=this.$t("packageCopy"),this.typeFlag="add",this.$refs.packagemodel.show(JSON.parse(JSON.stringify(t)))},delItem:function(t){var e=this;this.$Modal.confirm({title:this.$t("flow.Confirmdelete"),onOk:function(){Object(c["c"])({packageIdList:[t.id]}).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:e.$t("address.Operationreminder"),desc:e.$t("common.Successful")}),e.goPageFirst(1)})).catch((function(t){}))}})},searchcorpid:function(){var t=this;Object(u["F"])({userName:this.$store.state.user.userName}).then((function(e){"0000"==e.code&&(t.corpId=e.data,t.goPageFirst(1),t.judgeChannelCreatePackage())})).catch((function(t){console.error(t)})).finally((function(){}))},judgeChannelCreatePackage:function(){var t=this;Object(c["h"])({corpId:this.corpId}).then((function(e){"0000"==e.code&&(!1===e.data?t.showFalg=!1:t.showFalg=!0)})).catch((function(t){console.error(t)})).finally((function(){}))},getLocalList:function(){var t=this;Object(s["f"])().then((function(e){if(!e||"0000"!=e.code)throw e;var a=e.data;t.countryList=a,t.countryList.sort((function(t,e){return t.countryEn.localeCompare(e.countryEn)}));var o=new Map;a.map((function(t,e){o.set(t.mcc,t.countryEn)})),t.localMap=o})).catch((function(t){})).finally((function(){}))},getsupplier:function(){var t=this;Object(i["d"])({pageNum:-1,pageSize:-1}).then((function(e){"0000"==e.code&&(t.supplierList=e.data)})).catch((function(t){console.error(t)})).finally((function(){}))}}}),p=l,d=(a("64ef"),a("2877")),h=Object(d["a"])(p,o,n,!1,null,"7c8b3bf8",null);e["default"]=h.exports}}]);