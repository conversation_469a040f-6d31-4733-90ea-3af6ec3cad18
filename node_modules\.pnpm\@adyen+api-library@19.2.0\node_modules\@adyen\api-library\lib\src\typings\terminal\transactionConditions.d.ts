/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
export declare class TransactionConditions {
    'AcquirerID'?: Array<string>;
    'AllowedLoyaltyBrand'?: Array<string>;
    'AllowedPaymentBrand'?: Array<string>;
    'CustomerLanguage'?: string;
    'DebitPreferredFlag'?: boolean;
    'ForceEntryMode'?: Array<TransactionConditions.ForceEntryModeEnum>;
    'ForceOnlineFlag'?: boolean;
    'LoyaltyHandling'?: TransactionConditions.LoyaltyHandlingEnum;
    'MerchantCategoryCode'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace TransactionConditions {
    enum ForceEntryModeEnum {
        CheckReader,
        Contactless,
        File,
        Icc,
        Keyed,
        MagStripe,
        Manual,
        Rfid,
        Scanned,
        SynchronousIcc,
        Tapped
    }
    enum LoyaltyHandlingEnum {
        Allowed,
        Forbidden,
        Processed,
        Proposed,
        Required
    }
}
