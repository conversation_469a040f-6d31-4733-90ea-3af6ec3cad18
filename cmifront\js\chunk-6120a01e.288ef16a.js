(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6120a01e"],{"00b4":function(e,t,a){"use strict";a("ac1f");var c=a("23e7"),r=a("c65b"),i=a("1626"),n=a("825a"),o=a("577e"),l=function(){var e=!1,t=/[ac]/;return t.exec=function(){return e=!0,/./.exec.apply(this,arguments)},!0===t.test("abc")&&e}(),s=/./.test;c({target:"RegExp",proto:!0,forced:!l},{test:function(e){var t=n(this),a=o(e),c=t.exec;if(!i(c))return r(s,t,a);var l=r(c,t,a);return null!==l&&(n(l),!0)}})},"466d":function(e,t,a){"use strict";var c=a("c65b"),r=a("d784"),i=a("825a"),n=a("7234"),o=a("50c4"),l=a("577e"),s=a("1d80"),p=a("dc4a"),u=a("8aa5"),d=a("14c3");r("match",(function(e,t,a){return[function(t){var a=s(this),r=n(t)?void 0:p(t,e);return r?c(r,t,a):new RegExp(t)[e](l(a))},function(e){var c=i(this),r=l(e),n=a(t,c,r);if(n.done)return n.value;if(!c.global)return d(c,r);var s=c.unicode;c.lastIndex=0;var p,g=[],h=0;while(null!==(p=d(c,r))){var f=l(p[0]);g[h]=f,""===f&&(c.lastIndex=u(r,o(c.lastIndex),s)),h++}return 0===h?null:g}]}))},"6e6f":function(e,t,a){"use strict";a("c5f2")},"7a6c":function(e,t,a){"use strict";a.r(t);a("caad"),a("b0c0");var c=function(){var e=this,t=e._self._c;return t("Card",{staticStyle:{width:"100%",padiing:"16px"}},[t("Form",{ref:"searchForm",attrs:{model:e.searchObj,inline:""},nativeOn:{submit:function(e){e.preventDefault()}}},[t("FormItem",[t("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入套餐组名称",clearable:""},model:{value:e.searchObj.groupName,callback:function(t){e.$set(e.searchObj,"groupName",t)},expression:"searchObj.groupName"}})],1),t("FormItem",[t("Select",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择套餐组类型",clearable:""},model:{value:e.searchObj.groupType,callback:function(t){e.$set(e.searchObj,"groupType",t)},expression:"searchObj.groupType"}},[t("Option",{attrs:{value:"1"}},[e._v("二次定价")]),t("Option",{attrs:{value:"2"}},[e._v("非二次定价")])],1)],1),t("FormItem",[t("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{margin:"0 2px"},attrs:{type:"primary"},on:{click:e.searchPackageGroup}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"ios-search"}}),e._v(" 搜索\n\t\t\t\t\t")],1)]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{margin:"0 2px"},attrs:{type:"info"},on:{click:e.packageGroupAdd}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"md-add"}}),e._v(" 新增\n\t\t\t\t\t")],1)]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"batchDelete",expression:"'batchDelete'"}],staticStyle:{margin:"0 2px"},attrs:{type:"error"},on:{click:e.deleteList}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"ios-trash"}}),e._v(" 批量删除\n\t\t\t\t\t")],1)])],1)],1),t("div",[t("Table",{ref:"selection",attrs:{columns:e.columns,data:e.tableData,ellipsis:!0,loading:e.tableLoading},on:{"on-selection-change":e.handleRowChange,"on-select-cancel":e.cancelSigle,"on-select-all-cancel":e.cancelAll},scopedSlots:e._u([{key:"include",fn:function(a){var c=a.row;a.index;return[t("Button",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"info",size:"small"},on:{click:function(t){return e.packageGroupInfo(c)}}},[e._v("详情")])]}},{key:"action",fn:function(a){var c=a.row;a.index;return[t("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small",disabled:"1"==c.isChannelCreate},on:{click:function(t){return e.packageGroupEdit(c)}}},[e._v("编辑")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"error",size:"small",disabled:"1"==c.isChannelCreate},on:{click:function(t){return e.packageGroupDel(c.groupId)}}},[e._v("删除")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],attrs:{type:"primary",size:"small"},on:{click:function(t){return e.exportfile(c)}}},[e._v("导出")])]}},{key:"approval",fn:function(a){var c=a.row;a.index;return[[1].includes(+c.auditStatus)?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small"},on:{click:function(t){return e.cooperativeApproval(c,"2")}}},[e._v("通过")]):e._e(),[1].includes(+c.auditStatus)?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],attrs:{type:"error",size:"small"},on:{click:function(t){return e.cooperativeApproval(c,"3")}}},[e._v("不通过")]):e._e()]}}])}),t("Page",{staticStyle:{margin:"15px 0"},attrs:{total:e.total,"page-size":e.pageSize,current:e.page,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.page=t},"on-change":e.loadByPage}})],1),t("Modal",{attrs:{title:e.packageGroupTitle,"footer-hide":!0,"mask-closable":!1},on:{"on-cancel":e.cancelModal},model:{value:e.packageGroupEditFlag,callback:function(t){e.packageGroupEditFlag=t},expression:"packageGroupEditFlag"}},[t("div",{staticStyle:{padding:"0 16px"}},[t("Form",{ref:"editObj",attrs:{model:e.editObj,"label-width":118,rules:e.ruleEditValidate}},[t("FormItem",{attrs:{label:"套餐组名称",prop:"groupName"}},[t("Input",{attrs:{clearable:!0,maxlength:"50",placeholder:"请输入套餐组名称"},model:{value:e.editObj.groupName,callback:function(t){e.$set(e.editObj,"groupName",t)},expression:"editObj.groupName"}})],1),t("FormItem",{attrs:{label:"合作模式",prop:"cooperationMode"}},[t("Select",{staticStyle:{width:"200px"},attrs:{disabled:"Update"==e.operationType,placeholder:"请选择合作模式"},model:{value:e.editObj.cooperationMode,callback:function(t){e.$set(e.editObj,"cooperationMode",t)},expression:"editObj.cooperationMode"}},[t("Option",{attrs:{value:"1"}},[e._v("代销")]),t("Option",{attrs:{value:"2"}},[e._v("A2Z")])],1)],1),t("FormItem",{attrs:{label:"套餐组类型",prop:"groupType"}},[t("Select",{staticStyle:{width:"200px"},attrs:{disabled:"Add"!==e.operationType,placeholder:"请选择套餐组类型"},model:{value:e.editObj.groupType,callback:function(t){e.$set(e.editObj,"groupType",t)},expression:"editObj.groupType"}},[t("Option",{attrs:{value:"1"}},[e._v("二次定价")]),t("Option",{attrs:{value:"2"}},[e._v("非二次定价")])],1)],1),t("FormItem",{attrs:{label:"套餐列表选择方式",prop:"choseType"}},[t("Select",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择方式"},model:{value:e.editObj.choseType,callback:function(t){e.$set(e.editObj,"choseType",t)},expression:"editObj.choseType"}},[t("Option",{attrs:{value:"1"}},[e._v("页面选择展示")]),t("Option",{attrs:{value:"2"}},[e._v("文件上传展示")])],1)],1),"1"===e.editObj.choseType?t("FormItem",{attrs:{label:"套餐列表"}},["1"===e.editObj.choseType?t("Select",{staticClass:"packages-two-price",attrs:{multiple:"",placeholder:"请选择套餐",clearable:!0},on:{"on-open-change":e.selectionDropdwon},model:{value:e.editObj.packages,callback:function(t){e.$set(e.editObj,"packages",t)},expression:"editObj.packages"}},[t("div",{staticClass:"select-list",attrs:{slot:"prefix"},slot:"prefix"},e._l(e.editObj.packages,(function(a,c){return t("Tag",{key:c,attrs:{closable:""},on:{"on-close":function(t){return e.deletePackages(c)}}},[e._v("\n\t\t\t\t\t\t\t\t\t"+e._s(a.packageName)+"\n\t\t\t\t\t\t\t\t")])})),1),t("Form",{ref:"form",staticStyle:{display:"flex",width:"100%"}},[t("FormItem",{staticStyle:{display:"flex"},attrs:{label:"套餐名称:"}},[t("Input",{staticStyle:{width:"200px"},attrs:{clearable:!0,maxlength:"50",placeholder:"请输入套餐名称"},model:{value:e.packagegroupName,callback:function(t){e.packagegroupName=t},expression:"packagegroupName"}})],1),t("FormItem",[t("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{margin:"0 2px"},attrs:{type:"primary"},on:{click:e.searchPackagegroupName}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"ios-search"}}),e._v(" 搜索\n\t\t\t\t  \t\t\t\t")],1)])],1)],1),t("Table",{staticStyle:{"margin-top":"10px"},attrs:{columns:e.packageSelectColumns,data:e.packageSelectTableData,ellipsis:!0,loading:e.packageLoading},on:{"on-select":e.selectPackage,"on-select-cancel":e.cancelPackage,"on-select-all":e.selectPackage,"on-select-all-cancel":e.cancelPackageAll,"on-row-click":e.onRowClick}}),t("Page",{staticStyle:{margin:"15px 0"},style:{margin:"10px 20px"},attrs:{total:e.packageSelectTableTotal,"page-size":10,current:e.packageSelectTableCurrent,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.packageSelectTableCurrent=t},"on-change":e.loadByPackageSelectTableCurrentPage}})],1):e._e()],1):e._e(),"2"===e.editObj.choseType?t("FormItem",{attrs:{label:"上传套餐列表",prop:"file"}},[t("div",{staticStyle:{display:"flex"}},[t("Upload",{ref:"upload",attrs:{action:e.uploadUrl,"on-success":e.fileSuccess,"on-error":e.handleError,"before-upload":e.handleBeforeUpload,"on-progress":e.fileUploading},model:{value:e.editObj.file,callback:function(t){e.$set(e.editObj,"file",t)},expression:"editObj.file"}},[t("Button",{attrs:{icon:"ios-cloud-upload-outline"}},[e._v("点击上传")])],1),t("div",{staticStyle:{width:"500px","margin-left":"50px"}},[t("Button",{attrs:{type:"primary",icon:"ios-download"},on:{click:e.downloadFile}},[e._v("下载模板")])],1)],1),e.file?t("ul",{staticClass:"ivu-upload-list"},[t("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[t("span",[t("Icon",{attrs:{type:"ios-folder"}}),e._v(e._s(e.file.name)+"\n\t\t\t\t\t\t\t\t")],1),t("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:e.removeFile}})])]):e._e()]):e._e()],1),t("div",{staticStyle:{"text-align":"center"}},["Add"==e.operationType?t("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],attrs:{type:"primary"},on:{click:function(t){return e.submit("editObj")}}},[e._v("提交")]):e._e(),"Update"==e.operationType?t("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],attrs:{type:"primary"},on:{click:function(t){return e.submit("editObj")}}},[e._v("提交")]):e._e(),t("Button",{staticStyle:{"margin-left":"8px"},on:{click:function(t){return e.reseteditObj("editObj",1)}}},[e._v("重置")])],1)],1)]),t("Table",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"modelTable",attrs:{columns:e.modelColumns,data:e.modelData}}),t("Table",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"modelTable",attrs:{columns:e.NotmodelColumns,data:e.NotmodelData}}),t("Modal",{attrs:{title:"套餐组详情","footer-hide":!0,"mask-closable":!1,width:"950px"},on:{"on-cancel":e.cancelModal},model:{value:e.packageGroupInfoFlag,callback:function(t){e.packageGroupInfoFlag=t},expression:"packageGroupInfoFlag"}},[t("Form",{staticStyle:{"font-weight":"bold"},attrs:{"label-width":100}},[t("div",[t("FormItem",{attrs:{label:"套餐组名称:"}},[t("span",[e._v(e._s(e.info.groupName))])])],1),t("div",[t("FormItem",{attrs:{label:"套餐组类型:"}},[t("span",[e._v(e._s("1"===e.info.groupType?"二次定价":"2"===e.info.groupType?"非二次定价":""))])])],1),t("div",[t("FormItem",{attrs:{label:"合作模式:"}},[t("span",[e._v(e._s("1"===e.info.cooperationMode?"代销":"2"===e.info.cooperationMode?"A2Z":""))])])],1)]),t("div",{staticStyle:{padding:"0 16px"}},[t("Table",{attrs:{columns:e.packageColumns,data:e.packageTableData,ellipsis:!0,loading:e.packageLoading,"max-height":"500"}}),t("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[t("Page",{staticStyle:{margin:"15px 0"},attrs:{total:e.packageTotal,"page-size":e.packagePageSize,current:e.packageCurrentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.packageCurrentPage=t},"on-change":e.loadByPackagePage}})],1)],1)],1),t("a",{ref:"downloadLink",staticStyle:{display:"none"}})],1)},r=[],i=(a("d9e2"),a("99af"),a("4de4"),a("c740"),a("d81d"),a("14d9"),a("a434"),a("b64b"),a("d3b7"),a("ac1f"),a("00b4"),a("3ca3"),a("466d"),a("5319"),a("159b"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("e3b7")),n=function(){var e=this,t=e._self._c;return t("Table",{attrs:{border:"",columns:e.packageSelectColumns,data:e.packageSelectTableData,ellipsis:!0,loading:e.loading},on:{"on-select":e.selectPackage,"on-select-cancel":e.cancelPackage,"on-select-all":e.selectPackage,"on-select-all-cancel":e.cancelPackageAll}})},o=[],l={props:{row:Object,editObj:Object,tempSelectDataArrzj:[]},data:function(){var e=this;this.$createElement;return{loading:!1,tempSelectDataArr:[],packageSelectColumns:[{type:"selection",width:60,align:"center"},{title:"加油包名称",key:"nameCn",align:"center"},{title:"人民币价格",key:"cny",width:150,align:"center",render:function(t,a){var c=a.row,r=(a.column,a.index);if("1"===e.editObj.groupType){var i=function(){if(e.tempSelectDataArrzj.length&&e.tempSelectDataArrzj[0].id===e.row.id){var t=!1;e.tempSelectDataArrzj.forEach((function(a){a.id===e.packageSelectTableData[r].id&&(t=!0,a.cny=e.packageSelectTableData[r].cny,a.hkd=e.packageSelectTableData[r].hkd,a.usd=e.packageSelectTableData[r].usd)})),t||e.tempSelectDataArrzj.push(e.packageSelectTableData[r])}else e.tempSelectDataArrzj.push(e.packageSelectTableData[r]);e.editObj.packages.forEach((function(t){t.packageId===e.row.id&&t.refuelDetailList.forEach((function(t){t.refuelId===e.packageSelectTableData[r].id&&(t.cny=e.packageSelectTableData[r].cny,e.$nextTick((function(){document.querySelector(".errorPrice")?e.twicePriceFormat=!0:e.twicePriceFormat=!1})))}))})),e.$set(e.tempSelectDataArrzj,"tempSelectDataArrzj",e.tempSelectDataArrzj)};return e.editObj.packages.forEach((function(t){t.packageId===e.packageSelectTableData[r].id&&(e.packageSelectTableData[r]._checked=!0)})),t("div",[t("Input",{attrs:{maxlength:"13",placeholder:"请输入二次定价价格"},model:{value:e.packageSelectTableData[r].cny,callback:function(t){e.$set(e.packageSelectTableData[r],"cny",t)}},on:{"on-change":i}}),/^(([1-9]\d{0,9})|0)(\.\d{0,2})?$/.test(e.packageSelectTableData[r].cny)?null:t("p",{class:"errorPrice",style:"text-align: left;color:red"},["二次定价格最高支持10位整数和2位小数正数或零"])])}return t("span",[c.cny])}},{title:"港币价格",key:"hkd",width:150,align:"center",render:function(t,a){var c=a.row,r=(a.column,a.index);if("1"===e.editObj.groupType){var i=function(){if(e.tempSelectDataArrzj.length&&e.tempSelectDataArrzj[0].id===e.row.id){var t=!1;e.tempSelectDataArrzj.forEach((function(a){a.id===e.packageSelectTableData[r].id&&(t=!0,a.cny=e.packageSelectTableData[r].cny,a.hkd=e.packageSelectTableData[r].hkd,a.usd=e.packageSelectTableData[r].usd)})),t||e.tempSelectDataArrzj.push(e.packageSelectTableData[r])}else e.tempSelectDataArrzj.push(e.packageSelectTableData[r]);e.editObj.packages.forEach((function(t){t.packageId===e.row.id&&t.refuelDetailList.forEach((function(t){t.refuelId===e.packageSelectTableData[r].id&&(t.hkd=e.packageSelectTableData[r].hkd)}))})),e.$nextTick((function(){document.querySelector(".errorPrice")?e.twicePriceFormat=!0:e.twicePriceFormat=!1})),e.$set(e.tempSelectDataArrzj,"tempSelectDataArrzj",e.tempSelectDataArrzj)};return e.editObj.packages.forEach((function(t){t.packageId===e.packageSelectTableData[r].id&&(e.packageSelectTableData[r]._checked=!0)})),t("div",[t("Input",{attrs:{placeholder:"请输入二次定价价格"},model:{value:e.packageSelectTableData[r].hkd,callback:function(t){e.$set(e.packageSelectTableData[r],"hkd",t)}},on:{"on-change":i}}),/^(([1-9]\d{0,9})|0)(\.\d{0,2})?$/.test(e.packageSelectTableData[r].hkd)?null:t("p",{class:"errorPrice",style:"text-align: left;color:red"},["二次定价格最高支持10位整数和2位小数正数或零"])])}return t("span",[c.hkd])}},{title:"美元价格",key:"usd",width:150,align:"center",render:function(t,a){var c=a.row,r=(a.column,a.index);if("1"===e.editObj.groupType){var i=function(){if(e.tempSelectDataArrzj.length&&e.tempSelectDataArrzj[0].id===e.row.id){var t=!1;e.tempSelectDataArrzj.forEach((function(a){a.id===e.packageSelectTableData[r].id&&(t=!0,a.cny=e.packageSelectTableData[r].cny,a.hkd=e.packageSelectTableData[r].hkd,a.usd=e.packageSelectTableData[r].usd)})),t||e.tempSelectDataArrzj.push(e.packageSelectTableData[r])}else e.tempSelectDataArrzj.push(e.packageSelectTableData[r]);e.editObj.packages.forEach((function(t){t.packageId===e.row.id&&t.refuelDetailList.forEach((function(t){t.refuelId===e.packageSelectTableData[r].id&&(t.usd=e.packageSelectTableData[r].usd,e.$nextTick((function(){document.querySelector(".errorPrice")?e.twicePriceFormat=!0:e.twicePriceFormat=!1})))}))})),e.$set(e.tempSelectDataArrzj,"tempSelectDataArrzj",e.tempSelectDataArrzj)};return e.editObj.packages.forEach((function(t){t.packageId===e.packageSelectTableData[r].id&&(e.packageSelectTableData[r]._checked=!0)})),t("div",[t("Input",{attrs:{placeholder:"请输入二次定价价格"},model:{value:e.packageSelectTableData[r].usd,callback:function(t){e.$set(e.packageSelectTableData[r],"usd",t)}},on:{"on-change":i}}),/^(([1-9]\d{0,9})|0)(\.\d{0,2})?$/.test(e.packageSelectTableData[r].usd)?null:t("p",{class:"errorPrice",style:"text-align: left;color:red"},["二次定价格最高支持10位整数和2位小数正数或零"])])}return t("span",[c.usd])}}],packageSelectTableData:[]}},mounted:function(){var e=this;this.packageSelectTableData=this.row.refuelList,this.packageSelectTableData.forEach((function(t){t.packageId=e.row.id})),console.log(this.tempSelectDataArrzj),this.tempSelectDataArrzj.length>0&&this.tempSelectDataArrzj.forEach((function(t){e.packageSelectTableData.forEach((function(a){a.id===t.id&&t.packageId===e.row.id&&(a.cny=t.cny,a.hkd=t.hkd,a.usd=t.usd)}))})),this.packageSelectTableData.forEach((function(e){e._disabled=!0})),this.editObj.packages&&this.editObj.packages.length&&this.editObj.packages.forEach((function(t,a){e.row.id===t.packageId&&e.packageSelectTableData.forEach((function(e){e["_checked"]=!0}))}))},methods:{selectPackage:function(e,t){var a=this;e.forEach((function(t,c){var r=a.packageSelectTableData.findIndex((function(e){return e.id==t.id}));e[c]._index=r}));var c=e.map((function(e){return a.packageSelectTableData[e._index]._checked=!0,{refuelId:e.id,cny:e.cny,hkd:e.hkd,usd:e.usd}}));if(this.editObj.refuelDetailList&&this.editObj.refuelDetailList.length){var r=[];c.forEach((function(e){var t=!0;a.editObj.refuelDetailList.forEach((function(a){a.packageId===e.packageId&&(t=!1)})),t&&r.push(e)})),this.editObj.refuelDetailList=this.editObj.refuelDetailList.concat(r)}else this.$set(this.editObj.packages,"refuelDetailList",c),this.$set(this.editObj.packages,"packages",c)},cancelPackage:function(e,t){this.editObj.refuelDetailList&&this.editObj.refuelDetailList.length&&(this.editObj.refuelDetailList=this.editObj.refuelDetailList.filter((function(e){return e.packageId!==t.id}))),this.packageSelectTableData.forEach((function(e){e.id===t.id&&(e._checked=!1)}))},cancelPackageAll:function(e,t){this.editObj.refuelDetailList&&this.editObj.refuelDetailList.length&&(this.editObj.refuelDetailList=[]),this.packageSelectTableData.forEach((function(e){e._checked=!1}))}},created:function(){}},s=l,p=a("2877"),u=Object(p["a"])(s,n,o,!1,null,null,null),d=u.exports,g={components:{},data:function(){var e=this,t=(this.$createElement,function(t,a,c){return!e.twicePriceFormat}),a=function(t,a,c){e.uploadList&&0===e.uploadList.length?c(new Error("请上传文件")):c()};return{searchObj:{groupName:"",groupType:""},uploadList:[],packagegroupName:"",uploadUrl:"",file:null,modelData:[{"套餐ID":"********","加油包ID":"********","人民币价格":"********","港币价格":"********","美元价格":"********"}],modelColumns:[{title:"套餐ID",key:"套餐ID"},{title:"加油包ID",key:"加油包ID"},{title:"人民币价格",key:"人民币价格"},{title:"港币价格",key:"港币价格"},{title:"美元价格",key:"美元价格"}],NotmodelData:[{"套餐ID":"********","加油包ID":"********"}],NotmodelColumns:[{title:"套餐ID",key:"套餐ID"},{title:"加油包ID",key:"加油包ID"}],twicePriceFormat:!1,packageGroupEditFlag:!1,packageGroupInfoFlag:!1,packageGroupTitle:"",editObj:{groupName:"",cooperationMode:"",groupType:"",choseType:"",file:"",packages:[{refuelDetailList:[]}]},tempSelectDataArrzj:[],editObjreset:{},currentRow:"",packageList:[],ruleEditValidate:{groupName:[{required:!0,type:"string",message:"套餐组名称不能为空"}],cooperationMode:[{required:!0,message:"合作模式不能为空",trigger:"change"}],groupType:[{required:!0,message:"套餐组类型不能为空",trigger:"change"}],choseType:[{required:!0,message:"套餐列表选择方式不能为空",trigger:"change"}],file:[{required:!0,validator:a,trigger:"change"}],packages:[{required:!0,message:"套餐组列表不能为空"},{validator:t,message:"二次定价格最高支持10位整数和2位小数正数或零"}],"packages.index.cny":[{required:!0,message:"人民币不能为空"}],"packages.index.hkd":[{required:!0,message:"港币不能为空"}],"packages.index.usd":[{required:!0,message:"美元不能为空"}]},ruleEditValidateadd:{groupName:[{required:!0,type:"string",message:"套餐组名称不能为空"}],cooperationMode:[{required:!0,message:"合作模式不能为空",trigger:"change"}],groupType:[{required:!0,message:"套餐组类型不能为空",trigger:"change"}],choseType:[{required:!0,message:"套餐列表选择方式不能为空",trigger:"change"}],file:[{required:!0,validator:a,trigger:"change"}],packages:[{required:!0,message:"套餐组列表不能为空"},{validator:t,message:"二次定价格最高支持10位整数和2位小数正数或零"}],"packages.index.cny":[{required:!0,message:"人民币不能为空"}],"packages.index.hkd":[{required:!0,message:"港币不能为空"}],"packages.index.usd":[{required:!0,message:"美元不能为空"}]},operationType:"Add",tableData:[],selection:[],selectionIds:[],selectionList:[],tableLoading:!1,total:0,pageSize:10,page:1,columns:[{type:"selection",minWidth:60,maxWidth:60,align:"center"},{title:"套餐组名称",key:"groupName",align:"center",minWidth:150,tooltip:!0},{title:"创建时间",key:"createTime",align:"center",minWidth:120,tooltip:!0},{title:"套餐组类型",key:"groupType",align:"center",minWidth:120,tooltip:!0,render:function(e,t){var a=t.row,c="1"===a.groupType?"二次定价":"2"===a.groupType?"非二次定价":"";return e("label",c)}},{title:"包含套餐",slot:"include",minWidth:120,align:"center"},{title:"操作",slot:"action",minWidth:300,align:"center"},{title:"审批状态",key:"checkStatus",align:"center",minWidth:120,render:function(e,t){var a=t.row,c="3"==a.auditStatus?"#ff0000":"2"==a.auditStatus?"#00cc66":"#27A1FF",r="3"==a.auditStatus?"审批未通过":"2"==a.auditStatus?"审批通过":"待审批";return e("label",{style:{color:c}},r)}},{title:"审批操作",slot:"approval",minWidth:150,align:"center"}],packagePageSize:10,packagePage:0,packageLoading:!1,packageCurrentPage:1,packageTotal:2,packageColumns:[{title:"套餐ID",key:"id",align:"center",minWidth:130},{title:"套餐名称",key:"packageName",align:"center",minWidth:130},{title:"覆盖国家/地区",key:"mcc",align:"center",minWidth:110},{title:"人民币价格",key:"cny",align:"center",minWidth:80},{title:"港币价格",key:"hkd",align:"center",minWidth:80},{title:"美元价格",key:"usd",align:"center",minWidth:80}],packageTableData:[],packageSelectTableData:[],tempSelectDataArr:[],packageSelectColumns:[{type:"selection",width:60,align:"center"},{type:"expand",width:20,render:function(t,a){return t(d,{style:{padding:0},props:{row:a.row,editObj:e.editObj,tempSelectDataArrzj:e.tempSelectDataArrzj}})}},{title:"套餐名称",key:"nameCn",align:"center"},{title:"人民币价格",key:"cny",width:150,align:"center",render:function(t,a){var c=a.row,r=(a.column,a.index);if("1"===e.editObj.groupType){var i=function(){if(e.tempSelectDataArr.length){var t=!1;e.tempSelectDataArr.forEach((function(a){a.id===e.packageSelectTableData[r].id&&(t=!0,a.cny=e.packageSelectTableData[r].cny,a.hkd=e.packageSelectTableData[r].hkd,a.usd=e.packageSelectTableData[r].usd)})),t||e.tempSelectDataArr.push(e.packageSelectTableData[r])}else e.tempSelectDataArr.push(e.packageSelectTableData[r]);e.editObj.packages.forEach((function(t){t.packageId===e.packageSelectTableData[r].id&&(t.cny=e.packageSelectTableData[r].cny,e.$nextTick((function(){document.querySelector(".errorPrice")?e.twicePriceFormat=!0:e.twicePriceFormat=!1})))}))};return e.editObj.packages.forEach((function(t){t.packageId===e.packageSelectTableData[r].id&&(e.packageSelectTableData[r]._checked=!0)})),t("div",[t("Input",{attrs:{maxlength:"13",placeholder:"请输入二次定价价格"},model:{value:e.packageSelectTableData[r].cny,callback:function(t){e.$set(e.packageSelectTableData[r],"cny",t)}},on:{"on-change":i}})," ",/^(([1-9]\d{0,9})|0)(\.\d{0,2})?$/.test(e.packageSelectTableData[r].cny)?null:t("p",{class:"errorPrice",style:"text-align: left;color:red"},["二次定价格最高支持10位整数和2位小数正数或零 "])," "])}return t("span",[" ",c.cny," "])}},{title:"港币价格",key:"hkd",width:150,align:"center",render:function(t,a){var c=a.row,r=(a.column,a.index);if("1"===e.editObj.groupType){var i=function(){if(e.tempSelectDataArr.length){var t=!1;e.tempSelectDataArr.forEach((function(a){a.id===e.packageSelectTableData[r].id&&(t=!0,a.cny=e.packageSelectTableData[r].cny,a.hkd=e.packageSelectTableData[r].hkd,a.usd=e.packageSelectTableData[r].usd)})),t||e.tempSelectDataArr.push(e.packageSelectTableData[r])}else e.tempSelectDataArr.push(e.packageSelectTableData[r]);e.editObj.packages.forEach((function(t){t.packageId===e.packageSelectTableData[r].id&&(t.hkd=e.packageSelectTableData[r].hkd)})),e.$nextTick((function(){document.querySelector(".errorPrice")?e.twicePriceFormat=!0:e.twicePriceFormat=!1}))};return e.editObj.packages.forEach((function(t){t.packageId===e.packageSelectTableData[r].id&&(e.packageSelectTableData[r]._checked=!0)})),t("div",[t("Input",{attrs:{placeholder:"请输入二次定价价格"},model:{value:e.packageSelectTableData[r].hkd,callback:function(t){e.$set(e.packageSelectTableData[r],"hkd",t)}},on:{"on-change":i}}),/^(([1-9]\d{0,9})|0)(\.\d{0,2})?$/.test(e.packageSelectTableData[r].hkd)?null:t("p",{class:"errorPrice",style:"text-align: left;color:red"},["二次定价格最高支持10位整数和2位小数正数或零 "])," "])}return t("span",[" ",c.hkd," "])}},{title:"美元价格",key:"usd",width:150,align:"center",render:function(t,a){var c=a.row,r=(a.column,a.index);if("1"===e.editObj.groupType){var i=function(){if(e.tempSelectDataArr.length){var t=!1;e.tempSelectDataArr.forEach((function(a){a.id===e.packageSelectTableData[r].id&&(t=!0,a.cny=e.packageSelectTableData[r].cny,a.hkd=e.packageSelectTableData[r].hkd,a.usd=e.packageSelectTableData[r].usd)})),t||e.tempSelectDataArr.push(e.packageSelectTableData[r])}else e.tempSelectDataArr.push(e.packageSelectTableData[r]);e.editObj.packages.forEach((function(t){t.packageId===e.packageSelectTableData[r].id&&(t.usd=e.packageSelectTableData[r].usd,e.$nextTick((function(){document.querySelector(".errorPrice")?e.twicePriceFormat=!0:e.twicePriceFormat=!1})))}))};return e.editObj.packages.forEach((function(t){t.packageId===e.packageSelectTableData[r].id&&(e.packageSelectTableData[r]._checked=!0)})),t("div",[t("Input",{attrs:{placeholder:"请输入二次定价价格"},model:{value:e.packageSelectTableData[r].usd,callback:function(t){e.$set(e.packageSelectTableData[r],"usd",t)}},on:{"on-change":i}})," ",/^(([1-9]\d{0,9})|0)(\.\d{0,2})?$/.test(e.packageSelectTableData[r].usd)?null:t("p",{class:"errorPrice",style:"text-align: left;color:red"},["二次定价格最高支持10位整数和2位小数正数或零 "])," "])}return t("span",[" ",c.usd," "])}}],packageSelectTableCurrent:1,packageSelectTableTotal:0,info:[]}},methods:{deletePackages:function(e){this.editObj.packages.splice(e,1)},formatMockData:function(e){function t(e){return e.replace(/\_(\w)/g,(function(e,t){return t.toUpperCase()}))}var a=[];return e.forEach((function(e){var c={};Object.keys(e).forEach((function(a){c[t(a)]=e[a].value})),a.push(c)})),console.log(a[0]),a},init:function(){this.getPackageGroupList(0),this.getPackageList(0)},changeType:function(){},getPackageGroupList:function(e){var t=this;0===e&&(this.page=1),Object(i["x"])({groupName:this.searchObj.groupName,groupType:this.searchObj.groupType,pageSize:10,pageNum:this.page}).then((function(e){if("0000"===e.code){var a=e.data,c=[];a.records.map((function(e,t){c.push(e)})),t.selectionList.forEach((function(e){c.forEach((function(a){a.groupId==e.groupId&&t.$set(a,"_checked",!0)}))})),t.tableData=c,t.total=a.total}}))},updatePackageGroup:function(){var e=this;Object(i["A"])().then((function(t){"0000"===t.code&&e.$Notice.success({title:"操作提示",desc:"更新成功"})}))},deletePackageGroup:function(e){var t=this;Object(i["m"])({groupId:e}).then((function(e){"0000"===e.code&&(t.init(),t.$Notice.success({title:"操作提示",desc:"删除成功"}))}))},loadByPackageSelectTableCurrentPage:function(e){this.getPackageList(e)},selectionDropdwon:function(e){this.packagegroupName="",e&&(this.packageSelectTableCurrent=1,this.getPackageList(0))},searchPackagegroupName:function(){this.getPackageList(0)},getPackageList:function(e){var t=this;0===e&&(this.packageSelectTableCurrent=1),Object(i["r"])({pageNum:e,pageSize:10,groupId:"Update"===this.operationType?this.editObj.groupId:"",packageName:this.packagegroupName}).then((function(e){"0000"===e.code&&(t.editObj.packages&&t.editObj.packages.length&&t.editObj.packages.forEach((function(t,a){e.data.records.forEach((function(e){e.id===t.packageId&&(e["_checked"]=!0)}))})),t.tempSelectDataArr.length&&t.tempSelectDataArr.forEach((function(t){e.data.records.forEach((function(e){e.id===t.id&&(e.cny=t.cny,e.hkd=t.hkd,e.usd=t.usd)}))})),t.packageSelectTableData=e.data.records,t.packageSelectTableTotal=e.data.total)}))},submit:function(e){var t=this;this.$nextTick((function(){document.querySelector(".errorPrice")?t.twicePriceFormat=!0:t.twicePriceFormat=!1})),this.$refs[e].validate((function(e){var a;if(e)if("2"===t.editObj.choseType){var c=new FormData;c.append("file",t.file),c.append("groupName",t.editObj.groupName),c.append("cooperationMode",t.editObj.cooperationMode),c.append("groupType",t.editObj.groupType),"Add"!=t.operationType&&c.append("groupId",t.editObj.groupId),Object(i["j"])(c).then((function(e){"0000"===e.code&&(t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.file=null,t.tempSelectDataArr=[],t.tempSelectDataArrzj=[],t.packageGroupEditFlag=!1,t.init(),t.uploadList=[],t.reset("editObj"))}))}else{if("1"===t.editObj.choseType&&t.editObj.packages.length<1)return void t.$Message.warning("套餐组列表不能为空");a="Add"===t.operationType?i["e"]:i["z"],a(t.editObj).then((function(e){"0000"===e.code&&(t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.tempSelectDataArr=[],t.tempSelectDataArrzj=[],t.init(),t.packageGroupEditFlag=!1,t.reset("editObj"))}))}}))},reset:function(e,t){this.packageSelectTableData=[],this.packageSelectTableTotal=0,"Update"===this.operationType&&1===t?this.$refs[e].fields.forEach((function(e){"groupType"!==e.prop&&e.resetField()})):this.$refs[e].resetFields()},reseteditObj:function(e,t){var a=this;if(this.packageSelectTableData=[],this.packageSelectTableTotal=0,"Update"===this.operationType&&1===t?this.$refs[e].fields.forEach((function(e){"groupType"!==e.prop&&e.resetField()})):this.$refs[e].resetFields(),"Update"===this.operationType){var c=this.editObjreset;this.editObj={groupName:c.groupName,cooperationMode:c.cooperationMode,groupType:c.groupType,groupId:c.groupId,choseType:"1"},Object(i["w"])({groupId:c.groupId,groupType:c.groupType,pageSize:-1,pageNum:1}).then((function(e){if("0000"===e.code&&e.data){var t=e.data.records.map((function(e){var t=e.refuelPackList.map((function(e){return{refuelId:e.id,cny:e.cny,hkd:e.hkd,usd:e.usd}}));return{packageName:e.packageName,packageId:e.id,cny:e.cny,hkd:e.hkd,usd:e.usd,refuelDetailList:t}}));a.editObj.packages=t}}))}},onRowClick:function(e,t){},selectPackage:function(e,t){var a=this;e.forEach((function(t,c){a.packageSelectTableData.splice(),a.packageSelectTableData[c]._expanded=!1;var r=a.packageSelectTableData.findIndex((function(e){return e.id==t.id}));e[c]._index=r}));var c=e.map((function(e){a.packageSelectTableData[e._index]._checked=!0;var t=e.refuelList.map((function(e){return{refuelId:e.id,cny:e.cny,hkd:e.hkd,usd:e.usd}}));return{packageName:e.nameCn,packageId:e.id,cny:e.cny,hkd:e.hkd,usd:e.usd,refuelDetailList:t}}));if(this.editObj.packages&&this.editObj.packages.length){var r=[];c.forEach((function(e){var t=!0;a.editObj.packages.forEach((function(a){a.packageId===e.packageId&&(t=!1)})),t&&r.push(e)})),this.editObj.packages=this.editObj.packages.concat(r)}else this.$set(this.editObj,"packages",c)},cancelPackage:function(e,t){var a=this;this.editObj.packages&&this.editObj.packages.length&&(this.editObj.packages=this.editObj.packages.filter((function(e){return e.packageId!==t.id}))),this.packageSelectTableData.forEach((function(e,c){e.id===t.id&&(e._checked=!1,a.packageSelectTableData.splice(),a.packageSelectTableData[c]._expanded=!1)}))},cancelPackageAll:function(e,t){var a=this;this.editObj.packages&&this.editObj.packages.length&&(this.editObj.packages=[]),this.packageSelectTableData.forEach((function(e,t){e._checked=!1,a.packageSelectTableData.splice(),a.packageSelectTableData[t]._expanded=!1}))},loadByPage:function(e){this.getPackageGroupList(e)},loadByPackagePage:function(e){var t=this;0===e&&(this.packageCurrentPage=1),Object(i["w"])({groupId:this.currentRow.groupId,groupType:this.currentRow.groupType,pageSize:10,pageNum:this.packageCurrentPage}).then((function(e){"0000"===e.code&&e.data&&(t.packageTableData=e.data.records,t.packageTotal=e.data.total)}))},searchPackageGroup:function(){this.getPackageGroupList(0)},packageGroupAdd:function(){this.packageGroupTitle="套餐组新增",this.operationType="Add",this.editObj={groupType:"",groupName:"",cooperationMode:"",choseType:"2",packages:[],file:""},this.packageGroupEditFlag=!0},packageGroupInfo:function(e){this.packageGroupTitle="套餐组详情",this.operationType="Info",this.packageGroupInfoFlag=!0,this.info.groupName=e.groupName,this.info.cooperationMode=e.cooperationMode,this.info.groupType=e.groupType,this.currentRow=e,this.loadByPackagePage(0)},packageGroupEdit:function(e){var t=this;this.packageGroupTitle="套餐组编辑",this.operationType="Update",this.editObjreset=e,Object(i["w"])({groupId:e.groupId,groupType:e.groupType,pageSize:-1,pageNum:1}).then((function(a){if("0000"===a.code&&a.data){var c=a.data.records.map((function(e){var t=e.refuelPackList.map((function(e){return{refuelId:e.id,cny:e.cny,hkd:e.hkd,usd:e.usd}}));return{packageName:e.packageName,packageId:e.id,cny:e.cny,hkd:e.hkd,usd:e.usd,refuelDetailList:t}}));t.editObj.packages=c,t.editObj={groupName:e.groupName,cooperationMode:e.cooperationMode,groupType:e.groupType,groupId:e.groupId,packages:c,choseType:"1"}}})),this.packageGroupEditFlag=!0},packageGroupDel:function(e){var t=this;this.$Modal.confirm({title:"确认删除？",onOk:function(){t.deletePackageGroup(e)}})},exportfile:function(e){var t=this;Object(i["o"])({groupId:e.groupId,groupName:e.groupName,groupType:e.groupType,pageSize:-1,pageNum:-1}).then((function(e){var a=e.data,c=decodeURI(e.headers["content-disposition"].match(/=(.*)$/)[1]);if("download"in document.createElement("a")){var r=t.$refs.downloadLink,i=URL.createObjectURL(a);r.download=c,r.href=i,r.click(),URL.revokeObjectURL(i)}else navigator.msSaveBlob(a,c)})).finally((function(){}))},handleRowChange:function(e){var t=this;this.selection=e,e.map((function(e,a){var c=!0;t.selectionList.map((function(t,a){e.groupId===t.groupId&&(c=!1)})),c&&t.selectionList.push(e)}))},cancelSigle:function(e,t){var a=this;this.selectionList.forEach((function(e,c){e.groupId===t.groupId&&a.selectionList.splice(c,1)}))},cancelAll:function(e,t){this.selectionList=[]},deleteList:function(){var e=this,t=this.selectionList.length;t<1?this.$Message.warning("请至少选择一条记录"):this.$Modal.confirm({title:"确认删除？",onOk:function(){var t=[];e.selectionList.map((function(e,a){t.push(e.groupId)})),Object(i["l"])(t).then((function(t){"0000"===t.code&&(e.init(),e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.selection=[],e.selectionList=[])}))}})},cooperativeApproval:function(e,t){var a=this;Object(i["g"])({groupId:e.groupId,status:t}).then((function(e){"0000"===e.code&&(a.init(),a.$Notice.success({title:"操作提示",desc:"操作成功"}))}))},cancelModal:function(){this.packageGroupEditFlag=!1,this.packageTableData=[],this.packageSelectTableData=[],this.tempSelectDataArr=[],this.tempSelectDataArrzj=[],this.packageSelectTableCurrent=1,this.packageCurrentPage=1,this.file=null,this.uploadList=[],this.$refs["editObj"].resetFields()},fileSuccess:function(e,t,a){this.message="请先下载模板文件，并按格式填写后上传"},handleError:function(e,t){var a=this;setTimeout((function(){a.uploading=!1,a.$Notice.warning({title:"错误提示",desc:"上传失败！"})}),3e3)},handleBeforeUpload:function(e,t){return/^.+(\.csv)$/.test(e.name)?(this.file=e,this.uploadList=t):this.$Notice.warning({title:"文件格式不正确",desc:"文件 "+e.name+" 格式不正确，请上传csv格式文件。"}),!1},fileUploading:function(e,t,a){this.message="文件上传中、待进度条消失后再操作"},removeFile:function(){this.file=""},downloadFile:function(){"1"===this.editObj.groupType?this.$refs.modelTable.exportCsv({filename:"二次定价套餐列表",columns:this.modelColumns,data:this.modelData}):this.$refs.modelTable.exportCsv({filename:"非二次定价套餐列表",columns:this.NotmodelColumns,data:this.NotmodelData})}},mounted:function(){this.init()}},h=g,f=(a("6e6f"),Object(p["a"])(h,c,r,!1,null,null,null));t["default"]=f.exports},c5f2:function(e,t,a){},e3b7:function(e,t,a){"use strict";a.d(t,"t",(function(){return n})),a.d(t,"s",(function(){return o})),a.d(t,"k",(function(){return l})),a.d(t,"u",(function(){return s})),a.d(t,"n",(function(){return p})),a.d(t,"p",(function(){return u})),a.d(t,"d",(function(){return d})),a.d(t,"a",(function(){return g})),a.d(t,"f",(function(){return h})),a.d(t,"x",(function(){return f})),a.d(t,"w",(function(){return k})),a.d(t,"v",(function(){return m})),a.d(t,"r",(function(){return b})),a.d(t,"A",(function(){return y})),a.d(t,"l",(function(){return S})),a.d(t,"m",(function(){return v})),a.d(t,"e",(function(){return T})),a.d(t,"z",(function(){return D})),a.d(t,"g",(function(){return j})),a.d(t,"j",(function(){return O})),a.d(t,"o",(function(){return w})),a.d(t,"i",(function(){return x})),a.d(t,"h",(function(){return I})),a.d(t,"y",(function(){return P})),a.d(t,"q",(function(){return N})),a.d(t,"c",(function(){return A})),a.d(t,"b",(function(){return E}));var c=a("66df"),r="pms",i="cms",n=function(e){return c["a"].request({url:i+"/channel/distributors/detail",data:e,method:"post"})},o=function(e){return c["a"].request({url:i+"/channel/distributors/info",params:e,method:"get"})},l=function(e){return c["a"].request({url:i+"/channel/distributors/delete",data:e,method:"delete"})},s=function(e){return c["a"].request({url:i+"/channel/distributors/record",data:e,method:"post"})},p=function(e){return c["a"].request({url:i+"/channel/distributors/record/export/"+e.corpId,method:"get",responseType:"blob"})},u=function(e){return c["a"].request({url:i+"/channel/distributors/remunerate/export",params:e,method:"post",responseType:"blob"})},d=function(e){return c["a"].request({url:i+"/channel/newChannel",data:e,method:"post"})},g=function(e){return c["a"].request({url:i+"/channel/updateChannel",data:e,method:"put"})},h=function(e){return c["a"].request({url:i+"/channel/approvalChannel",params:e,method:"put"})},f=function(e){return c["a"].request({url:r+"/packageGroup/queryPackageGroupRelation",params:e,method:"get"})},k=function(e){return c["a"].request({url:r+"/packageGroup/queryPackageGroupDetail",params:e,method:"get"})},m=function(e){return c["a"].request({url:r+"/packageGroup/purchasePart",params:e,method:"get"})},b=function(e){return c["a"].request({url:r+"/packageGroup/queryPackageList",params:e,method:"get"})},y=function(e){return c["a"].request({url:r+"/update",params:e,method:"put"})},S=function(e){return c["a"].request({url:r+"/packageGroup/deleteBatchPackageGroup",data:e,method:"delete"})},v=function(e){return c["a"].request({url:r+"/packageGroup/deletePackageGroup",params:e,method:"delete"})},T=function(e){return c["a"].request({url:r+"/packageGroup/newPackageGroup",data:e,method:"post"})},D=function(e){return c["a"].request({url:r+"/packageGroup/updatePackageGroup",data:e,method:"put"})},j=function(e){return c["a"].request({url:r+"/packageGroup/approvalPackageGroup",params:e,method:"put"})},O=function(e){return c["a"].request({url:r+"/packageGroup/create/byFile",data:e,method:"post",contentType:"multipart/form-data"})},w=function(e){return c["a"].request({url:r+"/packageGroup/packageGroupDetailExport",params:e,method:"get",responseType:"blob"})},x=function(e){return c["a"].request({url:i+"/channel/distributors/channelBill",data:e,method:"post"})},I=function(e){return c["a"].request({url:i+"/channel/distributors/channelBill/export",data:e,method:"post"})},P=function(e){return c["a"].request({url:i+"/channel/getCorpFlowDetail",params:e,method:"get"})},N=function(e){return c["a"].request({url:i+"/channel/corpFlowDetailExport",params:e,method:"get"})},A=function(e){return c["a"].request({url:i+"/channel/distributors/card/suspend",params:e,method:"get"})},E=function(e){return c["a"].request({url:i+"/channel/distributors/card/recover",params:e,method:"get"})}}}]);