/**
 * CMI 项目菜单数据结构定义
 * 基于原 cmi-web 项目的菜单结构
 */

import type { AppCustomRouteRecordRaw } from '@/types/router'

// CMI 菜单项接口
export interface CMIMenuItem {
  id: string
  name: string // 路由名称
  path: string // 路由路径
  component?: string // 组件路径
  redirect?: string // 重定向路径
  meta: {
    title: string // 菜单标题
    icon?: string // 菜单图标
    access?: string[] // 权限控制
    hideInMenu?: boolean // 是否在菜单中隐藏
    notCache?: boolean // 是否不缓存
    showAlways?: boolean // 是否总是显示
    href?: string // 外部链接
  }
  children?: CMIMenuItem[]
}

// 从权限数组生成的路由配置
export interface CMIRouteConfig {
  path: string
  name: string
  component: string // 修改为字符串路径，用于服务端路由生成
  redirect?: string
  meta: {
    title: string
    icon?: string
    hidden?: boolean
    alwaysShow?: boolean
    roles?: string[]
    noCache?: boolean
    affix?: boolean
  }
  children?: CMIRouteConfig[]
}

// CMI 项目的动态路由映射 - 基于原 cmi-web 项目的完整路由结构
export const CMI_ROUTE_MAP: Record<string, CMIRouteConfig> = {

  // 首页
  home: {
    path: '/newcmi',
    name: 'Dashboard',
    component: '#', // 使用 # 表示 Layout 组件
    redirect: '/newcmi/home',
    meta: {
      title: '首页',
      icon: 'vi-ant-design:dashboard-filled',
      alwaysShow: true,
      hidden: true
    },
    children: [
      {
        path: 'home',
        name: 'Home',
        component: 'views/CMI/Home/Home', // 使用相对路径
        meta: {
          title: '首页',
          noCache: true,
          affix: true
        }
      }
    ]
  },

  // 系统管理
  system_mngr: {
    path: '/newcmi/system',
    name: 'SystemManagement',
    component: '#', // 使用 # 表示 Layout 组件
    meta: {
      title: '系统管理',
      icon: 'vi-ep:setting',
      alwaysShow: true
    },
    children: [
      {
        path: 'account',
        name: 'AccountManagement',
        component: 'views/CMI/System/Account', // 使用相对路径
        meta: {
          title: '账户管理',
          roles: ['account_list']
        }
      },
      {
        path: 'password',
        name: 'PasswordManagement',
        component: 'views/CMI/System/Password',
        meta: {
          title: '账户密码管理',
          roles: ['pwd_mngr']
        }
      },
      {
        path: 'role',
        name: 'RoleManagement',
        component: 'views/CMI/System/Role',
        meta: {
          title: '角色管理',
          roles: ['pri_mngr']
        }
      },
      {
        path: 'login-log',
        name: 'LoginLog',
        component: 'views/CMI/System/LoginLog',
        meta: {
          title: '登录日志',
          roles: ['login_mngr']
        }
      }
    ]
  },

  // 渠道自服务
  channel_mngr: {
    path: '/newcmi/channel',
    name: 'ChannelSelfService',
    component: '#',
    meta: {
      title: '渠道自服务',
      icon: 'vi-ep:shop',
      alwaysShow: true
    },
    children: [
      // 测试页面
      {
        path: 'test',
        name: 'ChannelTest',
        component: 'views/CMI/Channel/Test/index',
        meta: {
          title: '测试页面',
          roles: ['test']
        }
      },
      // 账户管理相关
      {
        path: 'deposit',
        name: 'DepositManagement',
        component: 'views/CMI/Channel/Deposit/index',
        meta: {
          title: '充值管理',
          roles: ['deposit_mngr']
        }
      },
      {
        path: 'deposit/marketing-account',
        name: 'MarketingAccount',
        component: 'views/CMI/Channel/Deposit/MarketingAccount',
        meta: {
          title: '营销账户',
          roles: ['marketingAccount'],
          hidden: true
        }
      },
      // 库存管理相关
      {
        path: 'stock',
        name: 'StockManagement',
        component: 'views/CMI/Channel/Stock/index',
        meta: {
          title: '库存管理',
          roles: ['stock_mngr']
        }
      },
      // 订单管理相关
      {
        path: 'order',
        name: 'OrderManagement',
        component: 'views/CMI/Channel/Order/index',
        meta: {
          title: '订单管理',
          roles: ['order_mngr']
        }
      },
      // 流量池管理
      {
        path: 'flowpool',
        name: 'FlowPoolManagement',
        component: 'views/CMI/Channel/FlowPool/index',
        meta: {
          title: '流量池管理',
          roles: ['flowpool_mngr']
        }
      },
      // 业务管理相关
      {
        path: 'aqcode',
        name: 'AQCodeManagement',
        component: 'views/CMI/Channel/AQCode/index',
        meta: {
          title: 'AQ码管理',
          roles: ['aqCode_mngr']
        }
      },
      {
        path: 'fuelpack',
        name: 'FuelPackManagement',
        component: 'views/CMI/Channel/FuelPack/index',
        meta: {
          title: '燃料包管理',
          roles: ['channelfuelPack_mngr']
        }
      },
      {
        path: 'package',
        name: 'PackageManagement',
        component: 'views/CMI/Channel/Package/index',
        meta: {
          title: '套餐管理',
          roles: ['channelpackage']
        }
      },
      {
        path: 'buymeal',
        name: 'BuyMealManagement',
        component: 'views/CMI/Channel/BuyMeal/index',
        meta: {
          title: '购买套餐',
          roles: ['buymeal_mngr']
        }
      },
      // 服务支持相关
      {
        path: 'support',
        name: 'SupportManagement',
        component: 'views/CMI/Channel/Support/index',
        meta: {
          title: '服务支持',
          roles: ['support_mngr']
        }
      },
      // 地址管理
      {
        path: 'address',
        name: 'AddressManagement',
        component: 'views/CMI/Channel/Address/index',
        meta: {
          title: '地址管理',
          roles: ['address_mngr']
        }
      }
    ]
  }
}

/**
 * 根据用户权限生成动态路由配置
 * @param permissions 用户权限数组
 * @returns 路由配置数组
 */
export const generateCMIRoutes = (permissions: string[]): CMIRouteConfig[] => {
  const routes: CMIRouteConfig[] = []

  // 遍历路由映射，根据权限生成路由
  Object.entries(CMI_ROUTE_MAP).forEach(([key, routeConfig]) => {
    // 检查是否有权限访问该路由
    if (hasPermission(permissions, key, routeConfig)) {
      const route = { ...routeConfig }

      // 处理子路由
      if (route.children) {
        route.children = route.children.filter((child) => hasChildPermission(permissions, child))

        // 如果没有可访问的子路由，则不添加该路由
        if (route.children.length === 0) {
          return
        }
      }

      routes.push(route)
    }
  })

  return routes
}

/**
 * 检查是否有权限访问路由
 */
const hasPermission = (permissions: string[], key: string, route: CMIRouteConfig): boolean => {
  // 如果路由没有权限要求，则允许访问
  if (!route.meta.roles || route.meta.roles.length === 0) {
    return permissions.includes(key)
  }

  // 检查用户是否有任一所需权限
  return route.meta.roles.some((role) => permissions.includes(role)) || permissions.includes(key)
}

/**
 * 检查是否有权限访问子路由
 */
const hasChildPermission = (permissions: string[], child: CMIRouteConfig): boolean => {
  if (!child.meta.roles || child.meta.roles.length === 0) {
    return true
  }

  return child.meta.roles.some((role) => permissions.includes(role))
}

/**
 * 将 CMI 路由配置转换为 Vue Router 格式
 * 现在 CMI 路由配置已经是正确的格式，直接返回
 */
export const transformCMIRoutesToVueRouter = (
  cmiRoutes: CMIRouteConfig[]
): AppCustomRouteRecordRaw[] => {
  return cmiRoutes as AppCustomRouteRecordRaw[]
}
