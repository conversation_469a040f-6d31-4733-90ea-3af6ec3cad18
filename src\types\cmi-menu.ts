/**
 * CMI 项目菜单数据结构定义
 * 基于原 cmi-web 项目的菜单结构
 */

import type { AppCustomRouteRecordRaw } from '@/types/router'

// CMI 菜单项接口
export interface CMIMenuItem {
  id: string
  name: string // 路由名称
  path: string // 路由路径
  component?: string // 组件路径
  redirect?: string // 重定向路径
  meta: {
    title: string // 菜单标题
    icon?: string // 菜单图标
    access?: string[] // 权限控制
    hideInMenu?: boolean // 是否在菜单中隐藏
    notCache?: boolean // 是否不缓存
    showAlways?: boolean // 是否总是显示
    href?: string // 外部链接
  }
  children?: CMIMenuItem[]
}

// 从权限数组生成的路由配置
export interface CMIRouteConfig {
  path: string
  name: string
  component: string // 修改为字符串路径，用于服务端路由生成
  redirect?: string
  meta: {
    title: string
    icon?: string
    hidden?: boolean
    alwaysShow?: boolean
    roles?: string[]
    noCache?: boolean
    affix?: boolean
  }
  children?: CMIRouteConfig[]
}

// CMI 项目的动态路由映射 - 基于原 cmi-web 项目的完整路由结构
export const CMI_ROUTE_MAP: Record<string, CMIRouteConfig> = {
  // 首页
  home: {
    path: '/dashboard',
    name: 'Dashboard',
    component: '#', // 使用 # 表示 Layout 组件
    redirect: '/dashboard/analysis',
    meta: {
      title: '首页',
      icon: 'vi-ant-design:dashboard-filled',
      alwaysShow: true
    },
    children: [
      {
        path: 'analysis',
        name: 'Analysis',
        component: 'views/CMI/Home/Home', // 使用相对路径
        meta: {
          title: '首页',
          noCache: true,
          affix: true
        }
      }
    ]
  },

  // 系统管理
  system_mngr: {
    path: '/system',
    name: 'SystemManagement',
    component: '#', // 使用 # 表示 Layout 组件
    meta: {
      title: '系统管理',
      icon: 'vi-ep:setting',
      alwaysShow: true
    },
    children: [
      {
        path: 'account',
        name: 'AccountManagement',
        component: 'views/CMI/System/Account', // 使用相对路径
        meta: {
          title: '账户管理',
          roles: ['account_list']
        }
      },
      {
        path: 'password',
        name: 'PasswordManagement',
        component: 'views/CMI/System/Password',
        meta: {
          title: '账户密码管理',
          roles: ['pwd_mngr']
        }
      },
      {
        path: 'role',
        name: 'RoleManagement',
        component: 'views/CMI/System/Role',
        meta: {
          title: '角色管理',
          roles: ['pri_mngr']
        }
      },
      {
        path: 'login-log',
        name: 'LoginLog',
        component: 'views/CMI/System/LoginLog',
        meta: {
          title: '登录日志',
          roles: ['login_mngr']
        }
      }
    ]
  },

  // 资源管理
  resource_mngr: {
    path: '/resource',
    name: 'ResourceManagement',
    component: '#',
    meta: {
      title: '资源管理',
      icon: 'vi-ep:files',
      alwaysShow: true
    },
    children: [
      {
        path: 'msisdn',
        name: 'MSISDNManagement',
        component: 'views/CMI/Resource/MSISDN',
        meta: {
          title: 'MSISDN管理',
          roles: ['msisdn']
        }
      },
      {
        path: 'iccid',
        name: 'ICCIDManagement',
        component: 'views/CMI/Resource/ICCID',
        meta: {
          title: 'ICCID管理',
          roles: ['iccid']
        }
      },
      {
        path: 'imsi',
        name: 'IMSIManagement',
        component: 'views/CMI/Resource/IMSI',
        meta: {
          title: 'IMSI管理',
          roles: ['imsi']
        }
      },
      {
        path: 'supply-imsi',
        name: 'SupplyIMSIManagement',
        component: 'views/CMI/Resource/SupplyIMSI',
        meta: {
          title: '消息上报IMSI管理',
          roles: ['supplyImsi']
        }
      }
    ]
  },

  // 产品管理
  product_mngr: {
    path: '/product',
    name: 'ProductManagement',
    component: '#',
    meta: {
      title: '产品管理',
      icon: 'vi-ep:goods',
      alwaysShow: true
    },
    children: [
      {
        path: 'make-card',
        name: 'MakeCardManagement',
        component: 'views/CMI/Product/MakeCard',
        meta: {
          title: '制卡管理',
          roles: ['makeCard']
        }
      },
      {
        path: 'master-card',
        name: 'MasterCardManagement',
        component: 'views/CMI/Product/MasterCard',
        meta: {
          title: '主卡管理',
          roles: ['masterCard']
        }
      },
      {
        path: 'card-pool',
        name: 'CardPoolManagement',
        component: 'views/CMI/Product/CardPool',
        meta: {
          title: '卡池管理',
          roles: ['cardPool']
        }
      },
      {
        path: 'vimsi',
        name: 'VIMSIManagement',
        component: 'views/CMI/Product/VIMSI',
        meta: {
          title: 'VIMSI管理',
          roles: ['vimsi']
        }
      }
    ]
  },

  // 客户管理
  customer_mngr: {
    path: '/customer',
    name: 'CustomerManagement',
    component: '#',
    meta: {
      title: '客户管理',
      icon: 'vi-ep:user',
      alwaysShow: true
    },
    children: [
      {
        path: 'channel',
        name: 'ChannelManagement',
        component: 'views/CMI/Customer/Channel',
        meta: {
          title: '渠道商管理',
          roles: ['channelManage']
        }
      },
      {
        path: 'cooperative',
        name: 'CooperativeManagement',
        component: 'views/CMI/Customer/Cooperative',
        meta: {
          title: '合作商管理',
          roles: ['cooperativeManage']
        }
      }
    ]
  }
}

/**
 * 根据用户权限生成动态路由配置
 * @param permissions 用户权限数组
 * @returns 路由配置数组
 */
export const generateCMIRoutes = (permissions: string[]): CMIRouteConfig[] => {
  const routes: CMIRouteConfig[] = []

  // 遍历路由映射，根据权限生成路由
  Object.entries(CMI_ROUTE_MAP).forEach(([key, routeConfig]) => {
    // 检查是否有权限访问该路由
    if (hasPermission(permissions, key, routeConfig)) {
      const route = { ...routeConfig }

      // 处理子路由
      if (route.children) {
        route.children = route.children.filter((child) => hasChildPermission(permissions, child))

        // 如果没有可访问的子路由，则不添加该路由
        if (route.children.length === 0) {
          return
        }
      }

      routes.push(route)
    }
  })

  return routes
}

/**
 * 检查是否有权限访问路由
 */
const hasPermission = (permissions: string[], key: string, route: CMIRouteConfig): boolean => {
  // 如果路由没有权限要求，则允许访问
  if (!route.meta.roles || route.meta.roles.length === 0) {
    return permissions.includes(key)
  }

  // 检查用户是否有任一所需权限
  return route.meta.roles.some((role) => permissions.includes(role)) || permissions.includes(key)
}

/**
 * 检查是否有权限访问子路由
 */
const hasChildPermission = (permissions: string[], child: CMIRouteConfig): boolean => {
  if (!child.meta.roles || child.meta.roles.length === 0) {
    return true
  }

  return child.meta.roles.some((role) => permissions.includes(role))
}

/**
 * 将 CMI 路由配置转换为 Vue Router 格式
 * 现在 CMI 路由配置已经是正确的格式，直接返回
 */
export const transformCMIRoutesToVueRouter = (
  cmiRoutes: CMIRouteConfig[]
): AppCustomRouteRecordRaw[] => {
  return cmiRoutes as AppCustomRouteRecordRaw[]
}
