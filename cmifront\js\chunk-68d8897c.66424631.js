(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-68d8897c"],{"00b4":function(t,e,n){"use strict";n("ac1f");var a=n("23e7"),r=n("c65b"),i=n("1626"),o=n("825a"),c=n("577e"),s=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),l=/./.test;a({target:"RegExp",proto:!0,forced:!s},{test:function(t){var e=o(this),n=c(t),a=e.exec;if(!i(a))return r(l,e,n);var s=r(a,e,n);return null!==s&&(o(s),!0)}})},"129f":function(t,e,n){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},"1a36":function(t,e,n){"use strict";n("5ed6")},"209e":function(t,e,n){},3177:function(t,e,n){"use strict";n.d(e,"c",(function(){return i})),n.d(e,"d",(function(){return o})),n.d(e,"a",(function(){return c})),n.d(e,"f",(function(){return s})),n.d(e,"e",(function(){return l})),n.d(e,"b",(function(){return u}));var a=n("66df"),r="/pms/api/v1/cardPoolMccGroup",i=function(t){return a["a"].request({url:r+"/getCardPoolGroup",data:t,method:"POST"})},o=function(t){return a["a"].request({url:r+"/getCardPoolGroupDetailNew",data:t,method:"POST"})},c=function(t){return a["a"].request({url:r+"/add",data:t,method:"POST"})},s=function(t){return a["a"].request({url:r+"/update",data:t,method:"POST"})},l=function(t){return a["a"].request({url:r+"/getCardPoolMcc",params:t,method:"get"})},u=function(t){return a["a"].request({url:r+"/batchDelete",data:t,method:"delete"})}},"3f7e":function(t,e,n){"use strict";var a=n("b5db"),r=a.match(/firefox\/(\d+)/i);t.exports=!!r&&+r[1]},"466d":function(t,e,n){"use strict";var a=n("c65b"),r=n("d784"),i=n("825a"),o=n("7234"),c=n("50c4"),s=n("577e"),l=n("1d80"),u=n("dc4a"),d=n("8aa5"),p=n("14c3");r("match",(function(t,e,n){return[function(e){var n=l(this),r=o(e)?void 0:u(e,t);return r?a(r,e,n):new RegExp(e)[t](s(n))},function(t){var a=i(this),r=s(t),o=n(e,a,r);if(o.done)return o.value;if(!a.global)return p(a,r);var l=a.unicode;a.lastIndex=0;var u,f=[],m=0;while(null!==(u=p(a,r))){var h=s(u[0]);f[m]=h,""===h&&(a.lastIndex=d(r,c(a.lastIndex),l)),m++}return 0===m?null:f}]}))},"4e82":function(t,e,n){"use strict";var a=n("23e7"),r=n("e330"),i=n("59ed"),o=n("7b0b"),c=n("07fa"),s=n("083a"),l=n("577e"),u=n("d039"),d=n("addb"),p=n("a640"),f=n("3f7e"),m=n("99f4"),h=n("1212"),g=n("ea83"),y=[],b=r(y.sort),v=r(y.push),C=u((function(){y.sort(void 0)})),I=u((function(){y.sort(null)})),k=p("sort"),O=!u((function(){if(h)return h<70;if(!(f&&f>3)){if(m)return!0;if(g)return g<603;var t,e,n,a,r="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:n=3;break;case 68:case 71:n=4;break;default:n=2}for(a=0;a<47;a++)y.push({k:e+a,v:n})}for(y.sort((function(t,e){return e.v-t.v})),a=0;a<y.length;a++)e=y[a].k.charAt(0),r.charAt(r.length-1)!==e&&(r+=e);return"DGBEFHACIJK"!==r}})),S=C||!I||!k||!O,x=function(t){return function(e,n){return void 0===n?-1:void 0===e?1:void 0!==t?+t(e,n)||0:l(e)>l(n)?1:-1}};a({target:"Array",proto:!0,forced:S},{sort:function(t){void 0!==t&&i(t);var e=o(this);if(O)return void 0===t?b(e):b(e,t);var n,a,r=[],l=c(e);for(a=0;a<l;a++)a in e&&v(r,e[a]);d(r,x(t)),n=c(r),a=0;while(a<n)e[a]=r[a++];while(a<l)s(e,a++);return e}})},"4ec9":function(t,e,n){"use strict";n("6f48")},"5ed6":function(t,e,n){},"61ef":function(t,e,n){},"6f25":function(t,e,n){"use strict";n("61ef")},"6f48":function(t,e,n){"use strict";var a=n("6d61"),r=n("6566");a("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),r)},"78c0":function(t,e,n){"use strict";n.d(e,"t",(function(){return o})),n.d(e,"a",(function(){return c})),n.d(e,"b",(function(){return s})),n.d(e,"z",(function(){return l})),n.d(e,"i",(function(){return u})),n.d(e,"j",(function(){return d})),n.d(e,"f",(function(){return p})),n.d(e,"s",(function(){return f})),n.d(e,"c",(function(){return m})),n.d(e,"d",(function(){return h})),n.d(e,"h",(function(){return g})),n.d(e,"g",(function(){return y})),n.d(e,"e",(function(){return b})),n.d(e,"v",(function(){return v})),n.d(e,"r",(function(){return C})),n.d(e,"n",(function(){return I})),n.d(e,"m",(function(){return k})),n.d(e,"w",(function(){return O})),n.d(e,"k",(function(){return S})),n.d(e,"o",(function(){return x})),n.d(e,"y",(function(){return D})),n.d(e,"l",(function(){return $})),n.d(e,"u",(function(){return j})),n.d(e,"x",(function(){return w})),n.d(e,"p",(function(){return T})),n.d(e,"q",(function(){return A}));n("99af");var a=n("66df"),r="/pms/api/v1/package",i="/oms/api/v1",o=function(t){return a["a"].request({url:r+"/getList",data:t,method:"POST"})},c=function(t){return a["a"].request({url:r+"/add",data:t,method:"POST"})},s=function(t){return a["a"].request({url:r+"/addPhoto",data:t,method:"POST",contentType:"multipart/form-data"})},l=function(t){return a["a"].request({url:r+"/update",data:t,method:"POST",contentType:"multipart/form-data"})},u=function(t,e){return a["a"].request({url:r+"/batchUpdate",data:t,method:"POST"})},d=function(t,e){return a["a"].request({url:r+"/check/".concat(t,"/").concat(e),method:"PUT"})},p=function(t){return a["a"].request({url:r+"/batchDelete",data:t,method:"delete"})},f=function(t){return a["a"].request({url:"/cms/api/v1/terminal"+"/settleRule/".concat(t),method:"GET"})},m=function(t){return a["a"].request({url:r+"/batchDelete",data:t,method:"post",contentType:"multipart/form-data"})},h=function(t){return a["a"].request({url:r+"/batchUpdatePackage",data:t,method:"post",contentType:"multipart/form-data"})},g=function(t){return a["a"].request({url:r+"/selectTask",params:t,method:"get",contentType:"multipart/form-data"})},y=function(t){return a["a"].request({url:r+"/fileUpload",params:t,method:"post",responseType:"blob"})},b=function(t){return a["a"].request({url:r+"/batchAuth",params:t,method:"post"})},v=function(t){return a["a"].request({url:r+"/getRefuelList",data:t,method:"post"})},C=function(t){return a["a"].request({url:r+"/getDetailsRefuelList",data:t,method:"post"})},I=function(t){return a["a"].request({url:r+"/exportList",data:t,method:"post"})},k=function(t){return a["a"].request({url:r+"/exportPackageCountryList",data:t,method:"post"})},O=function(t){return a["a"].request({url:"/pms/api/v1/upccTemplate/packageGetUpcc",params:t,method:"get"})},S=function(t){return a["a"].request({url:"pms/api/v1/cardPoolMccGroup/packageGetCardPool",params:t,method:"get"})},x=function(t){return a["a"].request({url:r+"/getPackageCardPool",params:t,method:"POST"})},D=function(t){return a["a"].request({url:"/pms/api/v1/directional/packageGetDirectional",params:t,method:"get"})},$=function(t){return a["a"].request({url:r+"/deatilGetDirect",params:t,method:"POST"})},j=function(t){return a["a"].request({url:"/cms/api/v1/packageCard/IsPackageSale",params:t,method:"get"})},w=function(t){return a["a"].request({url:r+"/getSelfPackageFlowinfoMcc",data:t,method:"post"})},T=function(t){return a["a"].request({url:i+"/country/getContinent",data:t,method:"get"})},A=function(t){return a["a"].request({url:r+"/getSelfPackageFlowinfoMccNew",data:t,method:"post"})}},"841c":function(t,e,n){"use strict";var a=n("c65b"),r=n("d784"),i=n("825a"),o=n("7234"),c=n("1d80"),s=n("129f"),l=n("577e"),u=n("dc4a"),d=n("14c3");r("search",(function(t,e,n){return[function(e){var n=c(this),r=o(e)?void 0:u(e,t);return r?a(r,e,n):new RegExp(e)[t](l(n))},function(t){var a=i(this),r=l(t),o=n(e,a,r);if(o.done)return o.value;var c=a.lastIndex;s(c,0)||(a.lastIndex=0);var u=d(a,r);return s(a.lastIndex,c)||(a.lastIndex=c),null===u?-1:u.index}]}))},"90fe":function(t,e,n){"use strict";n.d(e,"e",(function(){return i})),n.d(e,"f",(function(){return o})),n.d(e,"a",(function(){return c})),n.d(e,"g",(function(){return s})),n.d(e,"b",(function(){return l})),n.d(e,"d",(function(){return u})),n.d(e,"c",(function(){return d}));var a=n("66df"),r="/oms/api/v1",i=function(t){return a["a"].request({url:r+"/country/queryCounrty",params:t,method:"get"})},o=function(){return a["a"].request({url:r+"/country/queryCounrtyList",method:"get"})},c=function(t){return a["a"].request({url:r+"/country/addCounrty",data:t,method:"post",contentType:"multipart/form-data"})},s=function(t){return a["a"].request({url:r+"/country/updateCounrty",data:t,method:"post",contentType:"multipart/form-data"})},l=function(t){return a["a"].request({url:r+"/country/deleteCounrty",params:t,method:"delete"})},u=function(t){return a["a"].request({url:r+"/country/getOperators",params:t,method:"get"})},d=function(t){return a["a"].request({url:r+"/operator/a2zChannelOperator",params:t,method:"get"})}},"99f4":function(t,e,n){"use strict";var a=n("b5db");t.exports=/MSIE|Trident/.test(a)},ad00:function(t,e,n){"use strict";n("caad"),n("ac1f"),n("2532"),n("841c"),n("498a");var a=function(){var t=this,e=t._self._c;return e("div",[e("Modal",{attrs:{title:t.title,"mask-closable":!1,width:"750px"},on:{"on-cancel":t.cancelModal},model:{value:t.modal,callback:function(e){t.modal=e},expression:"modal"}},[e("Form",{ref:"formObj",attrs:{model:t.formObj,"label-width":150,rules:t.ruleAddValidate}},[e("FormItem",{attrs:{label:t.$t("deposit.mealname"),prop:"nameCn"}},[e("Input",{staticStyle:{width:"350px"},attrs:{type:"textarea",rows:5,maxlength:500,readonly:"info"==t.typeFlag,clearable:!0,placeholder:t.$t("support.fiveCharacters")},model:{value:t.formObj.nameCn,callback:function(e){t.$set(t.formObj,"nameCn",e)},expression:"formObj.nameCn"}})],1),e("FormItem",{attrs:{label:t.$t("support.packageDescription")+":",prop:"descCn"}},[e("Input",{staticStyle:{width:"350px"},attrs:{type:"textarea",rows:5,maxlength:4e3,readonly:"info"==t.typeFlag,clearable:!0,placeholder:t.$t("support.fourtCharacters")},model:{value:t.formObj.descCn,callback:function(e){t.$set(t.formObj,"descCn",e)},expression:"formObj.descCn"}})],1),e("FormItem",{attrs:{label:t.$t("support.Periodtype")+":",prop:"periodUnit"}},[e("Select",{staticStyle:{width:"350px"},attrs:{filterable:"",disabled:"info"==t.typeFlag,placeholder:t.$t("support.selectType"),clearable:!0},model:{value:t.formObj.periodUnit,callback:function(e){t.$set(t.formObj,"periodUnit",e)},expression:"formObj.periodUnit"}},t._l(t.periodUnitList,(function(n){return e("Option",{key:n.value,attrs:{value:n.value}},[t._v(t._s(n.label))])})),1)],1),e("FormItem",{attrs:{label:t.$t("support.Continuouscycle")+":",prop:"keepPeriod"}},[e("Input",{staticStyle:{width:"350px"},attrs:{maxlength:11,readonly:"info"==t.typeFlag,placeholder:t.$t("support.selectDuration")},model:{value:t.formObj.keepPeriod,callback:function(e){t.$set(t.formObj,"keepPeriod",e)},expression:"formObj.keepPeriod"}})],1),e("FormItem",{attrs:{label:t.$t("support.packageValidity")+":",prop:"effectiveDay"}},[e("Input",{staticStyle:{width:"350px"},attrs:{maxlength:11,readonly:"info"==t.typeFlag,placeholder:t.$t("support.inputPackageValidity")},model:{value:t.formObj.effectiveDay,callback:function(e){t.$set(t.formObj,"effectiveDay",e)},expression:"formObj.effectiveDay"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v(t._s(t.$t("support.day")))])])],1),e("FormItem",{attrs:{label:t.$t("support.DataRestrictionType")+":",prop:"flowLimitType"}},[e("Select",{staticStyle:{width:"350px"},attrs:{filterable:"",disabled:"info"==t.typeFlag||"update"==t.typeFlag,placeholder:t.$t("support.selectDataResetType"),clearable:!0},on:{"on-change":t.getcontrolLogic},model:{value:t.formObj.flowLimitType,callback:function(e){t.$set(t.formObj,"flowLimitType",e)},expression:"formObj.flowLimitType"}},[e("Option",{attrs:{value:1}},[t._v(t._s(t.$t("support.DataRestrictionCycle")))]),e("Option",{attrs:{value:2}},[t._v(t._s(t.$t("support.DataRestrictionSingle")))])],1)],1),e("FormItem",{attrs:{label:t.$t("flow.Controllogic")+":",prop:"controlLogic"}},[e("Select",{staticStyle:{width:"350px"},attrs:{filterable:"",disabled:"info"==t.typeFlag||"update"==t.typeFlag,placeholder:t.$t("support.selectRestrictionLogic")},model:{value:t.formObj.controlLogic,callback:function(e){t.$set(t.formObj,"controlLogic",e)},expression:"formObj.controlLogic"}},[1!==t.formObj.flowLimitType&&2!==t.formObj.flowLimitType&&t.formObj.flowLimitType?t._e():e("Option",{attrs:{value:1}},[t._v(t._s(t.$t("support.RestrictedSpeedLimit")))]),1!==t.formObj.flowLimitType&&t.formObj.flowLimitType?t._e():e("Option",{attrs:{value:2}},[t._v(t._s(t.$t("support.ReleaseAfterLimit")))])],1)],1),e("FormItem",{attrs:{label:t.$t("support.aupportHotspot")+":",prop:"isSupportedHotspots"}},[e("Select",{staticStyle:{width:"350px"},attrs:{filterable:"",disabled:"info"==t.typeFlag||"update"==t.typeFlag,placeholder:t.$t("support.isAupportHotspot")},on:{"on-change":function(e){return t.getcountryList(t.formObj.isSupportedHotspots)}},model:{value:t.formObj.isSupportedHotspots,callback:function(e){t.$set(t.formObj,"isSupportedHotspots",e)},expression:"formObj.isSupportedHotspots"}},[e("Option",{attrs:{value:1}},[t._v(t._s(t.$t("order.yes")))]),e("Option",{attrs:{value:2}},[t._v(t._s(t.$t("order.no")))])],1)],1),e("FormItem",{attrs:{label:t.$t("flow.SelectDestination")+":",prop:"mccList"}},[e("Select",{directives:[{name:"defaultSelect",rawName:"v-defaultSelect",value:[t.formObj.mccList],expression:"[formObj.mccList]"}],staticStyle:{width:"350px"},attrs:{filterable:"",multiple:"",disabled:!0,placeholder:t.$t("buymeal.selectCountry"),clearable:!0},model:{value:t.formObj.mccList,callback:function(e){t.$set(t.formObj,"mccList",e)},expression:"formObj.mccList"}},t._l(t.countryList,(function(n){return e("Option",{key:n.countryId,attrs:{value:n.countryId}},[t._v(t._s(n.countryName))])})),1),e("Button",{directives:[{name:"show",rawName:"v-show",value:"info"!=t.typeFlag,expression:"typeFlag!='info'"}],staticStyle:{"margin-left":"10px"},attrs:{type:"primary"},on:{click:t.showCountry}},[t._v(t._s(t.$t("country.editCountry")))])],1),t._l(t.formObj.combinationList,(function(n,a){return e("div",{key:a},[e("FormItem",{staticStyle:{"margin-bottom":"30px"},attrs:{label:t.$t("support.usage2")+":",prop:"combinationList."+a+".consumption",rules:t.ruleAddValidate.consumption}},[e("Input",{directives:[{name:"show",rawName:"v-show",value:"info"!=t.typeFlag,expression:"typeFlag!='info'"}],staticStyle:{width:"350px"},attrs:{maxlength:18,readonly:"info"==t.typeFlag,clearable:!0,placeholder:t.$t("support.inputUsage")},model:{value:n.consumption,callback:function(e){t.$set(n,"consumption",e)},expression:"item.consumption"}},[e("Select",{staticStyle:{width:"70px"},attrs:{slot:"append",disabled:"info"==t.typeFlag},slot:"append",model:{value:n.unit,callback:function(e){t.$set(n,"unit",e)},expression:"item.unit"}},[e("Option",{attrs:{value:"MB"}},[t._v("MB")]),e("Option",{attrs:{value:"GB"}},[t._v("GB")]),e("Option",{attrs:{value:"TB"}},[t._v("TB")])],1)],1),e("Input",{directives:[{name:"show",rawName:"v-show",value:"info"==t.typeFlag,expression:"typeFlag=='info'"}],staticStyle:{width:"350px"},attrs:{disabled:"info"==t.typeFlag},model:{value:n.displayConsumption,callback:function(e){t.$set(n,"displayConsumption",e)},expression:"item.displayConsumption"}})],1),e("FormItem",{attrs:{label:t.$t("support.speedTemplate")+":",prop:"combinationList."+a+".upccTemplateId",rules:[{required:!0,message:t.$t("support.selectSpeedTemplate")}]}},[e("Select",{directives:[{name:"show",rawName:"v-show",value:"info"!=t.typeFlag,expression:"typeFlag!='info'"}],staticStyle:{width:"350px"},attrs:{filterable:"",disabled:"info"==t.typeFlag,placeholder:t.$t("support.selectSpeedTemplate"),clearable:!0},model:{value:n.upccTemplateId,callback:function(e){t.$set(n,"upccTemplateId",e)},expression:"item.upccTemplateId"}},t._l(t.upccTemplateList,(function(n,a){return e("Option",{key:a,attrs:{value:n.templateId,title:n.templateDesc}},[t._v(t._s(n.templateName.length>35?n.templateName.substring(0,35)+"…":n.templateName)+"\n\t\t\t\t\t\t\t\t")])})),1),e("Input",{directives:[{name:"show",rawName:"v-show",value:"info"==t.typeFlag,expression:"typeFlag=='info'"}],staticStyle:{width:"350px"},attrs:{disabled:"info"==t.typeFlag},model:{value:n.templateName,callback:function(e){t.$set(n,"templateName",e)},expression:"item.templateName"}}),e("Button",{directives:[{name:"show",rawName:"v-show",value:"info"!=t.typeFlag&&(1===t.formObj.combinationList.length&&0===a||t.formObj.combinationList.length>1&&a===t.formObj.combinationList.length-1),expression:"typeFlag!='info' && ((formObj.combinationList.length===1 && index===0)\n\t\t\t\t\t\t\t||(formObj.combinationList.length>1 && index===(formObj.combinationList.length-1)))"}],staticStyle:{"margin-left":"10px"},attrs:{type:"success",ghost:""},on:{click:function(e){return t.addItem()}}},[t._v(t._s(t.$t("support.add")))]),e("Button",{directives:[{name:"show",rawName:"v-show",value:"info"!=t.typeFlag&&t.formObj.combinationList.length>1,expression:"typeFlag!='info' && formObj.combinationList.length>1"}],staticStyle:{"margin-left":"10px"},attrs:{type:"error",ghost:""},on:{click:function(e){return t.delItem(a)}}},[t._v(t._s(t.$t("address.Delete")))])],1)],1)})),e("FormItem",{attrs:{label:t.$t("support.unlimitedUsageTemplate")+":",prop:"templateName"}},[e("Select",{staticStyle:{width:"350px"},attrs:{filterable:"",disabled:"info"==t.typeFlag,placeholder:t.$t("support.selectUnlimitedUsageTemplate"),clearable:!0},model:{value:t.formObj.templateName,callback:function(e){t.$set(t.formObj,"templateName",e)},expression:"formObj.templateName"}},t._l(t.upccTemplateList,(function(n,a){return e("Option",{key:a,attrs:{value:n.templateId,title:n.templateDesc}},[t._v(t._s(n.templateName.length>35?n.templateName.substring(0,35)+"…":n.templateName)+"\n\t\t\t\t\t\t\t")])})),1)],1),e("FormItem",{attrs:{label:t.$t("support.supportAddon")+":",prop:"hasRefuelPackage"}},[e("i-switch",{attrs:{size:"large",disabled:"info"==t.typeFlag},on:{"on-change":t.fuelPackaChange},model:{value:t.formObj.hasRefuelPackage,callback:function(e){t.$set(t.formObj,"hasRefuelPackage",e)},expression:"formObj.hasRefuelPackage"}},[e("span",{attrs:{slot:"open"},slot:"open"},[t._v(t._s(t.$t("order.yes")))]),e("span",{attrs:{slot:"close"},slot:"close"},[t._v(t._s(t.$t("order.no")))])])],1),t.formObj.hasRefuelPackage?e("FormItem",{attrs:{label:t.$t("support.bindAddon"),prop:"selectionTypes"}},[e("Button",{staticClass:"inputSty",staticStyle:{width:"350px"},attrs:{type:"dashed"},on:{click:t.RefuelPackageList}},[t._v(t._s(t.$t("support.AddonList")))])],1):t._e(),e("Row",[0!=t.appInfos.length?e("Col",{attrs:{span:"24"}},[e("FormItem",{attrs:{label:t.$t("directionalApp.supportUsage")+":",prop:"isSupportDirect"}},[e("Select",{staticClass:"inputSty",staticStyle:{width:"350px"},attrs:{placeholder:t.$t("directionalApp.selectSupportUsage"),disabled:"info"==t.typeFlag||1==t.notClick,clearable:"info"!=t.typeFlag},on:{"on-change":function(e){return t.changeDirect(e)}},model:{value:t.formObj.isSupportDirect,callback:function(e){t.$set(t.formObj,"isSupportDirect",e)},expression:"formObj.isSupportDirect"}},[e("Option",{attrs:{value:"1"}},[t._v(t._s(t.$t("order.yes")))]),e("Option",{attrs:{value:"2"}},[t._v(t._s(t.$t("order.no")))])],1)],1)],1):t._e()],1),t._l(t.formObj.directAppInfos,(function(n,a){return e("div",{key:a},["1"==t.formObj.isSupportDirect?e("Row",{attrs:{type:"flex",justify:"start",align:"middle"}},[e("Col",{attrs:{span:"24"}},[e("FormItem",{attrs:{label:t.$t("directionalApp.selectAPP")+":",prop:"directAppInfos."+a+".appId",rules:t.ruleAddValidate.appId}},[e("Select",{staticStyle:{width:"350px"},attrs:{multiple:"",filterable:"",placeholder:t.$t("directionalApp.pleaseSelectAPP"),disabled:"info"==t.typeFlag||1==t.notClick,clearable:""},on:{"on-change":function(e){return t.changeAppId(e,a)}},model:{value:n.appId,callback:function(e){t.$set(n,"appId",e)},expression:"fitem.appId"}},t._l(t.choseAppInfos(n.appId),(function(a,r){return e("Option",{key:a.id,attrs:{value:a.id,disabled:!n.appId.includes(a.id)&&1==t.appTotal}},[t._v(t._s(a.appName))])})),1),0!=a?e("Button",{staticStyle:{"margin-left":"20px",width:"80px"},attrs:{disabled:"info"==t.typeFlag||1==t.notClick,size:"small",type:"error"},on:{click:function(e){return t.deleteApp(a)}}},[t._v(t._s(t.$t("directionalApp.deleteAPP")))]):t._e(),a==t.formObj.directAppInfos.length-1?e("Button",{staticStyle:{"margin-left":"20px",width:"80px"},attrs:{disabled:"info"==t.typeFlag||1==t.notClick||1==t.appTotal,size:"small",type:"primary"},on:{click:function(e){return t.addApp()}}},[t._v(t._s(t.$t("directionalApp.addAPP")))]):t._e()],1)],1)],1):t._e(),"1"==t.formObj.isSupportDirect?e("Row",[e("Col",{attrs:{span:"24"}},[e("FormItem",{attrs:{label:t.$t("directionalApp.specificAPPLogic")+":",prop:"directAppInfos."+a+".directType",rules:t.ruleAddValidate.directType}},[e("RadioGroup",{on:{"on-change":function(e){return t.changeLogic(a)}},model:{value:n.directType,callback:function(e){t.$set(n,"directType",e)},expression:"fitem.directType"}},[e("Radio",{attrs:{label:"1",disabled:"info"==t.typeFlag||1==t.notClick}},[t._v(t._s(t.$t("flow.Restricted")))]),e("Radio",{attrs:{label:"2",disabled:"info"==t.typeFlag||1==t.notClick}},[t._v(t._s(t.$t("directionalApp.freeFlow")))])],1)],1)],1)],1):t._e(),t._l(n.appConsumption,(function(r,i){return"1"==t.formObj.isSupportDirect&&"2"==n.directType?e("div",{key:i},[e("Row",{staticStyle:{"margin-bottom":"10px"}},[e("Col",{attrs:{span:"16"}},[e("FormItem",{attrs:{label:t.$t("directionalApp.dataValue")+":",prop:"directAppInfos."+a+".appConsumption."+i+".consumption",rules:t.ruleAddValidate.consumption1}},[e("Input",{staticStyle:{width:"320px"},attrs:{type:"number",readonly:"info"==t.typeFlag,clearable:"info"!=t.typeFlag,disabled:1==t.notClick,placeholder:t.$t("directionalApp.inputDataValue")},model:{value:r.consumption,callback:function(e){t.$set(r,"consumption","string"===typeof e?e.trim():e)},expression:"item1.consumption"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("MB")])])],1)],1),0!=i?e("Col",{staticStyle:{padding:"6px 0 0 10px"},attrs:{span:"4"}},[e("Button",{attrs:{size:"small",type:"error",ghost:"",disabled:"info"==t.typeFlag||1==t.notClick},on:{click:function(e){return t.delFlowValue(a,i)}}},[t._v(t._s(t.$t("directionalApp.deleteDataValue")))])],1):t._e(),i==n.appConsumption.length-1?e("Col",{staticStyle:{padding:"6px 0 0 10px"},attrs:{span:"4"}},[e("Button",{attrs:{size:"small",type:"info",ghost:"",disabled:"info"==t.typeFlag||1==t.notClick},on:{click:function(e){return t.addFlowValue(a)}}},[t._v(t._s(t.$t("directionalApp.addDataValue")))])],1):t._e()],1),e("Row",{attrs:{type:"flex",justify:"start",align:"middle"}},[e("Col",{attrs:{span:"24"}},[e("FormItem",{attrs:{label:t.$t("directionalApp.selectUPCCTemplate"),prop:"directAppInfos."+a+".appConsumption."+i+".upccTemplateId",rules:t.ruleAddValidate.upccTemplateId1}},t._l(n.appId,(function(n,a){return e("div",{staticStyle:{display:"flex","justify-content":"flex-statrt","align-items":"flex-start"}},[e("Select",{key:n,staticStyle:{"margin-bottom":"20px",width:"350px"},attrs:{filterable:"",placeholder:t.$t("directionalApp.pleaseSelectUPCCTemplate"),clearable:"info"!=t.typeFlag,disabled:"info"==t.typeFlag||1==t.notClick},model:{value:r.upccTemplateId[a],callback:function(e){t.$set(r.upccTemplateId,a,e)},expression:"item1.upccTemplateId[uindex]"}},t._l(t.directTemplateList[n],(function(n){return e("Option",{key:n.upccTemplateId,attrs:{value:n.upccTemplateId,title:n.templateName}},[t._v(t._s(n.templateName))])})),1)],1)})),0)],1)],1)],1):t._e()})),"1"==t.formObj.isSupportDirect&&"2"==n.directType?e("Row",[e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:t.$t("directionalApp.continueDataUsage")+":",prop:"directAppInfos."+a+".isUsePackage",rules:t.ruleAddValidate.isUsePackage}},[e("Select",{staticStyle:{width:"350px"},attrs:{placeholder:t.$t("directionalApp.PleaseDataUsage"),disabled:"info"==t.typeFlag||1==t.notClick,clearable:"info"!=t.typeFlag},on:{"on-change":function(e){return t.changeUsePackage(e)}},model:{value:n.isUsePackage,callback:function(e){t.$set(n,"isUsePackage",e)},expression:"fitem.isUsePackage"}},[e("Option",{attrs:{value:"1"}},[t._v(t._s(t.$t("order.yes")))]),e("Option",{attrs:{value:"2"}},[t._v(t._s(t.$t("order.no")))])],1)],1)],1)],1):t._e(),"1"==t.formObj.isSupportDirect?e("Row",["1"==n.directType?e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:t.$t("directionalApp.restrictedTemplate")+":",prop:"directAppInfos."+a+".noLimitTemplateId",rules:t.ruleAddValidate.noLimitTemplateId}},t._l(n.appId,(function(a,r){return e("div",[e("Select",{staticStyle:{"margin-bottom":"20px",width:"350px"},attrs:{filterable:"",placeholder:t.$t("directionalApp.pleasepRrestrictedTemplate"),disabled:"info"==t.typeFlag||1==t.notClick,clearable:"info"!=t.typeFlag},model:{value:n.noLimitTemplateId[r],callback:function(e){t.$set(n.noLimitTemplateId,r,e)},expression:"fitem.noLimitTemplateId[dindex]"}},t._l(t.directTemplateList[a],(function(n){return e("Option",{key:n.upccTemplateId,attrs:{value:n.upccTemplateId}},[t._v(t._s(n.templateName))])})),1)],1)})),0)],1):t._e(),n.isUsePackage&&"2"==n.directType?e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:"2"==n.isUsePackage?t.$t("directionalApp.FreeTemplate"):t.$t("directionalApp.FreeContinueTemplate"),prop:"directAppInfos."+a+".noLimitTemplateId",rules:t.ruleAddValidate.noLimitTemplateId}},t._l(n.appId,(function(a,r){return e("div",[e("Select",{staticStyle:{"margin-bottom":"20px",width:"350px"},attrs:{placeholder:"2"==n.isUsePackage?t.$t("directionalApp.pleaseFree"):t.$t("directionalApp.pleaseFreeContinue"),disabled:"info"==t.typeFlag||1==t.notClick,clearable:"info"!=t.typeFlag},model:{value:n.noLimitTemplateId[r],callback:function(e){t.$set(n.noLimitTemplateId,r,e)},expression:"fitem.noLimitTemplateId[dindex]"}},t._l(t.directTemplateList[a],(function(n){return e("Option",{key:n.upccTemplateId,attrs:{value:n.upccTemplateId}},[t._v(t._s(n.templateName))])})),1)],1)})),0)],1):t._e()],1):t._e(),e("div",{staticStyle:{"margin-bottom":"30px"}})],2)}))],2),e("div",{staticClass:"footer_wrap",attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v(t._s(t.$t("common.cancel")))]),e("Button",{directives:[{name:"show",rawName:"v-show",value:"info"!=t.typeFlag,expression:"typeFlag!='info'"}],attrs:{type:"primary",loading:t.submitloading},on:{click:t.submit}},[t._v(t._s(t.$t("common.determine")))])],1)],1),e("Modal",{attrs:{title:t.$t("support.newAddon"),"mask-closable":!1,width:"700px"},on:{"on-cancel":t.cancelBagModal},model:{value:t.addRefuelModel,callback:function(e){t.addRefuelModel=e},expression:"addRefuelModel"}},["info"!==this.typeFlag?e("Form",{ref:"searchObj",staticStyle:{"font-weight":"bold",display:"flex","justify-content":"flex-start","flex-wrap":"wrap"},attrs:{model:t.searchObj,"label-width":80,inline:""}},[e("FormItem",{attrs:{label:t.$t("support.AddonName")}},[e("Input",{staticStyle:{width:"205px"},attrs:{type:"text",clearable:"",placeholder:t.$t("support.inputAddonName")},model:{value:t.searchObj.gaspackname,callback:function(e){t.$set(t.searchObj,"gaspackname","string"===typeof e?e.trim():e)},expression:"searchObj.gaspackname"}})],1),e("FormItem",{attrs:{label:t.$t("support.AddonID")}},[e("Input",{staticStyle:{width:"205px"},attrs:{type:"text",clearable:"",placeholder:t.$t("support.inputAddonId")},model:{value:t.searchObj.gaspacknameid,callback:function(e){t.$set(t.searchObj,"gaspacknameid","string"===typeof e?e.trim():e)},expression:"searchObj.gaspacknameid"}})],1),e("div",[e("Button",{attrs:{type:"primary",icon:"md-search",loading:t.searchObjloading},on:{click:t.search}},[t._v(t._s(t.$t("address.search")))])],1)],1):t._e(),e("Table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{columns:t.Unitedcolumns,data:t.Uniteddata,loading:t.Unitedloading},on:{"on-selection-change":t.handleRowChange,"on-select-cancel":t.cancelPackage,"on-select-all-cancel":t.cancelPackageAll}}),e("div",{staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.Unitedtotal,current:t.UnitedcurrentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.UnitedcurrentPage=e},"on-change":t.UnitedgoPage}})],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{staticStyle:{"margin-left":"8px"},on:{click:t.cancelBagModal}},[t._v(t._s(t.$t("common.cancel")))]),"info"!==this.typeFlag?e("Button",{attrs:{type:"primary"},on:{click:t.Confirm}},[t._v(t._s(t.$t("common.determine")))]):t._e()],1)],1),e("Modal",{attrs:{title:t.$t("flow.SelectDestination"),"mask-closable":!1,width:"1700px",styles:{top:"45px"}},on:{"on-cancel":t.canceCountry},model:{value:t.countryShowType,callback:function(e){t.countryShowType=e},expression:"countryShowType"}},[e("AddCountry",{ref:"addCountry",attrs:{title:t.title,typeFlag:t.typeFlag,corpId:t.corpId,supportedHotspots:t.formObj.isSupportedHotspots}}),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{staticStyle:{"margin-left":"8px"},on:{click:t.canceCountry}},[t._v(t._s(t.$t("common.cancel")))]),"info"!=this.typeFlag?e("Button",{attrs:{type:"primary"},on:{click:t.ConfirmCountry}},[t._v(t._s(t.$t("common.determine")))]):t._e()],1)],1)],1)},r=[],i=n("b85c"),o=n("2909"),c=n("c7eb"),s=n("3835"),l=n("1da1"),u=(n("d9e2"),n("4de4"),n("7db0"),n("d81d"),n("14d9"),n("fb6a"),n("a434"),n("e9c4"),n("a9e3"),n("b64b"),n("d3b7"),n("00b4"),n("25f0"),n("6062"),n("1e70"),n("79a4"),n("c1a1"),n("8b00"),n("a4e7"),n("1e5a"),n("72c3"),n("3ca3"),n("466d"),n("159b"),n("ddb0"),function(){var t=this,e=t._self._c;return e("div",{staticStyle:{height:"705px","overflow-y":"auto"}},[e("DualTableSelect",{ref:"dualTable",attrs:{"source-columns":t.sourceColumns,"source-data":t.sourceData,"selected-columns":t.selectedColumns,"selected-data":t.selectedData,total:t.total,current:t.currentPage,"page-size":t.pageSize,loading:t.loading,"check-all":t.checkAll,indeterminate:t.indeterminate},on:{"on-check-all":t.handleCheckAll,"on-page-change":t.handlePageChange,"on-select":t.handleSelect,"on-select-cancel":t.handleSelectCancel,"on-select-all":t.handleSelectAll,"on-select-all-cancel":t.handleSelectAllCancel,"on-remove":t.handleRemove},model:{value:t.selectedValues,callback:function(e){t.selectedValues=e},expression:"selectedValues"}},[e("template",{slot:"source-header"},[e("div",{staticStyle:{width:"88%",height:"130px"}},[e("div",{staticClass:"custom-selected-header"},[e("h3",[t._v(t._s(t.$t("country.select")))])]),e("div",{staticClass:"country-select-wrapper"},[e("Select",{ref:"countrySelect",staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",multiple:"","max-tag-count":4,"max-tag-placeholder":t.maxTagPlaceholder,placeholder:t.$t("country.select")},on:{"on-change":t.handleCountrySelectChange,"on-open-change":t.handleCountrySelectBlur},model:{value:t.selectedCountrySearch,callback:function(e){t.selectedCountrySearch=e},expression:"selectedCountrySearch"}},t._l(t.countrySearchOptions,(function(n){return e("Option",{key:n.mccId,attrs:{value:n.mccId,disabled:t.isCountryOptionDisabled(n),label:t.getCountryOptionLabel(n)}},[t._v("\n                "+t._s("zh-CN"===t.language?n.countryCn:n.countryEn)+"\n                "),("zh-CN"===t.language?n.remarkCn:n.remarkEn)?e("span",{staticStyle:{color:"#999","margin-left":"5px"}},[t._v("\n                  ("+t._s("zh-CN"===t.language?n.remarkCn:n.remarkEn)+")\n                ")]):t._e()])})),1)],1),e("div",{staticClass:"continent-buttons"},[e("div",{staticClass:"continent-buttons-wrapper"},[e("Button",{staticClass:"continentButton",attrs:{type:"all"===t.currentContinent?"primary":"default"},on:{click:function(e){return t.selectCountry("all")}}},[t._v("\n              "+t._s(t.$t("country.all"))+"\n            ")]),t._l(t.continentList,(function(n,a){return e("Button",{key:n._id||a,staticClass:"continentButton",attrs:{type:t.currentContinent===n.continentEn?"primary":"default"},on:{click:function(e){return t.selectCountry(n.continentEn)}}},[t._v("\n              "+t._s("zh-CN"==t.language?n.continentCn:n.continentEn)+"\n            ")])}))],2)])])]),e("template",{slot:"all-check-box"},[e("div",{staticClass:"check-all-container",staticStyle:{"text-align":"right","margin-top":"15px"}},[e("Checkbox",{attrs:{indeterminate:t.computedIndeterminate,value:t.computedCheckAll,disabled:0===t.sourceData.length},on:{"on-change":t.handleCheckAll}},[t._v(t._s(t.$t("country.selectAll")))])],1)]),e("template",{slot:"empty"},[e("div",{staticClass:"empty-state"},[e("Icon",{attrs:{type:"ios-alert-outline",size:"48"}}),e("p",[t._v(t._s(t.$t("country.noData")))]),"all"!==t.currentContinent?e("Button",{on:{click:function(e){return t.selectCountry("all")}}},[t._v("\n          "+t._s(t.$t("country.viewAll"))+"\n        ")]):t._e()],1)]),e("template",{slot:"selected-header"},[e("div",{staticStyle:{height:"130px"}},[e("div",{staticClass:"custom-selected-header"},[e("h3",[t._v(t._s(t.$t("country.selected")))])]),e("div",{staticClass:"continent-buttons",staticStyle:{height:"73px"}})])])],2),t.checkAllLoading?e("Spin",{attrs:{size:"large",fix:""}},[e("Icon",{staticClass:"spin-icon-load",attrs:{type:"ios-loading",size:"18"}}),e("div",[t._v(t._s(t.loadingMessage))])],1):t._e()],1)}),d=[],p=n("5530"),f=(n("99af"),n("c740"),n("4ec9"),n("d1c9")),m=n("78c0"),h={continent:"",name:"AddCountry",components:{DualTableSelect:f["a"]},props:{corpId:{type:String,default:""},typeFlag:{type:String,default:""},supportedHotspots:{type:[String,Number],default:""}},data:function(){return{language:localStorage.getItem("local"),continentList:[],currentContinent:"all",checkAll:!1,indeterminate:!1,loading:!1,checkAllLoading:!1,loadingMessage:"",isAllSelected:!1,continent:"",currentPage:1,pageSize:10,total:0,selectedValues:[],sourceData:[],selectedData:[],allCountryData:null,selectedCountrySearch:[],countrySearchOptions:[]}},watch:{selectedValues:function(t){t&&0!==t.length||(this.selectedCountrySearch=[])}},mounted:function(){this.currentPage=1,this.currentContinent="all",this.continentEn="",this.getContinentList(),this.loadData()},methods:{extractMccFromMccId:function(t){if(!t)return"";var e=t.match(/^(\d+)(?:-|$)/);return e?e[1]:t},handleError:function(t,e){this.$Message.error({content:e,duration:5,closable:!0})},getContinentList:function(){var t=this;return Object(l["a"])(Object(c["a"])().mark((function e(){var n;return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(m["p"])();case 3:n=e.sent,n&&"0000"===n.code?t.continentList=n.data.map((function(t,e){return{continentCn:t.continentCn,continentEn:t.continentEn,_id:e}})):t.handleError(null,t.$t("country.getContinentFail"),!0,t.getContinentList),e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](0),t.handleError(e.t0,"获取大洲列表失败",!0,t.getContinentList);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})))()},getCountryData:function(){var t=this;return Object(l["a"])(Object(c["a"])().mark((function e(){var n,a;return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,n={continentEn:t.continent&&t.continent.continentEn?t.continent.continentEn:"",pageNo:t.currentPage,pageSize:t.pageSize,corpId:t.corpId,supportedHotspots:t.supportedHotspots},e.next=4,Object(m["q"])(n);case 4:if(a=e.sent,!a||"0000"!=a.code){e.next=9;break}return e.abrupt("return",a);case 9:return t.handleError(null,t.$t("country.getCountryFail")),e.abrupt("return",null);case 11:e.next=17;break;case 13:return e.prev=13,e.t0=e["catch"](0),t.handleError(e.t0,"获取国家列表失败"),e.abrupt("return",null);case 17:case"end":return e.stop()}}),e,null,[[0,13]])})))()},loadData:function(t){var e=this;return Object(l["a"])(Object(c["a"])().mark((function n(){var a,r;return Object(c["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!e.loading){n.next=2;break}return n.abrupt("return");case 2:if(e.loading=!0,n.prev=3,!t&&e.allCountryData){n.next=15;break}return n.prev=5,n.next=8,e.getAllCountryData();case 8:e.selectedValues.length>0&&e.allCountryData&&e.allCountryData.data&&e.allCountryData.data.records&&0===e.selectedData.length&&(a=e.allCountryData.data.records.filter((function(t){return e.selectedValues.includes(t.mccId)})),a.length>0&&(e.selectedData=a.map((function(t){return{id:t.mccId,countryCn:t.countryCn,countryEn:t.countryEn,continentCn:t.continentCn,continentEn:t.continentEn,mcc:e.extractMccFromMccId(t.mccId),mccId:t.mccId,remarkCn:t.remarkCn||"",remarkEn:t.remarkEn||"",remark:"zh-CN"===e.language?t.remarkCn||"":t.remarkEn||""}})),e.selectedCountrySearch=Object(o["a"])(e.selectedValues))),n.next=15;break;case 11:n.prev=11,n.t0=n["catch"](5),console.error("获取全量数据错误:",n.t0),e.$Message.error(e.$t("country.getAllCountryFail"));case 15:return n.next=17,e.getCountryData();case 17:r=n.sent,r&&"0000"==r.code?(e.sourceData=r.data.records.map((function(t){var n=e.extractMccFromMccId(t.mccId),a=e.selectedData.some((function(e){return e.mccId===t.mccId})),r=e.selectedData.some((function(a){return e.extractMccFromMccId(a.mccId)===n&&a.mccId!==t.mccId}));return{id:t.mccId,countryCn:t.countryCn,countryEn:t.countryEn,continentCn:t.continentCn,continentEn:t.continentEn,mcc:n,mccId:t.mccId,remarkCn:t.remarkCn||"",remarkEn:t.remarkEn||"",remark:"zh-CN"===e.language?t.remarkCn||"":t.remarkEn||"",_checked:a,_disabled:r||"info"===e.typeFlag}})),e.total=r.data.total||0,e.updateCheckAllStatus(),e.adjustAllCheckStatus()):e.handleError(null,r.msg||"获取国家列表失败"),n.next=24;break;case 21:n.prev=21,n.t1=n["catch"](3),e.handleError(n.t1,"加载数据错误");case 24:return n.prev=24,e.loading=!1,n.finish(24);case 27:case"end":return n.stop()}}),n,null,[[3,21,24,27],[5,11]])})))()},handlePageChange:function(t){this.currentPage=t,this.loadData()},handleSelect:function(t,e){var n=this;if("info"!==this.typeFlag){if(e._disabled){var a=this.sourceData.findIndex((function(t){return t.id===e.id}));a>-1&&(this.sourceData[a]._checked=!1,this.$refs.dualTable&&this.$refs.dualTable.cancleOneSelect(e));var r=this.extractMccFromMccId(e.mccId),i=this.selectedData.find((function(t){return n.extractMccFromMccId(t.mccId)===r&&t.mccId!==e.mccId}));return i&&this.$Message.warning(this.$t("country.alreadySelected")+i.countryCn),void this.$nextTick((function(){n.refreshUIState()}))}var o=this.extractMccFromMccId(e.mccId),c=this.selectedData.find((function(t){return n.extractMccFromMccId(t.mccId)===o&&t.mccId!==e.mccId}));if(c){var s=this.sourceData.findIndex((function(t){return t.id===e.id}));return s>-1&&(this.sourceData[s]._checked=!1,this.$refs.dualTable&&this.$refs.dualTable.cancleOneSelect(e)),this.$Message.warning(this.$t("country.alreadySelected")+c.countryCn),void this.$nextTick((function(){n.refreshUIState()}))}var l=t.some((function(t){return t.id===e.id}));if(l){if(!this.selectedData.some((function(t){return t.id===e.id}))){var u=Object(p["a"])(Object(p["a"])({},e),{},{remark:"zh-CN"===this.language?e.remarkCn||"":e.remarkEn||""});this.selectedData.push(u)}this.selectedCountrySearch.includes(e.id)||this.selectedCountrySearch.push(e.id)}else{var d=this.selectedData.findIndex((function(t){return t.id===e.id}));d>-1&&this.selectedData.splice(d,1);var f=this.selectedCountrySearch.indexOf(e.id);f>-1&&this.selectedCountrySearch.splice(f,1)}this.selectedValues=this.selectedData.map((function(t){return t.id})),this.refreshUIState(),this.$emit("input",this.selectedValues),this.$emit("on-change",this.selectedData),this.updateCheckAllStatus()}else this.refreshUIState()},handleSelectCancel:function(t,e){var n=this;if(e){var a=this.selectedData.findIndex((function(t){return t&&t.id===e.id}));if(a>-1){this.selectedData.splice(a,1),this.selectedValues=this.selectedData.map((function(t){return t.id}));var r=this.selectedCountrySearch.indexOf(e.id);r>-1&&this.selectedCountrySearch.splice(r,1);var i=this.extractMccFromMccId(e.mccId);this.sourceData&&this.sourceData.length&&this.sourceData.forEach((function(t){if(n.extractMccFromMccId(t.mccId)===i){var e=n.selectedData.some((function(t){return t&&n.extractMccFromMccId(t.mccId)===i}));e||(t._disabled="info"===n.typeFlag)}}))}this.refreshUIState(),this.$emit("input",this.selectedValues),this.$emit("on-change",this.selectedData),this.updateCheckAllStatus()}else console.warn("handleSelectCancel: row参数为空")},isRowSelectable:function(t){if("info"===this.typeFlag)return!1;var e=this.selectedData.find((function(e){return e.mcc===t.mcc&&e.id!==t.id}));return!e},customRowSelection:function(t,e){var n=this;return{on:{click:function(e){if(t._disabled){e.stopPropagation(),e.preventDefault();var a=n.selectedData.find((function(e){return e.mcc===t.mcc&&e.id!==t.id}));return a&&n.$Message.warning(n.$t("country.alreadySelected")+a.countryCn),n.refreshUIState(),!1}}}}},getAllCountryData:function(){var t=this;return Object(l["a"])(Object(c["a"])().mark((function e(){var n,a;return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,n={continentEn:t.continent.continentEn,pageNo:-1,pageSize:-1,corpId:t.corpId,supportedHotspots:t.supportedHotspots},e.next=4,Object(m["q"])(n);case 4:if(a=e.sent,!a||"0000"!==a.code){e.next=11;break}return t.allCountryData=a,t.continent.continentEn&&""!=t.continent.continentEn||t.initCountrySearchOptions(),e.abrupt("return",a);case 11:return t.$Message.error(t.$t("country.getAllCountryFail")),e.abrupt("return",null);case 13:e.next=20;break;case 15:return e.prev=15,e.t0=e["catch"](0),console.error("获取全量数据错误:",e.t0),t.$Message.error(t.$t("country.getAllCountryFail")),e.abrupt("return",null);case 20:case"end":return e.stop()}}),e,null,[[0,15]])})))()},handleCheckAll:function(t){var e=this;return Object(l["a"])(Object(c["a"])().mark((function n(){var a,r,i,s;return Object(c["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if("info"!==e.typeFlag){n.next=2;break}return n.abrupt("return");case 2:if(n.prev=2,e.loading=!0,e.checkAllLoading=!0,e.loadingMessage=t?e.$t("country.selectingContinent"):e.$t("country.deselectingContinent"),e.$Message.loading({content:e.loadingMessage,duration:0}),!t){n.next=32;break}if(a=[],e.allCountryData){n.next=12;break}return n.next=12,e.getAllCountryData();case 12:if(!(e.allCountryData&&"0000"===e.allCountryData.code&&e.allCountryData.data&&e.allCountryData.data.records)){n.next=24;break}r=e.allCountryData.data.records.filter((function(t){return"all"===e.currentContinent||t.continentEn===e.currentContinent})),a=r.map((function(t){return{id:t.mccId,countryCn:t.countryCn,countryEn:t.countryEn,continentCn:t.continentCn,continentEn:t.continentEn,mcc:e.extractMccFromMccId(t.mccId),mccId:t.mccId,remarkCn:t.remarkCn||"",remarkEn:t.remarkEn||"",remark:"zh-CN"===e.language?t.remarkCn||"":t.remarkEn||""}})),i=Object(o["a"])(e.selectedData),s=new Map,e.selectedData.forEach((function(t){var n=e.extractMccFromMccId(t.mccId);s.set(n,t.id)})),a.forEach((function(t){var n=e.extractMccFromMccId(t.mccId),a=s.get(n);a&&a!==t.id||e.selectedValues.includes(t.id)||(i.push(t),s.set(n,t.id))})),e.selectedData=i,e.selectedValues=i.map((function(t){return t.id})),e.selectedCountrySearch=Object(o["a"])(e.selectedValues),n.next=29;break;case 24:return e.$Message.error("获取大洲国家数据失败"),e.loading=!1,e.checkAllLoading=!1,e.$Message.destroy(),n.abrupt("return");case 29:e.updateCheckAllStatus(),n.next=35;break;case 32:"all"===e.currentContinent?(e.selectedData=[],e.selectedValues=[],e.selectedCountrySearch=[]):(e.selectedData=e.selectedData.filter((function(t){return t.continentEn!==e.currentContinent})),e.selectedValues=e.selectedData.map((function(t){return t.id})),e.selectedCountrySearch=Object(o["a"])(e.selectedValues)),e.checkAll=!1,e.indeterminate=e.selectedData.length>0;case 35:e.refreshUIState(),e.$emit("input",e.selectedValues),e.$emit("on-change",e.selectedData),n.next=44;break;case 40:n.prev=40,n.t0=n["catch"](2),console.error("全选处理错误:",n.t0),e.$Message.error(e.$t("country.operationFail")+(n.t0.message||e.$t("country.getCountryFail")));case 44:return n.prev=44,e.loading=!1,e.checkAllLoading=!1,e.$Message.destroy(),n.finish(44);case 49:case"end":return n.stop()}}),n,null,[[2,40,44,49]])})))()},refreshUIState:function(){var t=this;if(this.sourceData&&0!==this.sourceData.length){var e=new Set;this.selectedData&&this.selectedData.length&&this.selectedData.forEach((function(n){n&&n.mccId&&e.add(t.extractMccFromMccId(n.mccId))})),this.sourceData.forEach((function(n){var a=t.selectedData.some((function(t){return t&&t.id===n.id})),r=t.extractMccFromMccId(n.mccId),i=e.has(r)&&!t.selectedData.some((function(t){return t&&t.id===n.id}));n._checked=a,n._disabled=i||"info"===t.typeFlag})),this.$nextTick((function(){t.$forceUpdate()}))}else console.warn("sourceData为空或者长度为0，跳过UI刷新")},adjustAllCheckStatus:function(){if(!this.sourceData||0===this.sourceData.length)return this.checkAll=!1,void(this.indeterminate=!1);var t=this.sourceData.filter((function(t){return!t._disabled})).length,e=this.sourceData.filter((function(t){return t._checked})).length;0===e?(this.checkAll=!1,this.indeterminate=!1):e===t&&t>0?(this.checkAll=!0,this.indeterminate=!1):(this.checkAll=!1,this.indeterminate=!0)},updateCheckAllStatus:function(){var t=this;if(!this.allCountryData||!this.allCountryData.data)return this.checkAll=!1,void(this.indeterminate=this.selectedData.length>0);var e=[];this.allCountryData.data.records&&(e=this.allCountryData.data.records.filter((function(e){return"all"===t.currentContinent||e.continentEn===t.currentContinent})));var n=e.filter((function(e){var n=t.extractMccFromMccId(e.mccId);return!t.selectedData.some((function(a){return t.extractMccFromMccId(a.mccId)===n&&a.mccId!==e.mccId}))})),a=n.filter((function(e){return t.selectedValues.includes(e.mccId)})).length;0===n.length||0===a?(this.checkAll=!1,this.indeterminate=!1):a===n.length?(this.checkAll=!0,this.indeterminate=!1):(this.checkAll=!1,this.indeterminate=!0)},handleSelectAllCancel:function(t){var e=this;if("info"!==this.typeFlag){var n=Object(o["a"])(this.sourceData),a=this.selectedData.filter((function(t){return!n.some((function(e){return e.id===t.id}))})),r=new Set;n.forEach((function(t){var n=e.extractMccFromMccId(t.mccId);e.selectedData.some((function(e){return e.id===t.id}))&&!a.some((function(e){return e.id===t.id}))&&r.add(n)})),this.selectedData=a,this.selectedValues=a.map((function(t){return t.id})),this.selectedCountrySearch=Object(o["a"])(this.selectedValues),this.sourceData.forEach((function(t){var n=e.extractMccFromMccId(t.mccId);if(r.has(n)){var a=e.selectedData.some((function(t){return e.extractMccFromMccId(t.mccId)===n}));a||(t._disabled="info"===e.typeFlag)}})),this.refreshUIState(),this.$emit("input",this.selectedValues),this.$emit("on-change",this.selectedData),this.updateCheckAllStatus()}},handleSelectAll:function(t){var e=this;if("info"!==this.typeFlag){var n=Object(o["a"])(this.sourceData);this.selectedData.filter((function(t){return!n.some((function(e){return e.id===t.id}))}));if(t.length>0){var a=new Map;this.selectedData.forEach((function(t){var n=e.extractMccFromMccId(t.mccId);a.set(n,t.id)}));var r,c=[],s=Object(i["a"])(n);try{for(s.s();!(r=s.n()).done;){var l=r.value,u=this.extractMccFromMccId(l.mccId),d=a.get(u);d&&d!==l.id||(this.selectedValues.includes(l.id)?d===l.id&&c.push(l):(c.push(l),a.set(u,l.id)))}}catch(g){s.e(g)}finally{s.f()}var p=new Set(n.map((function(t){return t.id}))),f=this.selectedData.filter((function(t){return!p.has(t.id)})),m=[].concat(Object(o["a"])(f),c);this.selectedData=m,this.selectedValues=m.map((function(t){return t.id})),this.selectedCountrySearch=Object(o["a"])(this.selectedValues)}else{var h=new Set(n.map((function(t){return t.id})));this.selectedData=this.selectedData.filter((function(t){return!h.has(t.id)})),this.selectedValues=this.selectedData.map((function(t){return t.id})),this.selectedCountrySearch=Object(o["a"])(this.selectedValues),this.sourceData.forEach((function(t){t._checked=!1}))}this.refreshUIState(),this.$emit("input",this.selectedValues),this.$emit("on-change",this.selectedData),this.updateCheckAllStatus()}else this.refreshUIState()},handleRemove:function(t){if("info"!==this.typeFlag){var e=this.selectedData.findIndex((function(e){return e.id===t.id}));if(e>-1){this.selectedData.splice(e,1),this.selectedValues=this.selectedData.map((function(t){return t.id}));var n=this.selectedCountrySearch.indexOf(t.id);n>-1&&this.selectedCountrySearch.splice(n,1),this.isAllSelected?(this.isAllSelected=!1,this.checkAll=!1,this.indeterminate=this.selectedData.length>0):this.updateCheckAllStatus(),this.refreshUIState(),this.$emit("input",this.selectedValues),this.$emit("on-change",this.selectedData)}}},selectCountry:function(t){this.currentContinent=t,this.continent="all"===t?"":{continentEn:t},this.currentPage=1,this.isAllSelected=!1,this.allCountryData=null,this.checkAll=!1,this.indeterminate=this.selectedData.length>0,this.loadData(!0)},getCountryOptionLabel:function(t){var e="zh-CN"===this.language?t.countryCn:t.countryEn,n="zh-CN"===this.language?t.remarkCn:t.remarkEn;return n?"".concat(e," (").concat(n,")"):e},processBatchSelection:function(){var t=this;if(this.selectedValues&&this.selectedValues.length>0&&0===this.selectedData.length&&this.allCountryData&&this.allCountryData.data&&this.allCountryData.data.records){var e=this.allCountryData.data.records.filter((function(e){return t.selectedValues.includes(e.mccId)}));e.length>0&&(this.selectedData=e.map((function(e){return{id:e.mccId,countryCn:e.countryCn,countryEn:e.countryEn,continentCn:e.continentCn,continentEn:e.continentEn,mcc:t.extractMccFromMccId(e.mccId),mccId:e.mccId,remarkCn:e.remarkCn||"",remarkEn:e.remarkEn||"",remark:"zh-CN"===t.language?e.remarkCn||"":e.remarkEn||""}})),this.selectedCountrySearch=Object(o["a"])(this.selectedValues))}},initCountrySearchOptions:function(){var t=this;if(this.allCountryData&&this.allCountryData.data&&this.allCountryData.data.records){var e=this.allCountryData.data.records;this.countrySearchOptions=e.map((function(e){return{mccId:e.mccId,countryCn:e.countryCn,countryEn:e.countryEn,continentCn:e.continentCn,continentEn:e.continentEn,mcc:t.extractMccFromMccId(e.mccId),remarkCn:e.remarkCn||"",remarkEn:e.remarkEn||""}})),this.selectedValues&&this.selectedValues.length>0&&(this.selectedCountrySearch=Object(o["a"])(this.selectedValues)),this.processBatchSelection()}},handleCountrySelectChange:function(t){var e=this;if(!t||0===t.length)return this.selectedValues=[],this.selectedData=[],this.refreshUIState(),this.$emit("input",this.selectedValues),void this.$emit("on-change",this.selectedData);var n=t.filter((function(t){return!e.selectedValues.includes(t)})),a=this.selectedValues.filter((function(e){return!t.includes(e)}));if(n.length>0){var r,o=Object(i["a"])(n);try{var c=function(){var t=r.value,n=e.countrySearchOptions.find((function(e){return e.mccId===t}));if(n){var a=e.extractMccFromMccId(n.mccId),i=e.selectedData.find((function(t){return e.extractMccFromMccId(t.mccId)===a&&t.mccId!==n.mccId}));if(i){e.$Message.warning(e.$t("country.alreadySelected")+i.countryCn);var o=e.selectedCountrySearch.indexOf(t);return-1!==o&&e.selectedCountrySearch.splice(o,1),1}var c={id:n.mccId,countryCn:n.countryCn,countryEn:n.countryEn,continentCn:n.continentCn,continentEn:n.continentEn,mcc:a,mccId:n.mccId,remarkCn:n.remarkCn||"",remarkEn:n.remarkEn||"",remark:"zh-CN"===e.language?n.remarkCn||"":n.remarkEn||"",_checked:!0};e.selectedData.push(c)}};for(o.s();!(r=o.n()).done;)c()}catch(d){o.e(d)}finally{o.f()}}if(a.length>0){var s,l=Object(i["a"])(a);try{var u=function(){var t=s.value,n=e.selectedData.findIndex((function(e){return e.id===t}));-1!==n&&e.selectedData.splice(n,1)};for(l.s();!(s=l.n()).done;)u()}catch(d){l.e(d)}finally{l.f()}}this.selectedValues=this.selectedData.map((function(t){return t.id})),this.refreshUIState(),this.$emit("input",this.selectedValues),this.$emit("on-change",this.selectedData),this.updateCheckAllStatus()},isCountryOptionDisabled:function(t){var e=this;if("info"===this.typeFlag)return!0;var n=this.extractMccFromMccId(t.mccId),a=this.selectedData.find((function(a){return a&&e.extractMccFromMccId(a.mccId)===n&&a.mccId!==t.mccId}));return!!a},getRowClass:function(t){if("info"===this.typeFlag)return"readonly-row";var e=this.selectedData.find((function(e){return e.mcc===t.mcc&&e.id!==t.id}));return e?"disabled-row":""},isItemSelectable:function(t){if("info"===this.typeFlag)return!1;var e=this.selectedData.find((function(e){return e.mcc===t.mcc&&e.id!==t.id}));return!e},maxTagPlaceholder:function(t){return"..."},handleCountrySelectBlur:function(t){!1===t&&this.$refs.countrySelect&&(this.$refs.countrySelect.query="")}},computed:{computedCheckAll:function(){var t=this;if(!this.allCountryData||!this.allCountryData.data)return!1;var e=[];this.allCountryData.data.records&&(e=this.allCountryData.data.records.filter((function(e){return"all"===t.currentContinent||e.continentEn===t.currentContinent})));var n=e.filter((function(e){var n=t.extractMccFromMccId(e.mccId);return!t.selectedData.some((function(a){return t.extractMccFromMccId(a.mccId)===n&&a.mccId!==e.mccId}))}));return 0!==n.length&&n.every((function(e){return t.selectedValues.includes(e.mccId)}))},computedIndeterminate:function(){var t=this;if(!this.allCountryData||!this.allCountryData.data)return!1;var e=[];this.allCountryData.data.records&&(e=this.allCountryData.data.records.filter((function(e){return"all"===t.currentContinent||e.continentEn===t.currentContinent})));var n=e.filter((function(e){var n=t.extractMccFromMccId(e.mccId);return!t.selectedData.some((function(a){return t.extractMccFromMccId(a.mccId)===n&&a.mccId!==e.mccId}))})),a=n.filter((function(e){return t.selectedValues.includes(e.mccId)})).length;return a>0&&a<n.length},sourceColumns:function(){return"zh-CN"===this.language?[{type:"selection",width:60,align:"center"},{title:this.$t("country.nameCn"),key:"countryCn",minWidth:120,align:"center",tooltip:!0},{title:this.$t("country.continentCn"),key:"continentCn",minWidth:120,align:"center",tooltip:!0},{title:this.$t("flow.remark"),key:"remark",minWidth:120,align:"center",tooltip:!0}]:[{type:"selection",width:60,align:"center"},{title:this.$t("country.nameEn"),key:"countryEn",minWidth:120,align:"center",tooltip:!0},{title:this.$t("country.continentEn"),key:"continentEn",minWidth:120,align:"center",tooltip:!0},{title:this.$t("flow.remark"),key:"remark",minWidth:120,align:"center",tooltip:!0}]},selectedColumns:function(){var t={title:this.$t("order.action"),slot:"action",width:110,align:"center"};return"zh-CN"===this.language?[{title:this.$t("country.nameCn"),key:"countryCn",minWidth:120,align:"center",tooltip:!0},{title:this.$t("country.continentCn"),key:"continentCn",minWidth:120,align:"center",tooltip:!0},{title:this.$t("flow.remark"),key:"remark",minWidth:120,align:"center",tooltip:!0},t]:[{title:this.$t("country.nameEn"),key:"countryEn",minWidth:120,align:"center",tooltip:!0},{title:this.$t("country.continentEn"),key:"continentEn",minWidth:120,align:"center",tooltip:!0},{title:this.$t("flow.remark"),key:"remark",minWidth:120,align:"center",tooltip:!0},t]}}},g=h,y=(n("1a36"),n("2877")),b=Object(y["a"])(g,u,d,!1,null,"df6608d2",null),v=b.exports,C=(n("90fe"),n("d2d0")),I=(n("3177"),{components:{AddCountry:v},props:{title:{type:String,default:""},typeFlag:{type:String,default:""},corpId:{type:String,default:""}},data:function(){var t=this,e=function(t,e,n){var a=/^[0-9]\d*$/;return a.test(e)},n=function(e,n,a){var r=e.field.match(/\d+/g),i=r[0],o=r[1],c=t.formObj.directAppInfos[i].appConsumption[o].upccTemplateId,s=t.formObj.directAppInfos[i].appId,l=c.filter((function(t){return void 0!==t&&null!==t&&""!==t})),u=c.every((function(t){return null!==t&&""!==t&&void 0!==t}));0==c.length||c.length<s.length?a(new Error(e.message)):u?c.length!=l.length?a(new Error(e.message)):a():a(new Error(e.message))},a=function(e,n,a){var r,i=e.field.match(/\d+/g),o=i[0],c=t.formObj.directAppInfos[o].noLimitTemplateId,s=t.formObj.directAppInfos[o].appId,l=c.filter((function(t){return void 0!==t&&null!==t&&""!==t})),u=l.every((function(t){return null!==t&&""!==t&&void 0!==t})),d=s.length,p=c.slice(0,d),f=p.filter((function(t){return void 0!==t&&null!==t&&""!==t})),m=t.formObj.directAppInfos[o].directType,h=t.formObj.directAppInfos[o].isUsePackage;r="1"==m?t.$t("directionalApp.dingTemMandatory"):"1"==h?t.$t("directionalApp.useTemMandatory"):t.$t("directionalApp.freeTemMandatory"),0==c.length||c.length<s.length?a(new Error(r)):u?p.length!=f.length?a(new Error(r)):a():a(new Error(r))},r=function(e,n,a){var r=e.field.match(/\d+/g),i=r[0],o=[];if(t.formObj.directAppInfos[i].appConsumption.length>1){t.formObj.directAppInfos[i].appConsumption.forEach((function(t,e){o.push(t.consumption)})),o=o.map(String);for(var c=new Set,s=[],l=0;l<o.length;l++)c.has(o[l])?s.push(o[l]):c.add(o[l]);s.includes(n)?a(new Error(e.message)):a()}else a()},i=function(e,n,a){var r=e.field.match(/\d+/g),i=r[0],o=r[1],c=t.formObj.directAppInfos[i].appConsumption[o].consumption;if(t.formObj.directAppInfos[i].appConsumption.length>1){var s=o>0?t.formObj.directAppInfos[i].appConsumption[o-1].consumption:null;s&&Number(c)<Number(s)?a(new Error(e.message)):a()}else a()},o=function(e,n,a){if("string"===typeof n&&(n=n.trim()),!n)return a(new Error(t.$t("support.inputUsage")));var r=/^[1-9]\d{0,9}$/;if(!r.test(n))return a(new Error(t.$t("support.wrongUsageFormat")));var i,o=e.field.match(/combinationList\.(\d+)\.consumption/),c=parseInt(o[1],10),s=t.formObj.combinationList,l=s[c],u=l.unit;i="MB"===u?BigInt(n):"GB"===u?1024n*BigInt(n):"TB"===u?1024n*BigInt(n)*1024n:BigInt(n);var d=9999999999n,p="";if(p="MB"===u?"9999999999MB":"GB"===u?Math.floor(Number(d/1024n))+"GB":"TB"===u?Math.floor(Number(d/1024n/1024n))+"TB":"9999999999MB",i>d)return a(new Error(t.$t("sessionInfo.UsageMax")+p));if(i<10n)return a(new Error(t.$t("sessionInfo.UsageMin")));var f=c-1;if(f>=0){var m=s[f],h=Number(m.consumption),g=m.unit,y=t.convertToMB(h,g);if(i<=y)return void a(new Error(t.$t("directionalApp.usageRule")))}a()};return{language:localStorage.getItem("local"),selectCountryData:[],countryShowType:!1,Unitedtotal:0,UnitedcurrentPage:1,Unitedpage:0,modal:!1,submitloading:!1,searchObjloading:!1,Unitedloading:!1,addRefuelModel:!1,notClick:!1,appTotal:!1,initialized:!0,selectedValues:[],oldValue:[],countryList:[],upccTemplateList:[],Uniteddata:[],refuelIDList:[],refuelIDLists:[],formObj:{nameCn:"",descCn:"",periodUnit:"",keepPeriod:"",effectiveDay:"",flowLimitType:"",controlLogic:"",isSupportedHotspots:"",mccList:"",templateName:"",combinationList:[{consumption:"",unit:"MB",upccTemplateId:""}],hasRefuelPackage:!1,selectionTypes:[],isSupportDirect:"",directAppInfos:[{index:1,appId:[],directType:"",appConsumption:[{index1:1,consumption:"",upccTemplateId:[]}],isUsePackage:"",noLimitTemplateId:[]}]},index:1,index1:1,upccIndex:"",upccChange:"",appInfos:[],directTemplateList:{},selectedOptions:[],submitList:[],searchObj:{gaspackname:"",gaspacknameid:""},periodUnitList:[{value:"1",label:this.$t("buymeal.hour")},{value:"2",label:this.$t("buymeal.day")},{value:"3",label:this.$t("buymeal.month")},{value:"4",label:this.$t("buymeal.year")}],unitList:[{value:"MB",label:"MB"},{value:"GB",label:"GB"},{value:"TB",label:"TB"}],Unitedcolumns:[{type:"selection",width:60,align:"center"},{title:this.$t("support.AddonID"),key:"id",minWidth:120,align:"center",tooltip:!0},{title:this.$t("support.AddonName"),key:"nameCn",minWidth:180,align:"center",tooltip:!0,render:function(e,n){var a=n.row,r="zh-CN"===t.$i18n.locale?a.nameCn:"en-US"===t.$i18n.locale?a.nameEn:"";return e("label",r)}},{title:this.$t("support.AddonAmount")+"(MB)",key:"flowValue",minWidth:120,align:"center",tooltip:!0}],ruleAddValidate:{nameCn:[{required:!0,message:this.$t("support.packageNameNotNull")}],descCn:[{required:!0,message:this.$t("support.packageDescriptionNotNull")}],periodUnit:[{required:!0,message:this.$t("support.selectType")}],keepPeriod:[{required:!0,message:this.$t("support.selectDuration")},{validator:e,message:this.$t("support.prongDurationFormat")},{validator:function(t,e,n){return Number(2147483647)>=Number(e)},message:this.$t("support.DurationValueLarge")}],effectiveDay:[{validator:function(t,e,n){return/^[0-9]\d*$/.test(e)||""==e},message:this.$t("support.wrongPackageValidity")},{validator:function(t,e,n){return Number(2147483647)>=Number(e)||""==e},message:this.$t("support.packageValidityLarge")}],flowLimitType:[{required:!0,message:this.$t("support.selectDataResetType")}],controlLogic:[{required:!0,message:this.$t("support.selectRestrictionLogic")}],isSupportedHotspots:[{required:!0,message:this.$t("support.isAupportHotspot")}],mccList:[{required:!0,message:this.$t("buymeal.selectCountry")}],consumption:[{required:!0,message:this.$t("support.inputUsage")},{validator:o}],upccTemplateId:[{required:!0,message:this.$t("support.selectSpeedTemplate")}],templateName:[{required:!0,message:this.$t("support.selectUnlimitedUsageTemplate")}],selectionTypes:[{validator:function(e,n,a){var r=t.formObj.selectionTypes.length;return r>0||!t.formObj.hasRefuelPackage},message:this.$t("support.AddonListMandatory")}],isSupportDirect:[{required:!0,message:this.$t("directionalApp.SupportDataMandatory")}],directType:[{required:!0,message:this.$t("directionalApp.LogicMandatory")}],appId:[{required:!0,type:"array",message:this.$t("directionalApp.APPMandatory")}],isUsePackage:[{required:!0,message:this.$t("directionalApp.ContinueMandatory")}],consumption1:[{required:!0,message:this.$t("directionalApp.valueMandatory")},{validator:function(t,e,n){var a=/^[1-9]\d*$/;return a.test(e)},message:this.$t("flow.Pleaseinteger")},{validator:r,message:this.$t("directionalApp.valueRepeat")},{validator:i,message:this.$t("directionalApp.gearRule")}],upccTemplateId1:[{required:!0,validator:n,message:this.$t("directionalApp.upccMandatory")}],noLimitTemplateId:[{required:!0,validator:a}]},editRowData:{},allCountryData:[]}},mounted:function(){this.packageGetDirectional(),this.language=localStorage.getItem("local")},computed:{choseAppInfos:function(){var t=this;return function(e){var n=JSON.parse(JSON.stringify(t.appInfos)),a=[];return t.formObj.directAppInfos.forEach((function(t){t.appId.map((function(t){return a.push(t),a}))})),n=n.filter((function(t){return e.includes(t.id)||-1==a.indexOf(t.id)?t:void 0})),n}}},methods:{convertToMB:function(t,e){return"MB"===e?t.toString():"GB"===e?(1024n*BigInt(t)).toString():"TB"===e?(1024n*BigInt(t)*1024n).toString():t.toString()},show:function(t,e){var n=this;return Object(l["a"])(Object(c["a"])().mark((function a(){var r;return Object(c["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(n.countryList=[],n.allCountryData=[],n.formObj.mccList=[],n.editRowData={},!t){a.next=35;break}return n.editRowData=JSON.parse(JSON.stringify(t)),n.refuelIDList=JSON.parse(JSON.stringify(t.refuelIDList)),n.formObj.id=t.id,n.packageGetDirectional(),n.formObj.isSupportDirect=t.isSupportDirect,"1"==t.isSupportDirect&&n.setData(),"true"==e&&"1"==t.isSupportDirect?n.getPackageSale(t.id):n.notClick=!1,n.formObj.nameCn=t.nameCn,n.formObj.descCn=t.descCn,n.formObj.periodUnit=t.periodUnit,n.formObj.effectiveDay=t.effectiveDay,n.formObj.keepPeriod=t.keepPeriod,n.formObj.flowLimitType=Number(t.flowLimitType),n.formObj.controlLogic=Number(t.controlLogic),n.formObj.isSupportedHotspots=Number(t.isSupportedHotspots),n.$refs.addCountry.currentContinent="all",n.$refs.addCountry.continent="",a.next=24,n.$refs.addCountry.getAllCountryData();case 24:return r=a.sent,n.allCountryData=r.data.records,a.next=28,n.getcountryList(n.formObj.isSupportedHotspots);case 28:n.formObj.templateName=t.noLimitTemplateId,n.formObj.mccList=t.mccList,n.formObj.combinationList=[],n.formObj.mccMap=t.mccMap,n.formObj.hasRefuelPackage=t.refuelIDList.length>0,n.formObj.selectionTypes=t.refuelIDList,t.packageConsumptions.map((function(t){var e=t.displayConsumption.split(" "),a=Object(s["a"])(e,2),r=a[0],i=a[1],o=i||"MB";n.formObj.combinationList.push({consumption:r,displayConsumption:t.displayConsumption,unit:o,upccTemplateId:t.upccTemplateId,templateName:t.templateName})}));case 35:n.modal=!0;case 36:case"end":return a.stop()}}),a)})))()},addItem:function(){this.formObj.combinationList.push({consumption:"",unit:"MB",upccTemplateId:""})},delItem:function(t){this.formObj.combinationList.splice(t,1)},getcontrolLogic:function(){this.formObj.controlLogic=2===this.formObj.flowLimitType?1:null},getcountryList:function(t){var e=this;return Object(l["a"])(Object(c["a"])().mark((function n(){return Object(c["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:e.formObj.combinationList=[{consumption:"",unit:"MB"}],t&&(e.isSupportGetMccList(t),e.getUpccList(t));case 2:case"end":return n.stop()}}),n)})))()},showCountry:function(){var t=this;this.$refs.addCountry.selectedData=[],this.$refs.addCountry.selectedValues=Object(o["a"])(this.formObj.mccList),this.$refs.addCountry.currentContinent="all",this.$refs.addCountry.continent="",this.$refs.addCountry.getAllCountryData().then((function(){t.$refs.addCountry.loadData(!0),t.countryShowType=!0}))},fuelPackaChange:function(){this.formObj.selectionTypes=[]},RefuelPackageList:function(){"info"===this.typeFlag?this.getDetailsRefuelList(1):this.getRefuelList(1)},getDetailsRefuelList:function(t){var e=this;this.Unitedloading=!0,Object(m["r"])({pageNum:t,pageSize:10,refuelID:this.searchObj.gaspacknameid,refuelName:this.searchObj.gaspackname,packageID:this.formObj.id}).then((function(n){if(!n||"0000"!=n.code)throw n;e.Uniteddata=n.data,e.Unitedtotal=n.count,e.UnitedcurrentPage=t,e.formObj.selectionTypes.forEach((function(t){n.data.forEach((function(n){n.id==t.id&&(e.$set(n,"_checked",!0),e.$set(n,"_disabled",!0))}))})),e.addRefuelModel=!0})).catch((function(t){})).finally((function(){e.Unitedloading=!1,e.searchObjloading=!1}))},getRefuelList:function(t){var e=this;this.Unitedloading=!0,Object(m["v"])({pageNum:t,pageSize:10,refuelID:this.searchObj.gaspacknameid,refuelName:this.searchObj.gaspackname,corpId:this.corpId}).then((function(n){if(!n||"0000"!=n.code)throw n;e.Uniteddata=n.data,e.Unitedtotal=n.count,e.UnitedcurrentPage=t,e.formObj.selectionTypes.forEach((function(t){n.data.forEach((function(n){n.id==t.id&&e.$set(n,"_checked",!0)}))})),e.addRefuelModel=!0})).catch((function(t){})).finally((function(){e.Unitedloading=!1,e.searchObjloading=!1}))},search:function(){this.searchObjloading=!0,"info"===this.typeFlag?this.getDetailsRefuelList(1):this.getRefuelList(1)},handleRowChange:function(t){var e=this;this.selection=t,t.map((function(t,n){var a=!0;e.formObj.selectionTypes.map((function(e,n){t.id===e.id&&(a=!1)})),a&&e.formObj.selectionTypes.push(t)}))},cancelPackage:function(t,e){var n=this;this.formObj.selectionTypes.forEach((function(t,a){t.id===e.id&&n.formObj.selectionTypes.splice(a,1)}))},cancelPackageAll:function(t,e){this.formObj.selectionTypes=[]},Confirm:function(){this.addRefuelModel=!1,this.searchObj.gaspackname="",this.searchObj.gaspacknameid="",this.$refs["formObj"].validateField("selectionTypes",(function(t){})),this.refuelIDLists=JSON.parse(JSON.stringify(this.formObj.selectionTypes))},ConfirmCountry:function(){this.countryShowType=!1,this.selectCountryData=this.$refs.addCountry.selectedData,this.formObj.mccList=this.selectCountryData.map((function(t){return t.mccId})),this.countryList=this.selectCountryData.map((function(t){return{countryId:t.mccId,countryName:t.mccId.includes("-")?t.countryEn+"("+t.remark+")":t.countryEn}})),this.$refs.addCountry.currentContinent="all",this.$refs.addCountry.currentPage=1},cancelBagModal:function(){this.addRefuelModel=!1,this.searchObj.gaspackname="",this.searchObj.gaspacknameid="","info"!==this.typeFlag&&(0==this.refuelIDLists.length?this.formObj.selectionTypes=JSON.parse(JSON.stringify(this.refuelIDList)):this.formObj.selectionTypes=JSON.parse(JSON.stringify(this.refuelIDLists)))},canceCountry:function(){this.countryShowType=!1,this.$refs.addCountry.currentContinent="all",this.$refs.addCountry.currentPage=1,this.countryList=[]},UnitedgoPage:function(t){"info"===this.typeFlag?this.getDetailsRefuelList(t):this.getRefuelList(t)},cancelModal:function(){var t=this;this.modal=!1,this.formObj.controlLogic="",this.formObj.combinationList=[{consumption:"",unit:"MB",upccTemplateId:""}],this.selectCountryData=[],this.$nextTick((function(){t.$refs["formObj"].resetFields()})),this.formObj.directAppInfos=[{index:1,appId:[],directType:"",appConsumption:[{index1:1,consumption:"",upccTemplateId:[]}],isUsePackage:"",noLimitTemplateId:[]}],this.formObj.mccList=[],this.refuelIDLists=[],this.notClick=!1,this.editRowData={}},transferData:function(t){var e,n=[],a=Object(i["a"])(t);try{var r=function(){var t=e.value,a=[];t.appId.forEach((function(e,n){if(t.appConsumption){var r=t.appConsumption.map((function(t){return{consumption:parseInt(t.consumption),upccTemplateId:t.upccTemplateId[n]}})),i=t.noLimitTemplateId,o={appConsumption:"2"==t.directType?r:[],appId:e,noLimitTemplateId:i[n]};a.push(o)}}));var r={appDetailInfos:a,directType:t.directType,isUsePackage:t.isUsePackage};n.push(r)};for(a.s();!(e=a.n()).done;)r()}catch(o){a.e(o)}finally{a.f()}this.submitList=n},submit:function(){var t=this;"1"==this.formObj.isSupportDirect&&this.transferData(this.formObj.directAppInfos),this.$refs["formObj"].validate((function(e){if(e){var n=JSON.parse(JSON.stringify(t.formObj.combinationList));n.forEach((function(e){e.consumption=t.convertToMB(e.consumption,e.unit)}));var a=JSON.parse(JSON.stringify(n));a.forEach((function(t){delete t.displayConsumption,delete t.templateName}));var r=[];t.formObj.hasRefuelPackage&&t.formObj.selectionTypes.forEach((function(t,e){r.push(t.id)})),t.submitloading=!0,"info"===t.typeFlag||"add"===t.typeFlag?Object(C["a"])({nameCn:t.formObj.nameCn,descCn:t.formObj.descCn,periodUnit:t.formObj.periodUnit,keepPeriod:t.formObj.keepPeriod,effectiveDay:t.formObj.effectiveDay,flowLimitType:t.formObj.flowLimitType,controlLogic:t.formObj.controlLogic,isSupportedHotspots:t.formObj.isSupportedHotspots,packageConsumptions:"update"===t.typeFlag?a:n,noLimitTemplateId:t.formObj.templateName,mccList:t.formObj.mccList,corpId:t.corpId,groupId:t.formObj.groupId,supportRefuel:t.formObj.hasRefuelPackage?1:2,refuelList:r,isSupportDirect:0==t.appInfos.length?2:t.formObj.isSupportDirect,directAppInfos:"1"==t.formObj.isSupportDirect?t.submitList:[]}).then((function(e){if(!e||"0000"!=e.code)throw e;t.$emit("goPageFirst",1),t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("common.Successful")}),t.cancelModal()})).catch((function(t){})).finally((function(){t.submitloading=!1})):"update"===t.typeFlag&&Object(C["i"])({id:t.formObj.id,nameCn:t.formObj.nameCn,descCn:t.formObj.descCn,periodUnit:t.formObj.periodUnit,keepPeriod:t.formObj.keepPeriod,effectiveDay:t.formObj.effectiveDay,flowLimitType:t.formObj.flowLimitType,controlLogic:t.formObj.controlLogic,isSupportedHotspots:t.formObj.isSupportedHotspots,packageConsumptions:"update"===t.typeFlag?a:n,noLimitTemplateId:t.formObj.templateName,mccList:t.formObj.mccList,corpId:t.corpId,groupId:t.formObj.groupId,supportRefuel:t.formObj.hasRefuelPackage?1:2,refuelList:r,isSupportDirect:t.formObj.isSupportDirect,directAppInfos:"1"==t.formObj.isSupportDirect?t.submitList:[]}).then((function(e){if(!e||"0000"!=e.code)throw e;t.$emit("goPageFirst",1),t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("common.Successful")}),t.cancelModal()})).catch((function(t){})).finally((function(){t.submitloading=!1}))}}))},addApp:function(){this.index++,this.formObj.directAppInfos.push({index:this.index,appId:[],directType:"",appConsumption:[{index1:1,consumption:"",upccTemplateId:[]}],isUsePackage:"",noLimitTemplateId:[]})},deleteApp:function(t){this.formObj.directAppInfos.splice(t,1),this.index--;var e=[];this.formObj.directAppInfos.forEach((function(t){t.appId.map((function(t){return e.push(t),e}))})),e.length>="9"?this.appTotal=!0:this.appTotal=!1},delFlowValue:function(t,e){var n=this.formObj.directAppInfos[t];n.appConsumption.splice(e,1),this.index1--},addFlowValue:function(t){var e=this.formObj.directAppInfos[t];this.index1++,e.appConsumption.push({index1:this.index1,consumption:"",upccTemplateId:[]})},changeDirect:function(t){},changeLogic:function(t){var e=this.formObj.directAppInfos[t];e.appConsumption=[{index1:this.index1,consumption:"",upccTemplateId:[]}],e.isUsePackage="",e.noLimitTemplateId=[]},changeUsePackage:function(t){},changeAppId:function(t,e){var n=[];if(this.formObj.directAppInfos.forEach((function(t){t.appId.map((function(t){return n.push(t),n}))})),n.length>="9"?this.appTotal=!0:this.appTotal=!1,this.upccChange=t,this.upccIndex=e,this.initialized){if(console.error(this.initialized,"处理数据，初始化已经完成"),this.formObj.directAppInfos[e].appId==t){var a=this.oldValue;if(this.oldValue=t,t.length<a.length){var r=function(t,e){for(var n=0;n<t.length;n++)if(-1===e.indexOf(t[n]))return n;return-1},i="";i=r(a,t);var o=this.formObj.directAppInfos[e].appConsumption;o.forEach((function(t){t.upccTemplateId.splice(i,1)}))}}}else console.error(this.initialized,"不处理")},packageGetDirectional:function(){var t=this;Object(m["y"])({corpId:sessionStorage.getItem("corpId")}).then((function(e){if(!e||"0000"!=e.code)throw e;t.appInfos=e.data,t.appInfos.map((function(e,n){t.directTemplateList[e.id]=e.appUpccInfo})),t.initialized=!1})).catch((function(t){})).finally((function(){t.initialized=!0}))},getPackageSale:function(t){var e=this;Object(m["u"])({packageId:t}).then((function(t){if(!t||"0000"!=t.code)throw t;e.notClick=t.data})).catch((function(t){})).finally((function(){}))},getUpccList:function(t){var e=this;Object(C["g"])({corpId:this.corpId,isSupportedHotspots:t}).then((function(t){"0000"==t.code&&(e.upccTemplateList=t.data)})).catch((function(t){console.error(t)})).finally((function(){}))},isSupportGetMccList:function(t){var e=this,n=this;Object(m["x"])({corpId:this.corpId,supportedHotspots:t}).then((function(t){if("0000"==t.code)if(e.formObj.groupId=t.data.groupIds,Array.isArray(n.allCountryData)&&Array.isArray(e.editRowData.mccList)){var a=n.allCountryData.filter((function(t){return e.editRowData.mccList.includes(t.mccId)}));e.countryList=a.map((function(t){var n="zh-CN"==e.language?"("+t.remarkCn+")":"("+t.remarkEn+")",a=t.mccId.includes("-")?t.countryEn+""+n:t.countryEn;return{countryId:t.mccId,countryName:a}}))}else e.countryList=[]})).catch((function(t){console.error(t)})).finally((function(){}))},setData:function(){var t=this;Object(m["l"])({packageId:this.formObj.id}).then((function(e){if(!e||"0000"!=e.code)throw e;var n=[];e.data.forEach((function(e,a){var r={index:a+1,appId:[],directType:e.directType,noLimitTemplateId:e.appDetailInfos.map((function(t){return t.noLimitTemplateId})),isUsePackage:e.isUsePackage};e.appDetailInfos.forEach((function(e){r.appId.push(e.appId),e.appConsumption.length>0?e.appConsumption.forEach((function(t){r.appConsumption||(r.appConsumption=[]);var e=r.appConsumption.find((function(e){return e.consumption===t.consumption}));e?e.upccTemplateId.push(t.upccTemplateId):r.appConsumption.push({index1:r.index,consumption:t.consumption,upccTemplateId:[t.upccTemplateId]})})):r.appConsumption=[{index1:t.index1,consumption:"",upccTemplateId:[]}]})),n.push(r)})),t.formObj.directAppInfos=n,t.initialized=!1})).catch((function(t){})).finally((function(){t.initialized=!0}))}}}),k=I,O=(n("6f25"),Object(y["a"])(k,a,r,!1,null,"0a90788f",null));e["a"]=O.exports},addb:function(t,e,n){"use strict";var a=n("f36a"),r=Math.floor,i=function(t,e){var n=t.length;if(n<8){var o,c,s=1;while(s<n){c=s,o=t[s];while(c&&e(t[c-1],o)>0)t[c]=t[--c];c!==s++&&(t[c]=o)}}else{var l=r(n/2),u=i(a(t,0,l),e),d=i(a(t,l),e),p=u.length,f=d.length,m=0,h=0;while(m<p||h<f)t[m+h]=m<p&&h<f?e(u[m],d[h])<=0?u[m++]:d[h++]:m<p?u[m++]:d[h++]}return t};t.exports=i},c337:function(t,e,n){"use strict";n("209e")},d1c9:function(t,e,n){"use strict";n("b64b");var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"dual-table-container",style:{height:t.containerHeight}},[e("div",{staticClass:"source-table"},[e("div",{staticClass:"table-header"},[t._t("source-header",(function(){return[e("div",{staticStyle:{height:"32px","line-height":"32px","margin-right":"10px"}},[t._v("渠道商简称")]),t.showDefaultSearch?e("Input",{staticStyle:{width:"200px","margin-right":"8px"},attrs:{placeholder:t.searchPlaceholder,clearable:""},model:{value:t.searchKeyword,callback:function(e){t.searchKeyword=e},expression:"searchKeyword"}}):t._e(),t.showDefaultSearch?e("Button",{attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("搜索")]):t._e(),t.showCheckAll?e("Checkbox",{staticStyle:{"margin-left":"auto"},attrs:{indeterminate:t.indeterminate,value:t.checkAll},nativeOn:{click:function(e){return e.preventDefault(),t.handleCheckAll.apply(null,arguments)}}},[t._v("全选")]):t._e()]})),t._t("all-check-box")],2),e("div",{staticClass:"table-content"},[e("Table",{ref:"sourceTable",attrs:{columns:t.sourceColumns,data:t.sourceData,loading:t.loading,height:t.tableHeight,ellipsis:!0,border:""},on:{"on-select":t.handleSelect,"on-select-cancel":t.handleSelectCancel,"on-select-all":t.handleSelectAll,"on-select-all-cancel":t.handleSelectAllCancel},scopedSlots:t._u([t._l(Object.keys(t.$slots),(function(e){return{key:e,fn:function(n){return[t._t(e,null,null,n)]}}}))],null,!0)},[e("template",{slot:"noDataText"},[e("span",[t._v(t._s(t.loading?"加载中...":"暂无数据"))])])],2),t.showPagination?e("Page",{staticStyle:{"margin-top":"10px","text-align":"right"},attrs:{total:t.total,current:t.current,"page-size":t.pageSize,size:"small","show-total":"","show-elevator":""},on:{"on-change":t.handlePageChange}}):t._e()],1)]),e("div",{staticClass:"selected-table"},[e("div",{staticClass:"table-header"},[t._t("selected-header",(function(){return[e("div",{staticClass:"selected-title"},[t._v("已选数据")])]}))],2),e("div",{staticClass:"table-content"},[e("Table",{attrs:{columns:t.selectedColumns,data:t.selectedData,height:t.tableHeight,ellipsis:!0,border:""},scopedSlots:t._u([t._l(Object.keys(t.$slots),(function(e){return{key:e,fn:function(n){return[t._t(e,null,null,n)]}}})),{key:"action",fn:function(n){var a=n.row;return[e("Button",{attrs:{type:"error",size:"small"},on:{click:function(e){return t.handleRemove(a)}}},[e("Icon",{attrs:{type:"md-close"}})],1)]}}],null,!0)})],1)])])},r=[],i=(n("a9e3"),{name:"DualTableSelect",props:{sourceColumns:{type:Array,required:!0},sourceData:{type:Array,default:function(){return[]}},selectedColumns:{type:Array,required:!0},containerHeight:{type:String,default:""},tableHeight:{type:[Number,String],default:520},showDefaultSearch:{type:Boolean,default:!0},searchPlaceholder:{type:String,default:"请输入关键词搜索"},showCheckAll:{type:Boolean,default:!0},showPagination:{type:Boolean,default:!0},total:{type:Number,default:0},current:{type:Number,default:1},pageSize:{type:Number,default:10},loading:{type:Boolean,default:!1},value:{type:Array,default:function(){return[]}},selectedData:{type:Array,default:function(){return[]}},indeterminate:{type:Boolean,default:!1},checkAll:{type:Boolean,default:!1}},data:function(){return{searchKeyword:""}},methods:{handleSearch:function(){this.$emit("on-search",this.searchKeyword)},handleCheckAll:function(){this.$emit("on-check-all",!this.checkAll)},handlePageChange:function(t){this.$emit("on-page-change",t)},handleSelect:function(t,e){this.$emit("on-select",t,e)},handleSelectCancel:function(t,e){this.$emit("on-select-cancel",t,e)},handleSelectAll:function(t){this.$emit("on-select-all",t)},handleSelectAllCancel:function(t){this.$emit("on-select-all-cancel",t)},handleRemove:function(t){this.$emit("on-remove",t)},reset:function(){this.searchKeyword=""}}}),o=i,c=(n("c337"),n("2877")),s=Object(c["a"])(o,a,r,!1,null,"5355fcb0",null);e["a"]=s.exports},d2d0:function(t,e,n){"use strict";n.d(e,"e",(function(){return i})),n.d(e,"a",(function(){return o})),n.d(e,"i",(function(){return c})),n.d(e,"c",(function(){return s})),n.d(e,"g",(function(){return l})),n.d(e,"h",(function(){return u})),n.d(e,"f",(function(){return d})),n.d(e,"d",(function(){return p})),n.d(e,"b",(function(){return f}));n("99af");var a=n("66df"),r="/pms/api/v1/channelBuiltPackage",i=function(t){return a["a"].request({url:r+"/getChannelList",data:t,method:"post"})},o=function(t){return a["a"].request({url:r+"/add",data:t,method:"post"})},c=function(t){return a["a"].request({url:r+"/update",data:t,method:"post"})},s=function(t){return a["a"].request({url:r+"/batchDelete",data:t,method:"delete"})},l=function(t){return a["a"].request({url:"/pms/api/v1/upccTemplate/packageGetUpcc",params:t,method:"get"})},u=function(t){return a["a"].request({url:"/cms/channel/distributors/judgeChannelCreatePackage",params:t,method:"get"})},d=function(t){return a["a"].request({url:"/pms/refuelPackage/channelSelf/get",data:t,method:"post"})},p=function(t,e){return a["a"].request({url:"/pms/refuelPackage/channelSelf/del/".concat(t,"/").concat(e),method:"delete"})},f=function(t){return a["a"].request({url:"/pms/refuelPackage/channelSelf/add",data:t,method:"post"})}},ea83:function(t,e,n){"use strict";var a=n("b5db"),r=a.match(/AppleWebKit\/(\d+)\./);t.exports=!!r&&+r[1]}}]);