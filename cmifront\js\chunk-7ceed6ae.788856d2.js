(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7ceed6ae"],{"53c4":function(t,e,s){},"63ca":function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"payment-page"},[e("div",{staticClass:"payment-status"},[t._m(0),e("div",{staticClass:"status-message"},[e("p",{staticClass:"error-details"},[t._v(t._s(t.$t("paymentResultpageTexts.errorDetails")))])])]),e("div",{staticClass:"button-group"},[e("button",{staticClass:"btn btn-blue",on:{click:t.viewOrder}},[t._v(t._s(t.$t("paymentResultpageTexts.viewOrder")))]),e("button",{staticClass:"btn",on:{click:t.goToHome}},[t._v(t._s(t.$t("paymentResultpageTexts.goToHome")))])])])},n=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"status-icon error"},[e("span",[t._v("!")])])}],o=(s("14d9"),{name:"PaymentPage",data:function(){return{isSuccess:!1,failureReason:"Invalid Card Number"}},methods:{viewOrder:function(){console.log("查看订单被点击"),this.$router.push({path:"/paymentOrder/management"})},goToHome:function(){this.$router.push({path:"/"})},retryPayment:function(){console.log("重新支付被点击")}},mounted:function(){}}),i=o,r=(s("870e"),s("2877")),c=Object(r["a"])(i,a,n,!1,null,"2efba02a",null);e["default"]=c.exports},"870e":function(t,e,s){"use strict";s("53c4")}}]);