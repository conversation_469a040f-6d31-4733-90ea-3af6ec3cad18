import { defineStore } from 'pinia'
import { store } from '../index'
import { UserLoginType, UserType } from '@/api/login/types'
import { ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { loginOutApi } from '@/api/login'
import { useTagsViewStore } from './tagsView'
import router from '@/router'
import {
  login,
  logout,
  getUserInfo,
  getMenuList,
  ssoLoginGDS,
  searchcorpid,
  logoutClearRedis,
  type LoginData,
  type UserInfo,
  type MenuData,
  type LoginResponse
} from '@/api/cmi/auth'
import { cmiConfig } from '@/config/cmi'

interface UserState {
  userInfo?: UserType
  tokenKey: string
  token: string
  roleRouters?: string[] | AppCustomRouteRecordRaw[]
  rememberMe: boolean
  loginInfo?: UserLoginType
  // CMI 项目特有字段
  userId: string
  userName: string
  menuList: MenuData[]
  permissions: string[]
  roleId: string
  isUpdatePassword: number  // 密码是否需要更新：1-需要，2-不需要
  userBtnPriv: Array<{ url: string; priv: string[] }>  // 按钮权限
}

export const useUserStore = defineStore('user', {
  state: (): UserState => {
    return {
      userInfo: undefined,
      tokenKey: 'Authorization',
      token: '',
      roleRouters: undefined,
      // 记住我
      rememberMe: true,
      loginInfo: undefined,
      // CMI 项目特有字段
      userId: '',
      userName: '',
      menuList: [],
      permissions: [],
      roleId: '',
      isUpdatePassword: 2,
      userBtnPriv: []
    }
  },
  getters: {
    getTokenKey(): string {
      return this.tokenKey
    },
    getToken(): string {
      return this.token
    },
    getUserInfo(): UserType | undefined {
      return this.userInfo
    },
    getRoleRouters(): string[] | AppCustomRouteRecordRaw[] | undefined {
      return this.roleRouters
    },
    getRememberMe(): boolean {
      return this.rememberMe
    },
    getLoginInfo(): UserLoginType | undefined {
      return this.loginInfo
    },
    // CMI 项目特有 getters
    getUserId(): string {
      return this.userId
    },
    getUserName(): string {
      return this.userName
    },
    getMenuList(): MenuData[] {
      return this.menuList
    },
    getPermissions(): string[] {
      return this.permissions
    },
    getRoleId(): string {
      return this.roleId
    },
    getIsUpdatePassword(): number {
      return this.isUpdatePassword
    },
    getUserBtnPriv(): Array<{ url: string; priv: string[] }> {
      return this.userBtnPriv
    }
  },
  actions: {
    setTokenKey(tokenKey: string) {
      this.tokenKey = tokenKey
    },
    setToken(token: string) {
      this.token = token
    },
    setUserInfo(userInfo?: UserType) {
      this.userInfo = userInfo
    },
    setRoleRouters(roleRouters: string[] | AppCustomRouteRecordRaw[]) {
      this.roleRouters = roleRouters
    },
    logoutConfirm() {
      const { t } = useI18n()
      ElMessageBox.confirm(t('common.loginOutMessage'), t('common.reminder'), {
        confirmButtonText: t('common.ok'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      })
        .then(async () => {
          const res = await loginOutApi().catch(() => {})
          if (res) {
            this.reset()
          }
        })
        .catch(() => {})
    },
    reset() {
      const tagsViewStore = useTagsViewStore()
      tagsViewStore.delAllViews()
      this.setToken('')
      this.setUserInfo(undefined)
      this.setRoleRouters([])
      // 清理 CMI 特有状态
      this.setUserId('')
      this.setUserName('')
      this.setMenuList([])
      this.setPermissions([])
      this.setRoleId('')
      this.setIsUpdatePassword(2)
      this.setUserBtnPriv([])
      // 清理会话存储
      sessionStorage.clear()
      const navLang = localStorage.getItem('local')
      localStorage.clear()
      if (navLang) {
        localStorage.setItem('local', navLang)
      }
      router.replace('/login')
    },
    logout() {
      this.reset()
    },
    setRememberMe(rememberMe: boolean) {
      this.rememberMe = rememberMe
    },
    setLoginInfo(loginInfo: UserLoginType | undefined) {
      this.loginInfo = loginInfo
    },
    // CMI 项目特有 actions
    setUserId(userId: string) {
      this.userId = userId
    },
    setUserName(userName: string) {
      this.userName = userName
    },
    setMenuList(menuList: MenuData[]) {
      this.menuList = menuList
    },
    setPermissions(permissions: string[]) {
      this.permissions = permissions
    },
    setRoleId(roleId: string) {
      this.roleId = roleId
    },
    setIsUpdatePassword(flag: number) {
      this.isUpdatePassword = flag
    },
    setUserBtnPriv(btnPrivs: Array<{ url: string; priv: string[] }>) {
      this.userBtnPriv = btnPrivs
    },
    resetToken() {
      this.token = ''
    },
    // CMI 登录方法
    async cmiLogin(loginData: LoginData) {
      try {
        const res = await login(loginData)
        if ((res as any).code === '0000' && res.data) {
          const userDetails = (res.data as any).userDetails
          const oauth2AccessToken = (res.data as any).oauth2AccessToken

          // 设置基本用户信息
          this.setToken(oauth2AccessToken.access_token)
          this.setUserId(userDetails.id)
          this.setUserName(userDetails.username)
          this.setRoleId(userDetails.roleId)
          this.setIsUpdatePassword(userDetails.rePassword)

          // 处理权限信息
          const privList = userDetails.pagePrivileges || []
          const access = privList.map((priv: any) => priv.access)
          const btnPrivs = privList.map((priv: any) => ({
            url: priv.url,
            priv: priv.buttons
          }))

          this.setPermissions(access)
          this.setUserBtnPriv(btnPrivs)

          return res
        }
        throw new Error((res as any).msg || '登录失败')
      } catch (error) {
        throw error
      }
    },
    // CMI 获取用户信息
    async getCMIUserInfo() {
      try {
        const res = await getUserInfo()
        if (res.data) {
          this.setUserInfo(res.data as any)
          return res.data
        }
        throw new Error((res as any).msg || '获取用户信息失败')
      } catch (error) {
        throw error
      }
    },
    // CMI 获取菜单列表
    async getCMIMenuList() {
      try {
        const res = await getMenuList()
        if (res.data) {
          this.setMenuList(res.data as unknown as MenuData[])
          return res.data
        }
        throw new Error((res as any).msg || '获取菜单失败')
      } catch (error) {
        throw error
      }
    },
    // CMI SSO 登录方法
    async cmiSSOLogin(ssoData: any) {
      try {
        const res = await ssoLoginGDS(ssoData)
        if ((res as any).code === '0000' && res.data) {
          const userDetails = (res.data as any).userDetails
          const oauth2AccessToken = (res.data as any).oauth2AccessToken

          // 设置基本用户信息
          this.setToken(oauth2AccessToken.access_token)
          this.setUserId(userDetails.id)
          this.setUserName(userDetails.username)
          this.setRoleId(userDetails.roleId)
          this.setIsUpdatePassword(userDetails.rePassword)

          // 处理权限信息
          const privList = userDetails.pagePrivileges || []
          const access = privList.map((priv: any) => priv.access)
          const btnPrivs = privList.map((priv: any) => ({
            url: priv.url,
            priv: priv.buttons
          }))

          this.setPermissions(access)
          this.setUserBtnPriv(btnPrivs)

          return res
        }
        throw new Error((res as any).msg || 'SSO登录失败')
      } catch (error) {
        throw error
      }
    },
    // CMI 登出方法
    async cmiLogout() {
      try {
        // await logout()
        // 清除 Redis 中的登录信息
        if (this.userName) {
          await logoutClearRedis(this.userName)
        }
      } catch (error) {
        console.error('登出请求失败:', error)
      } finally {
        this.reset()
      }
    }
  },
  persist: true
})

export const useUserStoreWithOut = () => {
  return useUserStore(store)
}
