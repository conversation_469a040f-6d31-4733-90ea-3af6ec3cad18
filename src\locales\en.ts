export default {
  common: {
    inputText: 'Please input',
    selectText: 'Please select',
    startTimeText: 'Start time',
    endTimeText: 'End time',
    login: 'Login',
    required: 'This is required',
    loginOut: 'Login out',
    document: 'Document',
    reminder: 'Reminder',
    loginOutMessage: 'Exit the system?',
    back: 'Back',
    ok: 'OK',
    cancel: 'Cancel',
    reload: 'Reload current',
    closeTab: 'Close current',
    closeTheLeftTab: 'Close left',
    closeTheRightTab: 'Close right',
    closeOther: 'Close other',
    closeAll: 'Close all',
    prevLabel: 'Prev',
    nextLabel: 'Next',
    skipLabel: 'Jump',
    doneLabel: 'End',
    menu: 'Menu',
    menuDes: 'Menu bar rendered in routed structure',
    collapse: 'Collapse',
    collapseDes: 'Expand and zoom the menu bar',
    tagsView: 'Tags view',
    tagsViewDes: 'Used to record routing history',
    tool: 'Tool',
    toolDes: 'Used to set up custom systems',
    query: 'Query',
    reset: 'Reset',
    shrink: 'Put away',
    expand: 'Expand',
    delMessage: 'Delete the selected data?',
    delWarning: 'Warning',
    delOk: 'OK',
    delCancel: 'Cancel',
    delNoData: 'Please select the data to delete',
    delSuccess: 'Deleted successfully',
    refresh: 'Refresh',
    fullscreen: 'Fullscreen',
    size: 'Size',
    columnSetting: 'Column setting',
    lengthRange: 'The length should be between {min} and {max}',
    notSpace: 'Spaces are not allowed',
    notSpecialCharacters: 'Special characters are not allowed',
    isEqual: 'The two are not equal',
    // 列设置
    setting: 'Setting'
  },
  lock: {
    lockScreen: 'Lock screen',
    lock: 'Lock',
    lockPassword: 'Lock screen password',
    unlock: 'Click to unlock',
    backToLogin: 'Back to login',
    entrySystem: 'Entry the system',
    placeholder: 'Please enter the lock screen password',
    message: 'Lock screen password error'
  },
  error: {
    noPermission: `Sorry, you don't have permission to access this page.`,
    pageError: 'Sorry, the page you visited does not exist.',
    networkError: 'Sorry, the server reported an error.',
    returnToHome: 'Return to home'
  },
  setting: {
    projectSetting: 'Project setting',
    theme: 'Theme',
    layout: 'Layout',
    systemTheme: 'System theme',
    menuTheme: 'Menu theme',
    interfaceDisplay: 'Interface display',
    breadcrumb: 'Breadcrumb',
    breadcrumbIcon: 'Breadcrumb icon',
    collapseMenu: 'Collapse menu',
    hamburgerIcon: 'Hamburger icon',
    screenfullIcon: 'Screenfull icon',
    sizeIcon: 'Size icon',
    localeIcon: 'Locale icon',
    tagsView: 'Tags view',
    logo: 'Logo',
    greyMode: 'Grey mode',
    fixedHeader: 'Fixed header',
    headerTheme: 'Header theme',
    cutMenu: 'Cut Menu',
    copy: 'Copy',
    clearAndReset: 'Clear cache and reset',
    copySuccess: 'Copy success',
    copyFailed: 'Copy failed',
    footer: 'Footer',
    uniqueOpened: 'Unique opened',
    tagsViewIcon: 'Tags view icon',
    // 开启动态路由
    dynamicRouter: 'Enable dynamic router',
    serverDynamicRouter: 'Server dynamic router',
    reExperienced: 'Please exit the login experience again',
    fixedMenu: 'Fixed menu'
  },
  size: {
    default: 'Default',
    large: 'Large',
    small: 'Small'
  },
  login: {
    welcome: 'Welcome to the system',
    message: 'Backstage management system',
    username: 'Username',
    password: 'Password',
    register: 'Register',
    checkPassword: 'Confirm password',
    login: 'Sign in',
    otherLogin: 'Sign in with',
    remember: 'Remember me',
    hasUser: 'Existing account? Go to login',
    forgetPassword: 'Forget password',
    usernamePlaceholder: 'Please input username',
    passwordPlaceholder: 'Please input password',
    code: 'Verification code',
    codePlaceholder: 'Please input verification code',
    getCode: 'Get code'
  },
  router: {
    login: 'Login',
    level: 'Multi level menu',
    menu: 'Menu',
    menu1: 'Menu1',
    menu11: 'Menu1-1',
    menu111: 'Menu1-1-1',
    menu12: 'Menu1-2',
    menu2: 'Menu2',
    dashboard: 'Dashboard',
    analysis: 'Analysis',
    workplace: 'Workplace',
    guide: 'Guide',
    component: 'Component',
    icon: 'Icon',
    echart: 'Echart',
    countTo: 'Count to',
    watermark: 'Watermark',
    qrcode: 'Qrcode',
    highlight: 'Highlight',
    infotip: 'Infotip',
    form: 'Form',
    defaultForm: 'All examples',
    search: 'Search',
    table: 'Table',
    defaultTable: 'Basic example',
    editor: 'Editor',
    richText: 'Rich text',
    jsonEditor: 'JSON Editor',
    codeEditor: 'Code Editor',
    dialog: 'Dialog',
    imageViewer: 'Image viewer',
    descriptions: 'Descriptions',
    example: 'Example',
    exampleDialog: 'Example dialog',
    examplePage: 'Example page',
    exampleAdd: 'Example page - add',
    exampleEdit: 'Example page - edit',
    exampleDetail: 'Example page - detail',
    errorPage: 'Error page',
    authorization: 'Authorization',
    user: 'User management',
    role: 'Role management',
    document: 'Document',
    inputPassword: 'InputPassword',
    sticky: 'Sticky',
    treeTable: 'Tree table',
    PicturePreview: 'Table Image Preview',
    department: 'Department management',
    menuManagement: 'Menu management',
    // 权限测试页面
    permission: 'Permission test page',
    function: 'Function',
    multipleTabs: 'Multiple tabs',
    details: 'Details',
    iconPicker: 'Icon picker',
    request: 'Request',
    waterfall: 'Waterfall',
    imageCropping: 'Image cropping',
    videoPlayer: 'Video player',
    // 表格视频预览
    tableVideoPreview: 'Table video preview',
    cardTable: 'Card table',
    personalCenter: 'Personal center',
    personal: 'Personal',
    avatars: 'Avatars',
    iAgree: 'I agree',
    tree: 'Tree'
  },
  permission: {
    hasPermission: 'Please set the operation permission value'
  },
  analysis: {
    newUser: 'New user',
    unreadInformation: 'Unread information',
    transactionAmount: 'Transaction amount',
    totalShopping: 'Total Shopping',
    monthlySales: 'Monthly sales',
    userAccessSource: 'User access source',
    january: 'January',
    february: 'February',
    march: 'March',
    april: 'April',
    may: 'May',
    june: 'June',
    july: 'July',
    august: 'August',
    september: 'September',
    october: 'October',
    november: 'November',
    december: 'December',
    estimate: 'Estimate',
    actual: 'Actual',
    directAccess: 'Airect access',
    mailMarketing: 'Mail marketing',
    allianceAdvertising: 'Alliance advertising',
    videoAdvertising: 'Video advertising',
    searchEngines: 'Search engines',
    weeklyUserActivity: 'Weekly user activity',
    activeQuantity: 'Active quantity',
    monday: 'Monday',
    tuesday: 'Tuesday',
    wednesday: 'Wednesday',
    thursday: 'Thursday',
    friday: 'Friday',
    saturday: 'Saturday',
    sunday: 'Sunday'
  },
  workplace: {
    goodMorning: 'Good morning',
    happyDay: 'Wish you happy every day!',
    toady: `It's sunny today`,
    project: 'Project',
    access: 'Project access',
    toDo: 'To do',
    introduction: 'A serious introduction',
    more: 'More',
    shortcutOperation: 'Shortcut operation',
    operation: 'Operation',
    index: 'Index',
    personal: 'Personal',
    team: 'Team',
    quote: 'Quote',
    contribution: 'Contribution',
    hot: 'Hot',
    yield: 'Yield',
    dynamic: 'Dynamic',
    push: 'push',
    pushCode: 'Archer push code to Github',
    follow: 'Follow'
  },
  formDemo: {
    input: 'Input',
    inputNumber: 'InputNumber',
    default: 'Default',
    icon: 'Icon',
    mixed: 'Mixed',
    password: 'Password',
    textarea: 'Textarea',
    remoteSearch: 'Remote search',
    slot: 'Slot',
    position: 'Position',
    autocomplete: 'Autocomplete',
    select: 'Select',
    optionSlot: 'Option Slot',
    selectGroup: 'Select Group',
    selectV2: 'SelectV2',
    cascader: 'Cascader',
    switch: 'Switch',
    rate: 'Rate',
    colorPicker: 'Color Picker',
    transfer: 'Transfer',
    render: 'Render',
    radio: 'Radio',
    radioGroup: 'Radio Group',
    button: 'Button',
    checkbox: 'Checkbox',
    checkboxButton: 'Checkbox Button',
    checkboxGroup: 'Checkbox Group',
    slider: 'Slider',
    datePicker: 'Date Picker',
    shortcuts: 'Shortcuts',
    today: 'Today',
    yesterday: 'Yesterday',
    aWeekAgo: 'A week ago',
    week: 'Week',
    year: 'Year',
    month: 'Month',
    dates: 'Dates',
    daterange: 'Date Range',
    monthrange: 'Month Range',
    dateTimePicker: 'DateTimePicker',
    dateTimerange: 'Datetime Range',
    timePicker: 'Time Picker',
    timeSelect: 'Time Select',
    inputPassword: 'input Password',
    passwordStrength: 'Password Strength',
    defaultForm: 'All examples',
    formDes:
      'The secondary encapsulation of form components based on ElementPlus realizes data-driven and supports all Form parameters',
    example: 'example',
    operate: 'operate',
    change: 'Change',
    restore: 'Restore',
    disabled: 'Disabled',
    disablement: 'Disablement',
    delete: 'Delete',
    add: 'Add',
    setValue: 'Set value',
    resetValue: 'Reset value',
    set: 'Set',
    subitem: 'Subitem',
    formValidation: 'Form validation',
    verifyReset: 'Verify reset',
    // 富文本编辑器
    richText: 'Rich text',
    jsonEditor: 'JSON Editor',
    form: 'Form',
    // 远程加载
    remoteLoading: 'Remote loading',
    // 聚焦
    focus: 'Focus',
    treeSelect: 'Tree Select',
    showCheckbox: 'Show Checkbox',
    selectAnyLevel: 'Select Any Level',
    multiple: 'Multiple',
    filterable: 'Filterable',
    // 自定义节点内容
    customContent: 'Custom content',
    // 懒加载
    lazyLoad: 'Lazy load',
    upload: 'Upload',
    // 用户头像
    userAvatar: 'User avatar',
    iconPicker: 'Icon picker',
    iAgree: 'I agree'
  },
  guideDemo: {
    guide: 'Guide',
    start: 'Start',
    message:
      'The guide page is very useful for some people who enter the project for the first time. You can briefly introduce the functions of the project. The boot page is based on driver.js'
  },
  iconDemo: {
    icon: 'Icon',
    localIcon: 'Local Icon',
    iconify: 'Iconify component',
    recommendedUse: 'Recommended use',
    recommendeDes:
      'Iconify component basically contains all icons. You can query any icon you want. And packaging will only package the icons used.',
    accessAddress: 'Access address'
  },
  echartDemo: {
    echart: 'Echart',
    echartDes:
      'Based on the secondary packaging components of eckarts, the width is adaptive. The corresponding chart can be displayed by passing in the options and height attributes.'
  },
  countToDemo: {
    countTo: 'CountTo',
    countToDes:
      'The transformation is based on vue-count-to and supports all vue-count-to parameters.',
    suffix: 'Suffix',
    prefix: 'Prefix',
    separator: 'Separator',
    duration: 'Duration',
    endVal: 'End val',
    startVal: 'Start val',
    start: 'Start',
    pause: 'Pause',
    resume: 'Resume'
  },
  watermarkDemo: {
    watermark: 'Watermark',
    createdWatermark: 'Created watermark',
    clearWatermark: 'Clear watermark',
    resetWatermark: 'Reset watermark'
  },
  qrcodeDemo: {
    qrcode: 'Qrcode',
    qrcodeDes: 'Secondary packaging based on qrcode',
    basicUsage: 'Basic usage',
    imgTag: 'Img tag',
    style: 'Style config',
    click: 'Click event',
    asynchronousContent: 'Asynchronous content',
    invalid: 'Invalid',
    logoConfig: 'Logo config',
    logoStyle: 'Logo style',
    size: 'size config'
  },
  treeDemo: {
    treeTitle: 'Tree control (right-click node to customize menu options)',
    message:
      'The tree component is based on the secondary packaging of the tree component of ElementPlus'
  },
  highlightDemo: {
    highlight: 'Highlight',
    message: 'The best time to plant a tree is ten years ago, followed by now.',
    keys1: 'ten years ago',
    keys2: 'now'
  },
  infotipDemo: {
    infotip: 'Infotip',
    infotipDes: 'Secondary packaging of components based on Highlight',
    title: 'matters needing attention'
  },
  levelDemo: {
    menu: 'Multi level menu cache'
  },
  searchDemo: {
    search: 'Search',
    searchDes:
      'Based on the secondary encapsulation of form components, the functions of query and reset are realized',
    operate: 'operate',
    change: 'Change',
    grid: 'grid',
    button: 'Button',
    restore: 'Restore',
    inline: 'inline',
    bottom: 'Bottom',
    position: 'position',
    left: 'left',
    center: 'center',
    right: 'right',
    dynamicOptions: 'Dynamic options',
    // 删除单选框
    deleteRadio: 'Delete radio',
    // 还原单选框
    restoreRadio: 'Restore radio',
    loading: 'Loading',
    reset: 'Reset'
  },
  stickyDemo: {
    sticky: 'Sticky'
  },
  tableDemo: {
    table: 'Table',
    tableDes: 'Secondary packaging of Table components based on ElementPlus',
    index: 'Index',
    title: 'Title',
    author: 'Author',
    displayTime: 'Display time',
    importance: 'Importance',
    pageviews: 'Pageviews',
    action: 'Action',
    important: 'Important',
    good: 'Good',
    commonly: 'Commonly',
    operate: 'operate',
    example: 'example',
    show: 'Show',
    hidden: 'Hidden',
    pagination: 'pagination',
    reserveIndex: 'Reserve index',
    restoreIndex: 'Restore index',
    showSelections: 'Show selections',
    hiddenSelections: 'Restore selections',
    showExpandedRows: 'Show expanded rows',
    hiddenExpandedRows: 'Hidden expanded rows',
    changeTitle: 'Change title',
    header: 'Header',
    selectAllNone: 'Select all / none',
    delOrAddAction: 'Delete or add action',
    showOrHiddenStripe: 'Show or hidden stripe',
    showOrHiddenBorder: 'Show or hidden border',
    fixedHeaderOrAuto: 'Fixed header or auto',
    getSelections: 'Get selections',
    preview: 'Preview',
    showOrHiddenSortable: 'Show or hidden sortable',
    videoPreview: 'Video preview',
    cardTable: 'Card table'
  },
  richText: {
    richText: 'Rich text',
    richTextDes: 'Secondary packaging based on wangeditor',
    jsonEditor: 'JSON Editor',
    jsonEditorDes: 'Secondary packaging based on vue-json-pretty',
    codeEditor: 'Code Editor',
    codeEditorDes: 'Secondary packaging based on monaco-editor'
  },
  dialogDemo: {
    dialog: 'Dialog',
    resizeDialog: 'Resize dialog',
    dialogDes: 'Secondary packaging of Dialog components based on ElementPlus',
    open: 'Open',
    close: 'Close',
    combineWithForm: 'Combine with form',
    submit: 'Submit'
  },
  imageViewerDemo: {
    open: 'Open',
    imageViewer: 'Image viewer',
    imageViewerDes: 'Secondary packaging of ImageViewer components based on ElementPlus'
  },
  descriptionsDemo: {
    descriptions: 'Descriptions',
    descriptionsDes: 'Secondary packaging of Descriptions components based on ElementPlus',
    username: 'Username',
    nickName: 'NickName',
    phone: 'Phone',
    email: 'Email',
    addr: 'Address',
    form: 'Combined with Form component'
  },
  exampleDemo: {
    title: 'Title',
    add: 'Add',
    del: 'Delete',
    edit: 'Edit',
    author: 'Author',
    displayTime: 'Display time',
    importance: 'Importance',
    pageviews: 'Pageviews',
    important: 'Important',
    content: 'Content',
    save: 'Save',
    detail: 'Detail'
  },
  userDemo: {
    title: 'User management',
    message:
      'Because it is simulated data, only two accounts with different permissions are provided, which can be modified and combined by developers according to the actual situation.',
    index: 'Index',
    action: 'Action',
    username: 'Username',
    password: 'Password',
    role: 'Role',
    remark: 'Remark',
    remarkMessage1: 'Back end control routing permission',
    remarkMessage2: 'Front end control routing permission',
    // 部门列表
    departmentList: 'Department list',
    // 搜索部门
    searchDepartment: 'Search department',
    account: 'Account',
    email: 'Email',
    createTime: 'Create time',
    // 所属部门
    department: 'Department',
    departmentName: 'Department name',
    status: 'Status',
    enable: 'Enable',
    disable: 'Disable',
    superiorDepartment: 'Superior department'
  },
  menu: {
    menuName: 'Menu name',
    icon: 'Icon',
    // 权限
    permission: 'Permission',
    component: 'Component',
    path: 'Path',
    status: 'Status',
    hidden: 'Hidden',
    alwaysShow: 'Always show',
    noCache: 'No cache',
    breadcrumb: 'Breadcrumb',
    affix: 'Affix',
    noTagsView: 'No tags view',
    activeMenu: 'Active menu',
    canTo: 'Can to',
    name: 'Name'
  },
  role: {
    roleName: 'Role name',
    role: 'Role',
    // 菜单分配
    menu: 'Menu allocation'
  },
  inputPasswordDemo: {
    title: 'InputPassword',
    inputPasswordDes: 'Secondary packaging of Input components based on ElementPlus'
  },
  avatarsDemo: {
    title:
      'Avatar component for avatar list, secondary packaging based on element plus Avatar component'
  }
}
