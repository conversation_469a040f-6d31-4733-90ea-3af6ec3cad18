import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

/**
 * 组件安全性管理组合式函数
 * 用于防止DOM操作相关的错误和内存泄漏
 */
export function useComponentSafety(componentName = '组件') {
  // 组件状态管理
  const isComponentMounted = ref(false)
  const isComponentDestroyed = ref(false)
  const isComponentReady = ref(false)

  // 错误状态
  const hasError = ref(false)
  const errorMessage = ref('')

  // 加载状态
  const isLoading = ref(false)

  /**
   * 安全执行函数 - 确保组件未被销毁时才执行
   */
  const safeExecute = async <T>(
    fn: () => Promise<T> | T,
    errorMsg = '操作失败'
  ): Promise<T | null> => {
    if (isComponentDestroyed.value) {
      console.warn(`🚫 [${componentName}] 组件已销毁，取消操作`)
      return null
    }

    try {
      const result = await fn()
      return result
    } catch (error) {
      console.error(`❌ [${componentName}] ${errorMsg}:`, error)
      if (!isComponentDestroyed.value) {
        ElMessage.error(errorMsg)
        hasError.value = true
        errorMessage.value = errorMsg
      }
      return null
    }
  }

  /**
   * 安全的路由跳转
   */
  const safeNavigate = (router: any, to: any, errorMsg = '页面跳转失败') => {
    return safeExecute(async () => {
      console.log(`🔗 [${componentName}] 路由跳转:`, to)
      await router.push(to)
    }, errorMsg)
  }

  /**
   * 安全的数据加载
   */
  const safeLoadData = async <T>(
    loadFn: () => Promise<T>,
    errorMsg = '数据加载失败'
  ): Promise<T | null> => {
    if (isComponentDestroyed.value) {
      console.warn(`🚫 [${componentName}] 组件已销毁，取消数据加载`)
      return null
    }

    try {
      isLoading.value = true
      console.log(`📊 [${componentName}] 开始加载数据`)

      // 确保DOM已更新
      await nextTick()

      if (isComponentDestroyed.value) {
        console.warn(`🚫 [${componentName}] 组件在数据加载过程中被销毁`)
        return null
      }

      const result = await loadFn()

      if (!isComponentDestroyed.value) {
        console.log(`✅ [${componentName}] 数据加载完成`)
        return result
      }

      return null
    } catch (error) {
      console.error(`❌ [${componentName}] ${errorMsg}:`, error)
      if (!isComponentDestroyed.value) {
        ElMessage.error(errorMsg)
        hasError.value = true
        errorMessage.value = errorMsg
      }
      return null
    } finally {
      if (!isComponentDestroyed.value) {
        isLoading.value = false
      }
    }
  }

  /**
   * 安全的DOM操作
   */
  const safeDOMOperation = (operation: () => void, errorMsg = 'DOM操作失败') => {
    return safeExecute(() => {
      if (!isComponentMounted.value) {
        console.warn(`🚫 [${componentName}] 组件未挂载，跳过DOM操作`)
        return
      }
      operation()
    }, errorMsg)
  }

  /**
   * 创建安全的ref引用
   */
  const createSafeRef = <T = any>(initialValue?: T) => {
    const refValue = ref<T | null>(initialValue || null)
    
    // 在组件卸载时清理引用
    onUnmounted(() => {
      refValue.value = null
    })
    
    return refValue
  }

  /**
   * 初始化组件安全性
   */
  const initComponentSafety = async (initFn?: () => Promise<void> | void) => {
    console.log(`🎉 [${componentName}] 组件开始挂载`)

    try {
      // 标记组件已挂载
      isComponentMounted.value = true
      isComponentDestroyed.value = false
      hasError.value = false
      errorMessage.value = ''

      // 等待DOM完全渲染
      await nextTick()

      console.log(`✅ [${componentName}] 组件挂载完成`)

      // 执行初始化函数
      if (initFn) {
        // 延迟一小段时间确保所有DOM元素都已准备好
        setTimeout(async () => {
          if (!isComponentDestroyed.value) {
            try {
              await initFn()
              isComponentReady.value = true
              console.log(`🚀 [${componentName}] 组件初始化完成`)
            } catch (error) {
              console.error(`❌ [${componentName}] 组件初始化失败:`, error)
              hasError.value = true
              errorMessage.value = '组件初始化失败'
            }
          }
        }, 50)
      } else {
        isComponentReady.value = true
      }
    } catch (error) {
      console.error(`❌ [${componentName}] 组件挂载失败:`, error)
      hasError.value = true
      errorMessage.value = '组件挂载失败'
    }
  }

  /**
   * 清理组件资源
   */
  const cleanupComponent = () => {
    console.log(`🔄 [${componentName}] 组件开始卸载`)

    // 标记组件已销毁
    isComponentDestroyed.value = true
    isComponentMounted.value = false
    isComponentReady.value = false

    // 清理加载状态
    isLoading.value = false
    hasError.value = false
    errorMessage.value = ''

    console.log(`✅ [${componentName}] 组件卸载完成`)
  }

  // 自动设置生命周期钩子
  onMounted(() => initComponentSafety())
  onUnmounted(cleanupComponent)

  return {
    // 状态
    isComponentMounted,
    isComponentDestroyed,
    isComponentReady,
    isLoading,
    hasError,
    errorMessage,

    // 方法
    safeExecute,
    safeNavigate,
    safeLoadData,
    safeDOMOperation,
    createSafeRef,
    initComponentSafety,
    cleanupComponent
  }
}
