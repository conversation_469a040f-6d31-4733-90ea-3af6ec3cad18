const t={common:{inputText:{type:0,body:{type:2,items:[{type:3}],static:"Please input"}},selectText:{type:0,body:{type:2,items:[{type:3}],static:"Please select"}},startTimeText:{type:0,body:{type:2,items:[{type:3}],static:"Start time"}},endTimeText:{type:0,body:{type:2,items:[{type:3}],static:"End time"}},login:{type:0,body:{type:2,items:[{type:3}],static:"Login"}},required:{type:0,body:{type:2,items:[{type:3}],static:"This is required"}},loginOut:{type:0,body:{type:2,items:[{type:3}],static:"Login out"}},document:{type:0,body:{type:2,items:[{type:3}],static:"Document"}},reminder:{type:0,body:{type:2,items:[{type:3}],static:"<PERSON>minder"}},loginOutMessage:{type:0,body:{type:2,items:[{type:3}],static:"Exit the system?"}},back:{type:0,body:{type:2,items:[{type:3}],static:"Back"}},ok:{type:0,body:{type:2,items:[{type:3}],static:"OK"}},cancel:{type:0,body:{type:2,items:[{type:3}],static:"Cancel"}},reload:{type:0,body:{type:2,items:[{type:3}],static:"Reload current"}},closeTab:{type:0,body:{type:2,items:[{type:3}],static:"Close current"}},closeTheLeftTab:{type:0,body:{type:2,items:[{type:3}],static:"Close left"}},closeTheRightTab:{type:0,body:{type:2,items:[{type:3}],static:"Close right"}},closeOther:{type:0,body:{type:2,items:[{type:3}],static:"Close other"}},closeAll:{type:0,body:{type:2,items:[{type:3}],static:"Close all"}},prevLabel:{type:0,body:{type:2,items:[{type:3}],static:"Prev"}},nextLabel:{type:0,body:{type:2,items:[{type:3}],static:"Next"}},skipLabel:{type:0,body:{type:2,items:[{type:3}],static:"Jump"}},doneLabel:{type:0,body:{type:2,items:[{type:3}],static:"End"}},menu:{type:0,body:{type:2,items:[{type:3}],static:"Menu"}},menuDes:{type:0,body:{type:2,items:[{type:3}],static:"Menu bar rendered in routed structure"}},collapse:{type:0,body:{type:2,items:[{type:3}],static:"Collapse"}},collapseDes:{type:0,body:{type:2,items:[{type:3}],static:"Expand and zoom the menu bar"}},tagsView:{type:0,body:{type:2,items:[{type:3}],static:"Tags view"}},tagsViewDes:{type:0,body:{type:2,items:[{type:3}],static:"Used to record routing history"}},tool:{type:0,body:{type:2,items:[{type:3}],static:"Tool"}},toolDes:{type:0,body:{type:2,items:[{type:3}],static:"Used to set up custom systems"}},query:{type:0,body:{type:2,items:[{type:3}],static:"Query"}},reset:{type:0,body:{type:2,items:[{type:3}],static:"Reset"}},shrink:{type:0,body:{type:2,items:[{type:3}],static:"Put away"}},expand:{type:0,body:{type:2,items:[{type:3}],static:"Expand"}},delMessage:{type:0,body:{type:2,items:[{type:3}],static:"Delete the selected data?"}},delWarning:{type:0,body:{type:2,items:[{type:3}],static:"Warning"}},delOk:{type:0,body:{type:2,items:[{type:3}],static:"OK"}},delCancel:{type:0,body:{type:2,items:[{type:3}],static:"Cancel"}},delNoData:{type:0,body:{type:2,items:[{type:3}],static:"Please select the data to delete"}},delSuccess:{type:0,body:{type:2,items:[{type:3}],static:"Deleted successfully"}},refresh:{type:0,body:{type:2,items:[{type:3}],static:"Refresh"}},fullscreen:{type:0,body:{type:2,items:[{type:3}],static:"Fullscreen"}},size:{type:0,body:{type:2,items:[{type:3}],static:"Size"}},columnSetting:{type:0,body:{type:2,items:[{type:3}],static:"Column setting"}},lengthRange:{type:0,body:{type:2,items:[{type:3,value:"The length should be between "},{type:4,key:"min"},{type:3,value:" and "},{type:4,key:"max"}]}},notSpace:{type:0,body:{type:2,items:[{type:3}],static:"Spaces are not allowed"}},notSpecialCharacters:{type:0,body:{type:2,items:[{type:3}],static:"Special characters are not allowed"}},isEqual:{type:0,body:{type:2,items:[{type:3}],static:"The two are not equal"}},setting:{type:0,body:{type:2,items:[{type:3}],static:"Setting"}}},lock:{lockScreen:{type:0,body:{type:2,items:[{type:3}],static:"Lock screen"}},lock:{type:0,body:{type:2,items:[{type:3}],static:"Lock"}},lockPassword:{type:0,body:{type:2,items:[{type:3}],static:"Lock screen password"}},unlock:{type:0,body:{type:2,items:[{type:3}],static:"Click to unlock"}},backToLogin:{type:0,body:{type:2,items:[{type:3}],static:"Back to login"}},entrySystem:{type:0,body:{type:2,items:[{type:3}],static:"Entry the system"}},placeholder:{type:0,body:{type:2,items:[{type:3}],static:"Please enter the lock screen password"}},message:{type:0,body:{type:2,items:[{type:3}],static:"Lock screen password error"}}},error:{noPermission:{type:0,body:{type:2,items:[{type:3}],static:"Sorry, you don't have permission to access this page."}},pageError:{type:0,body:{type:2,items:[{type:3}],static:"Sorry, the page you visited does not exist."}},networkError:{type:0,body:{type:2,items:[{type:3}],static:"Sorry, the server reported an error."}},returnToHome:{type:0,body:{type:2,items:[{type:3}],static:"Return to home"}}},setting:{projectSetting:{type:0,body:{type:2,items:[{type:3}],static:"Project setting"}},theme:{type:0,body:{type:2,items:[{type:3}],static:"Theme"}},layout:{type:0,body:{type:2,items:[{type:3}],static:"Layout"}},systemTheme:{type:0,body:{type:2,items:[{type:3}],static:"System theme"}},menuTheme:{type:0,body:{type:2,items:[{type:3}],static:"Menu theme"}},interfaceDisplay:{type:0,body:{type:2,items:[{type:3}],static:"Interface display"}},breadcrumb:{type:0,body:{type:2,items:[{type:3}],static:"Breadcrumb"}},breadcrumbIcon:{type:0,body:{type:2,items:[{type:3}],static:"Breadcrumb icon"}},collapseMenu:{type:0,body:{type:2,items:[{type:3}],static:"Collapse menu"}},hamburgerIcon:{type:0,body:{type:2,items:[{type:3}],static:"Hamburger icon"}},screenfullIcon:{type:0,body:{type:2,items:[{type:3}],static:"Screenfull icon"}},sizeIcon:{type:0,body:{type:2,items:[{type:3}],static:"Size icon"}},localeIcon:{type:0,body:{type:2,items:[{type:3}],static:"Locale icon"}},tagsView:{type:0,body:{type:2,items:[{type:3}],static:"Tags view"}},logo:{type:0,body:{type:2,items:[{type:3}],static:"Logo"}},greyMode:{type:0,body:{type:2,items:[{type:3}],static:"Grey mode"}},fixedHeader:{type:0,body:{type:2,items:[{type:3}],static:"Fixed header"}},headerTheme:{type:0,body:{type:2,items:[{type:3}],static:"Header theme"}},cutMenu:{type:0,body:{type:2,items:[{type:3}],static:"Cut Menu"}},copy:{type:0,body:{type:2,items:[{type:3}],static:"Copy"}},clearAndReset:{type:0,body:{type:2,items:[{type:3}],static:"Clear cache and reset"}},copySuccess:{type:0,body:{type:2,items:[{type:3}],static:"Copy success"}},copyFailed:{type:0,body:{type:2,items:[{type:3}],static:"Copy failed"}},footer:{type:0,body:{type:2,items:[{type:3}],static:"Footer"}},uniqueOpened:{type:0,body:{type:2,items:[{type:3}],static:"Unique opened"}},tagsViewIcon:{type:0,body:{type:2,items:[{type:3}],static:"Tags view icon"}},dynamicRouter:{type:0,body:{type:2,items:[{type:3}],static:"Enable dynamic router"}},serverDynamicRouter:{type:0,body:{type:2,items:[{type:3}],static:"Server dynamic router"}},reExperienced:{type:0,body:{type:2,items:[{type:3}],static:"Please exit the login experience again"}},fixedMenu:{type:0,body:{type:2,items:[{type:3}],static:"Fixed menu"}}},size:{default:{type:0,body:{type:2,items:[{type:3}],static:"Default"}},large:{type:0,body:{type:2,items:[{type:3}],static:"Large"}},small:{type:0,body:{type:2,items:[{type:3}],static:"Small"}}},login:{welcome:{type:0,body:{type:2,items:[{type:3}],static:"Welcome to the system"}},message:{type:0,body:{type:2,items:[{type:3}],static:"Backstage management system"}},username:{type:0,body:{type:2,items:[{type:3}],static:"Username"}},password:{type:0,body:{type:2,items:[{type:3}],static:"Password"}},register:{type:0,body:{type:2,items:[{type:3}],static:"Register"}},checkPassword:{type:0,body:{type:2,items:[{type:3}],static:"Confirm password"}},login:{type:0,body:{type:2,items:[{type:3}],static:"Sign in"}},otherLogin:{type:0,body:{type:2,items:[{type:3}],static:"Sign in with"}},remember:{type:0,body:{type:2,items:[{type:3}],static:"Remember me"}},hasUser:{type:0,body:{type:2,items:[{type:3}],static:"Existing account? Go to login"}},forgetPassword:{type:0,body:{type:2,items:[{type:3}],static:"Forget password"}},usernamePlaceholder:{type:0,body:{type:2,items:[{type:3}],static:"Please input username"}},passwordPlaceholder:{type:0,body:{type:2,items:[{type:3}],static:"Please input password"}},code:{type:0,body:{type:2,items:[{type:3}],static:"Verification code"}},codePlaceholder:{type:0,body:{type:2,items:[{type:3}],static:"Please input verification code"}},getCode:{type:0,body:{type:2,items:[{type:3}],static:"Get code"}}},router:{login:{type:0,body:{type:2,items:[{type:3}],static:"Login"}},level:{type:0,body:{type:2,items:[{type:3}],static:"Multi level menu"}},menu:{type:0,body:{type:2,items:[{type:3}],static:"Menu"}},menu1:{type:0,body:{type:2,items:[{type:3}],static:"Menu1"}},menu11:{type:0,body:{type:2,items:[{type:3}],static:"Menu1-1"}},menu111:{type:0,body:{type:2,items:[{type:3}],static:"Menu1-1-1"}},menu12:{type:0,body:{type:2,items:[{type:3}],static:"Menu1-2"}},menu2:{type:0,body:{type:2,items:[{type:3}],static:"Menu2"}},dashboard:{type:0,body:{type:2,items:[{type:3}],static:"Dashboard"}},analysis:{type:0,body:{type:2,items:[{type:3}],static:"Analysis"}},workplace:{type:0,body:{type:2,items:[{type:3}],static:"Workplace"}},guide:{type:0,body:{type:2,items:[{type:3}],static:"Guide"}},component:{type:0,body:{type:2,items:[{type:3}],static:"Component"}},icon:{type:0,body:{type:2,items:[{type:3}],static:"Icon"}},echart:{type:0,body:{type:2,items:[{type:3}],static:"Echart"}},countTo:{type:0,body:{type:2,items:[{type:3}],static:"Count to"}},watermark:{type:0,body:{type:2,items:[{type:3}],static:"Watermark"}},qrcode:{type:0,body:{type:2,items:[{type:3}],static:"Qrcode"}},highlight:{type:0,body:{type:2,items:[{type:3}],static:"Highlight"}},infotip:{type:0,body:{type:2,items:[{type:3}],static:"Infotip"}},form:{type:0,body:{type:2,items:[{type:3}],static:"Form"}},defaultForm:{type:0,body:{type:2,items:[{type:3}],static:"All examples"}},search:{type:0,body:{type:2,items:[{type:3}],static:"Search"}},table:{type:0,body:{type:2,items:[{type:3}],static:"Table"}},defaultTable:{type:0,body:{type:2,items:[{type:3}],static:"Basic example"}},editor:{type:0,body:{type:2,items:[{type:3}],static:"Editor"}},richText:{type:0,body:{type:2,items:[{type:3}],static:"Rich text"}},jsonEditor:{type:0,body:{type:2,items:[{type:3}],static:"JSON Editor"}},codeEditor:{type:0,body:{type:2,items:[{type:3}],static:"Code Editor"}},dialog:{type:0,body:{type:2,items:[{type:3}],static:"Dialog"}},imageViewer:{type:0,body:{type:2,items:[{type:3}],static:"Image viewer"}},descriptions:{type:0,body:{type:2,items:[{type:3}],static:"Descriptions"}},example:{type:0,body:{type:2,items:[{type:3}],static:"Example"}},exampleDialog:{type:0,body:{type:2,items:[{type:3}],static:"Example dialog"}},examplePage:{type:0,body:{type:2,items:[{type:3}],static:"Example page"}},exampleAdd:{type:0,body:{type:2,items:[{type:3}],static:"Example page - add"}},exampleEdit:{type:0,body:{type:2,items:[{type:3}],static:"Example page - edit"}},exampleDetail:{type:0,body:{type:2,items:[{type:3}],static:"Example page - detail"}},errorPage:{type:0,body:{type:2,items:[{type:3}],static:"Error page"}},authorization:{type:0,body:{type:2,items:[{type:3}],static:"Authorization"}},user:{type:0,body:{type:2,items:[{type:3}],static:"User management"}},role:{type:0,body:{type:2,items:[{type:3}],static:"Role management"}},document:{type:0,body:{type:2,items:[{type:3}],static:"Document"}},inputPassword:{type:0,body:{type:2,items:[{type:3}],static:"InputPassword"}},sticky:{type:0,body:{type:2,items:[{type:3}],static:"Sticky"}},treeTable:{type:0,body:{type:2,items:[{type:3}],static:"Tree table"}},PicturePreview:{type:0,body:{type:2,items:[{type:3}],static:"Table Image Preview"}},department:{type:0,body:{type:2,items:[{type:3}],static:"Department management"}},menuManagement:{type:0,body:{type:2,items:[{type:3}],static:"Menu management"}},permission:{type:0,body:{type:2,items:[{type:3}],static:"Permission test page"}},function:{type:0,body:{type:2,items:[{type:3}],static:"Function"}},multipleTabs:{type:0,body:{type:2,items:[{type:3}],static:"Multiple tabs"}},details:{type:0,body:{type:2,items:[{type:3}],static:"Details"}},iconPicker:{type:0,body:{type:2,items:[{type:3}],static:"Icon picker"}},request:{type:0,body:{type:2,items:[{type:3}],static:"Request"}},waterfall:{type:0,body:{type:2,items:[{type:3}],static:"Waterfall"}},imageCropping:{type:0,body:{type:2,items:[{type:3}],static:"Image cropping"}},videoPlayer:{type:0,body:{type:2,items:[{type:3}],static:"Video player"}},tableVideoPreview:{type:0,body:{type:2,items:[{type:3}],static:"Table video preview"}},cardTable:{type:0,body:{type:2,items:[{type:3}],static:"Card table"}},personalCenter:{type:0,body:{type:2,items:[{type:3}],static:"Personal center"}},personal:{type:0,body:{type:2,items:[{type:3}],static:"Personal"}},avatars:{type:0,body:{type:2,items:[{type:3}],static:"Avatars"}},iAgree:{type:0,body:{type:2,items:[{type:3}],static:"I agree"}},tree:{type:0,body:{type:2,items:[{type:3}],static:"Tree"}}},permission:{hasPermission:{type:0,body:{type:2,items:[{type:3}],static:"Please set the operation permission value"}}},analysis:{newUser:{type:0,body:{type:2,items:[{type:3}],static:"New user"}},unreadInformation:{type:0,body:{type:2,items:[{type:3}],static:"Unread information"}},transactionAmount:{type:0,body:{type:2,items:[{type:3}],static:"Transaction amount"}},totalShopping:{type:0,body:{type:2,items:[{type:3}],static:"Total Shopping"}},monthlySales:{type:0,body:{type:2,items:[{type:3}],static:"Monthly sales"}},userAccessSource:{type:0,body:{type:2,items:[{type:3}],static:"User access source"}},january:{type:0,body:{type:2,items:[{type:3}],static:"January"}},february:{type:0,body:{type:2,items:[{type:3}],static:"February"}},march:{type:0,body:{type:2,items:[{type:3}],static:"March"}},april:{type:0,body:{type:2,items:[{type:3}],static:"April"}},may:{type:0,body:{type:2,items:[{type:3}],static:"May"}},june:{type:0,body:{type:2,items:[{type:3}],static:"June"}},july:{type:0,body:{type:2,items:[{type:3}],static:"July"}},august:{type:0,body:{type:2,items:[{type:3}],static:"August"}},september:{type:0,body:{type:2,items:[{type:3}],static:"September"}},october:{type:0,body:{type:2,items:[{type:3}],static:"October"}},november:{type:0,body:{type:2,items:[{type:3}],static:"November"}},december:{type:0,body:{type:2,items:[{type:3}],static:"December"}},estimate:{type:0,body:{type:2,items:[{type:3}],static:"Estimate"}},actual:{type:0,body:{type:2,items:[{type:3}],static:"Actual"}},directAccess:{type:0,body:{type:2,items:[{type:3}],static:"Airect access"}},mailMarketing:{type:0,body:{type:2,items:[{type:3}],static:"Mail marketing"}},allianceAdvertising:{type:0,body:{type:2,items:[{type:3}],static:"Alliance advertising"}},videoAdvertising:{type:0,body:{type:2,items:[{type:3}],static:"Video advertising"}},searchEngines:{type:0,body:{type:2,items:[{type:3}],static:"Search engines"}},weeklyUserActivity:{type:0,body:{type:2,items:[{type:3}],static:"Weekly user activity"}},activeQuantity:{type:0,body:{type:2,items:[{type:3}],static:"Active quantity"}},monday:{type:0,body:{type:2,items:[{type:3}],static:"Monday"}},tuesday:{type:0,body:{type:2,items:[{type:3}],static:"Tuesday"}},wednesday:{type:0,body:{type:2,items:[{type:3}],static:"Wednesday"}},thursday:{type:0,body:{type:2,items:[{type:3}],static:"Thursday"}},friday:{type:0,body:{type:2,items:[{type:3}],static:"Friday"}},saturday:{type:0,body:{type:2,items:[{type:3}],static:"Saturday"}},sunday:{type:0,body:{type:2,items:[{type:3}],static:"Sunday"}}},workplace:{goodMorning:{type:0,body:{type:2,items:[{type:3}],static:"Good morning"}},happyDay:{type:0,body:{type:2,items:[{type:3}],static:"Wish you happy every day!"}},toady:{type:0,body:{type:2,items:[{type:3}],static:"It's sunny today"}},project:{type:0,body:{type:2,items:[{type:3}],static:"Project"}},access:{type:0,body:{type:2,items:[{type:3}],static:"Project access"}},toDo:{type:0,body:{type:2,items:[{type:3}],static:"To do"}},introduction:{type:0,body:{type:2,items:[{type:3}],static:"A serious introduction"}},more:{type:0,body:{type:2,items:[{type:3}],static:"More"}},shortcutOperation:{type:0,body:{type:2,items:[{type:3}],static:"Shortcut operation"}},operation:{type:0,body:{type:2,items:[{type:3}],static:"Operation"}},index:{type:0,body:{type:2,items:[{type:3}],static:"Index"}},personal:{type:0,body:{type:2,items:[{type:3}],static:"Personal"}},team:{type:0,body:{type:2,items:[{type:3}],static:"Team"}},quote:{type:0,body:{type:2,items:[{type:3}],static:"Quote"}},contribution:{type:0,body:{type:2,items:[{type:3}],static:"Contribution"}},hot:{type:0,body:{type:2,items:[{type:3}],static:"Hot"}},yield:{type:0,body:{type:2,items:[{type:3}],static:"Yield"}},dynamic:{type:0,body:{type:2,items:[{type:3}],static:"Dynamic"}},push:{type:0,body:{type:2,items:[{type:3}],static:"push"}},pushCode:{type:0,body:{type:2,items:[{type:3}],static:"Archer push code to Github"}},follow:{type:0,body:{type:2,items:[{type:3}],static:"Follow"}}},formDemo:{input:{type:0,body:{type:2,items:[{type:3}],static:"Input"}},inputNumber:{type:0,body:{type:2,items:[{type:3}],static:"InputNumber"}},default:{type:0,body:{type:2,items:[{type:3}],static:"Default"}},icon:{type:0,body:{type:2,items:[{type:3}],static:"Icon"}},mixed:{type:0,body:{type:2,items:[{type:3}],static:"Mixed"}},password:{type:0,body:{type:2,items:[{type:3}],static:"Password"}},textarea:{type:0,body:{type:2,items:[{type:3}],static:"Textarea"}},remoteSearch:{type:0,body:{type:2,items:[{type:3}],static:"Remote search"}},slot:{type:0,body:{type:2,items:[{type:3}],static:"Slot"}},position:{type:0,body:{type:2,items:[{type:3}],static:"Position"}},autocomplete:{type:0,body:{type:2,items:[{type:3}],static:"Autocomplete"}},select:{type:0,body:{type:2,items:[{type:3}],static:"Select"}},optionSlot:{type:0,body:{type:2,items:[{type:3}],static:"Option Slot"}},selectGroup:{type:0,body:{type:2,items:[{type:3}],static:"Select Group"}},selectV2:{type:0,body:{type:2,items:[{type:3}],static:"SelectV2"}},cascader:{type:0,body:{type:2,items:[{type:3}],static:"Cascader"}},switch:{type:0,body:{type:2,items:[{type:3}],static:"Switch"}},rate:{type:0,body:{type:2,items:[{type:3}],static:"Rate"}},colorPicker:{type:0,body:{type:2,items:[{type:3}],static:"Color Picker"}},transfer:{type:0,body:{type:2,items:[{type:3}],static:"Transfer"}},render:{type:0,body:{type:2,items:[{type:3}],static:"Render"}},radio:{type:0,body:{type:2,items:[{type:3}],static:"Radio"}},radioGroup:{type:0,body:{type:2,items:[{type:3}],static:"Radio Group"}},button:{type:0,body:{type:2,items:[{type:3}],static:"Button"}},checkbox:{type:0,body:{type:2,items:[{type:3}],static:"Checkbox"}},checkboxButton:{type:0,body:{type:2,items:[{type:3}],static:"Checkbox Button"}},checkboxGroup:{type:0,body:{type:2,items:[{type:3}],static:"Checkbox Group"}},slider:{type:0,body:{type:2,items:[{type:3}],static:"Slider"}},datePicker:{type:0,body:{type:2,items:[{type:3}],static:"Date Picker"}},shortcuts:{type:0,body:{type:2,items:[{type:3}],static:"Shortcuts"}},today:{type:0,body:{type:2,items:[{type:3}],static:"Today"}},yesterday:{type:0,body:{type:2,items:[{type:3}],static:"Yesterday"}},aWeekAgo:{type:0,body:{type:2,items:[{type:3}],static:"A week ago"}},week:{type:0,body:{type:2,items:[{type:3}],static:"Week"}},year:{type:0,body:{type:2,items:[{type:3}],static:"Year"}},month:{type:0,body:{type:2,items:[{type:3}],static:"Month"}},dates:{type:0,body:{type:2,items:[{type:3}],static:"Dates"}},daterange:{type:0,body:{type:2,items:[{type:3}],static:"Date Range"}},monthrange:{type:0,body:{type:2,items:[{type:3}],static:"Month Range"}},dateTimePicker:{type:0,body:{type:2,items:[{type:3}],static:"DateTimePicker"}},dateTimerange:{type:0,body:{type:2,items:[{type:3}],static:"Datetime Range"}},timePicker:{type:0,body:{type:2,items:[{type:3}],static:"Time Picker"}},timeSelect:{type:0,body:{type:2,items:[{type:3}],static:"Time Select"}},inputPassword:{type:0,body:{type:2,items:[{type:3}],static:"input Password"}},passwordStrength:{type:0,body:{type:2,items:[{type:3}],static:"Password Strength"}},defaultForm:{type:0,body:{type:2,items:[{type:3}],static:"All examples"}},formDes:{type:0,body:{type:2,items:[{type:3}],static:"The secondary encapsulation of form components based on ElementPlus realizes data-driven and supports all Form parameters"}},example:{type:0,body:{type:2,items:[{type:3}],static:"example"}},operate:{type:0,body:{type:2,items:[{type:3}],static:"operate"}},change:{type:0,body:{type:2,items:[{type:3}],static:"Change"}},restore:{type:0,body:{type:2,items:[{type:3}],static:"Restore"}},disabled:{type:0,body:{type:2,items:[{type:3}],static:"Disabled"}},disablement:{type:0,body:{type:2,items:[{type:3}],static:"Disablement"}},delete:{type:0,body:{type:2,items:[{type:3}],static:"Delete"}},add:{type:0,body:{type:2,items:[{type:3}],static:"Add"}},setValue:{type:0,body:{type:2,items:[{type:3}],static:"Set value"}},resetValue:{type:0,body:{type:2,items:[{type:3}],static:"Reset value"}},set:{type:0,body:{type:2,items:[{type:3}],static:"Set"}},subitem:{type:0,body:{type:2,items:[{type:3}],static:"Subitem"}},formValidation:{type:0,body:{type:2,items:[{type:3}],static:"Form validation"}},verifyReset:{type:0,body:{type:2,items:[{type:3}],static:"Verify reset"}},richText:{type:0,body:{type:2,items:[{type:3}],static:"Rich text"}},jsonEditor:{type:0,body:{type:2,items:[{type:3}],static:"JSON Editor"}},form:{type:0,body:{type:2,items:[{type:3}],static:"Form"}},remoteLoading:{type:0,body:{type:2,items:[{type:3}],static:"Remote loading"}},focus:{type:0,body:{type:2,items:[{type:3}],static:"Focus"}},treeSelect:{type:0,body:{type:2,items:[{type:3}],static:"Tree Select"}},showCheckbox:{type:0,body:{type:2,items:[{type:3}],static:"Show Checkbox"}},selectAnyLevel:{type:0,body:{type:2,items:[{type:3}],static:"Select Any Level"}},multiple:{type:0,body:{type:2,items:[{type:3}],static:"Multiple"}},filterable:{type:0,body:{type:2,items:[{type:3}],static:"Filterable"}},customContent:{type:0,body:{type:2,items:[{type:3}],static:"Custom content"}},lazyLoad:{type:0,body:{type:2,items:[{type:3}],static:"Lazy load"}},upload:{type:0,body:{type:2,items:[{type:3}],static:"Upload"}},userAvatar:{type:0,body:{type:2,items:[{type:3}],static:"User avatar"}},iconPicker:{type:0,body:{type:2,items:[{type:3}],static:"Icon picker"}},iAgree:{type:0,body:{type:2,items:[{type:3}],static:"I agree"}}},guideDemo:{guide:{type:0,body:{type:2,items:[{type:3}],static:"Guide"}},start:{type:0,body:{type:2,items:[{type:3}],static:"Start"}},message:{type:0,body:{type:2,items:[{type:3}],static:"The guide page is very useful for some people who enter the project for the first time. You can briefly introduce the functions of the project. The boot page is based on driver.js"}}},iconDemo:{icon:{type:0,body:{type:2,items:[{type:3}],static:"Icon"}},localIcon:{type:0,body:{type:2,items:[{type:3}],static:"Local Icon"}},iconify:{type:0,body:{type:2,items:[{type:3}],static:"Iconify component"}},recommendedUse:{type:0,body:{type:2,items:[{type:3}],static:"Recommended use"}},recommendeDes:{type:0,body:{type:2,items:[{type:3}],static:"Iconify component basically contains all icons. You can query any icon you want. And packaging will only package the icons used."}},accessAddress:{type:0,body:{type:2,items:[{type:3}],static:"Access address"}}},echartDemo:{echart:{type:0,body:{type:2,items:[{type:3}],static:"Echart"}},echartDes:{type:0,body:{type:2,items:[{type:3}],static:"Based on the secondary packaging components of eckarts, the width is adaptive. The corresponding chart can be displayed by passing in the options and height attributes."}}},countToDemo:{countTo:{type:0,body:{type:2,items:[{type:3}],static:"CountTo"}},countToDes:{type:0,body:{type:2,items:[{type:3}],static:"The transformation is based on vue-count-to and supports all vue-count-to parameters."}},suffix:{type:0,body:{type:2,items:[{type:3}],static:"Suffix"}},prefix:{type:0,body:{type:2,items:[{type:3}],static:"Prefix"}},separator:{type:0,body:{type:2,items:[{type:3}],static:"Separator"}},duration:{type:0,body:{type:2,items:[{type:3}],static:"Duration"}},endVal:{type:0,body:{type:2,items:[{type:3}],static:"End val"}},startVal:{type:0,body:{type:2,items:[{type:3}],static:"Start val"}},start:{type:0,body:{type:2,items:[{type:3}],static:"Start"}},pause:{type:0,body:{type:2,items:[{type:3}],static:"Pause"}},resume:{type:0,body:{type:2,items:[{type:3}],static:"Resume"}}},watermarkDemo:{watermark:{type:0,body:{type:2,items:[{type:3}],static:"Watermark"}},createdWatermark:{type:0,body:{type:2,items:[{type:3}],static:"Created watermark"}},clearWatermark:{type:0,body:{type:2,items:[{type:3}],static:"Clear watermark"}},resetWatermark:{type:0,body:{type:2,items:[{type:3}],static:"Reset watermark"}}},qrcodeDemo:{qrcode:{type:0,body:{type:2,items:[{type:3}],static:"Qrcode"}},qrcodeDes:{type:0,body:{type:2,items:[{type:3}],static:"Secondary packaging based on qrcode"}},basicUsage:{type:0,body:{type:2,items:[{type:3}],static:"Basic usage"}},imgTag:{type:0,body:{type:2,items:[{type:3}],static:"Img tag"}},style:{type:0,body:{type:2,items:[{type:3}],static:"Style config"}},click:{type:0,body:{type:2,items:[{type:3}],static:"Click event"}},asynchronousContent:{type:0,body:{type:2,items:[{type:3}],static:"Asynchronous content"}},invalid:{type:0,body:{type:2,items:[{type:3}],static:"Invalid"}},logoConfig:{type:0,body:{type:2,items:[{type:3}],static:"Logo config"}},logoStyle:{type:0,body:{type:2,items:[{type:3}],static:"Logo style"}},size:{type:0,body:{type:2,items:[{type:3}],static:"size config"}}},treeDemo:{treeTitle:{type:0,body:{type:2,items:[{type:3}],static:"Tree control (right-click node to customize menu options)"}},message:{type:0,body:{type:2,items:[{type:3}],static:"The tree component is based on the secondary packaging of the tree component of ElementPlus"}}},highlightDemo:{highlight:{type:0,body:{type:2,items:[{type:3}],static:"Highlight"}},message:{type:0,body:{type:2,items:[{type:3}],static:"The best time to plant a tree is ten years ago, followed by now."}},keys1:{type:0,body:{type:2,items:[{type:3}],static:"ten years ago"}},keys2:{type:0,body:{type:2,items:[{type:3}],static:"now"}}},infotipDemo:{infotip:{type:0,body:{type:2,items:[{type:3}],static:"Infotip"}},infotipDes:{type:0,body:{type:2,items:[{type:3}],static:"Secondary packaging of components based on Highlight"}},title:{type:0,body:{type:2,items:[{type:3}],static:"matters needing attention"}}},levelDemo:{menu:{type:0,body:{type:2,items:[{type:3}],static:"Multi level menu cache"}}},searchDemo:{search:{type:0,body:{type:2,items:[{type:3}],static:"Search"}},searchDes:{type:0,body:{type:2,items:[{type:3}],static:"Based on the secondary encapsulation of form components, the functions of query and reset are realized"}},operate:{type:0,body:{type:2,items:[{type:3}],static:"operate"}},change:{type:0,body:{type:2,items:[{type:3}],static:"Change"}},grid:{type:0,body:{type:2,items:[{type:3}],static:"grid"}},button:{type:0,body:{type:2,items:[{type:3}],static:"Button"}},restore:{type:0,body:{type:2,items:[{type:3}],static:"Restore"}},inline:{type:0,body:{type:2,items:[{type:3}],static:"inline"}},bottom:{type:0,body:{type:2,items:[{type:3}],static:"Bottom"}},position:{type:0,body:{type:2,items:[{type:3}],static:"position"}},left:{type:0,body:{type:2,items:[{type:3}],static:"left"}},center:{type:0,body:{type:2,items:[{type:3}],static:"center"}},right:{type:0,body:{type:2,items:[{type:3}],static:"right"}},dynamicOptions:{type:0,body:{type:2,items:[{type:3}],static:"Dynamic options"}},deleteRadio:{type:0,body:{type:2,items:[{type:3}],static:"Delete radio"}},restoreRadio:{type:0,body:{type:2,items:[{type:3}],static:"Restore radio"}},loading:{type:0,body:{type:2,items:[{type:3}],static:"Loading"}},reset:{type:0,body:{type:2,items:[{type:3}],static:"Reset"}}},stickyDemo:{sticky:{type:0,body:{type:2,items:[{type:3}],static:"Sticky"}}},tableDemo:{table:{type:0,body:{type:2,items:[{type:3}],static:"Table"}},tableDes:{type:0,body:{type:2,items:[{type:3}],static:"Secondary packaging of Table components based on ElementPlus"}},index:{type:0,body:{type:2,items:[{type:3}],static:"Index"}},title:{type:0,body:{type:2,items:[{type:3}],static:"Title"}},author:{type:0,body:{type:2,items:[{type:3}],static:"Author"}},displayTime:{type:0,body:{type:2,items:[{type:3}],static:"Display time"}},importance:{type:0,body:{type:2,items:[{type:3}],static:"Importance"}},pageviews:{type:0,body:{type:2,items:[{type:3}],static:"Pageviews"}},action:{type:0,body:{type:2,items:[{type:3}],static:"Action"}},important:{type:0,body:{type:2,items:[{type:3}],static:"Important"}},good:{type:0,body:{type:2,items:[{type:3}],static:"Good"}},commonly:{type:0,body:{type:2,items:[{type:3}],static:"Commonly"}},operate:{type:0,body:{type:2,items:[{type:3}],static:"operate"}},example:{type:0,body:{type:2,items:[{type:3}],static:"example"}},show:{type:0,body:{type:2,items:[{type:3}],static:"Show"}},hidden:{type:0,body:{type:2,items:[{type:3}],static:"Hidden"}},pagination:{type:0,body:{type:2,items:[{type:3}],static:"pagination"}},reserveIndex:{type:0,body:{type:2,items:[{type:3}],static:"Reserve index"}},restoreIndex:{type:0,body:{type:2,items:[{type:3}],static:"Restore index"}},showSelections:{type:0,body:{type:2,items:[{type:3}],static:"Show selections"}},hiddenSelections:{type:0,body:{type:2,items:[{type:3}],static:"Restore selections"}},showExpandedRows:{type:0,body:{type:2,items:[{type:3}],static:"Show expanded rows"}},hiddenExpandedRows:{type:0,body:{type:2,items:[{type:3}],static:"Hidden expanded rows"}},changeTitle:{type:0,body:{type:2,items:[{type:3}],static:"Change title"}},header:{type:0,body:{type:2,items:[{type:3}],static:"Header"}},selectAllNone:{type:0,body:{type:2,items:[{type:3}],static:"Select all / none"}},delOrAddAction:{type:0,body:{type:2,items:[{type:3}],static:"Delete or add action"}},showOrHiddenStripe:{type:0,body:{type:2,items:[{type:3}],static:"Show or hidden stripe"}},showOrHiddenBorder:{type:0,body:{type:2,items:[{type:3}],static:"Show or hidden border"}},fixedHeaderOrAuto:{type:0,body:{type:2,items:[{type:3}],static:"Fixed header or auto"}},getSelections:{type:0,body:{type:2,items:[{type:3}],static:"Get selections"}},preview:{type:0,body:{type:2,items:[{type:3}],static:"Preview"}},showOrHiddenSortable:{type:0,body:{type:2,items:[{type:3}],static:"Show or hidden sortable"}},videoPreview:{type:0,body:{type:2,items:[{type:3}],static:"Video preview"}},cardTable:{type:0,body:{type:2,items:[{type:3}],static:"Card table"}}},richText:{richText:{type:0,body:{type:2,items:[{type:3}],static:"Rich text"}},richTextDes:{type:0,body:{type:2,items:[{type:3}],static:"Secondary packaging based on wangeditor"}},jsonEditor:{type:0,body:{type:2,items:[{type:3}],static:"JSON Editor"}},jsonEditorDes:{type:0,body:{type:2,items:[{type:3}],static:"Secondary packaging based on vue-json-pretty"}},codeEditor:{type:0,body:{type:2,items:[{type:3}],static:"Code Editor"}},codeEditorDes:{type:0,body:{type:2,items:[{type:3}],static:"Secondary packaging based on monaco-editor"}}},dialogDemo:{dialog:{type:0,body:{type:2,items:[{type:3}],static:"Dialog"}},resizeDialog:{type:0,body:{type:2,items:[{type:3}],static:"Resize dialog"}},dialogDes:{type:0,body:{type:2,items:[{type:3}],static:"Secondary packaging of Dialog components based on ElementPlus"}},open:{type:0,body:{type:2,items:[{type:3}],static:"Open"}},close:{type:0,body:{type:2,items:[{type:3}],static:"Close"}},combineWithForm:{type:0,body:{type:2,items:[{type:3}],static:"Combine with form"}},submit:{type:0,body:{type:2,items:[{type:3}],static:"Submit"}}},imageViewerDemo:{open:{type:0,body:{type:2,items:[{type:3}],static:"Open"}},imageViewer:{type:0,body:{type:2,items:[{type:3}],static:"Image viewer"}},imageViewerDes:{type:0,body:{type:2,items:[{type:3}],static:"Secondary packaging of ImageViewer components based on ElementPlus"}}},descriptionsDemo:{descriptions:{type:0,body:{type:2,items:[{type:3}],static:"Descriptions"}},descriptionsDes:{type:0,body:{type:2,items:[{type:3}],static:"Secondary packaging of Descriptions components based on ElementPlus"}},username:{type:0,body:{type:2,items:[{type:3}],static:"Username"}},nickName:{type:0,body:{type:2,items:[{type:3}],static:"NickName"}},phone:{type:0,body:{type:2,items:[{type:3}],static:"Phone"}},email:{type:0,body:{type:2,items:[{type:3}],static:"Email"}},addr:{type:0,body:{type:2,items:[{type:3}],static:"Address"}},form:{type:0,body:{type:2,items:[{type:3}],static:"Combined with Form component"}}},exampleDemo:{title:{type:0,body:{type:2,items:[{type:3}],static:"Title"}},add:{type:0,body:{type:2,items:[{type:3}],static:"Add"}},del:{type:0,body:{type:2,items:[{type:3}],static:"Delete"}},edit:{type:0,body:{type:2,items:[{type:3}],static:"Edit"}},author:{type:0,body:{type:2,items:[{type:3}],static:"Author"}},displayTime:{type:0,body:{type:2,items:[{type:3}],static:"Display time"}},importance:{type:0,body:{type:2,items:[{type:3}],static:"Importance"}},pageviews:{type:0,body:{type:2,items:[{type:3}],static:"Pageviews"}},important:{type:0,body:{type:2,items:[{type:3}],static:"Important"}},content:{type:0,body:{type:2,items:[{type:3}],static:"Content"}},save:{type:0,body:{type:2,items:[{type:3}],static:"Save"}},detail:{type:0,body:{type:2,items:[{type:3}],static:"Detail"}}},userDemo:{title:{type:0,body:{type:2,items:[{type:3}],static:"User management"}},message:{type:0,body:{type:2,items:[{type:3}],static:"Because it is simulated data, only two accounts with different permissions are provided, which can be modified and combined by developers according to the actual situation."}},index:{type:0,body:{type:2,items:[{type:3}],static:"Index"}},action:{type:0,body:{type:2,items:[{type:3}],static:"Action"}},username:{type:0,body:{type:2,items:[{type:3}],static:"Username"}},password:{type:0,body:{type:2,items:[{type:3}],static:"Password"}},role:{type:0,body:{type:2,items:[{type:3}],static:"Role"}},remark:{type:0,body:{type:2,items:[{type:3}],static:"Remark"}},remarkMessage1:{type:0,body:{type:2,items:[{type:3}],static:"Back end control routing permission"}},remarkMessage2:{type:0,body:{type:2,items:[{type:3}],static:"Front end control routing permission"}},departmentList:{type:0,body:{type:2,items:[{type:3}],static:"Department list"}},searchDepartment:{type:0,body:{type:2,items:[{type:3}],static:"Search department"}},account:{type:0,body:{type:2,items:[{type:3}],static:"Account"}},email:{type:0,body:{type:2,items:[{type:3}],static:"Email"}},createTime:{type:0,body:{type:2,items:[{type:3}],static:"Create time"}},department:{type:0,body:{type:2,items:[{type:3}],static:"Department"}},departmentName:{type:0,body:{type:2,items:[{type:3}],static:"Department name"}},status:{type:0,body:{type:2,items:[{type:3}],static:"Status"}},enable:{type:0,body:{type:2,items:[{type:3}],static:"Enable"}},disable:{type:0,body:{type:2,items:[{type:3}],static:"Disable"}},superiorDepartment:{type:0,body:{type:2,items:[{type:3}],static:"Superior department"}}},menu:{menuName:{type:0,body:{type:2,items:[{type:3}],static:"Menu name"}},icon:{type:0,body:{type:2,items:[{type:3}],static:"Icon"}},permission:{type:0,body:{type:2,items:[{type:3}],static:"Permission"}},component:{type:0,body:{type:2,items:[{type:3}],static:"Component"}},path:{type:0,body:{type:2,items:[{type:3}],static:"Path"}},status:{type:0,body:{type:2,items:[{type:3}],static:"Status"}},hidden:{type:0,body:{type:2,items:[{type:3}],static:"Hidden"}},alwaysShow:{type:0,body:{type:2,items:[{type:3}],static:"Always show"}},noCache:{type:0,body:{type:2,items:[{type:3}],static:"No cache"}},breadcrumb:{type:0,body:{type:2,items:[{type:3}],static:"Breadcrumb"}},affix:{type:0,body:{type:2,items:[{type:3}],static:"Affix"}},noTagsView:{type:0,body:{type:2,items:[{type:3}],static:"No tags view"}},activeMenu:{type:0,body:{type:2,items:[{type:3}],static:"Active menu"}},canTo:{type:0,body:{type:2,items:[{type:3}],static:"Can to"}},name:{type:0,body:{type:2,items:[{type:3}],static:"Name"}}},role:{roleName:{type:0,body:{type:2,items:[{type:3}],static:"Role name"}},role:{type:0,body:{type:2,items:[{type:3}],static:"Role"}},menu:{type:0,body:{type:2,items:[{type:3}],static:"Menu allocation"}}},inputPasswordDemo:{title:{type:0,body:{type:2,items:[{type:3}],static:"InputPassword"}},inputPasswordDes:{type:0,body:{type:2,items:[{type:3}],static:"Secondary packaging of Input components based on ElementPlus"}}},avatarsDemo:{title:{type:0,body:{type:2,items:[{type:3}],static:"Avatar component for avatar list, secondary packaging based on element plus Avatar component"}}}};export{t as default};
