:export{namespace:v;elNamespace:el}.cell{height:30px;padding:3px 0;box-sizing:border-box}.cell .text{position:absolute;left:50%;display:block;width:24px;height:24px;margin:0 auto;line-height:24px;border-radius:50%;-webkit-transform:translateX(-50%);transform:translate(-50%)}.cell.current .text{color:#fff;background:#626aef}.cell .holiday{position:absolute;bottom:0;left:50%;width:6px;height:6px;background:var(--el-color-danger);border-radius:50%;-webkit-transform:translateX(-50%);transform:translate(-50%)}.transfer-footer{padding:6px 5px;margin-left:15px}.el-upload{position:relative;overflow:hidden;cursor:pointer;border:1px dashed var(--el-border-color);border-radius:6px;-webkit-transition:var(--el-transition-duration-fast);transition:var(--el-transition-duration-fast)}.el-upload:hover{border-color:var(--el-color-primary)}.el-icon.avatar-uploader-icon{width:178px;height:178px;font-size:28px;color:#8c939d;text-align:center}
