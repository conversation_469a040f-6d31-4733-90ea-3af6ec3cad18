(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9a319a28"],{"129f":function(e,t,i){"use strict";e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!==e&&t!==t}},6326:function(e,t,i){"use strict";i("cfc7")},"841c":function(e,t,i){"use strict";var a=i("c65b"),o=i("d784"),r=i("825a"),n=i("7234"),l=i("1d80"),s=i("129f"),c=i("577e"),d=i("dc4a"),p=i("14c3");o("search",(function(e,t,i){return[function(t){var i=l(this),o=n(t)?void 0:d(t,e);return o?a(o,t,i):new RegExp(t)[e](c(i))},function(e){var a=r(this),o=c(e),n=i(t,a,o);if(n.done)return n.value;var l=a.lastIndex;s(l,0)||(a.lastIndex=0);var d=p(a,o);return s(a.lastIndex,l)||(a.lastIndex=l),null===d?-1:d.index}]}))},cfc7:function(e,t,i){},e8ff:function(e,t,i){"use strict";i.r(t);i("ac1f"),i("841c");var a=function(){var e=this,t=e._self._c;return t("Card",{staticStyle:{width:"100%",padiing:"16px"}},[t("div",{staticStyle:{display:"flex","justify-content":"center",margin:"20px 0"}},[t("Form",{ref:"formObj",attrs:{model:e.formObj,"label-width":150,rules:e.ruleAddValidate}},[t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"套餐名称(简中)",prop:"nameCn"}},[t("Input",{attrs:{maxlength:100,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入套餐名称(简中)"},model:{value:e.formObj.nameCn,callback:function(t){e.$set(e.formObj,"nameCn",t)},expression:"formObj.nameCn"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"套餐名称(繁中)",prop:"nameTw"}},[t("Input",{attrs:{maxlength:100,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入套餐名稱(繁中)"},model:{value:e.formObj.nameTw,callback:function(t){e.$set(e.formObj,"nameTw",t)},expression:"formObj.nameTw"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"套餐名称(英文)",prop:"nameEn"}},[t("Input",{attrs:{maxlength:100,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"Please enter package name (EN)"},model:{value:e.formObj.nameEn,callback:function(t){e.$set(e.formObj,"nameEn",t)},expression:"formObj.nameEn"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"套餐描述(简中)",prop:"descCn"}},[t("Input",{attrs:{maxlength:4e3,readonly:"Info"==e.typeFlag,type:"textarea",rows:3,placeholder:"请输入套餐描述(简中)"},model:{value:e.formObj.descCn,callback:function(t){e.$set(e.formObj,"descCn",t)},expression:"formObj.descCn"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"套餐描述(繁中)",prop:"descTw"}},[t("Input",{attrs:{maxlength:4e3,readonly:"Info"==e.typeFlag,type:"textarea",rows:3,placeholder:"请输入套餐描述(繁中)"},model:{value:e.formObj.descTw,callback:function(t){e.$set(e.formObj,"descTw",t)},expression:"formObj.descTw"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"套餐描述(英文)",prop:"descEn"}},[t("Input",{attrs:{maxlength:4e3,readonly:"Info"==e.typeFlag,type:"textarea",rows:3,placeholder:"Please enter package description (EN)"},model:{value:e.formObj.descEn,callback:function(t){e.$set(e.formObj,"descEn",t)},expression:"formObj.descEn"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"周期类型",prop:"periodUnit"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择周期类型",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},model:{value:e.formObj.periodUnit,callback:function(t){e.$set(e.formObj,"periodUnit",t)},expression:"formObj.periodUnit"}},e._l(e.periodUnitList,(function(i){return t("Option",{key:i.value,attrs:{value:i.value}},[e._v(e._s(i.label))])})),1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"持续周期",prop:"keepPeriod"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:11,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入持续周期"},model:{value:e.formObj.keepPeriod,callback:function(t){e.$set(e.formObj,"keepPeriod",t)},expression:"formObj.keepPeriod"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"套餐购买有效期(天)",prop:"effectiveDay"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:11,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入套餐购买有效期(天)"},model:{value:e.formObj.effectiveDay,callback:function(t){e.$set(e.formObj,"effectiveDay",t)},expression:"formObj.effectiveDay"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"套餐封面",prop:"picture"}},["Info"!=e.typeFlag?t("div",{staticStyle:{display:"flex","flex-direction":"column",width:"200px",height:"100px"}},[""==e.pictureUrl?t("Upload",{ref:"upload",attrs:{type:"drag",accept:"image/*",action:"#","before-upload":e.handleUpload,"show-upload-list":!1}},[t("div",{staticStyle:{padding:"20px",height:"100px",width:"100%"}},[t("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),t("p",[e._v("上传套餐封面图片")])],1)]):t("div",{staticStyle:{display:"flex","flex-direction":"column",width:"100%",height:"100%",border:"1px dashed #dcdee2","border-radius":"4px",position:"relative"}},[t("Icon",{staticClass:"mediaShowDelSty",attrs:{type:"md-close-circle",color:"#ff3300",size:"22"},on:{click:function(t){return e.cancelSelected()}}}),t("img",{staticStyle:{"object-fit":"contain"},attrs:{src:e.pictureUrl,width:"100%",height:"100%"},on:{click:function(t){e.pictureShowFlag=!0}}})],1)],1):t("div",{staticStyle:{display:"flex","flex-direction":"column",width:"200px",height:"100px"}},[t("div",{staticStyle:{display:"flex","flex-direction":"column",width:"100%",height:"100%",border:"1px dashed #dcdee2","border-radius":"4px",position:"relative"}},[t("img",{staticStyle:{"object-fit":"contain"},attrs:{src:e.pictureUrl,width:"100%",height:"100%"},on:{click:function(t){e.pictureShowFlag=!0}}})])])])],1),"Add"!=e.typeFlag?t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"套餐状态",prop:"status"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择套餐状态",disabled:!0},model:{value:e.formObj.status,callback:function(t){e.$set(e.formObj,"status",t)},expression:"formObj.status"}},e._l(e.statusList,(function(i){return t("Option",{key:i.value,attrs:{value:i.value}},[e._v(e._s(i.label))])})),1)],1)],1):e._e()],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"终端厂商套餐",prop:"isTerminal"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"是否为终端厂商套餐",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":e.isTerminalChange},model:{value:e.formObj.isTerminal,callback:function(t){e.$set(e.formObj,"isTerminal",t)},expression:"formObj.isTerminal"}},[t("Option",{attrs:{value:"1"}},[e._v("是")]),t("Option",{attrs:{value:"2"}},[e._v("否")])],1)],1)],1),"1"==e.formObj.isTerminal?t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"选择厂商",prop:"corpId"}},[t("Select",{attrs:{placeholder:"请选择厂商",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},model:{value:e.formObj.corpId,callback:function(t){e.$set(e.formObj,"corpId",t)},expression:"formObj.corpId"}},e._l(e.corpIdList,(function(i,a){return t("Option",{key:a,attrs:{value:i.corpId}},[e._v(e._s(i.corpName))])})),1)],1)],1):e._e()],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"促销套餐",prop:"isPromotion"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"是否为促销套餐",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},model:{value:e.formObj.isPromotion,callback:function(t){e.$set(e.formObj,"isPromotion",t)},expression:"formObj.isPromotion"}},[t("Option",{attrs:{value:"1"}},[e._v("是")]),t("Option",{attrs:{value:"2"}},[e._v("否")])],1)],1)],1),t("Col",{attrs:{span:"12"}},["1"==e.formObj.isPromotion?t("FormItem",{attrs:{label:"促销限购份数",prop:"saleLimit"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:11,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入促销限购份数"},model:{value:e.formObj.saleLimit,callback:function(t){e.$set(e.formObj,"saleLimit",t)},expression:"formObj.saleLimit"}})],1):e._e()],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"流量限制类型",prop:"flowLimitType"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择流量限制类型",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":e.getcontrolLogic},model:{value:e.formObj.flowLimitType,callback:function(t){e.$set(e.formObj,"flowLimitType",t)},expression:"formObj.flowLimitType"}},[t("Option",{attrs:{value:"1"}},[e._v("周期内限量")]),t("Option",{attrs:{value:"2"}},[e._v("按周期类型重置")])],1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"达量后控制逻辑",prop:"controlLogic"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择达量后控制逻辑",disabled:"Info"==e.typeFlag,clearable:"2"!=e.formObj.flowLimitType&&!("1"===e.formObj.isTerminal&&"1"===e.formObj.flowLimitType)},model:{value:e.formObj.controlLogic,callback:function(t){e.$set(e.formObj,"controlLogic",t)},expression:"formObj.controlLogic"}},["1"!==e.formObj.isTerminal||"1"!==e.formObj.flowLimitType?t("Option",{attrs:{value:"1"}},[e._v("达量限速")]):e._e(),"1"===e.formObj.flowLimitType?t("Option",{attrs:{value:"2"}},[e._v("达量释放")]):e._e()],1)],1)],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:"1"==e.formObj.isTerminal,expression:"formObj.isTerminal == '1'"}]},[t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"流量上限",prop:"flowLimitSum"}},[t("Input",{attrs:{maxlength:8,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入流量上限"},model:{value:e.formObj.flowLimitSum,callback:function(t){e.$set(e.formObj,"flowLimitSum",t)},expression:"formObj.flowLimitSum"}},[t("Select",{staticStyle:{width:"80px"},attrs:{slot:"append",disabled:"Info"==e.typeFlag},slot:"append",model:{value:e.flowLimitUnit,callback:function(t){e.flowLimitUnit=t},expression:"flowLimitUnit"}},[t("Option",{attrs:{value:"1"}},[e._v("GB")]),t("Option",{attrs:{value:"2"}},[e._v("MB")])],1)],1)],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"签约业务(高速)",prop:"signBizId"}},[t("Input",{attrs:{maxlength:50,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"签约业务(高速)"},model:{value:e.formObj.signBizId,callback:function(t){e.$set(e.formObj,"signBizId",t)},expression:"formObj.signBizId"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"签约业务(低速)",prop:"limitSignBizId"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:50,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入签约业务(低速)"},model:{value:e.formObj.limitSignBizId,callback:function(t){e.$set(e.formObj,"limitSignBizId",t)},expression:"formObj.limitSignBizId"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"签约业务(限速)",prop:"slowSignBizId"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:50,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入签约业务(限速)"},model:{value:e.formObj.slowSignBizId,callback:function(t){e.$set(e.formObj,"slowSignBizId",t)},expression:"formObj.slowSignBizId"}})],1)],1)],1)],1),t("Row",{directives:[{name:"show",rawName:"v-show",value:"2"==e.formObj.isTerminal,expression:"formObj.isTerminal == '2'"}]},[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"是否支持热点",prop:"isSupportedHotspots"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择是否支持热点",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":function(t){return e.getcountryList(e.formObj.isSupportedHotspots)}},model:{value:e.formObj.isSupportedHotspots,callback:function(t){e.$set(e.formObj,"isSupportedHotspots",t)},expression:"formObj.isSupportedHotspots"}},[t("Option",{attrs:{value:1}},[e._v("是")]),t("Option",{attrs:{value:2}},[e._v("否")])],1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"允许订购开始时间",prop:"startTime"}},[t("DatePicker",{staticClass:"inputSty",attrs:{type:"datetime",format:"yyyy/MM/dd HH:mm:ss",placeholder:"请选择开始时间",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":e.changeTime},model:{value:e.formObj.startTime,callback:function(t){e.$set(e.formObj,"startTime",t)},expression:"formObj.startTime"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"允许订购结束时间",prop:"endTime"}},[t("DatePicker",{staticClass:"inputSty",attrs:{type:"datetime",format:"yyyy/MM/dd HH:mm:ss",placeholder:"请选择结束时间",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":e.changeTime},model:{value:e.formObj.endTime,callback:function(t){e.$set(e.formObj,"endTime",t)},expression:"formObj.endTime"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"计费激活流量限额",prop:"billFlowLimit"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:11,readonly:"Info"==e.typeFlag,placeholder:"请输入计费激活流量限额"},model:{value:e.formObj.billFlowLimit,callback:function(t){e.$set(e.formObj,"billFlowLimit",t)},expression:"formObj.billFlowLimit"}},[t("span",{attrs:{slot:"append"},slot:"append"},[e._v("MB")])])],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"套餐价格(人民币)",prop:"cny"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:13,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入套餐价格(人民币)"},model:{value:e.formObj.cny,callback:function(t){e.$set(e.formObj,"cny",t)},expression:"formObj.cny"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"套餐价格(港币)",prop:"hkd"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:13,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入套餐价格(港币)"},model:{value:e.formObj.hkd,callback:function(t){e.$set(e.formObj,"hkd",t)},expression:"formObj.hkd"}})],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"套餐价格(美元)",prop:"usd"}},[t("Input",{staticClass:"inputSty",attrs:{maxlength:13,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入套餐价格(美元)"},model:{value:e.formObj.usd,callback:function(t){e.$set(e.formObj,"usd",t)},expression:"formObj.usd"}})],1)],1)],1),t("Row",{directives:[{name:"show",rawName:"v-show",value:"2"==e.formObj.isTerminal,expression:"formObj.isTerminal == '2'"}]},[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"卡池绑定方式",prop:"bindCardPoolType"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":e.changeBindCardpoolType,"on-clear":e.clearBindCardpoolType},model:{value:e.formObj.bindCardPoolType,callback:function(t){e.$set(e.formObj,"bindCardPoolType",t)},expression:"formObj.bindCardPoolType"}},[t("Option",{attrs:{value:1}},[e._v("关联卡池")]),t("Option",{attrs:{value:2}},[e._v("国家卡池关联组")])],1)],1)],1)],1),t("Row",{directives:[{name:"show",rawName:"v-show",value:"2"==e.formObj.bindCardPoolType,expression:"formObj.bindCardPoolType == '2'"}]},[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"选择国家卡池关联组",prop:"groupId",rules:"2"==e.formObj.bindCardPoolType?e.ruleAddValidate.groupId:[{required:!1}]}},[t("Select",{attrs:{placeholder:"选择国家卡池关联组",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,filterable:!0},on:{"on-change":function(t){return e.changeGroupId(e.formObj.groupId)},"on-clear":e.clearGroupId},model:{value:e.formObj.groupId,callback:function(t){e.$set(e.formObj,"groupId",t)},expression:"formObj.groupId"}},e._l(e.groupIdtList,(function(i,a){return t("Option",{key:i.groupId,attrs:{title:i.groupName,value:i.groupId}},[e._v(e._s(i.groupName.length>30?i.groupName.substring(0,30)+"…":i.groupName))])})),1)],1)],1)],1),t("Row",{directives:[{name:"show",rawName:"v-show",value:"1"==e.formObj.isTerminal,expression:"formObj.isTerminal == '1'"}]},[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"选择国家/地区",prop:"mccList",rules:"1"==e.formObj.isTerminal?e.ruleAddValidate.mccList:[{required:!1}]}},[t("Select",{attrs:{multiple:"",placeholder:"请选择国家/地区",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,filterable:!0},on:{"on-change":e.mccListChange},model:{value:e.formObj.mccList,callback:function(t){e.$set(e.formObj,"mccList",t)},expression:"formObj.mccList"}},e._l(e.continentList,(function(i){return t("Option",{key:i.id,attrs:{value:i.mcc}},[e._v(e._s(i.countryEn))])})),1)],1)],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:"2"==e.formObj.isTerminal,expression:"formObj.isTerminal == '2'"}]},[t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{directives:[{name:"show",rawName:"v-show",value:"1"==e.formObj.bindCardPoolType,expression:"formObj.bindCardPoolType == '1'"}],attrs:{label:"选择国家/地区",prop:"mccList",rules:"2"==e.formObj.isTerminal&&"1"==e.formObj.bindCardPoolType?e.ruleAddValidate.mccList:[{required:!1}]}},[t("Select",{attrs:{multiple:"",placeholder:"请选择国家/地区",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,filterable:!0},on:{"on-change":e.mccListChange},model:{value:e.formObj.mccList,callback:function(t){e.$set(e.formObj,"mccList",t)},expression:"formObj.mccList"}},e._l(e.continentList,(function(i){return t("Option",{key:i.id,attrs:{value:i.mcc}},[e._v(e._s(i.countryEn))])})),1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{directives:[{name:"show",rawName:"v-show",value:"2"==e.formObj.bindCardPoolType,expression:"formObj.bindCardPoolType == '2'"}],attrs:{label:"选择国家/地区",prop:"mccList",rules:"2"==e.formObj.bindCardPoolType?e.ruleAddValidate.mccList:[{required:!1}]}},[t("Select",{attrs:{multiple:"",placeholder:"请选择国家/地区",disabled:"Info"==e.typeFlag||!e.formObj.groupId,clearable:"Info"!=e.typeFlag,filterable:!0},on:{"on-change":e.mccListChange},model:{value:e.formObj.mccList,callback:function(t){e.$set(e.formObj,"mccList",t)},expression:"formObj.mccList"}},e._l(e.continentList1,(function(i,a){return t("Option",{key:a,attrs:{value:a}},[e._v(e._s(i))])})),1)],1)],1)],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:"2"==e.formObj.isTerminal,expression:"formObj.isTerminal == '2'"}]},[e._l(e.formObj.packageConsumptionStr,(function(i,a){return t("Row",{key:a,staticStyle:{display:"flex","align-items":"center","margin-bottom":"24px"}},[t("Col",{attrs:{span:"12"}},[t("FormItem",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"用量值",prop:"packageConsumptionStr."+a+".consumption",rules:"2"==e.formObj.isTerminal?e.ruleAddValidate.consumption:[{required:!1}],"label-width":150}},[t("Input",{staticStyle:{width:"200px"},attrs:{maxlength:18,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入用量值"},model:{value:i.consumption,callback:function(t){e.$set(i,"consumption",t)},expression:"item.consumption"}},[t("Select",{staticStyle:{width:"70px"},attrs:{slot:"append",disabled:"Info"==e.typeFlag},on:{"on-change":function(t){return e.handleUnitChange(a,t)},"on-select":function(t){return e.handleUnitSelect(a,t)}},slot:"append",model:{value:i.unit,callback:function(t){e.$set(i,"unit",t)},expression:"item.unit"}},e._l(e.unitList,(function(i){return t("Option",{key:i.value,attrs:{value:i.value}},[e._v("\n\t\t\t\t\t\t\t    "+e._s(i.label)+"\n\t\t\t\t\t\t\t  ")])})),1)],1)],1)],1),t("Col",{staticStyle:{display:"flex","align-items":"center"},attrs:{span:"12"}},[t("FormItem",{staticStyle:{"margin-right":"10px","margin-bottom":"0px"},attrs:{label:"选择模板",prop:"packageConsumptionStr."+a+".upccTemplateId",rules:"2"==e.formObj.isTerminal?e.ruleAddValidate.upccTemplateId:[{required:!1}],"label-width":150}},[t("Select",{staticStyle:{width:"150px"},attrs:{filterable:"",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":function(t){return e.handleUpccChange(a,t)},"on-select":function(t){return e.handleUpccSelect(a,t)}},model:{value:i.upccTemplateId,callback:function(t){e.$set(i,"upccTemplateId",t)},expression:"item.upccTemplateId"}},e._l(e.TemplateList,(function(i){return t("Option",{key:i.templateId,attrs:{title:i.templateDesc,value:i.templateId}},[e._v(e._s(i.templateName.length>30?i.templateName.substring(0,30)+"…":i.templateName))])})),1)],1),0!=a?t("Button",{staticStyle:{"margin-bottom":"0px","margin-top":"0px"},attrs:{type:"error",size:"small",loading:e.deleteLoading},on:{click:function(t){return e.removeTemplate(a)}}},[e._v("删除")]):e._e()],1)],1)})),t("div",{staticStyle:{"margin-top":"0px","margin-bottom":"10px","text-align":"right"}},[t("Button",{attrs:{size:"small",type:"primary",loading:e.addLoading},on:{click:e.addTemplate}},[e._v("添加")])],1)],2),t("Row",{directives:[{name:"show",rawName:"v-show",value:"2"==e.formObj.isTerminal,expression:"formObj.isTerminal == '2'"}]},[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"无上限模板",prop:"noLimitTemplateId"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"选择模板",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},model:{value:e.formObj.noLimitTemplateId,callback:function(t){e.$set(e.formObj,"noLimitTemplateId",t)},expression:"formObj.noLimitTemplateId"}},e._l(e.TemplateList,(function(i){return t("Option",{key:i.templateId,attrs:{title:i.templateDesc,value:i.templateId}},[e._v(e._s(i.templateName.length>30?i.templateName.substring(0,30)+"…":i.templateName))])})),1)],1)],1)],1),t("Row",[t("Col",{directives:[{name:"show",rawName:"v-show",value:"1"==e.formObj.bindCardPoolType||"1"==e.formObj.isTerminal,expression:"formObj.bindCardPoolType == '1' || formObj.isTerminal == '1'"}],attrs:{span:"24"}},[t("FormItem",{attrs:{label:"关联卡池",prop:"cardPool",rules:"1"==e.formObj.bindCardPoolType||"1"==e.formObj.isTerminal?e.ruleAddValidate.cardPool:[{required:!1}]}},[t("Button",{staticClass:"inputSty",attrs:{type:"dashed",long:"",disabled:0==e.formObj.mccList.length},on:{click:function(t){return e.loadCardPoolView(e.formObj.mccList)}}},[e._v("点击查看")])],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{label:"是否支持加油包",prop:"hasRefuelPackage"}},[t("i-switch",{attrs:{size:"large",disabled:"Info"==e.typeFlag},on:{"on-change":e.fuelPackaChange},model:{value:e.formObj.hasRefuelPackage,callback:function(t){e.$set(e.formObj,"hasRefuelPackage",t)},expression:"formObj.hasRefuelPackage"}},[t("span",{attrs:{slot:"open"},slot:"open"},[e._v("是")]),t("span",{attrs:{slot:"close"},slot:"close"},[e._v("否")])])],1)],1),t("Col",{attrs:{span:"12"}},[t("FormItem",{attrs:{prop:"selectionTypes"}},[e.formObj.hasRefuelPackage?t("Button",{staticClass:"inputSty",attrs:{type:"dashed",long:""},on:{click:e.RefuelPackageList}},[e._v("加油包列表")]):e._e()],1)],1)],1),t("Row",[t("Col",{attrs:{span:"12"}},["2"==e.formObj.isTerminal?t("FormItem",{attrs:{label:"允许中国激活",prop:"supportChina"}},[t("i-switch",{attrs:{size:"large",disabled:"Info"==e.typeFlag},model:{value:e.formObj.supportChina,callback:function(t){e.$set(e.formObj,"supportChina",t)},expression:"formObj.supportChina"}},[t("span",{attrs:{slot:"open"},slot:"open"},[e._v("是")]),t("span",{attrs:{slot:"close"},slot:"close"},[e._v("否")])])],1):e._e()],1)],1),t("Row",["2"==e.formObj.isTerminal?t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"套餐扣费模式",prop:"deductionModel"}},[t("Select",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择套餐扣费模式",readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":e.deductionModelChange},model:{value:e.formObj.deductionModel,callback:function(t){e.$set(e.formObj,"deductionModel",t)},expression:"formObj.deductionModel"}},[t("Option",{attrs:{value:"1"}},[e._v("标准模式")]),t("Option",{attrs:{value:"2"}},[e._v("绑定模式")])],1)],1)],1):e._e()],1),t("Row",["2"==e.formObj.isTerminal&&"2"==e.formObj.deductionModel?t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"扣费URL",prop:"deductionUrl"}},[t("Input",{attrs:{maxlength:100,readonly:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag,placeholder:"请输入扣费URL"},model:{value:e.formObj.deductionUrl,callback:function(t){e.$set(e.formObj,"deductionUrl",t)},expression:"formObj.deductionUrl"}})],1)],1):e._e()],1),"2"==e.formObj.isTerminal&&"1"==e.formObj.deductionModel?t("Row",[t("Col",{attrs:{span:"24"}},[t("FormItem",{attrs:{label:"是否支持定向流量",prop:"isSupportDirect"}},[t("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择是否支持定向流量",disabled:"Info"==e.typeFlag,clearable:"Info"!=e.typeFlag},on:{"on-change":function(t){return e.changeDirect(t)}},model:{value:e.formObj.isSupportDirect,callback:function(t){e.$set(e.formObj,"isSupportDirect",t)},expression:"formObj.isSupportDirect"}},[t("Option",{attrs:{value:"1"}},[e._v("是")]),t("Option",{attrs:{value:"2"}},[e._v("否")])],1)],1)],1)],1):e._e(),"2"==e.formObj.isTerminal&&"1"==e.formObj.deductionModel?t("div",[t("directApp",{ref:"procedureEdit",attrs:{typeFlag:e.typeFlag,isSupportDirect:e.formObj.isSupportDirect,notClick:null,packageId:e.packageId}})],1):e._e(),t("Modal",{attrs:{title:"添加加油包","mask-closable":!1,width:"1000px"},on:{"on-cancel":e.cancelModal},model:{value:e.addRefuelModel,callback:function(t){e.addRefuelModel=t},expression:"addRefuelModel"}},[t("Form",{ref:"searchObj",staticStyle:{"font-weight":"bold"},attrs:{model:e.searchObj,"label-width":80,inline:""}},[t("FormItem",{attrs:{label:"加油包名称"}},[t("Input",{attrs:{type:"text",clearable:"",placeholder:"加油包名称"},model:{value:e.searchObj.gaspackname,callback:function(t){e.$set(e.searchObj,"gaspackname",t)},expression:"searchObj.gaspackname"}})],1),t("FormItem",{attrs:{label:"加油包ID"}},[t("Input",{attrs:{type:"text",clearable:"",placeholder:"加油包ID"},model:{value:e.searchObj.gaspacknameid,callback:function(t){e.$set(e.searchObj,"gaspacknameid",t)},expression:"searchObj.gaspacknameid"}})],1),t("FormItem",[t("Button",{attrs:{type:"primary",loading:e.searchObjloading},on:{click:e.search}},[e._v("搜索")])],1)],1),t("Table",{staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:e.Unitedcolumns,data:e.Uniteddata,loading:e.Unitedloading},on:{"on-selection-change":e.handleRowChange,"on-select-cancel":e.cancelPackage,"on-select-all-cancel":e.cancelPackageAll}}),t("div",{staticStyle:{"margin-top":"15px"}},[t("Page",{attrs:{total:e.Unitedtotal,current:e.UnitedcurrentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.UnitedcurrentPage=t},"on-change":e.UnitedgoPage}})],1),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{staticStyle:{"margin-left":"8px"},on:{click:e.cancelModal}},[e._v("取消")]),t("Button",{attrs:{type:"primary"},on:{click:e.Confirm}},[e._v("确定")])],1)],1),"Info"!=e.typeFlag?t("div",{staticStyle:{"text-align":"center"}},[t("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:e.submitFlag},on:{click:e.submit}},[e._v("提交")]),t("Button",{staticStyle:{"margin-left":"8px"},on:{click:e.reset}},[e._v("重置")])],1):e._e(),"Info"==e.typeFlag?t("div",{staticStyle:{"text-align":"center"}},[t("Button",{staticStyle:{width:"100px"},on:{click:e.reback}},[e._v("返回")])],1):e._e()],1)],1),t("Modal",{attrs:{title:"封面预览","footer-hide":!0,width:"532px"},model:{value:e.pictureShowFlag,callback:function(t){e.pictureShowFlag=t},expression:"pictureShowFlag"}},[t("div",{staticStyle:{display:"flex","justify-content":"center","align-items":"center",width:"500px"}},[t("img",{staticStyle:{"object-fit":"contain"},attrs:{src:e.pictureUrl,width:"100%"}})])]),t("Drawer",{attrs:{title:"关联卡池管理",width:"350","mask-closable":!1,styles:e.styles},on:{"on-close":e.drawerClose},model:{value:e.drawer,callback:function(t){e.drawer=t},expression:"drawer"}},["Info"!=e.typeFlag?t("Button",{staticStyle:{margin:"0 15px"},attrs:{type:"success",size:"small"},on:{click:e.cardPoolEdit}},[e._v("编辑")]):e._e(),t("Tree",{ref:"cardPool",staticClass:"demo-tree-render",attrs:{data:e.cardPoolTree,"empty-text":e.emptyText}}),"Info"!=e.typeFlag?t("div",{staticClass:"demo-drawer-footer"},[t("Button",{staticStyle:{"margin-right":"8px"},on:{click:e.drawerClose}},[e._v("取消")]),t("Button",{attrs:{type:"primary"},on:{click:function(t){return e.toSetCardPool()}}},[e._v("确定")])],1):e._e()],1),t("Modal",{attrs:{title:"卡池编辑","mask-closable":!1,width:"730px"},on:{"on-cancel":e.cardPoolEditConfirm},model:{value:e.cardPoolEditFlag,callback:function(t){e.cardPoolEditFlag=t},expression:"cardPoolEditFlag"}},[t("div",{staticStyle:{padding:"0 16px"}},[t("Form",{ref:"cpEditForm",staticStyle:{"font-weight":"bold"},attrs:{model:e.filterSearchObj,inline:""}},[t("FormItem",[t("Input",{attrs:{type:"text",clearable:"",placeholder:"卡池名称"},model:{value:e.filterSearchObj.cpName,callback:function(t){e.$set(e.filterSearchObj,"cpName",t)},expression:"filterSearchObj.cpName"}})],1),t("FormItem",[t("Input",{attrs:{type:"text",clearable:"",placeholder:"供应商名称"},model:{value:e.filterSearchObj.sName,callback:function(t){e.$set(e.filterSearchObj,"sName",t)},expression:"filterSearchObj.sName"}})],1),t("FormItem",[t("Input",{attrs:{type:"text",clearable:"",placeholder:"国家/地区名称"},model:{value:e.filterSearchObj.cName,callback:function(t){e.$set(e.filterSearchObj,"cName",t)},expression:"filterSearchObj.cName"}})],1),t("FormItem",[t("Button",{attrs:{type:"primary",loading:e.cardPoolEditTreeLoad},on:{click:e.doCPTreeFilter}},[e._v("搜索")])],1)],1),t("div",{staticClass:"demo-spin-article"},[t("div",{staticStyle:{height:"295px","overflow-y":"auto"}},[t("Tree",{ref:"cardPool",staticClass:"demo-tree-render",attrs:{data:e.cardPoolEditTree,"empty-text":e.emptyText}})],1),e.cardPoolEditTreeLoad?t("Spin",{attrs:{size:"large",fix:""}}):e._e()],1)],1),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{attrs:{type:"primary"},on:{click:e.cardPoolEditConfirm}},[e._v("确定")])],1)])],1)},o=[],r=i("3835"),n=i("c7eb"),l=i("1da1"),s=i("b85c"),c=i("5530"),d=(i("d9e2"),i("99af"),i("d81d"),i("14d9"),i("4e82"),i("a434"),i("e9c4"),i("4ec9"),i("a9e3"),i("b64b"),i("d3b7"),i("4d63"),i("c607"),i("2c3e"),i("00b4"),i("25f0"),i("3ca3"),i("466d"),i("498a"),i("159b"),i("ddb0"),i("2f62")),p=i("90fe"),m=i("78c0"),u=i("2b6c"),f=i("f7fa"),b=i("3177"),h=i("0aa3"),g=i("c70b"),y={components:{directApp:h["a"]},data:function(){var e=this,t=function(e,t,i){var a=/^[0-9]\d*$/;return a.test(t)},i=function(e,t,i){if(t){var a=/^[1-9]\d*$/;return a.test(t)}i()};return{addRefuelModel:!1,searchObjloading:!1,Unitedloading:!1,addLoading:!1,deleteLoading:!1,searchObj:{gaspackname:"",gaspacknameid:""},Unitedcolumns:[{type:"selection",width:60,align:"center"},{title:"加油包ID",key:"id",minWidth:120,align:"center",tooltip:!0},{title:"加油包名称(简体中文)",key:"nameCn",minWidth:180,align:"center",tooltip:!0},{title:"加油包价格(人民币)",key:"cny",minWidth:180,align:"center",tooltip:!0},{title:"加油包价格(港币)",key:"hkd",minWidth:180,align:"center",tooltip:!0},{title:"加油包价格(美元)",key:"usd",minWidth:180,align:"center",tooltip:!0}],Unitedtotal:0,UnitedcurrentPage:1,Unitedpage:0,Uniteddata:[],submitFlag:!1,en:/^[0-9a-zA-Z\/\(\)\,\.\:\<\>\"\|\?\!\=\-\+\~\^\\\'\;\#\$\%\&\*\`\@\_\]\[\'\s]+$/,mccListTemp:"",usageType:"1",drawer:!1,emptyText:"未查询到任何卡池数据",styles:{height:"calc(100% - 55px)",overflow:"auto",paddingBottom:"53px",position:"static"},continentList:[],continentList1:[],statusList:[{label:"待上架",value:"1"},{label:"正常",value:"2"},{label:"下架",value:"3"}],cardPoolList:[],typeFlag:"Copy",formObj:{nameCn:"",nameTw:"",nameEn:"",descCn:"",descTw:"",descEn:"",cny:"",hkd:"",usd:"",mccList:[],periodUnit:"",keepPeriod:"",effectiveDay:"",isTerminal:"",corpId:"",status:"",startTime:"",endTime:"",isPromotion:"",saleLimit:"",billFlowLimit:"",cardPool:[],hasRefuelPackage:!1,refuelList:[],picture:null,flowLimitType:"",controlLogic:"",selectionTypes:[],supportChina:!1,deductionModel:"",deductionUrl:"",flowLimitSum:"",signBizId:"",limitSignBizId:"",slowSignBizId:"",isSupportedHotspots:"",packageConsumptionStr:[{consumption:"",upccTemplateId:"",unit:""}],groupId:"",noLimitTemplateId:"",bindCardPoolType:"",isSupportDirect:"",directAppInfos:[{directType:"",appConsumption:[{index1:1,consumption:"",upccTemplateId:[]}],isUsePackage:"",noLimitTemplateId:[]}]},flowLimitUnit:"1",pictureUrl:"",pictureShowFlag:!1,corpIdList:[],changedIsTerminal:["flowLimitSum","signBizId","limitSignBizId","slowSignBizId","isSupportedHotspots","noLimitTemplateId","bindCardPoolType"],ruleAddValidate:{nameCn:[{required:!0,type:"string",message:"套餐名称(简中)不能为空"}],nameEn:[{validator:function(e,t,i){var a=/^[0-9a-zA-Z\/\(\)\,\.\:\"\<\>\|\?\!\=\-\+\~\^\\\'\;\#\$\%\&\*\`\@\_\]\[\'\s]+$/;return a.test(t)||""==t},message:"Package name (EN) format error"}],descCn:[{required:!0,type:"string",message:"套餐描述(简中)不能为空"}],descEn:[{validator:function(e,t,i){var a=/^[0-9a-zA-Z\/\(\)\,\.\:\"\<\>\|\?\!\=\-\+\~\^\\\'\;\#\$\%\&\*\`\@\_\]\[\'\s]+$/;return a.test(t)||""==t},message:"Package description (EN) format error"}],periodUnit:[{required:!0,type:"string",message:"周期类型不能为空"}],keepPeriod:[{required:!0,message:"持续周期不能为空"},{validator:t,message:"持续周期格式错误"},{validator:function(e,t,i){return Number(2147483647)>=Number(t)},message:"持续周期数值过大"}],effectiveDay:[{required:!0,message:"购买有效期(天)不能为空"},{validator:t,message:"购买有效期(天)格式错误"},{validator:function(e,t,i){return Number(2147483647)>=Number(t)},message:"购买有效期(天)数值过大"}],corpId:[{required:!0,type:"string",message:"厂商不能为空"}],status:[],isTerminal:[{required:!0,type:"string",message:"是否为厂商套餐不能为空"}],isPromotion:[{required:!0,type:"string",message:"是否为促销套餐不能为空"}],signBizId:[{required:!0,type:"string",message:"签约业务(高速)不能为空"}],limitSignBizId:[{required:!0,type:"string",message:"签约业务(低速)不能为空"}],slowSignBizId:[{required:!0,type:"string",message:"签约业务(限速)不能为空"}],isSupportedHotspots:[{required:!0,message:"是否支持热点不能为空"}],noLimitTemplateId:[{required:!0,type:"string",message:"无上限模板不能为空"}],startTime:[{required:!0,type:"date",message:"开始时间不能为空"}],endTime:[{required:!0,type:"date",message:"结束时间不能为空"}],saleLimit:[{required:!0,message:"促销限购份数不能为空"},{validator:t,message:"促销限购份数格式错误"},{validator:function(e,t,i){return Number(2147483647)>=Number(t)},message:"促销限购份数数值过大"}],flowLimitType:[{required:!0,message:"请选择流量限制类型"}],flowLimitSum:[{required:!0,message:"请输入流量上限"},{validator:function(t,i,a){if("1"===e.formObj.isTerminal){if(!i)return a(new Error("请输入流量上限"));var o=/^[1-9]\d*$/;if(!o.test(i))return a(new Error("请输入正整数"))}a()}}],controlLogic:[{required:!0,message:"请选择达量后控制逻辑"}],cny:[{required:!0,message:"套餐价格(人民币)不能为空"},{validator:function(e,t,i){var a=/^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/;return a.test(t)},message:"最高支持8位整数和2位小数正数或零"}],hkd:[{required:!0,message:"套餐价格(港币)不能为空"},{validator:function(e,t,i){var a=/^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/;return a.test(t)},message:"最高支持8位整数和2位小数正数或零"}],usd:[{required:!0,message:"套餐价格(美元)不能为空"},{validator:function(e,t,i){var a=/^(([1-9]\d{0,7})|0)(\.\d{0,2})?$/;return a.test(t)},message:"最高支持8位整数和2位小数正数或零"}],mccList:[{required:!0,type:"array",message:"支持国家/地区不能为空"}],billFlowLimit:[{validator:i,message:"激活流量限额大于0的正整数"},{validator:function(e,t,i){return Number(2147483647)>=Number(t)},message:"激活流量限额数值过大"}],bindCardPoolType:[{required:!0,message:"卡池绑定方式不能为空"}],groupId:[{required:!0,message:"国家卡池关联组不能为空"}],cardPool:[{validator:function(t,i,a){var o=e.formObj.cardPool.length;return o>0},message:"关联卡池不能为空",trigger:"blur"}],picture:[{validator:function(t,i,a){return""!=e.pictureUrl||null!=e.formObj.picture},message:"封面图片不能为空"}],selectionTypes:[{validator:function(t,i,a){var o=e.formObj.selectionTypes.length;return o>0||!e.formObj.hasRefuelPackage},message:"加油包列表不能空"}],supportChina:[{required:!0,message:"请选择允许中国激活"}],deductionModel:[{required:!0,type:"string",message:"请选择套餐扣费模式"}],isSupportDirect:[{required:!0,type:"string",message:"是否支持定向流向不能为空"}],consumption:[{validator:this.validateConsumption}],upccTemplateId:[{}]},periodUnitList:[{value:"1",label:"24小时"},{value:"2",label:"自然日"},{value:"3",label:"自然月"},{value:"4",label:"自然年"}],cardPoolEditFlag:!1,cardPoolTree:[],cardPoolEditTree:[],cardPoolEditTreeLoad:!1,filterPool:[],filterTempPool:[],totalPool:[],totalTempPool:[],cpcrvList:[],firstLoad:!1,filterSearchObj:{cpName:"",sName:"",cName:""},localMap:new Map,TemplateList:[],groupIdtList:[],refuelIDList:[],refuelIDLists:[],packageId:"",isVaild:!0,isMountedFlag:!1,unitList:[{value:"MB",label:"MB"},{value:"GB",label:"GB"},{value:"TB",label:"TB"}]}},created:function(){var e=this;this.$nextTick((function(){e.changedIsTerminal.forEach((function(t){e.$set(e.ruleAddValidate[t][0],"required",!1)}))}))},methods:Object(c["a"])(Object(c["a"])({},Object(d["d"])(["closeTag"])),{},{transferData:function(e){var t,i=[],a=Object(s["a"])(e);try{var o=function(){var e=t.value,a=[];e.appId.forEach((function(t,i){if(e.appConsumption){var o=e.appConsumption.map((function(e){return{consumption:parseInt(e.consumption),upccTemplateId:e.upccTemplateId[i]}})),r=e.noLimitTemplateId,n={appConsumption:"2"==e.directType?o:[],appId:t,noLimitTemplateId:r[i]};a.push(n)}}));var o={appDetailInfos:a,directType:e.directType,isUsePackage:e.isUsePackage};i.push(o)};for(a.s();!(t=a.n()).done;)o()}catch(r){a.e(r)}finally{a.f()}this.formObj.directAppInfos=i},submit:function(){var e=this;return Object(l["a"])(Object(n["a"])().mark((function t(){var i,a,o;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("2"==e.formObj.isTerminal&&"1"==e.formObj.deductionModel&&(e.formObj.directAppInfos=e.$refs["procedureEdit"].childMethod()),"1"==e.formObj.isTerminal?e.changedIsTerminal.forEach((function(t){e.ruleAddValidate["isSupportedHotspots"][0].required=!1,e.ruleAddValidate["noLimitTemplateId"][0].required=!1,e.ruleAddValidate["bindCardPoolType"][0].required=!1,e.ruleAddValidate["flowLimitSum"].required=!0,e.ruleAddValidate["signBizId"][0].required=!0,e.ruleAddValidate["limitSignBizId"][0].required=!0,e.ruleAddValidate["slowSignBizId"][0].required=!0})):e.changedIsTerminal.forEach((function(t){e.ruleAddValidate["isSupportedHotspots"][0].required=!0,e.ruleAddValidate["noLimitTemplateId"][0].required=!0,e.ruleAddValidate["bindCardPoolType"][0].required=!0,e.ruleAddValidate["flowLimitSum"]=[],e.ruleAddValidate["signBizId"][0].required=!1,e.ruleAddValidate["limitSignBizId"][0].required=!1,e.ruleAddValidate["slowSignBizId"][0].required=!1})),"2"!=e.formObj.isTerminal){t.next=24;break}if(e.formObj.packageConsumptionStr&&0!==e.formObj.packageConsumptionStr.length){t.next=7;break}return e.$Notice.error({title:"操作提示",desc:"请至少填写一组用量值、选择模板信息"}),e.submitFlag=!1,t.abrupt("return");case 7:i=0;case 8:if(!(i<e.formObj.packageConsumptionStr.length)){t.next=19;break}if(a=e.formObj.packageConsumptionStr[i],a.hasOwnProperty("consumption")&&null!==a.consumption&&void 0!==a.consumption&&""!==a.consumption){t.next=13;break}return e.$Message.warning("请输入用量值！"),t.abrupt("return");case 13:if(a.hasOwnProperty("upccTemplateId")&&null!==a.upccTemplateId&&void 0!==a.upccTemplateId&&""!==a.upccTemplateId){t.next=16;break}return e.$Message.warning("请输入选择模板！"),t.abrupt("return");case 16:i++,t.next=8;break;case 19:if("1"!=e.formObj.isSupportDirect||"1"!=e.formObj.deductionModel){t.next=24;break}return t.next=22,e.$refs["procedureEdit"].childSubmit();case 22:e.isVaild=t.sent,e.transferData(e.formObj.directAppInfos);case 24:return t.next=26,e.$nextTick();case 26:if("1"!=e.formObj.isTerminal&&("2"!=e.formObj.isTerminal||"1"!=e.formObj.bindCardPoolType)){t.next=34;break}if(e.cpcrvList&&0!==e.cpcrvList.length){t.next=34;break}return t.next=30,e.firstLoadCardPool(e.formObj.mccList);case 30:if(o=t.sent,o){t.next=34;break}return e.$Message.warning("关联卡池数据加载失败"),t.abrupt("return");case 34:e.$refs["formObj"].validate((function(t){if(console.log(t,"valid"),t&&e.isVaild){if(("1"==e.formObj.isTerminal||"2"==e.formObj.isTerminal&&"1"==e.formObj.bindCardPoolType)&&(!e.cpcrvList||0===e.cpcrvList.length))return void e.$Message.warning("关联卡池不能为空");if("2"==e.formObj.isTerminal&&"2"==e.formObj.bindCardPoolType&&!e.formObj.groupId)return void e.$Message.warning("国家卡池关联组不能为空");if(!e.validateCardPoolRatio())return;var i=Object.assign({},e.formObj),a=[];if(i.hasRefuelPackage&&e.formObj.selectionTypes.forEach((function(e,t){a.push(e.id)})),i.startTime>i.endTime)return e.$Notice.error({title:"操作提示",desc:"操作失败,允许订购时间有误"}),!1;i.cny=Number(g.format(100*Number(i.cny),12)),i.hkd=Number(g.format(100*Number(i.hkd),12)),i.usd=Number(g.format(100*Number(i.usd),12));var o,r=e.typeFlag,n=new FormData;if(("1"==e.formObj.isTerminal||"1"==e.formObj.bindCardPoolType)&&(o=e.cpcrvList),null!=i.picture&&n.append("file",i.picture),"Add"==r&&(e.submitFlag=!0,Object(m["a"])(n).then((function(t){if(!t||"0000"!=t.code)throw e.submitFlag=!1,t;e.$Notice.success({title:"操作提示",desc:"操作成功"}),setTimeout((function(){e.submitFlag=!1,e.reback()}),1500)})).catch((function(t){e.submitFlag=!1}))),"Copy"==r){var l="1"==i.isSupportDirect?i.directAppInfos:[];if(e.submitFlag=!0,null!=i.picture){var s;Object(m["b"])(n).then((function(t){if(!t||"0000"!=t.code)throw e.submitFlag=!1,t;s=t.data})).then((function(){Object(m["a"])({id:i.id,coverPath:s,nameCn:i.nameCn,nameTw:i.nameTw,nameEn:i.nameEn,descCn:i.descCn,descTw:i.descTw,descEn:i.descEn,cny:i.cny,hkd:i.hkd,usd:i.usd,mccList:i.mccList,periodUnit:i.periodUnit,keepPeriod:i.keepPeriod,effectiveDay:i.effectiveDay,isTerminal:i.isTerminal,corpId:"1"==i.isTerminal?i.corpId:"",status:i.status,startTime:i.startTime,endTime:i.endTime,flowLimitType:i.flowLimitType,flowLimitUnit:e.flowLimitUnit,controlLogic:i.controlLogic,isPromotion:i.isPromotion,saleLimit:"1"==i.isPromotion?i.saleLimit:"",billFlowLimit:i.billFlowLimit,billFlowLimitUnit:2,refuelList:a,supportRefuel:i.hasRefuelPackage?1:2,supportChina:i.supportChina?1:2,deductionUrl:i.deductionUrl,flowLimitSum:"1"==i.isTerminal?i.flowLimitSum:void 0,signBizId:"1"==i.isTerminal?i.signBizId:void 0,slowSignBizId:"1"==i.isTerminal?i.slowSignBizId:void 0,limitSignBizId:"1"==i.isTerminal?i.limitSignBizId:void 0,cpcrvList:o,deductionModel:"1"==i.isTerminal?1:i.deductionModel,bindCardPoolType:"2"==i.isTerminal?i.bindCardPoolType:void 0,packageConsumptions:"2"==i.isTerminal?i.packageConsumptionStr.map((function(t){return{consumption:e.convertConsumptionToMB(t.consumption,t.unit),unit:t.unit,upccTemplateId:t.upccTemplateId}})):void 0,noLimitTemplateId:"2"==i.isTerminal?i.noLimitTemplateId:void 0,isSupportedHotspots:"2"==i.isTerminal?i.isSupportedHotspots:void 0,groupId:"2"==i.isTerminal&&"2"==i.bindCardPoolType?i.groupId:void 0,isSupportDirect:"2"==i.isTerminal&&"1"==i.deductionModel?i.isSupportDirect:"2",directAppInfos:l}).then((function(t){if(!t||"0000"!=t.code)throw e.submitFlag=!1,t;e.$Notice.success({title:"操作提示",desc:"操作成功"}),setTimeout((function(){e.submitFlag=!1,e.reback()}),1500)})).catch((function(t){e.submitFlag=!1}))}))}else{var c=void 0;if("2"==i.isTerminal)try{c=i.packageConsumptionStr.map((function(t){return{consumption:e.convertConsumptionToMB(t.consumption,t.unit),unit:t.unit,upccTemplateId:t.upccTemplateId}}))}catch(d){console.error("映射 packageConsumptionStr 时发生错误:",d)}Object(m["a"])({id:i.id,coverPath:e.imageUrl,nameCn:i.nameCn,nameTw:i.nameTw,nameEn:i.nameEn,descCn:i.descCn,descTw:i.descTw,descEn:i.descEn,cny:i.cny,hkd:i.hkd,usd:i.usd,mccList:i.mccList,periodUnit:i.periodUnit,keepPeriod:i.keepPeriod,effectiveDay:i.effectiveDay,isTerminal:i.isTerminal,corpId:"1"==i.isTerminal?i.corpId:"",status:i.status,startTime:i.startTime,endTime:i.endTime,flowLimitType:i.flowLimitType,flowLimitUnit:e.flowLimitUnit,controlLogic:i.controlLogic,isPromotion:i.isPromotion,saleLimit:"1"==i.isPromotion?i.saleLimit:"",billFlowLimit:i.billFlowLimit,billFlowLimitUnit:2,refuelList:a,supportRefuel:i.hasRefuelPackage?1:2,supportChina:i.supportChina?1:2,deductionUrl:i.deductionUrl,flowLimitSum:"1"==i.isTerminal?i.flowLimitSum:void 0,signBizId:"1"==i.isTerminal?i.signBizId:void 0,slowSignBizId:"1"==i.isTerminal?i.slowSignBizId:void 0,limitSignBizId:"1"==i.isTerminal?i.limitSignBizId:void 0,cpcrvList:o,deductionModel:"1"==i.isTerminal?1:i.deductionModel,bindCardPoolType:"2"==i.isTerminal?i.bindCardPoolType:void 0,packageConsumptions:c,noLimitTemplateId:"2"==i.isTerminal?i.noLimitTemplateId:void 0,isSupportedHotspots:"2"==i.isTerminal?i.isSupportedHotspots:void 0,groupId:"2"==i.isTerminal&&"2"==i.bindCardPoolType?i.groupId:void 0,isSupportDirect:"2"==i.isTerminal&&"1"==i.deductionModel?i.isSupportDirect:"2",directAppInfos:l}).then((function(t){if(!t||"0000"!=t.code)throw e.submitFlag=!1,t;e.$Notice.success({title:"操作提示",desc:"操作成功"}),setTimeout((function(){e.submitFlag=!1,e.reback()}),1500)})).catch((function(t){e.submitFlag=!1}))}}"Update"==r&&(n.append("id",i.id),e.submitFlag=!0,Object(m["z"])(n).then((function(t){if(!t||"0000"!=t.code)throw e.submitFlag=!1,t;e.$Notice.success({title:"操作提示",desc:"操作成功"}),setTimeout((function(){e.submitFlag=!1,e.reback()}),1500)})).catch((function(t){e.submitFlag=!1})))}}));case 35:case"end":return t.stop()}}),t)})))()},reset:function(){this.formObj={nameCn:"",nameTw:"",nameEn:"",descCn:"",descTw:"",descEn:"",cny:"",hkd:"",usd:"",mccList:[],periodUnit:"",keepPeriod:"",effectiveDay:"",isTerminal:"",corpId:"",status:"",startTime:"",endTime:"",isPromotion:"",saleLimit:"",billFlowLimit:"",cardPool:[],hasRefuelPackage:!1,refuelList:[],picture:null,flowLimitType:"",controlLogic:"",selectionTypes:[],deductionModel:"",deductionUrl:"",packageConsumptionStr:[{consumption:"",upccTemplateId:"",unit:"MB"}],bindCardPoolType:"",noLimitTemplateId:"",groupId:"",flowLimitSum:"",slowSignBizId:"",limitSignBizId:"",isSupportDirect:"",directAppInfos:[{directType:"",appConsumption:[{index1:1,consumption:"",upccTemplateId:[]}],isUsePackage:"",noLimitTemplateId:[]}]},this.pictureUrl="",this.$refs["formObj"].resetFields()},getLocalList:function(){var e=this;Object(p["f"])().then((function(t){if(!t||"0000"!=t.code)throw t;var i=t.data;e.continentList=i,e.continentList.sort((function(e,t){return e.countryEn.localeCompare(t.countryEn)}));var a=new Map;i.map((function(e,t){a.set(e.mcc,e.countryEn)})),e.localMap=a})).catch((function(e){})).finally((function(){}))},getLocalList2:function(){var e=this;Object(b["e"])({groupId:this.formObj.groupId,isSupportedHotspots:""}).then((function(t){if(!t||"0000"!=t.code)throw t;var i=t.data;for(var a in e.continentList1=i,e.formObj.mccMap){var o=-1;for(var r in i)e.formObj.mccMap[a]===i[r]&&(o=0);-1===o&&(e.countryList[a]=e.formObj.mccMap[a])}})).catch((function(e){})).finally((function(){}))},handleRowChange:function(e){var t=this;this.selection=e,e.map((function(e,i){var a=!0;t.formObj.selectionTypes.map((function(t,i){e.id===t.id&&(a=!1)})),a&&t.formObj.selectionTypes.push(e)}))},cancelPackage:function(e,t){var i=this;this.formObj.selectionTypes.forEach((function(e,a){e.id===t.id&&i.formObj.selectionTypes.splice(a,1)}))},cancelPackageAll:function(e,t){this.formObj.selectionTypes=[]},getRefuelList:function(e){var t=this;this.Unitedloading=!0,Object(m["v"])({pageNum:e,pageSize:10,refuelID:this.searchObj.gaspacknameid,refuelName:this.searchObj.gaspackname}).then((function(i){if(!i||"0000"!=i.code)throw i;t.Uniteddata=i.data,t.Unitedtotal=i.count,t.UnitedcurrentPage=e,t.formObj.selectionTypes.forEach((function(e){i.data.forEach((function(i){i.id==e.id&&t.$set(i,"_checked",!0)}))})),t.addRefuelModel=!0})).catch((function(e){})).finally((function(){t.Unitedloading=!1,t.searchObjloading=!1}))},RefuelPackageList:function(){this.getRefuelList(1)},fuelPackaChange:function(){this.formObj.selectionTypes=[]},deductionModelChange:function(){this.formObj.deductionUrl=""},search:function(){this.searchObjloading=!0,this.getRefuelList(1)},UnitedgoPage:function(e){this.getRefuelList(e)},addRefuelPackage:function(){var e=this.formObj.refuelList.length,t=this.formObj.refuelList[e-1];0==e||""!=t.nameCn&&""!=t.cny&&""!=t.hkd&&""!=t.hkd?this.formObj.refuelList.push({nameCn:"",nameTw:"",nameEn:"",flowValue:"",flowUnit:"",cny:"",hkd:"",usd:""}):this.$Message.error("请完善上条加油包信息")},delRefuelPackageBtn:function(e){this.formObj.refuelList.splice(e,1)},handleUpload:function(e){var t=this,i=e.type;if(-1!=i.indexOf("image")){this.formObj.picture=e,this.pictureUrl="";var a=new FileReader;a.readAsDataURL(e),a.onload=function(){var e=a.result;t.pictureUrl=e},this.$refs["formObj"].validateField("picture")}else this.$Notice.error({title:"操作提示",desc:"仅支持图片格式文件"});return!1},cancelSelected:function(){this.formObj.picture=null,this.pictureUrl="",this.$refs["formObj"].validateField("picture")},getcontrolLogic:function(){this.formObj.controlLogic="2"===this.formObj.flowLimitType?"1":"1"===this.formObj.isTerminal&&"1"===this.formObj.flowLimitType?"2":null},changeTime:function(){var e=this.formObj.startTime,t=this.formObj.endTime;""==e||""==t||e>t?this.formObj.status="":e>new Date?this.formObj.status="1":e<new Date&&t>new Date?this.formObj.status="2":t<new Date&&(this.formObj.status="3")},dateToStr:function(e){var t=new Date(e),i=t.getFullYear(),a=t.getMonth()+1,o=t.getDate(),r=t.getHours(),n=t.getMinutes(),l=t.getSeconds();return a<10&&(a="0"+a),o<10&&(o="0"+o),r<10&&(r="0"+r),n<10&&(n="0"+n),l<10&&(l="0"+l),i+"-"+a+"-"+o+" "+r+":"+n+":"+l},groupIdListChange:function(){var e=this;Object(m["k"])({isSupportedHotspots:this.formObj.isSupportedHotspots}).then((function(t){if(!t||"0000"!=t.code)throw t;e.groupIdtList=t.data})).catch((function(e){}))},handleUnitChange:function(e,t){var i=this,a=t&&t.value||t;a?this.formObj.packageConsumptionStr[e]&&(this.$set(this.formObj.packageConsumptionStr[e],"unit",a),console.log("单位变化:","索引 ".concat(e),"新值:",a),this.$nextTick((function(){i.$refs["formObj"].validateField("packageConsumptionStr.".concat(e,".consumption"))}))):console.error("无法获取单位值",t)},handleUnitSelect:function(e,t){console.log("单位被选中:",e,t),this.handleUnitChange(e,t.value)},handleUpccChange:function(e,t){var i=this,a=t&&t.value||t;a?this.formObj.packageConsumptionStr[e]&&(this.$set(this.formObj.packageConsumptionStr[e],"upccTemplateId",a),console.log("模板变化:","索引 ".concat(e),"新值:",a),this.$nextTick((function(){i.$refs["formObj"].validateField("packageConsumptionStr.".concat(e,".upccTemplateId"))}))):console.error("无法获取模板值",t)},handleUpccSelect:function(e,t){console.log("模板被选中:",e,t),this.handleUpccChange(e,t.value)},mccListChange:function(e){this.firstLoad?this.firstLoad=!1:(this.mccListTemp="",this.formObj.cardPool=[],this.totalPool=[],this.cpcrvList=[])},cardPoolEdit:function(){this.filterSearchObj={cpName:"",sName:"",cName:""},this.filterRateList("","","","all","edit"),this.drawer=!1,this.cardPoolEditFlag=!0},doCPTreeFilter:function(){this.cardPoolEditTreeLoad=!0;var e=this;this.saveTreeIntoTotalPool(),this.filterRateList(this.filterSearchObj.cpName,this.filterSearchObj.sName,this.filterSearchObj.cName,"all","edit"),setTimeout((function(){e.cardPoolEditTreeLoad=!1}),500)},saveTreeIntoTotalPool:function(){if(this.totalPool.length>0){var e=new Map;this.filterPool.map((function(t,i){t.children.map((function(i,a){null!=i.rate&&0!=i.rate&&e.set(t.id+i.mcc,i.rate)}))})),this.totalPool.map((function(t,i){t.children.map((function(i,a){e.has(t.id+i.mcc)&&(i.rate=e.get(t.id+i.mcc))}))}))}},cardPoolEditConfirm:function(){this.filterRateList("","","","filled","show"),this.cardPoolEditFlag=!1,this.drawer=!0},cardPoolEditCancle:function(){this.filterRateList("","","","filled","show"),this.cardPoolEditFlag=!1,this.drawer=!0},Confirm:function(){this.addRefuelModel=!1,this.searchObj.gaspackname="",this.searchObj.gaspacknameid="",this.refuelIDLists=JSON.parse(JSON.stringify(this.formObj.selectionTypes))},cancelModal:function(){this.addRefuelModel=!1,this.searchObj.gaspackname="",this.searchObj.gaspacknameid="",0==this.refuelIDLists.length?this.formObj.selectionTypes=JSON.parse(JSON.stringify(this.refuelIDList)):this.formObj.selectionTypes=JSON.parse(JSON.stringify(this.refuelIDLists))},loadTreeData:function(e){var t=this,i=[],a=e.length;try{for(var o,r=function(){var a=n,r=e[a],l={title:r.poolName+"-("+r.supplierName+")",id:r.poolId,poolName:r.poolName,supplierName:r.supplierName,expand:!0,children:[]};if(r.regionList&&r.regionList.length>0){var s=function(){var i=o,n=r.regionList[i];l.children.push({expand:!0,poolId:r.poolId,poolName:r.poolName,supplierName:r.supplierName,countryCn:n.countryCn,countryTw:n.countryTw,countryEn:n.countryEn,mcc:n.mcc,rate:n.rate,render:function(o,n){n.root,n.node,n.data;return o("div",{style:{display:"flex",width:"100%",height:"25px",flexDirection:"row",alignItems:"center"}},[o("Tooltip",{props:{placement:"left",content:r.regionList[i].countryEn},style:{width:"100px",display:"inline-block"}},[o("div",{style:{width:"100px",height:"25px",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",lineHeight:"30px"}},r.regionList[i].countryEn+"：")]),o("input",{domProps:{type:"Number",value:void 0==r.regionList[i].rate?null:r.regionList[i].rate,placeholder:"请输入分配比列(%)",max:100,min:0,disabled:"Info"==t.typeFlag},style:{width:"150px",height:"20px",textAlign:"center",border:"#ccc 1px solid",borderRadius:"5px",mozBorderRadius:"5px",webkitBorderRadius:"5px",marginLeft:"8px"},on:{input:function(t){var o=t.target.value;e[a].regionList[i].rate=o,l.children[i].rate=o}}})])}})};for(o=0;o<r.regionList.length;o++)s()}i.push(l)},n=0;n<a;n++)r();this.totalPool=i}catch(l){this.totalPool=[]}},filterRateList:function(e,t,i,a,o){var r=[];this.totalPool.length>0&&this.totalPool.map((function(o,n){var l=null!=o.poolName&&-1!=o.poolName.indexOf(e),s=null!=o.supplierName&&-1!=o.supplierName.indexOf(t),c={title:o.title,id:o.id,poolName:o.poolName,supplierName:o.supplierName,expand:!0,children:[]};l&&s&&o.children.map((function(e,t){var o=null!=e.countryEn&&-1!=e.countryEn.indexOf(i);o&&("all"==a&&c.children.push(e),"filled"==a&&null!=e.rate&&""!=e.rate&&c.children.push(e))})),c.children.length>0&&r.push(c)})),0!=r.length?("edit"==o&&(this.cardPoolEditTree=[{title:"关联卡池",expand:!0,children:[]}],this.cardPoolEditTree[0].children=r.concat(),this.filterPool=r.concat(),this.$forceUpdate()),"show"==o&&(this.cardPoolTree=[{title:"关联卡池",expand:!0,children:[]}],this.cardPoolTree[0].children=r.concat(),this.filterPool=r.concat(),this.$forceUpdate())):(this.cardPoolEditTree=[],this.cardPoolTree=[],this.filterPool=[])},loadTotalRateList:function(){var e=[];this.totalPool.map((function(t,i){t.children.map((function(t,i){null!=t.rate&&""!==t.rate&&e.push({poolId:t.poolId,poolName:t.poolName,mcc:t.mcc,rate:String(t.rate)})}))})),this.cpcrvList=e},firstLoadCardPool:function(e){var t=this,i=this.formObj.id;return this.mccListTemp!=JSON.stringify(e)?(this.mccListTemp=JSON.stringify(e),Object(u["b"])({usageType:"1"==this.formObj.isTerminal?"3":"1",mccList:e,packageId:void 0==i?null:i,cardIsUpdate:!0}).then((function(e){if(e&&"0000"==e.code){var i=e.data;return t.formObj.cardPool=i.data,t.formObj.cardPool.sort((function(e,t){return e.poolName.localeCompare(t.poolName)})),t.loadTreeData(t.formObj.cardPool),t.filterRateList("","","","filled","show"),t.loadTotalRateList(),!0}throw e})).catch((function(e){return console.error("加载卡池数据失败:",e),!1}))):(this.loadTreeData(this.formObj.cardPool),this.filterRateList("","","","filled","show"),this.loadTotalRateList(),Promise.resolve(!0))},loadCardPoolView:function(e){var t=this,i=this.formObj.id;this.mccListTemp!=JSON.stringify(e)?(this.mccListTemp=JSON.stringify(e),Object(u["b"])({usageType:"1"==this.formObj.isTerminal?"3":"1",mccList:e,packageId:void 0==i?null:i,cardIsUpdate:!0}).then((function(e){if(!e||"0000"!=e.code)throw e;var i=e.data;t.formObj.cardPool=i.data,t.formObj.cardPool.sort((function(e,t){return e.poolName.localeCompare(t.poolName)})),t.loadTreeData(t.formObj.cardPool),t.filterRateList("","","","filled","show"),t.drawer=!0})).catch((function(e){})).finally((function(){}))):(this.loadTreeData(this.formObj.cardPool),this.filterRateList("","","","filled","show"),this.drawer=!0)},drawerClose:function(){"Info"!=this.typeFlag&&(this.mccListTemp="",this.formObj.cardPool=[],this.cpcrvList=[],this.$refs["formObj"].validateField("cardPool")),this.drawer=!1},toSetCardPool:function(){var e=[],t=[];this.totalPool.length>0&&this.totalPool.map((function(i,a){i.children.map((function(i,a){null!=i.rate&&0!=i.rate&&(e.push({poolId:i.poolId,poolName:i.poolName,mcc:i.mcc,rate:String(i.rate)}),t.push(i.mcc))}))}));for(var i=new RegExp("^(\\d|[0-9]\\d|100)$"),a=!0,o=0;o<e.length;o++)if(!i.test(e[o].rate)&&""!=e[o].rate)return this.$Notice.warning({title:"操作提示",desc:"分配比输入错误(仅支持0-100)"}),a=!1,!1;var r=[];for(var n in e.map((function(e,t){var i=e.mcc;r[i]||(r[i]=[]),r[i].push({key:t,value:Number(e.rate)})})),r){var l=r[n];if(1==l.length)e[l[0].key].rate="100";else{var s=0;if(l.map((function(e,t){s+=e.value})),100!=s){var c=this.localMap.has(n)?this.localMap.get(n):"各国家";return this.$Notice.warning({title:"操作提示",desc:c+"分配比需满足100%"}),a=!1,!1}}if(!a)return!1}for(var d=this.formObj.mccList,p=0;p<d.length;p++)if(-1==t.indexOf(d[p])){c=this.localMap.has(d[p])?this.localMap.get(d[p]):"存在国家/地区";return this.$Notice.warning({title:"操作提示",desc:c+"未分配比例"}),a=!1,!1}if(!a)return!1;this.cpcrvList=e,this.$refs["formObj"].validateField("cardPool"),this.drawer=!1},isTerminalChange:function(e){"1"==e?(this.formObj.isSupportedHotspots="",this.formObj.noLimitTemplateId="",this.formObj.packageConsumptionStr=[{consumption:"",upccTemplateId:"",unit:""}],this.formObj.mccList=[],this.formObj.bindCardPoolType="",this.formObj.flowLimitSum="",this.getCompanyList(),this.getLocalList()):(this.formObj.flowLimitSum="",this.formObj.signBizId="",this.formObj.limitSignBizId="",this.formObj.slowSignBizId="",this.formObj.packageConsumptionStr=[{consumption:"",upccTemplateId:"",unit:"MB"}]),this.formObj.controlLogic="2"===this.formObj.flowLimitType?"1":"1"===this.formObj.isTerminal&&"1"===this.formObj.flowLimitType?"2":null,this.mccListTemp="",this.formObj.cardPool=[],this.cpcrvList=[],this.formObj.deductionModel="",this.formObj.deductionUrl=""},getCompanyList:function(){var e=this;Object(f["p"])({pageNumber:1,pageSize:-1,corpType:"7"}).then((function(t){if(!t||"0000"!=t.code)throw t;var i=t.data;e.corpIdList=i.records})).catch((function(e){})).finally((function(){}))},reback:function(){this.$router.push({name:"packageIndex"})},clearBindCardpoolType:function(){this.formObj.mccList=[],this.formObj.groupId=""},clearGroupId:function(){this.formObj.mccList=[]},changeBindCardpoolType:function(){var e=this;"1"==this.formObj.bindCardPoolType&&(this.formObj.groupId="",this.continentList1=[],this.formObj.mccList=[],this.formObj.cardPool=[],this.cpcrvList=[],this.totalPool=[],this.filterPool=[],this.cardPoolTree=[],this.cardPoolEditTree=[],this.getLocalList()),"2"==this.formObj.bindCardPoolType&&(this.formObj.cardPool=[],this.cpcrvList=[],this.totalPool=[],this.filterPool=[],this.cardPoolTree=[],this.cardPoolEditTree=[],this.formObj.mccList=[]),this.$nextTick((function(){e.$refs["formObj"].validateField("groupId")}))},getcountryList:function(e){e&&(this.getSelectTemplate(e),this.isMountedFlag&&(this.formObj.packageConsumptionStr.forEach((function(e){e.upccTemplateId=""})),this.formObj.noLimitTemplateId=""))},changeGroupId:function(e){e&&(this.getLocalList2(),this.formObj.mccList=[])},getSelectTemplate:function(e){var t=this;Object(m["w"])({isSupportedHotspots:e}).then((function(e){if(!e||"0000"!=e.code)throw e;t.TemplateList=e.data})).catch((function(e){}))},removeTemplate:function(e){this.deleteLoading=!0,this.formObj.packageConsumptionStr.splice(e,1),this.deleteLoading=!1},addTemplate:function(){this.addLoading=!0,this.formObj.packageConsumptionStr.push({consumption:"",upccTemplateId:"",unit:"MB"}),this.addLoading=!1},changeDirect:function(e){},validateCardPoolRatio:function(){if("1"==this.formObj.isTerminal||"2"==this.formObj.isTerminal&&"1"==this.formObj.bindCardPoolType)for(var e=this.formObj.mccList,t=this.cpcrvList.map((function(e){return e.mcc})),i=0;i<e.length;i++)if(-1==t.indexOf(e[i])){var a=this.localMap.has(e[i])?this.localMap.get(e[i]):e[i];return this.$Notice.warning({title:"操作提示",desc:a+"未分配比例"}),!1}return!0},convertConsumptionToMB:function(e,t){if(!e)return 0;var i=parseFloat(e);switch(t){case"TB":return 1024*i*1024;case"GB":return 1024*i;case"MB":return i;case"KB":return i/1024;case"B":return i/1048576;default:return i}},validateConsumption:function(e,t,i){t=t.trim();var a=e.field.match(/packageConsumptionStr\.([0-9]+)\.consumption/),o=parseInt(a[1],10),r=this.formObj.packageConsumptionStr,n=r[o],l=n.consumption,s=n.unit;if(void 0===l||void 0===s)return console.error("packageConsumptionStr 的元素中缺少 consumption 或 unit 属性"),void i(new Error("请输入用量值和单位"));var c=/^[1-9]\d{0,9}$/;if(c.test(l)){var d;try{var p=BigInt(l);switch(s){case"TB":d=1024n*p*1024n;break;case"GB":d=1024n*p;break;case"MB":d=p;break;default:return void i(new Error("无效的单位"))}}catch(O){return void i(new Error("用量值转换失败"))}var m=10n,u=9999999999n;if(d<m)i(new Error("用量值不能小于"+m+"MB"));else{var f;if(d>u)return"MB"===s?f=u+"MB":"GB"===s?f=u/1024n+"GB":"TB"===s&&(f=u/(1024n*1024n)+"TB"),void i(new Error("用量值超过最大限制"+f));var b=o-1,h=null;if(b>=0)try{var g=r[b],y=BigInt(g.consumption);switch(g.unit){case"TB":h=1024n*y*1024n;break;case"GB":h=1024n*y;break;case"MB":h=y;break}}catch(O){console.error("转换前一个用量值失败:",O)}null!==h&&d<=h?i(new Error("用量值逻辑不正确，每档的用量值需大于上一档次的用量值")):i()}}else i(new Error("用量值必须是1-10位整数数字"))}}),computed:{},mounted:function(){var e=this;this.groupIdListChange();try{if(null!=this.$route.query.package){this.firstLoad=!0;var t=JSON.parse(decodeURIComponent(this.$route.query.package));t.startTime=this.dateToStr(t.startTime),t.endTime=this.dateToStr(t.endTime),this.formObj=Object.assign({},t),this.formObj.picture=null,this.flowLimitUnit=Number(t.flowLimitUnit),this.refuelIDList=JSON.parse(JSON.stringify(this.formObj.refuelIDList)),this.formObj.selectionTypes=this.formObj.refuelIDList,this.formObj.billFlowLimit=0===this.formObj.billFlowLimit?"":this.formObj.billFlowLimit,this.formObj.hasRefuelPackage="1"===this.formObj.supportRefuel||(this.formObj.supportRefuel,!1),this.formObj.supportChina="1"===this.formObj.supportChina||(this.formObj.supportChina,!1),this.mccListTemp="",this.firstLoadCardPool(t.mccList),this.formObj.isSupportedHotspots=Number(this.formObj.isSupportedHotspots),this.getcountryList(this.formObj.isSupportedHotspots),this.formObj.packageConsumptionStr=[],"2"==t.isTerminal&&(this.formObj.bindCardPoolType=Number(t.bindCardpoolType),t.packageConsumptions.map((function(t){var i=t.displayConsumption.split(" "),a=Object(r["a"])(i,2),o=a[0],n=a[1];e.formObj.packageConsumptionStr.push({consumption:o,upccTemplateId:t.upccTemplateId,unit:n})}))),this.formObj.mccMap=t.mccMap,"1"==t.isTerminal?(this.getCompanyList(),this.changedIsTerminal.forEach((function(t){e.$set(e.ruleAddValidate["isSupportedHotspots"][0],"required",!1),e.$set(e.ruleAddValidate["noLimitTemplateId"][0],"required",!1),e.$set(e.ruleAddValidate["bindCardPoolType"][0],"required",!1),e.$set(e.ruleAddValidate["flowLimitSum"][0],"required",!0),e.$set(e.ruleAddValidate["signBizId"][0],"required",!0),e.$set(e.ruleAddValidate["limitSignBizId"][0],"required",!0),e.$set(e.ruleAddValidate["slowSignBizId"][0],"required",!0)})),this.getLocalList()):this.changedIsTerminal.forEach((function(t){e.$set(e.ruleAddValidate["isSupportedHotspots"][0],"required",!0),e.$set(e.ruleAddValidate["noLimitTemplateId"][0],"required",!0),e.$set(e.ruleAddValidate["bindCardPoolType"][0],"required",!0),e.$set(e.ruleAddValidate["flowLimitSum"][0],"required",!1),e.$set(e.ruleAddValidate["signBizId"][0],"required",!1),e.$set(e.ruleAddValidate["limitSignBizId"][0],"required",!1),e.$set(e.ruleAddValidate["slowSignBizId"][0],"required",!1)})),"2"==t.isTerminal&&("2"==t.bindCardpoolType?this.getLocalList2():this.getLocalList()),this.pictureUrl=t.coverUrl,this.typeFlag=t.type,this.imageUrl=t.coverPath,this.packageId=t.id,this.$nextTick((function(){e.$refs["formObj"].validate((function(e){e||console.log("表单验证失败，请检查必填字段")}))}))}}catch(i){console.error("初始化数据失败:",i)}this.isMountedFlag=!0}},O=y,v=(i("6326"),i("2877")),j=Object(v["a"])(O,a,o,!1,null,"c18fab44",null);t["default"]=j.exports}}]);