<template>
	<!-- 订单管理 -->
	<ElCard class="order-card">
		<div class="order-container">
			<!-- 搜索表单 -->
			<div class="search-form">
				<ElForm inline>
					<ElFormItem label="套餐名称" label-width="100px">
						<ElInput 
							v-model.trim="packageName" 
							placeholder="请输入套餐名称"
							clearable 
							style="width: 200px" 
						/>
					</ElFormItem>
					<ElFormItem label="卡号" label-width="100px">
						<ElInput 
							v-model.trim="iccid" 
							placeholder="请输入卡号" 
							clearable
							style="width: 200px" 
						/>
					</ElFormItem>
					<ElFormItem label="时间段" label-width="80px">
						<ElDatePicker 
							v-model="timeSlot" 
							type="daterange" 
							format="YYYY-MM-DD" 
							value-format="YYYY-MM-DD"
							placeholder="请选择时间" 
							style="width: 200px; margin-right: 10px;"
							@change="handleDateChange" 
							@clear="handleDateClear"
						/>
					</ElFormItem>
					<ElFormItem label="归属渠道" label-width="120px">
						<ElSelect v-model="attributableChannel" clearable style="width: 200px">
							<ElOption 
								v-for="item in attributableChannelList" 
								:key="item.corpId"
								:value="item.corpId"
								:label="item.corpName"
							/>
						</ElSelect>
					</ElFormItem>
				</ElForm>
				
				<!-- 操作按钮 -->
				<div class="action-buttons">
					<ElButton 
						v-if="checkPermission(['view'])" 
						type="success" 
						@click="showBill"
					>
						月度账单
					</ElButton>
					<ElButton 
						v-if="checkPermission(['search'])" 
						:disabled="cooperationMode === '3'" 
						type="primary" 
						:loading="searchLoading"
						@click="search"
						:icon="Search"
					>
						搜索
					</ElButton>
					<ElButton 
						v-if="checkPermission(['export'])" 
						:disabled="cooperationMode === '3'" 
						type="success" 
						:loading="downloading" 
						@click="exportFile"
						:icon="Download"
					>
						导出
					</ElButton>
					<ElButton 
						v-if="checkPermission(['trafficDetails'])"
						:disabled="cooperationMode === '1' || cooperationMode === '3'" 
						type="info" 
						@click="getTrafficDetails"
						:icon="Download"
					>
						流量详情
					</ElButton>
				</div>
			</div>
			
			<!-- 表格 -->
			<ElTable 
				:data="tableData" 
				style="width: 100%; margin-top: 40px;" 
				v-loading="loading"
				border
				class="order-table"
			>
				<ElTableColumn 
					prop="orderId"
					label="订单号"
					min-width="120"
					align="center"
				/>
				<ElTableColumn 
					prop="iccid"
					label="卡号"
					min-width="120"
					align="center"
				/>
				<ElTableColumn 
					prop="packageName"
					label="套餐名称"
					min-width="140"
					align="center"
				/>
				<ElTableColumn 
					prop="orderTime"
					label="订单时间"
					min-width="160"
					align="center"
				/>
				<ElTableColumn 
					prop="status"
					label="状态"
					min-width="100"
					align="center"
				>
					<template #default="{ row }">
						<ElTag :type="getStatusType(row.status)">
							{{ getStatusText(row.status) }}
						</ElTag>
					</template>
				</ElTableColumn>
				<ElTableColumn 
					label="操作"
					min-width="120"
					align="center"
					fixed="right"
				>
					<template #default="{ row }">
						<ElButton 
							v-if="row.isUsed === false && row.orderType !== '7'" 
							type="danger" 
							size="small"
							@click="deleteOrder(row)"
						>
							退订
						</ElButton>
					</template>
				</ElTableColumn>
			</ElTable>
			
			<!-- 分页 -->
			<div class="pagination-container">
				<ElPagination
					:current-page="currentPage"
					:page-size="pageSize"
					:total="total"
					:page-sizes="[10, 20, 50, 100]"
					layout="total, sizes, prev, pager, next, jumper"
					@current-change="goPage"
					@size-change="handleSizeChange"
				/>
			</div>
		</div>

		<!-- 流量详情弹窗 -->
		<ElDialog 
			v-model="trafficModal" 
			:close-on-click-modal="false"
			width="90%"
			class="traffic-dialog"
		>
			<template #header>
				<span>流量详情</span>
			</template>
			
			<div class="traffic-search">
				<ElDatePicker 
					v-model="trafficDateRange"
					type="daterange" 
					format="YYYY-MM-DD" 
					value-format="YYYY-MM-DD"
					placeholder="请选择日期" 
					style="width: 200px"
					@change="handleTrafficDateChange"
					@clear="handleTrafficDateClear"
				/>
				<ElButton 
					v-if="checkPermission(['searchTraffic'])" 
					type="primary" 
					:loading="searchTrafficLoading"
					@click="searchTraffic" 
					:icon="Search"
					style="margin: 0 20px;"
				>
					搜索
				</ElButton>
				<ElButton 
					v-if="checkPermission(['exportTraffic'])" 
					type="success" 
					:loading="exportTrafficLoading"
					@click="exportTrafficFile"
					:icon="Download"
				>
					导出
				</ElButton>
			</div>
			
			<div class="traffic-table">
				<ElTable 
					:data="trafficData" 
					v-loading="trafficLoading"
					border
					style="width: 100%"
				>
					<ElTableColumn 
						prop="date"
						label="日期"
						min-width="120"
						align="center"
					/>
					<ElTableColumn 
						prop="traffic"
						label="流量"
						min-width="120"
						align="center"
					/>
					<ElTableColumn 
						prop="destination"
						label="目的地"
						min-width="150"
						align="center"
					/>
				</ElTable>
			</div>
			
			<div class="traffic-pagination">
				<ElPagination
					:current-page="currentPageTraffic"
					:page-size="10"
					:total="totalTraffic"
					layout="total, prev, pager, next, jumper"
					@current-change="goTrafficPage"
				/>
			</div>
			
			<template #footer>
				<ElButton @click="cancelTrafficModal" :icon="ArrowLeft">
					返回
				</ElButton>
			</template>
		</ElDialog>

		<!-- 退订确认弹窗 -->
		<ElDialog 
			v-model="showModal" 
			:close-on-click-modal="false"
			width="500px"
			class="unsubscribe-dialog"
		>
			<template #header>
				<span>退订确认</span>
			</template>
			
			<div class="unsubscribe-content">
				<p class="warning-text">确定要退订此订单吗？此操作不可撤销。</p>
			</div>

			<template #footer>
				<div class="unsubscribe-footer">
					<ElButton @click="cancelModal" :icon="ArrowLeft">
						返回
					</ElButton>
					<ElButton type="primary" :loading="beSureLoading" @click="beSure">
						确定
					</ElButton>
				</div>
			</template>
		</ElDialog>
	</ElCard>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElCard, ElForm, ElFormItem, ElInput, ElDatePicker, ElSelect, ElOption, ElButton, ElTable, ElTableColumn, ElPagination, ElDialog, ElTag } from 'element-plus'
import { Search, Download, ArrowLeft } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

defineOptions({
  name: 'ChannelOrderManagement'
})

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const downloading = ref(false)
const searchTrafficLoading = ref(false)
const exportTrafficLoading = ref(false)
const trafficLoading = ref(false)
const beSureLoading = ref(false)

const trafficModal = ref(false)
const showModal = ref(false)

const cooperationMode = ref('')
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const totalTraffic = ref(0)
const currentPageTraffic = ref(1)

const packageName = ref('')
const attributableChannel = ref('')
const iccid = ref('')
const timeSlot = ref('')

const tableData = ref([])
const trafficData = ref([])
const trafficDateRange = ref([])
const attributableChannelList = ref([])

const selectedRow = ref(null)

// 权限检查函数
const checkPermission = (permissions: string[]): boolean => {
  return true
}

// 状态类型映射
const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': 'success',  // 已激活
    '2': 'warning',  // 待激活
    '3': 'danger',   // 已停用
    '4': 'info'      // 其他
  }
  return statusMap[status] || 'info'
}

// 状态文本映射
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': '已激活',
    '2': '待激活',
    '3': '已停用',
    '4': '其他'
  }
  return statusMap[status] || '-'
}

// 方法
const handleDateChange = (dates: string[]) => {
  if (dates && dates.length === 2) {
    console.log('日期范围:', dates)
  }
}

const handleDateClear = () => {
  timeSlot.value = ''
}

const handleTrafficDateChange = (dates: string[]) => {
  console.log('流量日期范围:', dates)
}

const handleTrafficDateClear = () => {
  trafficDateRange.value = []
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

const search = () => {
  searchLoading.value = true
  currentPage.value = 1
  loadData()
}

const goPage = (page: number) => {
  currentPage.value = page
  loadData()
}

const showBill = () => {
  router.push('/channel/order/bill')
}

const exportFile = () => {
  downloading.value = true
  setTimeout(() => {
    downloading.value = false
    ElMessage.success('导出成功')
  }, 2000)
}

const getTrafficDetails = () => {
  trafficModal.value = true
}

const deleteOrder = (row: any) => {
  selectedRow.value = row
  showModal.value = true
}

const cancelModal = () => {
  showModal.value = false
}

const cancelTrafficModal = () => {
  trafficModal.value = false
}

const beSure = () => {
  beSureLoading.value = true
  setTimeout(() => {
    beSureLoading.value = false
    showModal.value = false
    ElMessage.success('退订成功')
    loadData()
  }, 2000)
}

const searchTraffic = () => {
  searchTrafficLoading.value = true
  setTimeout(() => {
    searchTrafficLoading.value = false
  }, 1000)
}

const exportTrafficFile = () => {
  exportTrafficLoading.value = true
  setTimeout(() => {
    exportTrafficLoading.value = false
    ElMessage.success('导出成功')
  }, 2000)
}

const goTrafficPage = (page: number) => {
  currentPageTraffic.value = page
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    console.log('加载订单数据')

    // 模拟数据
    tableData.value = [
      {
        orderId: 'ORD001',
        iccid: '1234567890',
        packageName: '测试套餐',
        orderTime: '2024-01-15 10:30:00',
        status: '1',
        isUsed: false,
        orderType: '1'
      }
    ]
    total.value = 1
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
    searchLoading.value = false
  }
}

// 组件挂载时初始化
onMounted(() => {
  cooperationMode.value = sessionStorage.getItem('cooperationMode') || ''
  loadData()
})
</script>

<style scoped>
.order-card {
  margin: 20px;
}

.order-container {
  width: 100%;
  margin-top: 20px;
}

.search-form {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 15px;
  margin-right: 20px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.order-table {
  margin-top: 40px;
}

.pagination-container {
  margin-top: 40px;
  text-align: right;
}

/* 弹窗样式 */
.traffic-search {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.traffic-table {
  margin-top: 20px;
}

.traffic-pagination {
  margin-top: 15px;
  text-align: right;
}

.unsubscribe-content {
  text-align: center;
  padding: 20px 0;
}

.warning-text {
  color: #e6a23c;
  font-size: 16px;
  margin-bottom: 20px;
}

.unsubscribe-footer {
  display: flex;
  justify-content: center;
  gap: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form :deep(.el-form-item) {
    margin-bottom: 15px;
    margin-right: 0;
  }

  .action-buttons {
    flex-direction: column;
  }

  .pagination-container,
  .traffic-pagination {
    text-align: center;
  }
}
</style>
