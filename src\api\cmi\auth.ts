/**
 * CMI 认证相关 API
 * 从原 cmi-web 项目的 src/api/user.js 迁移而来
 */

import request from '@/axios'

const servicePre = '/rcs/api/v1'

// 登录接口
export interface LoginData {
  userName: string
  password: string
  captcha?: string
  captchaId?: string
  smsCode?: string
}

export interface LoginResponse {
  token: string
  userId: string
  userName: string
  userInfo?: any
}

export const login = (data: LoginData) => {
  return request.post<IResponse<LoginResponse>>({
    url: servicePre + '/passport/login',
    data
  })
}

// 获取用户信息
export interface UserInfo {
  userId: string
  userName: string
  realName?: string
  email?: string
  phone?: string
  roles?: string[]
  permissions?: string[]
}

export const getUserInfo = (token?: string) => {
  return request.get<IResponse<UserInfo>>({
    url: 'get_info',
    params: token ? { token } : undefined
  })
}

// 登出接口
export const logout = (token?: string) => {
  return request.post<IResponse<any>>({
    url: 'logout'
  })
}

// 获取验证码
export interface CaptchaResponse {
  captchaId: string
  captchaImage: string
}

export const getVerCode = () => {
  return request.get<IResponse<CaptchaResponse>>({
    url: servicePre + '/passport/captcha'
  })
}

// 获取开关配置
export const getConfigure = (data: any) => {
  return request.post<IResponse<any>>({
    url: '/auth/code/getIsOpen',
    data
  })
}

// 获取用户是否需要验证码开关配置
export const getNeedCode = (data: any) => {
  return request.get<IResponse<any>>({
    url: '/sys/api/v1/user/getUserInformation',
    params: data
  })
}

// 下发短信验证码
export const sendMsgForReg = (data: any) => {
  return request.get<IResponse<any>>({
    url: '/sys/api/v1/user/getVerifyCode',
    params: data
  })
}

// 忘记密码下发验证码
export const sendVerifyCode = (data: any) => {
  return request.put<IResponse<any>>({
    url: '/sys/api/v1/user/userForgetpasswd/sendVerifyCode',
    params: data
  })
}

// 获取菜单权限数据
export interface MenuData {
  id: string
  name: string
  path: string
  component?: string
  icon?: string
  children?: MenuData[]
  meta?: {
    title: string
    icon?: string
    roles?: string[]
  }
}

export const getMenuList = () => {
  return request.get<IResponse<MenuData[]>>({
    url: '/sys/api/v1/menu/getUserMenu'
  })
}

// 获取用户权限列表
export const getUserPermissions = () => {
  return request.get<IResponse<string[]>>({
    url: '/sys/api/v1/user/getUserPermissions'
  })
}
