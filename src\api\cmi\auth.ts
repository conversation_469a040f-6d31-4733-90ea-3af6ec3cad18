/**
 * CMI 认证相关 API
 * 从原 cmi-web 项目的 src/api/user.js 和 src/api/system/login.js 迁移而来
 */

import request from '@/axios'

const servicePre = '/rcs/api/v1'

// 登录接口
export interface LoginData {
  username: string  // 原项目使用 username 而不是 userName
  password: string
  captcha?: string
  captchaId?: string
  smsCode?: string
  type?: number  // 登录类型，原项目中使用
}

export interface LoginResponse {
  oauth2AccessToken: {
    access_token: string
  }
  userDetails: {
    id: string
    username: string
    roleId: string
    rePassword: number  // 密码是否需要更新
    pagePrivileges: Array<{
      access: string
      url: string
      buttons: string[]
    }>
    needVerifyCode?: string  // 是否需要验证码
  }
}

// 普通登录接口
export const login = (data: LoginData) => {
  return request.post<IResponse<LoginResponse>>({
    url: '/auth/login',  // 原项目使用的是 /auth/login
    data
  })
}

// SSO 单点登录接口
export interface SSOLoginData {
  corpId?: string
  [key: string]: any
}

export const ssoLoginGDS = (data: SSOLoginData) => {
  return request.post<IResponse<LoginResponse>>({
    url: '/aep/sso/ssoLoginGDS',
    data
  })
}

// 获取用户信息
export interface UserInfo {
  userId: string
  userName: string
  realName?: string
  email?: string
  phone?: string
  roles?: string[]
  permissions?: string[]
}

export const getUserInfo = (token?: string) => {
  return request.get<IResponse<UserInfo>>({
    url: 'get_info',
    params: token ? { token } : undefined
  })
}

// 登出接口
export const logout = () => {
  return request.post<IResponse<any>>({
    url: 'logout'
  })
}

// 清除 Redis 登出接口
export const logoutClearRedis = (userName: string) => {
  return request.delete<IResponse<any>>({
    url: `/auth/logout?userName=${userName}`
  })
}

// 获取验证码
export interface CaptchaResponse {
  captchaId: string
  captchaImage: string
}

export const getVerCode = () => {
  return request.get<IResponse<CaptchaResponse>>({
    url: servicePre + '/passport/captcha'
  })
}

// 获取开关配置
export const getConfigure = (data: any) => {
  return request.post<IResponse<any>>({
    url: '/auth/code/getIsOpen',
    data
  })
}

// 获取 SSO 登录开关配置
export const getSSOSwitchConfig = (data?: any) => {
  return request.get<IResponse<any>>({
    url: '/aep/sso/getSSOIsOpen',
    params: data
  })
}

// 获取用户是否需要验证码开关配置
export const getNeedCode = (data: any) => {
  return request.get<IResponse<any>>({
    url: '/sys/api/v1/user/getUserInformation',
    params: data
  })
}

// 下发短信验证码
export const sendMsgForReg = (data: any) => {
  return request.get<IResponse<any>>({
    url: '/sys/api/v1/user/getVerifyCode',
    params: data
  })
}

// 忘记密码下发验证码
export const sendVerifyCode = (data: any) => {
  return request.put<IResponse<any>>({
    url: '/sys/api/v1/user/userForgetpasswd/sendVerifyCode',
    params: data
  })
}

// 获取菜单权限数据
export interface MenuData {
  id: string
  name: string
  path: string
  component?: string
  icon?: string
  children?: MenuData[]
  meta?: {
    title: string
    icon?: string
    roles?: string[]
  }
}

export const getMenuList = () => {
  return request.get<IResponse<MenuData[]>>({
    url: '/sys/api/v1/menu/getUserMenu'
  })
}

// 获取用户权限列表
export const getUserPermissions = () => {
  return request.get<IResponse<string[]>>({
    url: '/sys/api/v1/user/getUserPermissions'
  })
}

// 查询企业ID（用于超管判断）
export const searchcorpid = (data: { userName: string }) => {
  return request.get<IResponse<any>>({
    url: '/sys/api/v1/user/searchcorpid',
    params: data
  })
}
