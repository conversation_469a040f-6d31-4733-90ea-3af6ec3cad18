/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { ApplicationInfo } from './applicationInfo';
export declare class SaleToAcquirerData {
    'applicationInfo'?: ApplicationInfo;
    'shopperEmail'?: string;
    'shopperReference'?: string;
    'recurringContract'?: string;
    'shopperStatement'?: string;
    'recurringDetailName'?: string;
    'store'?: string;
    'merchantAccount'?: string;
    'currency'?: string;
    'tenderOption'?: string;
    'additionalData'?: object;
    'metadata'?: {
        [key: string]: string;
    };
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
