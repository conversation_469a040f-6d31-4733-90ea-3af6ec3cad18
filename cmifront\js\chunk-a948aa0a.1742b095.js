(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a948aa0a"],{"129f":function(t,e,r){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},"1c31":function(t,e,r){"use strict";r.d(e,"t",(function(){return o})),r.d(e,"s",(function(){return i})),r.d(e,"a",(function(){return s})),r.d(e,"b",(function(){return u})),r.d(e,"c",(function(){return c})),r.d(e,"d",(function(){return d})),r.d(e,"e",(function(){return l})),r.d(e,"f",(function(){return m})),r.d(e,"g",(function(){return f})),r.d(e,"h",(function(){return p})),r.d(e,"i",(function(){return h})),r.d(e,"j",(function(){return g})),r.d(e,"k",(function(){return v})),r.d(e,"l",(function(){return y})),r.d(e,"x",(function(){return b})),r.d(e,"m",(function(){return k})),r.d(e,"n",(function(){return x})),r.d(e,"o",(function(){return w})),r.d(e,"p",(function(){return D})),r.d(e,"q",(function(){return q})),r.d(e,"v",(function(){return I})),r.d(e,"r",(function(){return P})),r.d(e,"w",(function(){return S})),r.d(e,"u",(function(){return T}));var a=r("66df"),n="/stat",o=function(t){return a["a"].request({url:"/cms/api/v1/packageCard/countReuseExport",data:t,responseType:"blob",method:"post"})},i=function(t){return a["a"].request({url:"/cms/api/v1/packageCard/countReuse",data:t,method:"post"})},s=function(t){return a["a"].request({url:n+"/activereport/detailDownload",params:t,responseType:"blob",method:"get"})},u=function(t){return a["a"].request({url:n+"/activereport/pageList",data:t,method:"post"})},c=function(t){return a["a"].request({url:n+"/cardReport",params:t,method:"get"})},d=function(t){return a["a"].request({url:n+"/cardReport/export",params:t,responseType:"blob",method:"get"})},l=function(t){return a["a"].request({url:n+"/offline/export",params:t,responseType:"blob",method:"get"})},m=function(t){return a["a"].request({url:n+"/offline/import",data:t,method:"post"})},f=function(t){return a["a"].request({url:n+"/offline/pageList",data:t,method:"post"})},p=function(t){return a["a"].request({url:n+"/operatorsettle/detailDownload",params:t,responseType:"blob",method:"get"})},h=function(t){return a["a"].request({url:n+"/operatorsettle/pageList",data:t,method:"post"})},g=function(t){return a["a"].request({url:n+"/postpaidsettle/detailDownload",params:t,responseType:"blob",method:"get"})},v=function(t){return a["a"].request({url:n+"/postpaidsettle/pageList",data:t,method:"post"})},y=function(t){return a["a"].request({url:n+"/rate",params:t,method:"get"})},b=function(t){return a["a"].request({url:n+"/rate",data:t,method:"post"})},k=function(t){return a["a"].request({url:n+"/rate/export",params:t,responseType:"blob",method:"get"})},x=function(t){return a["a"].request({url:n+"/report/package/analysis/export",params:t,responseType:"blob",method:"get"})},w=function(t){return a["a"].request({url:n+"/report/package/analysis/search",data:t,method:"post"})},D=function(t){return a["a"].request({url:n+"/terminalsettle/detailDownload",params:t,responseType:"blob",method:"get"})},q=function(t){return a["a"].request({url:n+"/terminalsettle/pageList",data:t,method:"post"})},I=function(t){return a["a"].request({url:"/charging/cost/supplierCostQuery",data:t,method:"post"})},P=function(t){return a["a"].request({url:"/charging/cost/supplierCostExport",data:t,responseType:"blob",method:"post"})},S=function(t){return a["a"].request({url:"/cms/esim/getEsimcardStats",params:t,method:"get"})},T=function(t){return a["a"].request({url:"/cms/esim/exportEsimcardStats",params:t,method:"get"})}},"841c":function(t,e,r){"use strict";var a=r("c65b"),n=r("d784"),o=r("825a"),i=r("7234"),s=r("1d80"),u=r("129f"),c=r("577e"),d=r("dc4a"),l=r("14c3");n("search",(function(t,e,r){return[function(e){var r=s(this),n=i(e)?void 0:d(e,t);return n?a(n,e,r):new RegExp(e)[t](c(r))},function(t){var a=o(this),n=c(t),i=r(e,a,n);if(i.done)return i.value;var s=a.lastIndex;u(s,0)||(a.lastIndex=0);var d=l(a,n);return u(a.lastIndex,s)||(a.lastIndex=s),null===d?-1:d.index}]}))},a225:function(t,e,r){"use strict";r.r(e);r("ac1f"),r("841c");var a=function(){var t=this,e=t._self._c;return e("Card",[e("div",{staticStyle:{display:"flex",width:"100%"}},[e("Form",{ref:"form",attrs:{"label-width":0,model:t.form,rules:t.rule,inline:""}},[e("FormItem",{attrs:{prop:"dimension"}},[e("Select",{staticStyle:{width:"200px","text-align":"left",margin:"0 10px"},attrs:{clearable:!0,placeholder:"请选择统计维度"},on:{"on-change":function(e){t.date="",t.resetField(["startDate","endDate"])}},model:{value:t.form.dimension,callback:function(e){t.$set(t.form,"dimension",e)},expression:"form.dimension"}},t._l(t.cycleList,(function(r,a){return e("Option",{key:a,attrs:{value:r.id}},[t._v(t._s(r.value))])})),1)],1),e("FormItem",{attrs:{prop:"cardform"}},[e("Select",{staticStyle:{width:"200px","text-align":"left",margin:"0 10px"},attrs:{clearable:!0,placeholder:"请选择卡类别"},model:{value:t.form.cardform,callback:function(e){t.$set(t.form,"cardform",e)},expression:"form.cardform"}},t._l(t.typeList,(function(r,a){return e("Option",{key:a,attrs:{value:r.id}},[t._v(t._s(r.value))])})),1)],1),"2"!=t.form.dimension?e("FormItem",{attrs:{prop:"startDate"}},[e("DatePicker",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{format:"yyyyMMdd",editable:!1,type:"daterange",placeholder:"选择时间段",clearable:""},on:{"on-change":t.checkDatePicker},model:{value:t.date,callback:function(e){t.date=e},expression:"date"}})],1):t._e(),"2"==t.form.dimension?e("FormItem",{attrs:{prop:"startDate"}},[e("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择开始月份",editable:!1},on:{"on-change":function(e){return t.checkDatePicker(e,1)}}})],1):t._e(),"2"==t.form.dimension?e("FormItem",{attrs:{prop:"endDate"}},[e("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择结束月份",editable:!1},on:{"on-change":function(e){return t.checkDatePicker(e,2)}}})],1):t._e(),e("FormItem",[e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",size:"large"},on:{click:function(e){return t.search()}}},[t._v("搜索")]),t._v("  \n        "),e("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],staticStyle:{"margin-left":"20px"},attrs:{type:"success",icon:"ios-cloud-download-outline",size:"large"},on:{click:function(e){return t.exportTable()}}},[t._v("导出")])],1)],1)],1),e("Table",{staticStyle:{width:"100%","margin-top":"50px"},attrs:{columns:t.columns12,data:t.data,loading:t.loading}}),e("div",{staticStyle:{"margin-left":"38%","margin-top":"100px","margin-bottom":"160px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),t.data1.length?e("Table",{staticStyle:{width:"100%","margin-top":"50px"},attrs:{columns:t.columns12,data:t.data1,loading:t.loading}}):t._e()],1)},n=[],o=r("5530"),i=(r("caad"),r("14d9"),r("d3b7"),r("2532"),r("3ca3"),r("159b"),r("ddb0"),r("2b3d"),r("bf19"),r("9861"),r("88a7"),r("271a"),r("5494"),r("1c31")),s=r("b35e"),u={mixins:[s["a"]],data:function(){return{date:"",loading:!1,form:{cardform:"",dimension:"",endDate:"",startDate:""},rule:{startDate:[{required:!0,message:"请选择时间"}],endDate:[{required:!0,message:"请选择时间"}],dimension:[{required:!0,message:"请选择维度"}]},total:0,currentPage:1,cycleList:[{id:1,value:"日"},{id:2,value:"月"}],typeList:[{id:1,value:"普通卡（实体卡）"},{id:2,value:"Esim卡"},{id:3,value:"贴片卡"},{id:4,value:"IMSI号"}],columns12:[{title:"卡类型",key:"cardform",align:"center",minWidth:80,render:function(t,e){var r={1:"普通卡（实体卡）",2:"Esim卡",3:"贴片卡",4:"IMSI号"};return t("span",e.row.cardform?r[e.row.cardform]:""===e.row.cardform?row.cardform:"合计")}},{title:"时间",key:"statTime",align:"center",minWidth:80},{title:"本期激活数",key:"totalActiveNum",align:"center",minWidth:80},{title:"H-IMSI激活数",key:"himisactivenum",align:"center",minWidth:120},{title:"V-IMSI激活已切换数",key:"vimisactivechagenum",align:"center",minWidth:120},{title:"V-IMSI激活未切换数",key:"vimisactiveunchagenum",align:"center",minWidth:120},{title:"加油包激活数",key:"totalActiveNumRefuel",align:"center",minWidth:80},{title:"港币收入",key:"hkdIncome",align:"center",minWidth:80},{title:"人民币收入",key:"cnyincome",align:"center",minWidth:80},{title:"美元收入 ",key:"usdIncome",align:"center",minWidth:80},{title:"总收入(单位:港币)",key:"totalincome",align:"center",minWidth:80}],data:[],data1:[],rules:{}}},created:function(){this.rule.startDate.push({validator:this.validateDate,trigger:"change"}),this.rule.endDate.push({validator:this.validateDate,trigger:"change"})},mounted:function(){console.log()},methods:{resetField:function(t){this.$refs["form"].fields.forEach((function(e){t.includes(e.prop)&&e.resetField()}))},checkDatePicker:function(t,e){Array.isArray(t)?(this.form.startDate=t[0],this.form.endDate=t[1]):1===e?this.form.startDate=t:this.form.endDate=t},goPageFirst:function(t){var e=this;0===t&&(this.currentPage=1);var r=this,a=10,n=this.currentPage;this.$refs["form"].validate((function(s){s&&(e.loading=!0,Object(i["b"])(Object(o["a"])({pageNum:n,pageSize:a},e.form)).then((function(a){"0000"==a.code&&(r.loading=!1,e.page=t,e.total=a.data.total,e.data=a.data.record,e.data1=a.data.records1[0]?a.data.records1:[])})).catch((function(t){console.error(t)})).finally((function(){e.loading=!1})))}))},goPage:function(t){this.goPageFirst(t)},search:function(){this.goPageFirst(0)},exportTable:function(){var t=this;this.$refs["form"].validate((function(e){e&&Object(i["a"])(Object(o["a"])({},t.form)).then((function(t){var e=t.data,r="套餐激活数.csv";if("download"in document.createElement("a")){var a=document.createElement("a"),n=URL.createObjectURL(e);a.download=r,a.href=n,a.click(),URL.revokeObjectURL(n)}else navigator.msSaveBlob(e,r)})).catch((function(){return t.downloading=!1}))}))},details:function(t){this.$router.push({path:"/channel/detailsList"})}}},c=u,d=r("2877"),l=Object(d["a"])(c,a,n,!1,null,null,null);e["default"]=l.exports},b35e:function(t,e,r){"use strict";r("d9e2");e["a"]={methods:{validateDate:function(t,e,r){var a=this.form.endDate||this.form.endTime,n=this.form.startDate||this.form.startTime;a&&n?"startDate"===t.field||"startTime"===t.field?this.$time(e,">",a)?r(new Error("开始时间不能大于结束时间")):r():this.$time(e,"<",a)?r(new Error("结束时间不能小于开始时间")):r():r()}}}}}]);