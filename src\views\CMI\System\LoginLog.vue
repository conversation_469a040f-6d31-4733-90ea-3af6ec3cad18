<template>
  <div class="login-log">
    <ContentWrap>
      <div class="page-header">
        <h2>登录日志</h2>
        <p>查看系统用户登录记录和安全审计信息</p>
      </div>

      <!-- 搜索区域 -->
      <div class="search-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="用户名">
            <el-input
              v-model="searchForm.username"
              placeholder="请输入用户名"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="登录状态">
            <el-select
              v-model="searchForm.loginStatus"
              placeholder="请选择登录状态"
              clearable
              style="width: 150px"
            >
              <el-option label="成功" value="success" />
              <el-option label="失败" value="failed" />
            </el-select>
          </el-form-item>
          <el-form-item label="登录时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 350px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <Icon icon="vi-ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button @click="handleReset">
              <Icon icon="vi-ep:refresh" class="mr-5px" />
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <el-button type="primary" @click="handleExport">
          <Icon icon="vi-ep:download" class="mr-5px" />
          导出日志
        </el-button>
        <el-button type="danger" @click="handleClearLog">
          <Icon icon="vi-ep:delete" class="mr-5px" />
          清理日志
        </el-button>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <el-table v-loading="loading" :data="tableData" stripe border>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="username" label="用户名" min-width="120" />
          <el-table-column prop="realName" label="真实姓名" min-width="120" />
          <el-table-column prop="loginTime" label="登录时间" width="160" />
          <el-table-column prop="loginIp" label="登录IP" width="140" />
          <el-table-column prop="loginLocation" label="登录地点" min-width="150" />
          <el-table-column prop="userAgent" label="浏览器" min-width="200" show-overflow-tooltip />
          <el-table-column prop="loginStatus" label="登录状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.loginStatus === 'success' ? 'success' : 'danger'">
                {{ row.loginStatus === 'success' ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="failReason" label="失败原因" min-width="150" show-overflow-tooltip />
          <el-table-column prop="sessionDuration" label="会话时长" width="120" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleViewDetail(row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </ContentWrap>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="登录详情" width="600px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="用户名">{{ detailData.username }}</el-descriptions-item>
        <el-descriptions-item label="真实姓名">{{ detailData.realName }}</el-descriptions-item>
        <el-descriptions-item label="登录时间">{{ detailData.loginTime }}</el-descriptions-item>
        <el-descriptions-item label="登录IP">{{ detailData.loginIp }}</el-descriptions-item>
        <el-descriptions-item label="登录地点" :span="2">{{ detailData.loginLocation }}</el-descriptions-item>
        <el-descriptions-item label="浏览器" :span="2">{{ detailData.userAgent }}</el-descriptions-item>
        <el-descriptions-item label="操作系统">{{ detailData.operatingSystem }}</el-descriptions-item>
        <el-descriptions-item label="登录状态">
          <el-tag :type="detailData.loginStatus === 'success' ? 'success' : 'danger'">
            {{ detailData.loginStatus === 'success' ? '成功' : '失败' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="失败原因" :span="2" v-if="detailData.loginStatus === 'failed'">
          {{ detailData.failReason }}
        </el-descriptions-item>
        <el-descriptions-item label="会话时长">{{ detailData.sessionDuration }}</el-descriptions-item>
        <el-descriptions-item label="登出时间">{{ detailData.logoutTime || '未登出' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

// 搜索表单
const searchForm = reactive({
  username: '',
  loginStatus: '',
  dateRange: []
})

// 表格数据
const loading = ref(false)
const tableData = ref([
  {
    id: 1,
    username: 'admin',
    realName: '系统管理员',
    loginTime: '2024-01-15 09:30:00',
    loginIp: '*************',
    loginLocation: '北京市朝阳区',
    userAgent: 'Chrome 120.0.0.0',
    operatingSystem: 'Windows 10',
    loginStatus: 'success',
    failReason: '',
    sessionDuration: '2小时30分钟',
    logoutTime: '2024-01-15 12:00:00'
  },
  {
    id: 2,
    username: 'manager',
    realName: '部门经理',
    loginTime: '2024-01-15 08:45:00',
    loginIp: '*************',
    loginLocation: '上海市浦东新区',
    userAgent: 'Firefox *********',
    operatingSystem: 'macOS 14.0',
    loginStatus: 'failed',
    failReason: '密码错误',
    sessionDuration: '',
    logoutTime: ''
  }
])

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 详情对话框
const detailDialogVisible = ref(false)
const detailData = ref<any>({})

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    loginStatus: '',
    dateRange: []
  })
  handleSearch()
}

// 导出日志
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 清理日志
const handleClearLog = async () => {
  try {
    await ElMessageBox.confirm('确定要清理历史登录日志吗？此操作不可恢复！', '警告', {
      type: 'warning'
    })
    ElMessage.success('日志清理成功')
    loadTableData()
  } catch (error) {
    // 用户取消操作
  }
}

// 查看详情
const handleViewDetail = (row: any) => {
  detailData.value = { ...row }
  detailDialogVisible.value = true
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  loadTableData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadTableData()
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    // 调用查询接口
    await new Promise(resolve => setTimeout(resolve, 500)) // 模拟接口调用
    pagination.total = tableData.value.length
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadTableData()
})
</script>

<style scoped>
.login-log {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.page-header p {
  color: #606266;
  margin: 0;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-section {
  margin-bottom: 20px;
}

.table-section {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}
</style>
