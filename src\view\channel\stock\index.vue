<template>
	<!-- 库存管理 -->
	<ElCard class="stock-card">
		<div class="stock-container">
			<!-- 搜索表单 -->
			<div class="search-form">
				<ElForm inline>
					<ElFormItem :label="$t('stock.order_number')" label-width="100px">
						<ElInput
							v-model="taskName"
							:placeholder="$t('stock.input_number')"
							clearable
							style="width: 200px"
						/>
					</ElFormItem>
					<ElFormItem :label="$t('order.timeslot')" label-width="80px">
						<ElDatePicker
							v-model="timeSlot"
							type="daterange"
							format="YYYY-MM-DD"
							value-format="YYYY-MM-DD"
							:placeholder="$t('stock.chose_time')"
							style="width: 200px; margin-right: 10px;"
							@change="handleDateChange"
							@clear="handleDateClear"
						/>
					</ElFormItem>
					<ElFormItem>
						<ElButton
							v-if="checkPermission(['search'])"
							type="primary"
							:loading="searchLoading"
							@click="search"
							:icon="Search"
						>
							{{ $t('stock.search') }}
						</ElButton>
						<ElButton
							v-if="checkPermission(['showiccid'])"
							type="warning"
							style="margin-left: 20px;"
							@click="showIccid"
						>
							{{ $t('stock.showiccid') }}
						</ElButton>
					</ElFormItem>
				</ElForm>
			</div>

			<!-- 表格 -->
			<ElTable
				:data="tableData"
				style="width: 100%; margin-top: 40px;"
				v-loading="loading"
				border
				class="stock-table"
			>
				<ElTableColumn prop="taskName" :label="$t('stock.taskName')" min-width="150">
					<template #default="{ row }">
						<strong>{{ row.taskName }}</strong>
					</template>
				</ElTableColumn>
				<ElTableColumn prop="taskNum" :label="$t('stock.taskNum')" min-width="150">
					<template #default="{ row }">
						<strong>{{ row.taskNum }}</strong>
					</template>
				</ElTableColumn>
				<ElTableColumn prop="totalCount" :label="$t('stock.totalCount')" min-width="100" align="center">
					<template #default="{ row }">
						{{ row.totalCount || 0 }}
					</template>
				</ElTableColumn>
				<ElTableColumn prop="successCount" :label="$t('stock.successCount')" min-width="100" align="center">
					<template #default="{ row }">
						{{ row.successCount || 0 }}
					</template>
				</ElTableColumn>
				<ElTableColumn prop="failCount" :label="$t('stock.failCount')" min-width="100" align="center">
					<template #default="{ row }">
						{{ row.failCount || 0 }}
					</template>
				</ElTableColumn>
				<ElTableColumn prop="status" :label="$t('stock.status')" min-width="100" align="center">
					<template #default="{ row }">
						<ElTag :type="getStatusType(row.status)">
							{{ getStatusText(row.status) }}
						</ElTag>
					</template>
				</ElTableColumn>
				<ElTableColumn prop="createTime" :label="$t('stock.createTime')" min-width="160">
					<template #default="{ row }">
						<strong>{{ row.createTime }}</strong>
					</template>
				</ElTableColumn>
				<ElTableColumn :label="$t('stock.action')" min-width="120" align="center" fixed="right">
					<template #default="{ row }">
						<ElButton
							v-if="checkPermission(['view'])"
							type="warning"
							size="small"
							@click="details(row)"
						>
							{{ $t('stock.details') }}
						</ElButton>
					</template>
				</ElTableColumn>
			</ElTable>

			<!-- 分页 -->
			<div class="pagination-container">
				<ElPagination
					:current-page="currentPage"
					:page-size="pageSize"
					:total="total"
					:page-sizes="[10, 20, 50, 100]"
					layout="total, sizes, prev, pager, next, jumper"
					@current-change="goPage"
					@size-change="handleSizeChange"
				/>
			</div>
		</div>
	</ElCard>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElCard, ElForm, ElFormItem, ElInput, ElDatePicker, ElButton, ElTable, ElTableColumn, ElTag, ElPagination } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { stockList, searchcorpid } from '@/api/channel.js'

defineOptions({
  name: 'ChannelStockManagement'
})

const router = useRouter()
const { t } = useI18n()

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const taskName = ref('')
const timeSlot = ref('')
const tableData = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const corpId = ref('')
const searchBeginTime = ref('')
const searchEndTime = ref('')

// 权限检查函数
const checkPermission = (permissions: string[]): boolean => {
  // TODO: 实现真实的权限检查逻辑
  return true
}

// 状态类型映射
const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': 'warning',  // 处理中
    '2': 'success',  // 已完成
    '3': 'danger'    // 失败
  }
  return statusMap[status] || 'info'
}

// 状态文本映射
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': t('stock.processing'),
    '2': t('stock.completed'),
    '3': t('stock.failed')
  }
  return statusMap[status] || t('stock.unknown')
}

// 日期变化处理
const handleDateChange = (dates: string[]) => {
  if (dates && dates.length === 2) {
    searchBeginTime.value = dates[0]
    searchEndTime.value = dates[1]
  }
}

// 日期清空处理
const handleDateClear = () => {
  searchBeginTime.value = ''
  searchEndTime.value = ''
}

// 页面大小变化处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadData(1)
}

// 获取企业ID并加载数据
const loadData = async (page: number) => {
  try {
    loading.value = true
    tableData.value = []

    // 获取企业ID
    const corpRes = await searchcorpid({
      userName: sessionStorage.getItem('userName') || ''
    })

    if (corpRes.code === '0000') {
      corpId.value = corpRes.data

      const pageNumber = page
      const startDate = searchBeginTime.value ? searchBeginTime.value + ' 00:00:00' : null
      const endDate = searchEndTime.value ? searchEndTime.value + ' 23:59:59' : null
      const taskNameParam = taskName.value || null

      const res = await stockList({
        pageNumber,
        pageSize: pageSize.value,
        startDate,
        endDate,
        corpId: corpId.value,
        taskName: taskNameParam
      })

      if (res.code === '0000') {
        currentPage.value = page
        total.value = res.data.total
        tableData.value = res.data.record
      }
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
    searchLoading.value = false
  }
}

// 搜索处理
const search = () => {
  searchLoading.value = true
  currentPage.value = 1
  loadData(1)
}

// 分页处理
const goPage = (page: number) => {
  loadData(page)
}

// 查看详情
const details = (row: any) => {
  router.push({
    path: '/cardList',
    query: {
      paymentChannel: encodeURIComponent(JSON.stringify(row))
    }
  })
}

// 显示ICCID
const showIccid = () => {
  router.push({
    path: '/showiccid',
    query: {
      corpId: encodeURIComponent(corpId.value)
    }
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadData(1)
})
</script>

<style scoped>
.stock-card {
  margin: 20px;
}

.stock-container {
  width: 100%;
  margin-top: 20px;
}

.search-form {
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 20px;
}

.stock-table {
  margin-top: 40px;
}

.pagination-container {
  margin-top: 40px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-form :deep(.el-form-item) {
    margin-bottom: 15px;
    margin-right: 0;
  }

  .pagination-container {
    text-align: center;
  }
}
</style>
