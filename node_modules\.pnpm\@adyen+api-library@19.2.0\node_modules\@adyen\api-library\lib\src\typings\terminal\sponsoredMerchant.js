"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SponsoredMerchant = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
class SponsoredMerchant {
    static getAttributeTypeMap() {
        return SponsoredMerchant.attributeTypeMap;
    }
}
exports.SponsoredMerchant = SponsoredMerchant;
SponsoredMerchant.discriminator = undefined;
SponsoredMerchant.attributeTypeMap = [
    {
        "name": "MerchantAddress",
        "baseName": "MerchantAddress",
        "type": "string"
    },
    {
        "name": "MerchantCategoryCode",
        "baseName": "MerchantCategoryCode",
        "type": "string"
    },
    {
        "name": "MerchantCountry",
        "baseName": "MerchantCountry",
        "type": "string"
    },
    {
        "name": "MerchantName",
        "baseName": "MerchantName",
        "type": "string"
    },
    {
        "name": "RegistrationID",
        "baseName": "RegistrationID",
        "type": "string"
    }
];
//# sourceMappingURL=sponsoredMerchant.js.map