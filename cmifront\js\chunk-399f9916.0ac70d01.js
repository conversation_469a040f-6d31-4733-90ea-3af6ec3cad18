(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-399f9916"],{"00b4":function(t,e,i){"use strict";i("ac1f");var a=i("23e7"),n=i("c65b"),r=i("1626"),o=i("825a"),l=i("577e"),s=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),c=/./.test;a({target:"RegExp",proto:!0,forced:!s},{test:function(t){var e=o(this),i=l(t),a=e.exec;if(!r(a))return n(c,e,i);var s=n(a,e,i);return null!==s&&(o(s),!0)}})},"129f":function(t,e,i){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},3874:function(t,e,i){"use strict";i("be6b")},"7b9b":function(t,e,i){"use strict";i.d(e,"i",(function(){return r})),i.d(e,"b",(function(){return o})),i.d(e,"a",(function(){return l})),i.d(e,"c",(function(){return s})),i.d(e,"h",(function(){return c})),i.d(e,"f",(function(){return u})),i.d(e,"g",(function(){return d})),i.d(e,"d",(function(){return m})),i.d(e,"e",(function(){return p}));i("99af");var a=i("66df"),n="/cms",r=function(t){return a["a"].request({url:n+"/nameAuth/human",params:t,method:"get"})},o=function(t){return a["a"].request({url:n+"/humanVerify/verify",params:t,method:"get"})},l=function(t){return a["a"].request({url:n+"/nameAuth/deleteHuman",params:t,method:"delete"})},s=function(t){return a["a"].request({url:n+"/nameAuth/cardInfo",params:t,method:"get"})},c=function(t){return a["a"].request({url:"sys/api/v3/realNameAuth/getEmails",params:t,method:"get"})},u=function(t,e,i){return a["a"].request({url:n+"/humanVerify/exportInfo/".concat(e,"/").concat(i),data:t,method:"post",contentType:"multipart/form-data"})},d=function(t){return a["a"].request({url:"sys/api/v3/realNameAuth/getImage",params:t,method:"get",responseType:"blob"})},m=function(t){return a["a"].request({url:n+"/humanVerify/authenticate/update",params:t,method:"post"})},p=function(t){return a["a"].request({url:n+"/nameAuth/cancelAuthentication",params:t,method:"post"})}},"841c":function(t,e,i){"use strict";var a=i("c65b"),n=i("d784"),r=i("825a"),o=i("7234"),l=i("1d80"),s=i("129f"),c=i("577e"),u=i("dc4a"),d=i("14c3");n("search",(function(t,e,i){return[function(e){var i=l(this),n=o(e)?void 0:u(e,t);return n?a(n,e,i):new RegExp(e)[t](c(i))},function(t){var a=r(this),n=c(t),o=i(e,a,n);if(o.done)return o.value;var l=a.lastIndex;s(l,0)||(a.lastIndex=0);var u=d(a,n);return s(a.lastIndex,l)||(a.lastIndex=l),null===u?-1:u.index}]}))},"93bd":function(t,e,i){"use strict";i.r(e);i("b0c0"),i("ac1f"),i("841c"),i("498a");var a=function(){var t=this,e=t._self._c;return e("Card",{attrs:{id:"card"}},[e("Form",{ref:"form",staticStyle:{width:"90%"},attrs:{"label-width":70,model:t.form,rules:t.rule,inline:""}},[e("Row",{attrs:{gutter:10}},[e("Col",{attrs:{span:"7"}},[e("FormItem",{attrs:{label:"ICCID"}},[e("Input",{staticClass:"inputSty",attrs:{placeholder:"请输入ICCID",clearable:!0},model:{value:t.form.iccid,callback:function(e){t.$set(t.form,"iccid","string"===typeof e?e.trim():e)},expression:"form.iccid"}})],1)],1),e("Col",{attrs:{span:"7"}},[e("FormItem",{attrs:{label:"MSISDN"}},[e("Input",{staticClass:"inputSty",attrs:{placeholder:"请输入MSISDN",clearable:!0},model:{value:t.form.msisdn,callback:function(e){t.$set(t.form,"msisdn","string"===typeof e?e.trim():e)},expression:"form.msisdn"}})],1)],1),e("Col",{attrs:{span:"10"}},[e("FormItem",{attrs:{label:"IMSI"}},[e("Input",{staticClass:"inputSty",attrs:{placeholder:"请输入IMSI",clearable:!0},model:{value:t.form.imsi,callback:function(e){t.$set(t.form,"imsi","string"===typeof e?e.trim():e)},expression:"form.imsi"}})],1)],1),e("Col",{attrs:{span:"7"}},[e("FormItem",{attrs:{label:"认证状态"}},[e("Select",{staticClass:"inputSty",attrs:{placeholder:"请选择认证状态",clearable:!0},model:{value:t.form.authStatus,callback:function(e){t.$set(t.form,"authStatus",e)},expression:"form.authStatus"}},[e("Option",{attrs:{value:1}},[t._v("认证通过")]),e("Option",{attrs:{value:2}},[t._v("非认证通过")])],1)],1)],1),e("Col",{attrs:{span:"7"}},[e("FormItem",{attrs:{label:"证件ID"}},[e("Input",{staticClass:"inputSty",attrs:{placeholder:"请输入证件ID",clearable:!0},model:{value:t.form.certificatesId,callback:function(e){t.$set(t.form,"certificatesId","string"===typeof e?e.trim():e)},expression:"form.certificatesId"}})],1)],1),e("Col",{attrs:{span:"10"}},[e("FormItem",[e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.search()}}},[t._v("搜索")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"success",loading:t.downloading,icon:"ios-download"},on:{click:function(e){return t.downloadFile()}}},[t._v("导出")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"apply",expression:"'apply'"}],attrs:{disabled:"1"==t.isSuperManger,type:"warning",icon:"md-unlock"},on:{click:t.viewPermission}},[t._v("申请查看权限")])],1)],1)],1)],1),e("Table",{attrs:{columns:t.columns,data:t.tableData,ellipsis:!0,loading:t.loading,"type:html":""},scopedSlots:t._u([{key:"cancelAuth",fn:function(i){var a=i.row;i.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"cancelAuthentication",expression:"'cancelAuthentication'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",disabled:!!a.cancelTime,ghost:""},on:{click:function(e){return t.cancelAuthInfo(a)}}},[t._v("取消实名认证信息")])]}},{key:"fileName",fn:function(i){var a=i.row;i.index;return["3"===a.authStatus&&0==a.needDesensitization&&a.fileName?e("Button",{directives:[{name:"has",rawName:"v-has",value:"showImg",expression:"'showImg'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"info",ghost:""},on:{click:function(e){return t.showimg(a)}}},[t._v("点击查看")]):e("Button",{directives:[{name:"has",rawName:"v-has",value:"showImg",expression:"'showImg'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"info",disabled:"",ghost:""}},[t._v("点击查看")])]}}])}),e("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"page-size":10,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Modal",{attrs:{title:"导出","mask-closable":!0,width:440},on:{"on-cancel":t.cancelModal},model:{value:t.exportModel,callback:function(e){t.exportModel=e},expression:"exportModel"}},[e("div",[e("Form",{ref:"ruleList",staticStyle:{"align-items":"center","justify-content":"center"},attrs:{model:t.ruleList,"label-position":"left",rules:t.rule,"label-width":100}},[e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"导出范围:",prop:"exporttype"}},[e("Select",{staticStyle:{width:"260px"},attrs:{placeholder:"下拉选择导出范围"},model:{value:t.ruleList.exporttype,callback:function(e){t.$set(t.ruleList,"exporttype",e)},expression:"ruleList.exporttype"}},t._l(t.typeList,(function(i,a){return e("Option",{key:a,attrs:{value:i.value}},[t._v(t._s(i.label))])})),1)],1),2===t.ruleList.exporttype||1===t.ruleList.exporttype?e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"发送邮箱:",prop:"email"}},[e("Select",{staticStyle:{width:"260px"},attrs:{filterable:"",placeholder:"下拉选择邮箱",clearable:""},model:{value:t.ruleList.email,callback:function(e){t.$set(t.ruleList,"email",e)},expression:"ruleList.email"}},t._l(t.emailList,(function(i,a){return e("Option",{key:a,attrs:{value:i.email}},[t._v(t._s(i.email))])})),1)],1):t._e(),2===t.ruleList.exporttype?e("FormItem",{attrs:{label:"上传号码列表",prop:"file"}},[e("div",{staticStyle:{display:"flex"}},[e("Upload",{ref:"upload",attrs:{action:t.uploadUrl,"on-success":t.fileSuccess,"on-error":t.handleError,"before-upload":t.handleBeforeUpload,"on-progress":t.fileUploading},model:{value:t.ruleList.file,callback:function(e){t.$set(t.ruleList,"file",e)},expression:"ruleList.file"}},[e("Button",{attrs:{icon:"ios-cloud-upload-outline"}},[t._v("点击上传")])],1),e("div",{staticStyle:{width:"500px","margin-left":"50px"}},[e("Button",{attrs:{type:"primary",icon:"ios-download"},on:{click:t.downloadTemplate}},[t._v("下载模板")])],1)],1),t.file?e("ul",{staticClass:"ivu-upload-list"},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("Icon",{attrs:{type:"ios-folder"}}),t._v(t._s(t.file.name))],1),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeFile}})])]):t._e()]):t._e()],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("返回")]),e("Button",{attrs:{type:"primary",loading:t.exportloading},on:{click:t.Confirm}},[t._v("确定")])],1)]),e("Table",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"modelTable",attrs:{columns:t.modelColumns,data:t.modelData}}),e("Modal",{attrs:{title:"图片查看","footer-hide":!0,width:"532px",id:"img"},on:{"on-cancel":t.cancelModal},model:{value:t.imgModel,callback:function(e){t.imgModel=e},expression:"imgModel"}},[e("div",{staticStyle:{display:"flex","justify-content":"center","align-items":"center",width:"500px"}},[e("img",{attrs:{src:t.pictureUrl,width:"100%"}})])]),e("Modal",{attrs:{title:"金锁模式申请",fullscreen:!1,"mask-closable":!1,width:"500px","footer-hide":!0},on:{"on-cancel":t.lockApplicationCancel},model:{value:t.lockApplicationModal,callback:function(e){t.lockApplicationModal=e},expression:"lockApplicationModal"}},[t.lockApplicationModal?e("lockApplication",{attrs:{page:2,type:2},on:{lockApplicationCancel:t.lockApplicationCancel,goPageFirst:function(e){return t.goPageFirst(1)}}}):t._e()],1)],1)},n=[],r=(i("d9e2"),i("d3b7"),i("00b4"),i("3ca3"),i("ddb0"),i("2b3d"),i("bf19"),i("9861"),i("88a7"),i("271a"),i("5494"),i("d245")),o=i("7b9b");function l(){return!1}var s={components:{lockApplication:r["a"]},data:function(){var t=this,e=function(e,i,a){t.uploadList&&0===t.uploadList.length?a(new Error("请上传文件")):a()};return{form:{},exportModel:!1,imgModel:!1,lockApplicationModal:!1,exportloading:!1,pictureUrl:"",uploadUrl:"",isSuperManger:"",tableData:[],ruleList:{exporttype:"",email:"",file:""},file:null,total:0,loading:!1,searchloading:!1,downloading:!1,currentPage:1,page:1,emailList:[],uploadList:[],typeList:[{value:1,label:"全量导出"},{value:2,label:"按文件导出"}],modelData:[{iccid:"********"}],modelColumns:[{title:"iccid",key:"iccid"}],columns:[{title:"ICCID",key:"iccid",align:"center",minWidth:200},{title:"MSISDN",key:"msisdn",align:"center",minWidth:200},{title:"IMSI",key:"imsi",align:"center",minWidth:200},{title:"姓名（中文）",key:"inputNameCh",align:"center",minWidth:150,sortable:!0,tooltip:!0},{title:"姓名（英文）",key:"inputName",align:"center",minWidth:150,sortable:!0,tooltip:!0},{title:"证件类型",key:"certificatesType",align:"center",minWidth:100,render:function(t,e){var i=e.row,a="1"===i.certificatesType?"护照":"2"===i.certificatesType?"港澳通行证":"3"===i.certificatesType?"香港身份证":"4"===i.certificatesType?"澳门身份证":"未知";return t("label",a)}},{title:"护照国家",key:"passportCountry",align:"center",minWidth:100},{title:"证件号",key:"certificatesId",align:"center",minWidth:150},{title:"出生年月日",key:"dateOfBirth",align:"center",minWidth:150},{title:"认证时间",key:"authTime",align:"center",minWidth:150},{title:"认证国家",key:"ruleName",align:"center",minWidth:120,sortable:!0,tooltip:!0},{title:"认证状态",key:"authstatus",align:"center",minWidth:120,render:function(t,e){var i=e.row,a="1"===i.authStatus?"待认证":"2"===i.authStatus?"认证中":"3"===i.authStatus?"认证通过":"4"===i.authStatus?"认证失败":"5"===i.authStatus?"证件已过期":"6"===i.authStatus?"已取消":"未知";return t("label",a)}},{title:"取消时间",key:"cancelTime",align:"center",minWidth:150},{title:"操作",slot:"cancelAuth",align:"center",minWidth:160},{title:"图片查看",slot:"fileName",align:"center",minWidth:120}],rule:{file:[{required:!0,validator:e,trigger:"change"}],exporttype:[{required:!0,message:"请选择导出范围"}],email:[{required:!0,message:"请选择邮箱"}]}}},computed:{},mounted:function(){this.isSuperManger=this.$store.state.user.roleId,document.getElementById("card").oncontextmenu=l,document.getElementById("card").oncopy=l,this.getemail()},methods:{goPageFirst:function(t){var e=this;this.loading=!0,Object(o["c"])({imsi:this.form.imsi,msisdn:this.form.msisdn,iccid:this.form.iccid,authStatus:this.form.authStatus,certificatesId:this.form.certificatesId,pageNumber:t,pageSize:10}).then((function(i){"0000"===i.code&&(e.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t,e.total=i.count,e.tableData=i.data)})).catch((function(t){console.error(t)})).finally((function(){e.loading=!1,e.searchloading=!1}))},goPage:function(t){this.goPageFirst(t)},search:function(){this.form.iccid||this.form.imsi||this.form.msisdn||this.form.authStatus||this.form.certificatesId?(this.searchloading=!0,this.goPageFirst(1)):this.$Notice.warning({title:"提示",desc:"查询条件必选一个"})},downloadFile:function(){this.exportModel=!0},viewPermission:function(){this.lockApplicationModal=!0},cancelModal:function(){this.file="",this.exportModel=!1,this.uploadList=[],this.$refs.ruleList.resetFields(),this.pictureUrl=""},lockApplicationCancel:function(){this.lockApplicationModal=!1},Confirm:function(){var t=this;this.$refs["ruleList"].validate((function(e){if(e){t.exportloading=!0;var i=new FormData;i.append("file",t.file),Object(o["f"])(i,t.ruleList.exporttype,t.ruleList.email).then((function(e){"0000"===e.code&&(t.$Notice.success({title:"提示",desc:"异步导出任务创建成功，会下发至指定邮箱，数据量较大生成较慢，请稍后"}),t.cancelModal())})).catch((function(t){console.error(t)})).finally((function(){t.exportModel=!1,t.exportloading=!1,t.cancelModal()}))}}))},fileSuccess:function(t,e,i){this.message="请先下载模板文件，并按格式填写后上传"},handleError:function(t,e){var i=this;setTimeout((function(){i.uploading=!1,i.$Notice.warning({title:"错误提示",desc:"上传失败！"})}),3e3)},handleBeforeUpload:function(t,e){return/^.+(\.csv)$/.test(t.name)?(this.file=t,this.uploadList=e):this.$Notice.warning({title:"文件格式不正确",desc:"文件 "+t.name+" 格式不正确，请上传csv格式文件。"}),!1},fileUploading:function(t,e,i){this.message="文件上传中、待进度条消失后再操作"},removeFile:function(){this.file=""},downloadTemplate:function(){this.$refs.modelTable.exportCsv({filename:"ICCID",type:"csv",columns:this.modelColumns,data:this.modelData})},getemail:function(){var t=this;Object(o["h"])().then((function(e){"0000"===e.code&&(t.emailList=e.data)}))},cancelAuthInfo:function(t){var e=this;Object(o["e"])({authID:t.authId}).then((function(t){"0000"===t.code&&(e.loading=!0,e.$Notice.success({title:"操作提醒",desc:"操作成功"}),e.goPageFirst(1),e.loading=!1)})).catch((function(t){console.error(t)})).finally((function(){e.loading=!1}))},showimg:function(t){var e=this;this.imgModel=!0,Object(o["g"])({fileName:t.fileName,type:"ocr"}).then((function(t){document.getElementById("img").oncontextmenu=l,document.getElementById("img").oncopy=l;var i=new Blob([t.data]);e.pictureUrl=window.URL.createObjectURL(i)})),t.ocrNumber&&(t.ocrNumber.length>10?t.ocrNumber.substring(0,10):t.ocrNumber)}}},c=s,u=(i("3874"),i("2877")),d=Object(u["a"])(c,a,n,!1,null,null,null);e["default"]=d.exports},be6b:function(t,e,i){},d245:function(t,e,i){"use strict";var a=function(){var t=this,e=t._self._c;return e("div",[e("Form",{ref:"formObj",attrs:{model:t.formObj,"label-width":90,rules:t.ruleAddValidate}},[e("FormItem",{attrs:{label:"申请原因",prop:"replyReason"}},[e("Input",{staticStyle:{width:"350px"},attrs:{type:"textarea",rows:3,maxlength:300,clearable:"",placeholder:"请输入原因"},model:{value:t.formObj.replyReason,callback:function(e){t.$set(t.formObj,"replyReason",e)},expression:"formObj.replyReason"}})],1),e("FormItem",{attrs:{label:"浏览时间",prop:"replyTime"}},[e("Select",{staticStyle:{width:"350px"},model:{value:t.formObj.replyTime,callback:function(e){t.$set(t.formObj,"replyTime",e)},expression:"formObj.replyTime"}},t._l(t.timeList,(function(i,a){return e("Option",{key:i,attrs:{value:i}},[t._v(t._s(i+"小时"))])})),1)],1),e("FormItem",{attrs:{label:"申请页面",prop:"pageNumber"}},[e("Select",{staticStyle:{width:"350px"},model:{value:t.formObj.pageNumber,callback:function(e){t.$set(t.formObj,"pageNumber",e)},expression:"formObj.pageNumber"}},[1==t.page?e("Option",{attrs:{value:"1"}},[t._v("个人订单管理")]):e("Option",{attrs:{value:"2"}},[t._v("认证信息")])],1)],1)],1),e("div",{staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),t._v("      \n\t  "),e("Button",{directives:[{name:"has",rawName:"v-has",value:"submit",expression:"'submit'"},{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:t.submitLoading},on:{click:t.submit}},[t._v("确定")])],1)],1)},n=[],r=(i("a9e3"),i("d3b7"),i("66df")),o="/sys/api/user",l=function(t){return r["a"].request({url:o+"/privilege/replyPrivilege",data:t,method:"post"})},s=function(t){return r["a"].request({url:o+"/privilege/getAvailableTime",data:t,method:"get"})},c={props:{page:{type:Number,default:""},type:{type:Number,default:""}},data:function(){return{submitLoading:!1,timeList:[],formObj:{replyReason:"",replyTime:"",pageNumber:""},ruleAddValidate:{replyReason:[{required:!0,type:"string",message:"原因不能为空"}],replyTime:[{required:!0,message:"时间不能为空"}],pageNumber:[{required:!0,type:"string",message:"页面不能为空"}]}}},mounted:function(){this.getTime(),1==this.page?this.formObj.pageNumber="1":this.formObj.pageNumber="2"},methods:{cancelModal:function(){this.$emit("lockApplicationCancel")},submit:function(){var t=this;this.$refs["formObj"].validate((function(e){e&&(t.submitLoading=!0,l({replyReason:t.formObj.replyReason,replyTime:t.formObj.replyTime,pageNumber:t.formObj.pageNumber}).then((function(e){if("0000"!==e.code)throw e;e.data;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.submitLoading=!1,t.$emit("lockApplicationCancel"),t.$emit("goPageFirst")})).catch((function(t){console.log(t)})).finally((function(){t.submitLoading=!1})))}))},getTime:function(){var t=this;s().then((function(e){"0000"===e.code&&(t.timeList=e.data)})).catch((function(t){console.log(t)}))}}},u=c,d=i("2877"),m=Object(d["a"])(u,a,n,!1,null,null,null);e["a"]=m.exports}}]);