{"name": "vue-element-plus-admin", "version": "2.9.0", "description": "一套基于vue3、element-plus、typesScript、vite4的后台集成方案。", "author": "Archer <<EMAIL>>", "private": false, "type": "module", "scripts": {"i": "pnpm install", "dev": "pnpm vite --mode base", "dev:cmi": "pnpm vite --mode base --open", "ts:check": "pnpm vue-tsc --noEmit --skipLib<PERSON><PERSON>ck", "build": "pnpm vite build --mode pro", "build:pro": "pnpm vite build --mode pro", "build:gitee": "pnpm vite build --mode gitee", "build:dev": "pnpm vite build --mode dev", "build:test": "pnpm vite build --mode test", "serve:pro": "pnpm vite preview --mode pro", "serve:dev": "pnpm vite preview --mode dev", "serve:test": "pnpm vite preview --mode test", "npm:check": "pnpx npm-check-updates -u", "clean": "pnpx rimraf node_modules", "clean:cache": "pnpx rimraf node_modules/.cache", "lint:eslint": "eslint . --fix \"src/**/*.{js,ts,tsx,vue,html}\"", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,vue,html,md}\"", "lint:style": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.cjs", "prepare": "husky install", "p": "plop", "icon": "esno ./scripts/icon.ts"}, "dependencies": {"@adyen/adyen-web": "^5.68.0", "@adyen/api-library": "v19.2.0", "@iconify/iconify": "^3.1.1", "@iconify/vue": "^4.3.0", "@vueuse/core": "^12.3.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.10", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "axios": "^1.7.9", "cropperjs": "^1.6.2", "dayjs": "^1.11.13", "driver.js": "^1.3.1", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "element-plus": "2.9.2", "fraction.js": "^4.1.1", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "marked": "^12.0.0", "mathjs": "^9.4.4", "mitt": "^3.0.1", "monaco-editor": "^0.52.2", "nprogress": "^0.2.0", "pinia": "^3.0.0", "pinia-plugin-persistedstate": "^4.2.0", "qrcode": "^1.5.4", "qs": "^6.13.1", "typed-js": "^0.2.3", "url": "^0.11.4", "vue": "3.5.13", "vue-draggable-plus": "^0.6.0", "vue-i18n": "11.0.1", "vue-json-pretty": "^2.4.0", "vue-pdf": "^4.2.0", "vue-router": "^4.5.0", "vue-types": "^5.1.3", "vue-uuid": "^2.0.2", "xgplayer": "^3.0.20", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@iconify/json": "^2.2.293", "@intlify/unplugin-vue-i18n": "^6.0.3", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/node": "^22.10.5", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.17", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "@unocss/transformer-variant-group": "^0.65.4", "@vitejs/plugin-legacy": "^6.0.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "autoprefixer": "^10.4.20", "chalk": "^5.4.1", "consola": "^3.3.3", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.32.0", "esno": "^4.8.0", "fs-extra": "^11.2.0", "husky": "^9.1.7", "inquirer": "^12.3.0", "less": "^4.2.1", "lint-staged": "^15.3.0", "mockjs": "^1.1.0", "plop": "^4.0.1", "postcss": "^8.4.49", "postcss-html": "^1.7.0", "postcss-less": "^6.0.0", "prettier": "^3.4.2", "rimraf": "^6.0.1", "rollup": "^4.30.1", "rollup-plugin-visualizer": "^5.14.0", "stylelint": "^16.12.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recommended": "^14.0.1", "stylelint-config-standard": "^36.0.1", "stylelint-order": "^6.0.4", "terser": "^5.37.0", "typescript": "5.7.3", "typescript-eslint": "^8.19.1", "unocss": "^0.65.4", "vite": "6.0.7", "vite-plugin-ejs": "^1.7.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-mock": "2.9.6", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.10.0", "vite-plugin-style-import": "2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-url-copy": "^1.1.4", "vue-tsc": "^2.2.0"}, "packageManager": "pnpm@9.15.3", "engines": {"node": ">=18.0.0", "pnpm": ">=8.1.0"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/kailong321200875/vue-element-plus-admin.git"}, "bugs": {"url": "https://github.com/kailong321200875/vue-element-plus-admin/issues"}, "homepage": "https://github.com/kailong321200875/vue-element-plus-admin"}