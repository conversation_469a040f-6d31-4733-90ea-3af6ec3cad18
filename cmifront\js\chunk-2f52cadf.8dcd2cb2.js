(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2f52cadf"],{"16d9":function(t,e,a){},"270b":function(t,e,a){"use strict";a("e3cd")},"71b3":function(t,e,a){"use strict";a("16d9")},"851c":function(t,e,a){},"90d2":function(t,e,a){"use strict";a("b12fe")},a2cc:function(t,e,a){"use strict";a.r(e);a("ac1f"),a("841c"),a("498a");var i=function(){var t=this,e=t._self._c;return e("div",[e("Card",[e("div",{staticClass:"search_head_i"},[e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("MSISDN")]),e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:t.$t("support.MSISDNenter"),clearable:""},model:{value:t.msisdn,callback:function(e){t.msisdn="string"===typeof e?e.trim():e},expression:"msisdn"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("ICCID")]),e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:t.$t("support.ICCIDenter"),clearable:""},model:{value:t.iccid,callback:function(e){t.iccid="string"===typeof e?e.trim():e},expression:"iccid"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("IMSI")]),e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:t.$t("support.IMSIenter"),clearable:""},model:{value:t.imsi,callback:function(e){t.imsi="string"===typeof e?e.trim():e},expression:"imsi"}})],1),e("div",{staticClass:"search_box"},[e("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{margin:"0 4px"},attrs:{disabled:"3"==t.cooperationMode,type:"primary",loading:t.loading},on:{click:function(e){return t.search()}}},[e("Icon",{attrs:{type:"ios-search"}}),t._v(" "+t._s(t.$t("support.search"))+"\n\t\t\t\t\t")],1)],1)]),e("div",{staticStyle:{"margin-top":"20px",display:"flex"}},[e("div",[e("mailTable",{attrs:{tableData:t.tableData1,realNameInfoData:t.realNameInfoData,tableStyle:{width:"930px"}}})],1),null!=this.ShowList?e("div",{staticStyle:{"margin-left":"80px"}},[e("Form",[e("FormItem",[null!=this.ShowList.mcc&&""!=this.ShowList.mcc?e("router-link",{staticStyle:{"margin-right":"10px"},attrs:{to:{name:"splocation_package",query:{imsi:this.ShowList.imsi,backimsi:this.imsi,backiccid:this.iccid,backmsisdn:this.msisdn,mcc:this.ShowList.mcc,localName:this.ShowList.local,localNameEn:this.ShowList.localEn,corpId:this.corpId}}}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"currentPackage",expression:"'currentPackage'"},{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{margin:"0 4px",width:"180px"},attrs:{type:"primary"}},[t._v("\n\t\t\t\t\t\t\t\t\t"+t._s(t.$t("location_package"))+"\n\t\t\t\t\t\t\t\t")])],1):e("Button",{directives:[{name:"has",rawName:"v-has",value:"currentPackage",expression:"'currentPackage'"},{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{margin:"0 4px",width:"180px"},attrs:{disabled:"",type:"primary"}},[t._v("\n\t\t\t\t\t\t\t\t"+t._s(t.$t("location_package"))+"\n\t\t\t\t\t\t\t")])],1),e("FormItem",[e("router-link",{attrs:{to:{name:"sppurchased_package",query:{type:this.ShowList.position,imsi:this.ShowList.imsi,iccid:this.ShowList.iccid,msisdn:this.ShowList.msisdn,mcc:this.ShowList.mcc,backimsi:this.imsi,backiccid:this.iccid,backmsisdn:this.msisdn,corpId:this.corpId,isBak:this.ShowList.isBak}}}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"purchasedPackage",expression:"'purchasedPackage'"},{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{margin:"0 4px",width:"180px"},attrs:{type:"primary"}},[t._v("\n\t\t\t\t\t\t\t\t\t"+t._s(t.$t("sppurchased_package"))+"\n\t\t\t\t\t\t\t\t")])],1)],1),e("FormItem",[e("Button",{directives:[{name:"has",rawName:"v-has",value:"locationRecord",expression:"'locationRecord'"},{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{margin:"0 4px",width:"180px"},attrs:{type:"primary",loading:t.locationloading},on:{click:function(e){return t.showLocalUpModal(t.ShowList)}}},[t._v("\n\t\t\t\t\t\t\t\t"+t._s(t.$t("support.Locationrecord"))+"\n\t\t\t\t\t\t\t")])],1),e("FormItem",[e("Button",{directives:[{name:"has",rawName:"v-has",value:"sendSMS",expression:"'sendSMS'"},{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{margin:"0 4px",width:"180px"},attrs:{type:"primary",loading:t.sendloading},on:{click:function(e){return t.showSmsModal(t.ShowList)}}},[t._v("\n\t\t\t\t\t\t\t\t"+t._s(t.$t("support.SendSMS"))+"\n\t\t\t\t\t\t\t")])],1),e("FormItem",[e("Button",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"},{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{margin:"0 4px",width:"180px"},attrs:{type:"primary",loading:t.viewloading},on:{click:function(e){return t.showTrafficModal(t.ShowList)}}},[t._v("\n\t\t\t\t\t\t\t\t"+t._s(t.$t("support.Flowdetails"))+"\n\t\t\t\t\t\t\t")])],1),"2"==this.ShowList.cardForm?e("FormItem",[e("Button",{directives:[{name:"has",rawName:"v-has",value:"esimInfo",expression:"'esimInfo'"}],staticStyle:{margin:"0 4px",width:"180px"},attrs:{type:"primary"},on:{click:function(e){return t.showEsimModal(t.ShowList)}}},[t._v("\n\t\t\t\t\t\t\t\t"+t._s(t.$t("support.EsimDetails"))+"\n\t\t\t\t\t\t\t")])],1):t._e(),e("FormItem",[e("Button",{directives:[{name:"has",rawName:"v-has",value:"sessionQuery",expression:"'sessionQuery'"}],staticStyle:{margin:"0 4px",width:"180px"},attrs:{type:"primary"},on:{click:function(e){return t.showSessionModal(t.ShowList)}}},[t._v("\n\t\t\t\t\t\t\t\t"+t._s(t.$t("sessionInfo.sessionQuery"))+"\n\t\t\t\t\t\t\t")])],1)],1)],1):t._e()])]),e("Modal",{attrs:{title:t.$t("support.Locationrecord"),"mask-closable":!1,width:"850px"},on:{"on-cancel":t.cancelModal},model:{value:t.localUpModal,callback:function(e){t.localUpModal=e},expression:"localUpModal"}},[t.localUpModal?e("update-record",{attrs:{imsi:t.localUpImsi,iccid:t.ShowList.iccid,local:t.localUpLocal}}):t._e(),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v(t._s(t.$t("support.close")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],attrs:{type:"primary"},on:{click:t.exportlocalUp}},[t._v(t._s(t.$t("stock.exporttb")))])],1)],1),e("Modal",{attrs:{title:t.$t("support.SendSMS"),"mask-closable":!1,width:"500px","footer-hide":!0},on:{"on-cancel":t.cancelModal},model:{value:t.smsModal,callback:function(e){t.smsModal=e},expression:"smsModal"}},[t.smsModal?e("sms-modal",{attrs:{row:t.row},on:{closeModal:t.cancelModal}}):t._e()],1),e("Modal",{attrs:{title:t.$t("support.usedetails"),"mask-closable":!1,width:"60%"},on:{"on-cancel":t.cancelModal},model:{value:t.trafficModal,callback:function(e){t.trafficModal=e},expression:"trafficModal"}},[t.trafficModal?e("traffic-detail",{attrs:{form:t.form,imsi:t.trafficImsi}}):t._e(),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"})],1),e("Modal",{attrs:{title:t.$t("support.EsimDetails"),"mask-closable":!1,width:"30%"},on:{"on-cancel":t.cancelModal},model:{value:t.esimModal,callback:function(e){t.esimModal=e},expression:"esimModal"}},[t.esimModal?e("esim-detail",{attrs:{esimIccid:t.esimIccid}}):t._e(),e("div",{staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{staticStyle:{margin:"0 4px"},on:{click:t.cancelModal}},[e("Icon",{attrs:{type:"ios-arrow-back"}}),t._v(" "+t._s(t.$t("support.back"))+"\n\t\t\t\t")],1),e("Button",{directives:[{name:"has",rawName:"v-has",value:"qrCode",expression:"'qrCode'"}],staticStyle:{"margin-left":"8px"},attrs:{type:"primary",loading:t.qrloading},on:{click:t.generateQRCode}},[t._v(t._s(t.$t("support.generateQRCode")))])],1)],1),e("Modal",{attrs:{title:t.$t("support.qrPicture"),"footer-hide":!0,width:"500px",id:"img"},on:{"on-cancel":t.imgCancelModal},model:{value:t.imgModel,callback:function(e){t.imgModel=e},expression:"imgModel"}},[e("div",{staticStyle:{display:"flex","justify-content":"center","align-items":"center",width:"450px"}},[e("img",{attrs:{src:t.pictureUrl,width:"90%"}})])]),e("Modal",{attrs:{title:t.$t("sessionInfo.sessionDetail"),"footer-hide":!0,"mask-closable":!1,width:"1400px"},on:{"on-cancel":t.cancelModal},model:{value:t.sessionModal,callback:function(e){t.sessionModal=e},expression:"sessionModal"}},[t.sessionModal?e("session-detail",{attrs:{iccid:t.ShowList.iccid,imsi:t.ShowList.imsi,corpId:t.corpId},on:{close:t.cancelModal}}):t._e()],1)],1)},s=[],o=(a("d81d"),a("14d9"),a("e9c4"),a("b64b"),a("d3b7"),a("25f0"),a("3ca3"),a("5319"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("0a21")),n=a("6dfa"),l=a("9819"),r=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{padding:"0 16px"}},[e("div",[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.$t("support.position"))+"："+t._s(t.localName))])]),e("br"),e("div",[e("Table",{attrs:{columns:t.columnsU,data:t.tableDataU,ellipsis:!0,loading:t.loadingU,"max-height":"500"}})],1),e("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.totalU,current:t.pageU,"page-size":t.pageSizeU,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.pageU=e},"on-change":t.loadRecords}})],1)])},c=[],d=a("8ba4"),p={props:{imsi:String,local:String,iccid:String},data:function(){var t=this;return{localName:"",pageU:1,loadingU:!1,totalU:0,pageSizeU:5,columnsU:[{title:this.$t("support.Report_time"),key:"reportTime",align:"center",minWidth:50},{title:this.$t("support.Report_address"),key:"mcc",align:"center",render:function(e,a){var i,s=a.row;return i="zh-CN"===t.$i18n.locale?"未知"==s.mcc?"":s.mcc:"UNKNOW"==s.mccEn?"":s.mccEn,e("label",i)}},{title:this.$t("support.Activationmethod"),key:"activeType",align:"center",render:function(e,a){var i=a.row,s="1"==i.activeType?t.$t("support.Automatic"):"2"==i.activeType?t.$t("support.Manual"):"-";return e("label",s)}},{title:this.$t("support.operator"),key:"operatorName",align:"center",tooltip:!0,render:function(t,e){var a=e.row,i="unknown"==a.operatorName?"":a.operatorName;return t("label",i)}},{title:this.$t("support.network"),key:"netType",align:"center",render:function(t,e){var a=e.row,i="1"==a.netType?"3G":"2"==a.netType?"2G":"3"==a.netType?"wifi":"4"==a.netType?"2G":"5"==a.netType?"3G":"6"==a.netType?"4G":"";return t("label",i)}}],tableDataU:[]}},methods:{loadRecords:function(t){var e=this;this.localName=this.local,this.loadingU=!0,this.pageU=t,Object(d["o"])({imsi:this.imsi,pageNumber:t,pageSize:this.pageSizeU,iccid:this.iccid,expiredData:1}).then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.tableDataU=a.records,e.totalU=a.totalCount})).catch((function(t){console.log(t)})).finally((function(){e.loadingU=!1}))}},mounted:function(){this.loadRecords(1)},watch:{imsi:function(t,e){this.loadRecords(1)}}},u=p,m=a("2877"),h=Object(m["a"])(u,r,c,!1,null,"6c9092ce",null),f=h.exports,g=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{padding:"0 16px"}},[e("Form",{ref:"SMSFormValidate",staticStyle:{"font-weight":"bold"},attrs:{model:t.smsInfo,rules:t.rules,"label-width":130,"label-height":100,inline:""}},[e("FormItem",{staticStyle:{width:"420px"},attrs:{label:t.$t("support.sendingmethod"),prop:"sendType"}},[e("Select",{staticStyle:{width:"85%"},attrs:{placeholder:t.$t("support.chosesend"),clearable:"",multiple:""},model:{value:t.smsInfo.sendType,callback:function(e){t.$set(t.smsInfo,"sendType",e)},expression:"smsInfo.sendType"}},t._l(t.sendTypeList,(function(a,i){return e("Option",{key:i,attrs:{value:a.key}},[t._v(t._s(a.value))])})),1)],1),e("FormItem",{staticStyle:{width:"420px"},attrs:{label:t.$t("support.template"),prop:"tempId"}},[e("Select",{staticStyle:{width:"85%"},attrs:{placeholder:t.$t("support.chosetemplate"),clearable:""},model:{value:t.smsInfo.tempId,callback:function(e){t.$set(t.smsInfo,"tempId",e)},expression:"smsInfo.tempId"}},t._l(t.tempList,(function(a,i){return e("Option",{key:i,attrs:{value:a.id}},[t._v(t._s(a.templateName))])})),1)],1),-1!=t.smsInfo.sendType.indexOf("1")?e("FormItem",{staticStyle:{width:"420px"},attrs:{label:t.$t("support.phone"),prop:"phoneNum"}},[e("Input",{staticStyle:{width:"85%"},attrs:{clearable:"",placeholder:t.$t("support.phoneprompt")},model:{value:t.smsInfo.phoneNum,callback:function(e){t.$set(t.smsInfo,"phoneNum",e)},expression:"smsInfo.phoneNum"}})],1):t._e(),-1!=t.smsInfo.sendType.indexOf("2")?e("FormItem",{staticStyle:{width:"420px"},attrs:{label:"HIMSI",prop:"HIMSI"}},[e("Input",{staticStyle:{width:"85%"},attrs:{disabled:"",placeholder:"请输入HIMSI"},model:{value:t.smsInfo.HIMSI,callback:function(e){t.$set(t.smsInfo,"HIMSI",e)},expression:"smsInfo.HIMSI"}})],1):t._e()],1),e("div",{staticStyle:{"text-align":"center",margin:"4px 0"}},[e("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary"},on:{click:t.sendSms}},[t._v(t._s(t.$t("support.sending")))]),e("Button",{staticStyle:{"margin-left":"8px"},on:{click:t.reSetInfo}},[t._v(t._s(t.$t("buymeal.Reset")))])],1)],1)},y=[],S=(a("00b4"),a("f121")),v=a("2b0e"),I=S["a"].customerSMSType.code,w=S["a"].customerSMSType.iccid,b={props:{row:Object},data:function(){var t=function(t,e,a){var i=/^[0-9]\d*$/;return i.test(e)};return{smsInfo:{HIMSI:"",phoneNum:"",sendType:["1"],tempId:null},sendTypeList:[{key:"1",value:this.$t("support.phone")},{key:"2",value:"HIMSI"}],rules:{sendType:[{required:!0,type:"array",message:this.$t("support.Sendingempty"),trigger:"change"}],tempId:[{required:!0,message:this.$t("support.SMSempty"),trigger:"change"}],phoneNum:[{required:!0,message:this.$t("support.Phoneempty"),type:"string"},{validator:t,message:this.$t("support.PhoneWrong")}],HIMSI:[{required:!0,message:"HIMSI不能为空",type:"string"}]},tempList:[]}},methods:{sendSms:function(t){var e=this;this.$refs["SMSFormValidate"].validate((function(t){if(t){for(var a=e.smsInfo.sendType,i=[],s=0;s<a.length;s++)"1"==a[s]&&i.push(e.smsInfo.phoneNum),"2"==a[s]&&i.push(e.row.msisdn);var o=[e.row.pin2],n={};v["default"].set(n,I,o),v["default"].set(n,w,[e.row.iccid]);var l={phones:i,templateId:e.smsInfo.tempId,language:0,params:n};Object(d["u"])(l).then((function(t){if(!t||"0000"!=t.code)throw t;e.$emit("closeModal",{type:"sms"}),e.$Notice.success({title:e.$t("address.Operationreminder"),desc:e.$t("support.Sentsuccessfully")})})).catch((function(t){console.log(t)})).finally((function(){}))}}))},reSetInfo:function(){this.$refs["SMSFormValidate"].resetFields(),this.smsInfo.HIMSI=this.row.imsi,this.smsInfo.phoneNum=""},getSmsTempList:function(){var t=this;Object(d["m"])({}).then((function(e){if(!e||"0000"!=e.code)throw e;t.tempList=e.data})).catch((function(t){console.log(t)})).finally((function(){}))}},mounted:function(){this.reSetInfo(),this.getSmsTempList()},watch:{}},k=b,$=(a("90d2"),Object(m["a"])(k,g,y,!1,null,"db542d3e",null)),x=$.exports,M=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{padding:"0 16px"}},[e("div",[e("Table",{attrs:{columns:t.columnsF,data:t.tableDataF,ellipsis:!0,loading:t.loadingF,"max-height":"500"}})],1)])},_=[],N={props:{form:{imsi:"",iccid:"",msisdn:""},imsi:""},data:function(){var t=this;return{pageF:1,loadingF:!1,totalF:0,pageSizeF:5,columnsF:[{title:this.$t("support.date"),key:"date",align:"center",render:function(t,e){var a=e.row,i=a.activeTime+"  -  "+a.expireTime;return t("label",i)}},{title:this.$t("support.used_flow"),key:"flowByteTotal",align:"center"},{title:this.$t("support.mealname"),key:"packageName",align:"center",render:function(e,a){var i=a.row,s="zh-CN"===t.$i18n.locale?i.packageName:"en-US"===t.$i18n.locale?i.nameEn:"";return e("label",s)}}],tableDataF:[]}},methods:{loadTrafficDetail:function(t){var e=this;this.loadingF=!0,this.pageF=t,Object(d["q"])({iccid:this.form.iccid,imsi:this.form.imsi,msisdn:this.form.msisdn}).then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.tableDataF=a,e.totalF=a.totalCount})).catch((function(t){console.log(t)})).finally((function(){e.loadingF=!1}))}},mounted:function(){this.loadTrafficDetail(1)},watch:{imsi:function(t,e){this.loadTrafficDetail(1)}}},D=N,C=Object(m["a"])(D,M,_,!1,null,"0f1d5322",null),T=C.exports,L=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{padding:"0 16px"}},[e("Form",{staticStyle:{"font-size":"16px"},attrs:{model:t.esimInfo,"label-position":"left","label-width":105}},[e("FormItem",{attrs:{label:t.$t("support.smDpAddress")}},[e("Input",{attrs:{readonly:"",title:t.esimInfo.smdpAddress},model:{value:t.esimInfo.smdpAddress,callback:function(e){t.$set(t.esimInfo,"smdpAddress",e)},expression:"esimInfo.smdpAddress"}})],1),e("FormItem",{attrs:{label:t.$t("support.activationCode")}},[e("Input",{attrs:{readonly:"",title:t.esimInfo.matchingId},model:{value:t.esimInfo.matchingId,callback:function(e){t.$set(t.esimInfo,"matchingId",e)},expression:"esimInfo.matchingId"}})],1),e("FormItem",{attrs:{label:t.$t("support.esimStatus")}},[e("Input",{attrs:{readonly:"",title:t.esimInfo.state},model:{value:t.esimInfo.state,callback:function(e){t.$set(t.esimInfo,"state",e)},expression:"esimInfo.state"}})],1),e("FormItem",{attrs:{label:t.$t("support.eid")}},[e("Input",{attrs:{readonly:"",title:t.esimInfo.eid},model:{value:t.esimInfo.eid,callback:function(e){t.$set(t.esimInfo,"eid",e)},expression:"esimInfo.eid"}})],1),e("FormItem",{attrs:{label:t.$t("support.installationTime")}},[e("Input",{attrs:{readonly:"",title:t.esimInfo.firstDownloadDate},model:{value:t.esimInfo.firstDownloadDate,callback:function(e){t.$set(t.esimInfo,"firstDownloadDate",e)},expression:"esimInfo.firstDownloadDate"}})],1),e("FormItem",{attrs:{label:t.$t("support.installationEquipment")}},[e("Input",{attrs:{readonly:"",title:t.esimInfo.device},model:{value:t.esimInfo.device,callback:function(e){t.$set(t.esimInfo,"device",e)},expression:"esimInfo.device"}})],1),e("FormItem",{attrs:{label:t.$t("support.instalAmount")}},[e("Input",{attrs:{readonly:"",title:t.esimInfo.downloadCounter},model:{value:t.esimInfo.downloadCounter,callback:function(e){t.$set(t.esimInfo,"downloadCounter",e)},expression:"esimInfo.downloadCounter"}})],1),e("FormItem",{attrs:{label:t.$t("support.updateTime")}},[e("Input",{attrs:{readonly:"",title:t.esimInfo.updateDate},model:{value:t.esimInfo.updateDate,callback:function(e){t.$set(t.esimInfo,"updateDate",e)},expression:"esimInfo.updateDate"}})],1)],1)],1)},F=[],U=a("2556"),R={props:["esimIccid"],data:function(){return{esimInfo:{smdpAddress:"",matchingId:"",state:"",eid:"",firstDownloadDate:"",device:"",downloadCounter:"",updateDate:""}}},methods:{goPageFirst:function(){var t=this,e=this;Object(U["e"])({iccid:this.esimIccid}).then((function(e){"0000"==e.code&&(t.esimInfo=e.data)})).catch((function(t){console.error(t)})).finally((function(){e.loading=!1,t.searchloading=!1}))}},mounted:function(){this.goPageFirst()}},O=R,E=(a("270b"),Object(m["a"])(O,L,F,!1,null,"2b0db86c",null)),P=E.exports,B=function(){var t=this,e=t._self._c;return e("div",[t._m(0),e("div",{staticStyle:{"margin-top":"5px"}},[e("Table",{ref:"selection",attrs:{columns:t.columns,data:t.tableData,ellipsis:!0,loading:t.loading,"max-height":"500"},scopedSlots:t._u([{key:"action",fn:function(a){var i=a.row;a.index;return["1"==i.status?e("Button",{attrs:{type:"success",size:"small",disabled:""}},[t._v("激活")]):e("Button",{attrs:{type:"success",size:"small"},on:{click:function(e){return t.activation(i)}}},[t._v("激活")])]}}])})],1),e("div",{staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.getLocalMeals}})],1)])},j=[function(){var t=this,e=t._self._c;return e("div",[e("span",{staticStyle:{"font-weight":"bold"}},[t._v("当前位置：中国四川新希望大厦")])])}],z={props:{cardId:String},data:function(){return{page:0,loading:!1,currentPage:1,total:0,columns:[{title:"套餐名称",key:"packageName",align:"center"},{title:"激活状态",key:"status",align:"center",render:function(t,e){var a=e.row,i="1"==a.status?"激活中":"未激活";return t("label",i)}},{title:"激活方式",key:"actType",align:"center"},{title:"周期类型",key:"cycleType",align:"center"},{title:"持续周期",key:"cycle",align:"center"},{title:"套餐过期时间",key:"validity",align:"center"},{title:"套餐价格",key:"price",align:"center"},{title:"币种",key:"currency",align:"center"},{title:"激活套餐",slot:"action",align:"center"}],tableData:[{mealId:"a9g69s6d971",packageName:"新年套餐",status:"1",actType:"自动",cycleType:"24小时",cycle:"7",validity:"2021-01-23 12:45:52",price:"50元",currency:"人民币"},{mealId:"a9g69s6d972",packageName:"元旦套餐",status:"0",actType:"自动",cycleType:"每周",cycle:"4",validity:"2021-01-23 12:45:52",price:"50元",currency:"人民币"},{mealId:"a9g69s6d972",packageName:"中秋套餐",status:"0",actType:"手动",cycleType:"每月",cycle:"1",validity:"2021-01-23 12:45:52",price:"50元",currency:"人民币"}]}},methods:{getLocalMeals:function(t){var e=this;this.loading=!0,this.pageF=t,Object(d["k"])({cardId:this.cardId,pageNumber:t,pageSize:10}).then((function(t){if(!t||"0000"!=t.code)throw t;e.tableData=t.data,e.total=t.total})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1}))},activation:function(t){this.doActivation({cardId:this.cardId,actInfo:t})},doActivation:function(t){var e=this;Object(d["c"])({cardId:t.cardId,mealId:t.actInfo.mealId}).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作成功",desc:"套餐激活成功"}),e.getLocalMeals(e.page)})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1}))}},mounted:function(){},watch:{cardId:function(t,e){this.getLocalMeals(0)}}},q=z,A=Object(m["a"])(q,B,j,!1,null,"ddbaa59e",null),H=A.exports,W=(a("b0c0"),function(){var t=this,e=t._self._c;return e("div",[e("table",{staticClass:"mailTable",style:t.styleObject},t._l(t.rowCount,(function(a){return e("tr",[e("td",{staticStyle:{"background-color":"#F8F8F9",border:"1px solid #DCDEE2",height:"40px",width:"200px","padding-left":"10px",color:"#333"}},[t._v(t._s(t.tableData[2*a-2].key))]),e("td",{staticStyle:{border:"1px solid #DCDEE2",height:"40px",width:"200px","padding-left":"10px",color:"#333"}},[t._v(t._s(t.tableData[2*a-2].value))]),e("td",{staticStyle:{"background-color":"#F8F8F9",border:"1px solid #DCDEE2",height:"40px",width:"200px","padding-left":"10px",color:"#333"}},[t._v(t._s(void 0!==t.tableData[2*a-1]?t.tableData[2*a-1].key:""))]),0===t.tableData[2*a-1].value?e("td",{staticStyle:{border:"1px solid #DCDEE2",height:"40px",width:"200px","padding-left":"10px",color:"#333"},attrs:{align:"center",valign:"middle"}},[e("Button",{attrs:{ghost:"",type:"warning"},on:{click:t.showView}},[t._v(t._s(t.$t("support.view")))])],1):e("td",{staticStyle:{border:"1px solid #DCDEE2",height:"40px",width:"200px","padding-left":"10px",color:"#333"}},[t._v(t._s(void 0!==t.tableData[2*a-1]?t.tableData[2*a-1].value:""))])])})),0),t.realNameInfoData&&t.realNameInfoData.length>0?e("Table",{ref:"selection",staticStyle:{"margin-top":"20px",width:"930px"},attrs:{columns:t.columns,data:t.realNameInfoData,ellipsis:!0},scopedSlots:t._u([{key:"action",fn:function(a){var i=a.row;a.index;return[e("Button",{staticStyle:{"margin-right":"20px"},attrs:{type:"warning",ghost:"",size:"small"},on:{click:function(e){return t.showView(i)}}},[t._v(t._s(t.$t("support.view")))])]}}],null,!1,4268106136)}):t._e(),e("Modal",{attrs:{title:t.$t("support.registration"),"mask-closable":!1,width:"500px"},on:{"on-cancel":t.cancelModal},model:{value:t.Modal,callback:function(e){t.Modal=e},expression:"Modal"}},[e("Form",[e("FormItem",{attrs:{label:t.$t("support.NameChinese")}},[e("span",[t._v(t._s(t.form.nameCh))])]),e("FormItem",{attrs:{label:t.$t("support.NameEnglish")}},[e("span",[t._v(t._s(t.form.name))])]),e("FormItem",{attrs:{label:t.$t("support.IDnumber")}},[e("span",[t._v(t._s(t.form.certificatesId))])]),e("FormItem",{attrs:{label:t.$t("support.IDtype")}},[e("span",[t._v(t._s(t.form.certificatesType))])]),e("FormItem",{attrs:{label:t.$t("support.Issuingcountry")}},[e("span",[t._v(t._s(t.form.passportCountry))])]),t.flag?e("FormItem",{attrs:{label:t.$t("support.errorDesc")}},[e("span",[t._v(t._s(t.form.errorDesc))])]):t._e()],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v(t._s(t.$t("support.back")))])],1)],1)],1)}),J=[],V=a("2909"),G=(a("6062"),a("1e70"),a("79a4"),a("c1a1"),a("8b00"),a("a4e7"),a("1e5a"),a("72c3"),{data:function(){var t=this;return{styleObject:{},form:{},Modal:!1,s_showByRow:!0,flag:!1,columns:[{title:this.$t("support.Complete"),key:"authStatus",align:"left",minWidth:180,tooltip:!0,render:function(e,a){var i=a.row,s="2"===i.useStatus&&"3"===i.authStatus?"#2b85e4":"2"===i.useStatus&&"5"===i.authStatus?"#00aa00":"1"===i.useStatus&&"1"===i.authStatus&&"1"===i.isRepeat?"#ff0000":"1"===i.useStatus&&"1"===i.authStatus&&"2"===i.isRepeat?"#69e457":"1"===i.useStatus&&"2"===i.authStatus?"#e47a49":"3"===i.useStatus&&"3"===i.authStatus?"#dd74e4":"3"===i.useStatus&&"5"===i.authStatus?"#24cbe4":"3"===i.useStatus&&"4"===i.authStatus?"#7009e4":i.realnameId?"#e4b809":"",o="2"===i.useStatus&&"3"===i.authStatus?t.$t("support.Approved"):"2"===i.useStatus&&"5"===i.authStatus?t.$t("support.IDexpired"):"1"===i.useStatus&&"1"===i.authStatus&&"1"===i.isRepeat?t.$t("support.WaitingUser"):"1"===i.useStatus&&"1"===i.authStatus&&"2"===i.isRepeat?t.$t("support.AuthFailed"):"1"===i.useStatus&&"2"===i.authStatus?t.$t("support.process"):"3"===i.useStatus&&"3"===i.authStatus?t.$t("support.Approved"):"3"===i.useStatus&&"5"===i.authStatus?t.$t("support.IDexpired"):"3"===i.useStatus&&"4"===i.authStatus?t.$t("support.Rejected"):i.realnameId?t.$t("support.NotCertified"):t.$t("support.NoCertification");return e("label",{style:{color:s}},o)}},{title:this.$t("support.registrationrules"),key:"ruleName",align:"left",minWidth:170,tooltip:!0},{title:this.$t("support.registrationcountry"),key:"realCountry",align:"left",minWidth:170,tooltip:!0,render:function(e,a){a.row;var i,s,o="...",n=Object(V["a"])(new Set(t.realNameInfoData[a.index].realCountry));return n&&1===n.length?i="zh-CN"===t.$i18n.locale?n[0].countryCN.toString():"en-US"===t.$i18n.locale?n[0].countryEN.toString():"":n&&n.length>1&&(i="zh-CN"===t.$i18n.locale?n[0].countryCN.toString():"en-US"===t.$i18n.locale?n[0].countryEN.toString():"",s="zh-CN"===t.$i18n.locale?n[1].countryCN.toString():"en-US"===t.$i18n.locale?n[1].countryEN.toString():""),n&&0!==n.length?1===n.length?e("span",i):2===n.length?e("div",[e("div",i),e("div",s)]):e("div",[e("Tooltip",{props:{placement:"bottom",transfer:!0},style:{cursor:"pointer"}},[e("span",{style:{display:"block"}},i),e("span",{},s),e("div",{},o),e("ul",{slot:"content",style:{listStyleType:"none",whiteSpace:"normal",wordBreak:"break-all"}},n.map((function(a){var i="zh-CN"===t.$i18n.locale?a.countryCN.toString():"en-US"===t.$i18n.locale?a.countryEN.toString():"";return e("li",i)})))])]):e("span","")}},{title:this.$t("support.validityPeriod"),key:"deadline",align:"left",minWidth:210,tooltip:!0,render:function(e,a){var i=a.row,s="1"===i.deadline?t.$t("support.longTerm"):"2"===i.deadline?t.$t("support.singlePackageEffective"):"";return e("label",s)}},{title:this.$t("support.registration"),slot:"action",align:"left",width:170}]}},props:["tableData","realNameInfoData","tableStyle","showByRow"],computed:{rowCount:function(){return Math.ceil(this.tableData.length/2)}},created:function(){this.styleObject=this.tableStyle,void 0!==this.showByRow&&(this.s_showByRow=this.showByRow)},methods:{cancelModal:function(){this.Modal=!1},showView:function(t){this.Modal=!0,this.form=JSON.parse(JSON.stringify(t)),this.form.certificatesType="1"===t.certificatesType?this.$t("support.Passport"):"2"===t.certificatesType?this.$t("support.Permit"):"3"===t.certificatesType?this.$t("support.HKIdentityCard"):"4"===t.certificatesType?this.$t("support.MacauIdentityCard"):"","1"===t.useStatus&&"2"===t.authStatus?(this.flag=!0,this.form.errorDesc="0"===t.errorDesc?this.$t("support.picturesAreNotSatisfied"):"1"===t.errorDesc?this.$t("support.nameIsInconsistent"):"2"===this.ShowList.errorDesc?this.$t("support.certificateHasExpired"):"3"===t.errorDesc?this.$t("support.IDIsInconsistent"):"4"===t.errorDesc?this.$t("support.sixteenyYearsOld"):""):this.flag=!1}}}),Q=G,K=(a("71b3"),Object(m["a"])(Q,W,J,!1,null,null,null)),Y=K.exports,X=(a("7b9b"),a("7d17")),Z={components:{SmsModal:x,TrafficDetail:T,UpdateRecord:f,LocalMeal:H,mailTable:Y,EsimDetail:P,SessionDetail:X["a"]},data:function(){return{msisdn:"",imsi:"",iccid:"",columns:[],formList:{},realNameInfoData:[],tableData1:[],public:[{title:"套餐查询",fixed:"right",width:180,slot:"actionOne",align:"center"},{title:"操作",fixed:"right",width:260,slot:"action",align:"center"}],tableData:[],details:{},localUpModal:!1,localUpImsi:"",localUpLocal:"",trafficImsi:"",smsModal:!1,trafficModal:!1,esimModal:!1,imgModel:!1,row:null,cardId:null,loading:!1,qrloading:!1,locationloading:!1,sendloading:!1,viewloading:!1,currentPage:1,startTime:null,endTime:null,total:0,pageSize:10,exportData:[],excelKey:[],excelTitle:[],ShowList:null,form:{imsi:"",iccid:"",msisdn:""},pictureUrl:"",esimIccid:"",cooperationMode:"",sessionModal:!1}},computed:{},methods:{init:function(){this.msisdn=null===localStorage.getItem("backmsisdn")?"":localStorage.getItem("backmsisdn"),this.iccid=null===localStorage.getItem("backiccid")?"":localStorage.getItem("backiccid"),this.imsi=null===localStorage.getItem("backimsi")?"":localStorage.getItem("backimsi"),localStorage.removeItem("backimsi"),localStorage.removeItem("backiccid"),localStorage.removeItem("backmsisdn"),this.goPageFirst(1)},goPageFirst:function(t){var e=this;(this.msisdn||this.iccid||this.imsi)&&(this.currentPage=t,this.loading=!0,Object(n["F"])({userName:this.$store.state.user.userName}).then((function(a){e.corpId=a.data,Object(l["e"])({current:t,size:e.pageSize,msisdn:e.msisdn.replace(/\s/g,""),iccid:e.iccid.replace(/\s/g,""),imsi:e.imsi.replace(/\s/g,""),isNeedMcc:!0,corpId:e.corpId,cooperationMode:e.cooperationMode}).then((function(t){if(!t||"0000"!=t.code)throw t;if(null!=t.data&&t.data.length>0){e.tableData=t.data,e.ShowList=t.data[0];var a="";null!=e.ShowList.realCountry&&e.ShowList.realCountry.length>0&&e.ShowList.realCountry.map((function(t,i){var s="zh-CN"===e.$i18n.locale?t.countryCN.toString():"en-US"===e.$i18n.locale?t.countryEN.toString():"";a=""===a?a+""+s:a+", "+s}));var i=[{useStatus:null,authStatus:null}];e.realNameInfoData=t.data[0].realNameInfos?t.data[0].realNameInfos:i,e.tableData1=[{key:"MSISDN",value:e.ShowList.msisdn},{key:"ICCID",value:e.ShowList.iccid},{key:"IMSI",value:e.ShowList.imsi},{key:"PIN",value:e.ShowList.pin2},{key:"PUK",value:e.ShowList.puk1},{key:e.$t("support.cardtype"),value:"1"==e.ShowList.cardForm?e.$t("stock.PhysicalSIM"):"2"==e.ShowList.cardForm?e.$t("stock.eSIM"):"3"==e.ShowList.cardForm?e.$t("stock.TSIM"):"4"==e.ShowList.cardForm?e.$t("support.imsi"):""},{key:e.$t("support.cardstate"),value:"1"==e.ShowList.status?e.$t("order.Normal"):"2"==e.ShowList.status?e.$t("order.Terminated"):"3"==e.ShowList.status?e.$t("order.Suspend"):"4"==e.ShowList.status?e.$t("order.Expired"):""},{key:e.$t("support.template"),value:e.ShowList.templateName},{key:e.$t("support.Activation"),value:"1"==e.ShowList.activeType?e.$t("support.Automatic"):"2"==e.ShowList.activeType?e.$t("support.Manual"):" "},{key:e.$t("support.SIMDate"),value:e.ShowList.expireTime},{key:e.$t("support.Targeting"),value:"1"==e.ShowList.position?e.$t("support.Hcard"):"2"==e.ShowList.position?e.$t("support.Vcard"):""},{key:e.$t("support.position"),value:"zh-CN"===e.$i18n.locale?e.ShowList.local:"en-US"===e.$i18n.locale?e.ShowList.localEn:""}]}else e.ShowList=null,e.tableData=[],e.tableData1=[],e.realNameInfoData=[],e.$Notice.error({title:e.$t("address.Operationreminder"),desc:e.$t("support.querycontent")})})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1}))})).catch((function(t){console.error(t)})).finally((function(){})))},search:function(){this.msisdn||this.iccid||this.imsi?(sessionStorage.removeItem("pictureUrl"),this.goPageFirst(1)):this.$Notice.warning({title:this.$t("address.Operationreminder"),desc:this.$t("support.searchcondition")})},cancelModal:function(){this.localUpModal=!1,this.trafficModal=!1,this.smsModal=!1,this.esimModal=!1,this.sessionModal=!1},imgCancelModal:function(){this.imgModel=!1,this.pictureUrl=""},exportlocalUp:function(){var t=this;Object(d["o"])({imsi:this.localUpImsi,pageNumber:1,pageSize:-1}).then((function(e){if(!e||"0000"!=e.code)throw e;var a=e.data,i=a.records;i.map((function(e,a){return"1"==e.activeType?e.activeType=t.$t("support.Automatic"):"2"==e.activeType?e.activeType=t.$t("support.Manual"):e.activeType=" "}));var s=["reportTime","mcc","activeType"],n=["reportTime","mcc","activeType"],l=t.$t("support.Locationexport"),r={title:n,key:s,data:i,autoWidth:!0,filename:l};o["a"].export_array_to_excel(r)})).catch((function(t){console.log(t)})).finally((function(){}))},exportTrafficFile:function(){var t=this;Object(d["n"])({key:this.trafficImsi,pageNumber:1,pageSize:-1,type:1}).then((function(e){if(!e||"0000"!=e.code)throw e;var a=e.data,i=a.records,s=["useDate","flowCount","packageName"],n=["useDate","flowCount(G)","packageName"],l=t.$t("support.recordexport"),r={title:n,key:s,data:i,autoWidth:!0,filename:l};o["a"].export_array_to_excel(r)})).catch((function(t){console.log(t)})).finally((function(){}))},showTrafficModal:function(t){this.trafficImsi=t.imsi,this.form={imsi:t.imsi,iccid:t.iccid,msisdn:t.msisdn},this.trafficModal=!0},showEsimModal:function(t){this.esimIccid=t.iccid,this.esimModal=!0},goPage:function(t){this.goPageFirst(t)},goPurchasedPackage:function(t){this.$router.push({name:"purchased_package",query:{MSISDN:t.MSISDN,ICCID:t.ICCID,IMSI:t.IMSI}})},showLocalUpModal:function(t){this.localUpImsi=this.ShowList.imsi,this.localUpLocal="zh-CN"===this.$i18n.locale?this.ShowList.local:"en-US"===this.$i18n.locale?this.ShowList.localEn:"",this.localUpModal=!0},goLocationPackage:function(t){this.$router.push({name:"location_package",query:{MSISDN:t.MSISDN,ICCID:t.ICCID,IMSI:t.IMSI}})},showSmsModal:function(t){this.row=this.ShowList,this.smsModal=!0},generateQRCode:function(){var t=this;this.qrloading=!0,sessionStorage.pictureUrl?this.pictureUrl=JSON.parse(sessionStorage.getItem("pictureUrl")):Object(U["f"])({iccid:this.esimIccid}).then((function(e){var a=new Blob([e.data]);t.pictureUrl=window.URL.createObjectURL(a),sessionStorage.setItem("pictureUrl",JSON.stringify(t.pictureUrl)),t.pictureUrl=JSON.parse(sessionStorage.getItem("pictureUrl"))})),document.getElementById("img").oncontextmenu=stop,document.getElementById("img").oncopy=stop,this.qrloading=!1,this.imgModel=!0},showSessionModal:function(t){this.sessionModal=!0}},mounted:function(){this.cooperationMode=sessionStorage.getItem("cooperationMode"),this.init()},watch:{}},tt=Z,et=(a("d034"),Object(m["a"])(tt,i,s,!1,null,"25405608",null));e["default"]=et.exports},b12fe:function(t,e,a){},d034:function(t,e,a){"use strict";a("851c")},e3cd:function(t,e,a){}}]);