(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d237ad7"],{fbaf:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e._self._c;return t("Card",[t("div",[t("Form",{ref:"form",attrs:{"label-width":0,model:e.form,inline:""}},[t("FormItem",[t("Select",{staticStyle:{width:"300px","margin-right":"10px"},attrs:{filterable:"",clearable:"",placeholder:e.$t("onlineOrder.choosePaymentStatusPlaceholder")},on:{"on-change":e.handlePaymentStatusChange},model:{value:e.form.paymentStatus,callback:function(t){e.$set(e.form,"paymentStatus",t)},expression:"form.paymentStatus"}},e._l(e.paymentStatusList,(function(r){return t("Option",{key:r.value,attrs:{value:r.value}},[e._v(e._s(r.label))])})),1)],1),t("FormItem",[t("Input",{staticStyle:{width:"300px","margin-right":"10px"},attrs:{placeholder:e.$t("onlineOrder.thirdOrderNoPlaceholder"),clearable:!0},model:{value:e.form.thirdOrderNo,callback:function(t){e.$set(e.form,"thirdOrderNo",t)},expression:"form.thirdOrderNo"}})],1),t("FormItem",[t("Input",{staticClass:"inputSty",staticStyle:{width:"300px","margin-right":"10px"},attrs:{placeholder:e.$t("onlineOrder.thirdMchorderNoPlaceholder"),clearable:!0},model:{value:e.form.thirdMchorderNo,callback:function(t){e.$set(e.form,"thirdMchorderNo",t)},expression:"form.thirdMchorderNo"}})],1),t("FormItem",{attrs:{prop:"date"}},[t("DatePicker",{staticStyle:{width:"300px"},attrs:{format:"yyyy-MM-dd",type:"daterange",placeholder:e.$t("onlineOrder.chooseCreateDate")},on:{"on-change":e.handleDateChange},model:{value:e.createDate,callback:function(t){e.createDate=t},expression:"createDate"}})],1),t("FormItem",[t("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"md-search",loading:e.searchloading},on:{click:e.getOrderList}},[e._v(e._s(this.$t("common.search")))])],1)],1)],1),t("Table",{staticStyle:{width:"100%"},attrs:{columns:e.columns,data:e.orderList,loading:e.loading},scopedSlots:e._u([{key:"action",fn:function(r){var n=r.row;return[""!=e.corpId?t("div",{staticStyle:{display:"flex","justify-content":"flex-start","flex-wrap":"wrap","padding-top":"10px"}},["已过期"!=n.timmer&&n.timmer?t("div",[t("div",{staticStyle:{"margin-bottom":"10px"}},[e._v(e._s(e.$t("onlineOrder.payTxt01"))),t("span",{staticStyle:{color:"red"}},[e._v("\n              "+e._s(n.timmer))]),e._v(" "+e._s(e.$t("onlineOrder.payTxt02")))]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"pay",expression:"'pay'"}],staticStyle:{margin:"0 10px 10px 0"},attrs:{type:"primary"},on:{click:function(t){return e.pay(n)}}},[e._v(e._s(e.$t("onlineOrder.payBtn")))]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"cancel",expression:"'cancel'"}],staticStyle:{margin:"0 10px 10px 0"},attrs:{type:"error"},on:{click:function(t){return e.showCancelDialog(n)}}},[e._v(e._s(e.$t("common.cancel")))])],1):"EXPIRED"==n.paymentStatus||"CLOSED"==n.paymentStatus?t("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],staticStyle:{margin:"0 0 10px 0"},on:{click:function(t){return e.showDeleteDialog(n)}}},[e._v(e._s(e.$t("common.del")))]):t("div")],1):e._e()]}}])}),t("div",{staticStyle:{"margin-top":"50px"}},[t("Page",{attrs:{total:e.total,current:e.currentPage,"page-size":e.pageSize,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1),t("Modal",{attrs:{title:e.$t("onlineOrder.onlineModalTitle"),"mask-closable":!1,width:720},model:{value:e.payModal,callback:function(t){e.payModal=t},expression:"payModal"}},[e.payModal?t("PaymentComponent",{attrs:{orderType:1==e.payOrderDetail.orderType?"channelBilling":"deposit",corpId:e.payOrderDetail.corpId,billId:e.payOrderDetail.productId,showDeposit:!1,paySuccessInfo:e.paySuccessInfo,payLoading:e.payStatus,amount:e.amount.realIncome,currencyCode:e.amount.currencyCode},on:{onlinePay:e.onlinePay}}):e._e(),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"})],1)],1)},a=[],o=r("5530"),i=(r("99af"),r("d3b7"),r("6062"),r("1e70"),r("79a4"),r("c1a1"),r("8b00"),r("a4e7"),r("1e5a"),r("72c3"),r("3ca3"),r("159b"),r("ddb0"),r("66df")),l="/order/onlinePayment",c="/order/acPayment",s=function(e){return i["a"].request({url:l+"/getList",params:e,method:"get"})},d=function(e){return i["a"].request({url:l+"/deleteOrder",params:e,method:"post"})},u=function(e){return i["a"].request({url:c+"/closeOrder",params:e,method:"post"})},m=r("4fc8"),h=r("90de"),p=r("8bde"),y={components:{PaymentComponent:p["a"]},data:function(){var e=this;return{columns:[{title:this.$t("onlineOrder.orderUniqueId"),key:"orderId",align:"center",minWidth:200},{title:this.$t("onlineOrder.corpId"),key:"corpId",align:"center",minWidth:300},{title:this.$t("onlineOrder.productId"),key:"productId",align:"center",minWidth:150},{title:this.$t("onlineOrder.amount"),key:"amount",align:"center",minWidth:150,render:function(e,t){var r=t.row,n=r.amount/100;return e("label",n)}},{title:this.$t("onlineOrder.currencyCode"),key:"currencyCode",align:"center",minWidth:120},{title:this.$t("onlineOrder.paymentMethod"),key:"paymentMethod",align:"center",minWidth:150,render:function(t,r){var n=r.row,a="wechat"===n.paymentMethod?e.$t("onlineOrder.wechat"):"alipay"===n.paymentMethod?e.$t("onlineOrder.alipay"):e.$t("onlineOrder.card");return t("label",a)}},{title:this.$t("onlineOrder.paymentStatus"),key:"paymentStatus",align:"center",minWidth:150},{title:this.$t("onlineOrder.exprieTime"),key:"exprieTime",align:"center",minWidth:200,render:function(e,t){var r=t.row,n=Object(h["d"])(r.exprieTime,"Y-M-D h:m:s");return e("label",n)}},{title:this.$t("onlineOrder.thirdOrderNo"),key:"thirdOrderNo",align:"center",minWidth:200},{title:this.$t("onlineOrder.thirdMchorderNo"),key:"thirdMchorderNo",align:"center",minWidth:200},{title:this.$t("onlineOrder.createTime"),key:"createTime",align:"center",minWidth:200,render:function(e,t){var r=t.row,n=Object(h["d"])(r.createTime,"Y-M-D h:m:s");return e("label",n)}},{title:this.$t("sys.opt"),slot:"action",minWidth:210,align:"center",fixed:"right"}],orderTypeList:[{value:"1",label:this.$t("onlineOrder.orderTypeBill")},{value:"2",label:this.$t("onlineOrder.orderTypedeposit")}],paymentStatusList:[{value:"PAYING",label:this.$t("onlineOrder.paying")},{value:"SUCCESS",label:this.$t("onlineOrder.paySuccess")},{value:"EXPIRED",label:this.$t("onlineOrder.payExpired")},{value:"CLOSED",label:this.$t("onlineOrder.payclosed")}],options3:{},form:{thirdOrderNo:"",thirdTransactionNo:"",thirdMchorderNo:"",paymentStatus:"",createStartTime:"",createEndTime:""},createDate:[],orderList:[],currentPage:1,pageSize:10,total:0,loading:!1,cancelDialogVisible:!1,deleteDialogVisible:!1,selectedOrder:null,searchloading:!1,addedCountdownDisplay:new Set,amount:{},payModal:!1,currency:"",payOrderDetail:{},paySuccessInfo:{},payStatus:!1,corpId:""}},mounted:function(){this.getOrderList(),this.corpId=sessionStorage.getItem("corpId")&&"undefined"!=sessionStorage.getItem("corpId")?sessionStorage.getItem("corpId"):""},beforeDestroy:function(){this.timer&&clearInterval(this.timer)},methods:{handlePaymentStatusChange:function(e){e&&(this.createDate=[],this.form.createEndTime="",this.form.createStartTime="")},handleDateChange:function(e){console.log(e[0]),this.form.createStartTime=e[0],this.form.createEndTime=e[1]},getOrderList:function(){var e=this;this.loading=!0;var t=Object(o["a"])({corpId:sessionStorage.getItem("corpId")&&"undefined"!=sessionStorage.getItem("corpId")?sessionStorage.getItem("corpId"):this.$store.state.user.roleId,pageNumber:this.currentPage,pageSize:this.pageSize},this.form);s(t).then((function(t){t.data.records.forEach((function(e){e.timmer=""})),e.orderList=t.data.records,e.startCountdown(),e.total=t.data.total,e.loading=!1})).catch((function(t){console.error("获取订单列表失败",t),e.loading=!1}))},goPage:function(e){this.currentPage=e,this.getOrderList()},showCancelDialog:function(e){var t=this;this.selectedOrder=e,this.$Modal.confirm({title:this.$t("common.cancel"),content:this.$t("onlineOrder.closeOrderContent"),cancelText:this.$t("common.cancel"),onOk:function(){u({id:e.id}).then((function(e){"0000"==e.code&&(t.$Message.success(t.$t("onlineOrder.closeSuccess")),t.getOrderList())})).catch((function(e){console.error("删除订单失败",e)}))},onCancel:function(){}})},showDeleteDialog:function(e){var t=this;this.selectedOrder=e,this.$Modal.confirm({title:this.$t("common.del"),content:this.$t("onlineOrder.deleteOrderContent"),cancelText:this.$t("common.cancel"),onOk:function(){d({id:e.id}).then((function(e){"0000"==e.code&&(t.$Message.success(t.$t("onlineOrder.deleteSuccess")),t.getOrderList())})).catch((function(e){console.error("删除订单失败",e)}))},onCancel:function(){}})},searchOrders:function(){this.currentPage=1,this.getOrderList()},formatCountdown:function(e){var t=new Date,r=e-t;if(r<=0)return"已过期";var n=Math.floor(r/36e5),a=Math.floor(r%36e5/6e4),o=Math.floor(r%6e4/1e3),i="";return n>0&&(i+="".concat(n,"时")),i+="".concat(a<10?"0":"").concat(a,"分").concat(o<10?"0":"").concat(o,"秒"),i},startCountdown:function(){var e=this,t=this.orderList.some((function(t){var r=new Date(t.exprieTime);return"已过期"!==e.formatCountdown(r)}));t?this.timer=setInterval((function(){var t=!0;e.orderList.forEach((function(r){if("PAYING"==r.paymentStatus){var n=new Date(r.exprieTime);r.timmer=e.formatCountdown(n),"已过期"!==r.timmer?t=!1:r.paymentStatus="EXPIRED"}})),t&&(clearInterval(e.timer),e.timer=null,console.log("所有订单都已过期，定时器已清除。"))}),1e3):console.log("所有订单都已过期，不启动定时器。")},pay:function(e){this.payOrderDetail=e,console.log(e),this.amount.realIncome=e.amount/100;var t="CNY"==e.currencyCode?"156":"USD"==e.currencyCode?"840":"HKD"==e.currencyCode?"344":"--";this.amount.currencyCode=t,this.payModal=!0},onlinePay:function(e){var t=this;if(this.payStatus)console.log("请求中...");else{this.payStatus=!0,console.log("支付信息：",e);var r={payOrderNo:this.payOrderDetail.thirdOrderNo,paymentMethod:e.paymentMethod,adyenStateData:e.adyenStateData,orderType:1==this.payOrderDetail.orderType?"channelBilling":"deposit",billId:this.payOrderDetail.productId,language:localStorage.getItem("local"),corpId:sessionStorage.getItem("corpId")};Object(m["c"])(r).then((function(e){console.log(e.data,"订购详情"),"0000"==e.code&&(t.payStatus=!1,t.paySuccessInfo=e.data,t.payModal=!1,t.payCont=e.data.payUrl,console.log(e.data.redirectUrl),document.querySelector("body").innerHTML=e.data.payUrl,document.forms[0].submit())})).catch((function(e){console.error("请求失败:",e),t.payStatus=!1}))}}}},f=y,g=r("2877"),O=Object(g["a"])(f,n,a,!1,null,"382c9ad0",null);t["default"]=O.exports}}]);