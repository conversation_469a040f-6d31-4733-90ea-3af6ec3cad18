<template>
  <!-- AQ码管理 -->
  <ContentWrap>
    <el-card>
      <div class="search-container">
        <el-form :model="searchForm" inline>
          <el-form-item label="AQ码：">
            <el-input 
              v-model="searchForm.aqCode" 
              placeholder="请输入AQ码" 
              clearable
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item label="状态：">
            <el-select 
              v-model="searchForm.status" 
              placeholder="请选择状态"
              clearable
              style="width: 150px;"
            >
              <el-option :value="1" label="可用" />
              <el-option :value="2" label="已使用" />
              <el-option :value="3" label="已过期" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间段：">
            <el-date-picker
              v-model="searchForm.timeRange"
              type="daterange"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 240px;"
            />
          </el-form-item>
          <el-form-item>
            <el-button 
              v-if="hasPermission('search')"
              type="primary" 
              :loading="searchLoading"
              @click="handleSearch"
            >
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button 
              v-if="hasPermission('generate')"
              type="success"
              @click="generateAQCode"
            >
              <Icon icon="ep:plus" class="mr-5px" />
              生成AQ码
            </el-button>
            <el-button 
              v-if="hasPermission('export')"
              type="warning" 
              :loading="exportLoading"
              @click="exportData"
            >
              <Icon icon="ep:download" class="mr-5px" />
              导出
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 表格 -->
      <div style="margin-top: 20px;">
        <el-table :data="tableData" v-loading="loading" border>
          <el-table-column prop="aqCode" label="AQ码" min-width="150" />
          <el-table-column prop="packageName" label="关联套餐" min-width="150" />
          <el-table-column prop="amount" label="金额" min-width="100" align="right" />
          <el-table-column prop="validDays" label="有效期(天)" min-width="100" align="center" />
          <el-table-column prop="status" label="状态" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusTag(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="160" />
          <el-table-column prop="useTime" label="使用时间" min-width="160">
            <template #default="{ row }">
              {{ row.useTime || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="expireTime" label="过期时间" min-width="160" />
          <el-table-column label="操作" min-width="120" align="center" fixed="right">
            <template #default="{ row }">
              <el-button 
                v-if="row.status === 1 && hasPermission('disable')"
                type="danger" 
                size="small"
                @click="disableAQCode(row)"
              >
                禁用
              </el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right;">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>

    <!-- 生成AQ码弹窗 -->
    <el-dialog
      v-model="generateVisible"
      title="生成AQ码"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="generateForm" :rules="generateRules" ref="generateFormRef" label-width="120px">
        <el-form-item label="关联套餐：" prop="packageId">
          <el-select 
            v-model="generateForm.packageId" 
            placeholder="请选择套餐"
            style="width: 100%;"
          >
            <el-option
              v-for="item in packageList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="生成数量：" prop="count">
          <el-input-number
            v-model="generateForm.count"
            :min="1"
            :max="1000"
            placeholder="请输入生成数量"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="有效期(天)：" prop="validDays">
          <el-input-number
            v-model="generateForm.validDays"
            :min="1"
            :max="365"
            placeholder="请输入有效期"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="备注：">
          <el-input
            v-model="generateForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="generateVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmGenerate" :loading="generateLoading">生成</el-button>
      </template>
    </el-dialog>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

// 权限检查函数
const hasPermission = (permission: string): boolean => {
  return true // TODO: 实现权限检查逻辑
}

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const exportLoading = ref(false)
const generateLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const generateVisible = ref(false)

const searchForm = reactive({
  aqCode: '',
  status: null as number | null,
  timeRange: [] as string[]
})

const generateForm = reactive({
  packageId: '',
  count: 10,
  validDays: 30,
  remark: ''
})

const generateRules = {
  packageId: [
    { required: true, message: '请选择套餐', trigger: 'change' }
  ],
  count: [
    { required: true, message: '请输入生成数量', trigger: 'blur' }
  ],
  validDays: [
    { required: true, message: '请输入有效期', trigger: 'blur' }
  ]
}

const tableData = ref<any[]>([])
const packageList = ref<any[]>([])
const generateFormRef = ref()

// 方法
const getStatusTag = (status: number) => {
  const statusMap: Record<number, string> = {
    1: 'success',
    2: 'info',
    3: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '可用',
    2: '已使用',
    3: '已过期'
  }
  return statusMap[status] || '未知'
}

const handleSearch = () => {
  currentPage.value = 1
  getTableData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getTableData()
}

const generateAQCode = () => {
  generateVisible.value = true
}

const confirmGenerate = async () => {
  try {
    await generateFormRef.value?.validate()
    generateLoading.value = true
    
    // TODO: 实现生成AQ码API调用
    ElMessage.success(`成功生成 ${generateForm.count} 个AQ码`)
    generateVisible.value = false
    getTableData()
  } catch (error) {
    ElMessage.error('生成失败')
  } finally {
    generateLoading.value = false
  }
}

const disableAQCode = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要禁用AQ码 ${row.aqCode} 吗？`,
      '确认禁用',
      {
        type: 'warning'
      }
    )
    
    // TODO: 实现禁用API调用
    ElMessage.success('禁用成功')
    getTableData()
  } catch (error) {
    // 用户取消操作
  }
}

const exportData = async () => {
  try {
    exportLoading.value = true
    // TODO: 实现导出功能
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        aqCode: 'AQ20240101001',
        packageName: '基础套餐',
        amount: 100.00,
        validDays: 30,
        status: 1,
        createTime: '2024-01-01 10:00:00',
        useTime: null,
        expireTime: '2024-01-31 23:59:59'
      }
    ]
    total.value = 1
  } catch (error) {
    ElMessage.error('获取AQ码数据失败')
  } finally {
    loading.value = false
  }
}

// 获取套餐列表
const getPackageList = async () => {
  try {
    // TODO: 实现API调用
    packageList.value = [
      { id: '1', name: '基础套餐' },
      { id: '2', name: '高级套餐' }
    ]
  } catch (error) {
    ElMessage.error('获取套餐列表失败')
  }
}

// 生命周期
onMounted(() => {
  getTableData()
  getPackageList()
})
</script>

<style scoped>
.search-container {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}
</style>
