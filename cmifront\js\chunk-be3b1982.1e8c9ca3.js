(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-be3b1982"],{"0fc3":function(e,t,a){"use strict";a.d(t,"c",(function(){return n})),a.d(t,"d",(function(){return l})),a.d(t,"e",(function(){return c})),a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return p}));var i=a("66df"),r="pms/api/v1/directional",n=function(e){return i["a"].request({url:r+"/getDirectionalApp",params:e,method:"get"})},l=function(e){return i["a"].request({url:r+"/newDirectionalApp",data:e,method:"POST"})},c=function(e){return i["a"].request({url:r+"/updateDirectionalApp",data:e,method:"PUT"})},o=function(e){return i["a"].request({url:r+"/delDirectionalApp",params:e,method:"delete"})},p=function(e){return i["a"].request({url:r+"/getAppInfo",data:e,method:"POST"})}},1777:function(e,t,a){"use strict";a.r(t);a("498a");var i=function(){var e=this,t=e._self._c;return t("Card",[t("div",{staticClass:"search_head_i"},[t("div",{staticClass:"search_box"},[t("span",{staticClass:"search_box_label"},[e._v("应用名称:")]),t("Input",{staticStyle:{width:"250px"},attrs:{clearable:"",placeholder:"请输入应用名称"},model:{value:e.appName,callback:function(t){e.appName="string"===typeof t?t.trim():t},expression:"appName"}})],1),t("div",{staticStyle:{width:"110px",display:"flex","justify-content":"center","margin-bottom":"20px"}},[t("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",loading:e.searchloading},on:{click:function(t){return e.searchOne()}}},[e._v("搜索")])],1),t("div",{staticStyle:{width:"110px",display:"flex","justify-content":"center","margin-bottom":"20px"}},[t("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],attrs:{type:"info",icon:"md-add"},on:{click:e.addItem}},[e._v("新增")])],1)]),t("Table",{staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:e.columns,data:e.data,loading:e.loading},scopedSlots:e._u([{key:"action",fn:function(a){var i=a.row;a.index;return[t("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"20px"},attrs:{type:"primary",ghost:""},on:{click:function(t){return e.updateItem(i)}}},[e._v("修改")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],attrs:{type:"error",ghost:""},on:{click:function(t){return e.delItem(i)}}},[e._v("删除")])]}}])}),t("div",{staticStyle:{"margin-top":"15px"}},[t("Page",{attrs:{total:e.total,current:e.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1),t("Modal",{attrs:{title:e.title,"mask-closable":!1,width:"670px"},on:{"on-cancel":e.cancelModal},model:{value:e.modal1,callback:function(t){e.modal1=t},expression:"modal1"}},[t("Form",{ref:"formObj",staticStyle:{padding:"10px"},attrs:{model:e.formObj,rules:e.ruleAddValidate,"label-width":130}},[t("FormItem",{attrs:{label:"应用名称:",prop:"appName"}},[t("Input",{staticStyle:{width:"350px"},attrs:{clearable:"",placeholder:"请输入应用名称"},model:{value:e.formObj.appName,callback:function(t){e.$set(e.formObj,"appName","string"===typeof t?t.trim():t)},expression:"formObj.appName"}})],1),t("FormItem",{attrs:{label:"RG值",prop:"rg"}},[t("Input",{staticStyle:{width:"350px"},attrs:{maxlength:"10",clearable:"",placeholder:"请输入RG值"},model:{value:e.formObj.rg,callback:function(t){e.$set(e.formObj,"rg","string"===typeof t?t.trim():t)},expression:"formObj.rg"}})],1),t("div",{staticStyle:{display:"flex","flex-wrap":"nowrap"}},[t("div",e._l(e.formObj.highSpeedUpccTemplate,(function(a,i){return t("div",{key:i,staticStyle:{display:"flex"}},[t("FormItem",{attrs:{label:"UPCC签约模板",prop:"highSpeedUpccTemplate."+i+".templateId",rules:{required:!0,message:"UPCC模板不能为空",trigger:"blur"}}},[t("Select",{staticStyle:{width:"350px"},attrs:{filterable:"",placeholder:"请选择UPCC签约模板",clearable:""},model:{value:a.templateId,callback:function(t){e.$set(a,"templateId",t)},expression:"item.templateId"}},e._l(e.choseUpccTemplateId(a.templateId),(function(a,i){return t("Option",{key:a.templateId,attrs:{title:a.templateName,value:a.templateId}},[e._v(e._s(a.templateName.length>40?a.templateName.substring(0,40):a.templateName))])})),1)],1),"Add"==e.typeFlag&&0!=i||"Update"==e.typeFlag&&i!=e.formObj.highSpeedUpccTemplate.length-1?t("Button",{staticStyle:{"margin-left":"10px","margin-top":"5px",width:"40px",height:"25px"},attrs:{type:"error",size:"small"},on:{click:function(t){return e.removeTemplate(i)}}},[e._v("删除")]):e._e()],1)})),0),t("div",[t("Button",{staticStyle:{"margin-left":"10px","margin-top":"5px"},attrs:{type:"info",size:"small"},on:{click:e.addTemplate}},[e._v("增加")])],1)]),t("FormItem",{attrs:{label:"低速UPCC签约模板",prop:"lowSpeedUpccTemplate"}},[t("Select",{staticStyle:{width:"350px"},attrs:{filterable:"",placeholder:"请选择低速UPCC签约模板",clearable:""},model:{value:e.formObj.lowSpeedUpccTemplate,callback:function(t){e.$set(e.formObj,"lowSpeedUpccTemplate",t)},expression:"formObj.lowSpeedUpccTemplate"}},e._l(e.upccTemplateList,(function(a,i){return t("Option",{key:a.templateId,attrs:{title:a.templateName,value:a.templateId}},[e._v(e._s(a.templateName.length>40?a.templateName.substring(0,40):a.templateName))])})),1)],1)],1),t("div",{staticClass:"footer_wrap",attrs:{slot:"footer"},slot:"footer"},[t("Button",{attrs:{icon:"ios-arrow-back"},on:{click:e.cancelModal}},[e._v("返回")]),t("Button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:e.submit}},[e._v("确定")])],1)],1)],1)},r=[],n=(a("4de4"),a("d81d"),a("14d9"),a("a434"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("159b"),a("0fc3")),l=a("6e08"),c={data:function(){var e=this;return{total:0,currentPage:1,page:0,appName:"",loading:!1,searchloading:!1,data:[],columns:[{title:"应用名称",key:"appName",minWidth:150,align:"center",tooltip:!0},{title:"RG值",key:"rg",minWidth:150,align:"center",tooltip:!0},{title:"UPCC签约模板",key:"highSpeedUpccTemplate",minWidth:250,align:"center",tooltip:!0,render:function(t,a){var i=a.row,r="";return null!=i.highSpeedUpccTemplate&&(r=i.highSpeedUpccTemplate[0].templateName),null!=i.highSpeedUpccTemplate&&i.highSpeedUpccTemplate.length>1?t("div",[t("Tooltip",{props:{placement:"bottom",transfer:!0},style:{cursor:"pointer"}},[t("span",{style:{display:"block"}},i.highSpeedUpccTemplate[0].templateName),t("span",{style:{display:"block"}},i.highSpeedUpccTemplate[1].templateName),t("span",{style:{display:"block"}},"……"),t("ul",{slot:"content",style:{whiteSpace:"normal",wordBreak:"break-all",listStyle:"none"}},e.data[a.index].highSpeedUpccTemplate.map((function(e){return t("li",e.templateName)})))])]):(r=r,t("label",r))}},{title:"操作",slot:"action",minWidth:180,align:"center",fixed:"right"}],modal1:!1,submitLoading:!1,typeFlag:"",title:"",index:0,formObj:{appName:"",rg:"",highSpeedUpccTemplate:[{index:0,templateId:""}],lowSpeedUpccTemplate:""},id:"",upccTemplateList:[],ruleAddValidate:{appName:[{required:!0,type:"string",message:"应用名称不能为空"}],rg:[{required:!0,message:"RG值不能为空"},{pattern:/^[0-9]\d*$/,message:"请输入纯数字",trigger:"blur"}],lowSpeedUpccTemplate:[{required:!0,type:"string",message:"低速UPCC签约模板不能为空"}]}}},mounted:function(){this.goPageFirst(1),this.getUpccTemplateList()},computed:{choseUpccTemplateId:function(){var e=this;return function(t){var a=JSON.parse(JSON.stringify(e.upccTemplateList)),i=e.formObj.highSpeedUpccTemplate.map((function(e){return e.templateId}));return a=a.filter((function(e){return t==e.templateId||-1==i.indexOf(e.templateId)?e:void 0})),a}}},methods:{goPageFirst:function(e){var t=this;this.loading=!0;var a=this;Object(n["c"])({pageNum:e,pageSize:10,appName:this.appName,corpId:this.$store.state.user.userId}).then((function(i){"0000"==i.code&&(a.loading=!1,t.searchloading=!1,t.page=e,t.currentPage=e,t.total=i.data.total,t.data=i.data.records)})).catch((function(e){console.error(e)})).finally((function(){a.loading=!1,t.searchloading=!1}))},searchOne:function(){this.searchloading=!0,this.goPageFirst(1)},goPage:function(e){this.goPageFirst(e)},addItem:function(){this.typeFlag="Add",this.title="新增应用",this.modal1=!0},updateItem:function(e){this.typeFlag="Update",this.title="修改应用",this.formObj=JSON.parse(JSON.stringify(e)),this.modal1=!0,this.id=e.id;var t=JSON.parse(JSON.stringify(e.highSpeedUpccTemplate));t.forEach((function(e){delete e.rate,delete e.unit,delete e.templateName})),this.$set(this.formObj,"highSpeedUpccTemplate",t)},delItem:function(e){var t=this,a={id:e.id,appName:e.appName};this.$Modal.confirm({title:"确认删除？",onOk:function(){Object(n["a"])(a).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.goPageFirst(1)})).catch((function(e){t.$Notice.error({title:"操作提示",desc:"操作失败"})}))}})},removeTemplate:function(e){this.formObj.highSpeedUpccTemplate.splice(e,1),this.index--},addTemplate:function(){this.index++,this.formObj.highSpeedUpccTemplate.length<this.upccTemplateList.length?this.formObj.highSpeedUpccTemplate.push({index:this.index,templateId:""}):this.$Notice.open({title:"操作提醒：",desc:"不能再添加了 ！"})},cancelModal:function(){this.modal1=!1,this.$refs["formObj"].resetFields(),this.formObj.highSpeedUpccTemplate=[{index:0,templateId:""}],this.id=""},submit:function(){var e=this;this.$refs["formObj"].validate((function(t){if(t){e.submitLoading=!0;var a=[];e.formObj.highSpeedUpccTemplate.forEach((function(e){a.push(e.templateId)}));var i="Add"==e.typeFlag?n["d"]:n["e"];i({id:e.id,appName:e.formObj.appName,rg:Number(e.formObj.rg),highSpeedUpccTemplate:a,lowSpeedUpccTemplate:e.formObj.lowSpeedUpccTemplate}).then((function(t){if(!t||"0000"!=t.code)throw e.submitLoading=!1,t;e.$Notice.success({title:"操作提示",desc:"操作成功"}),setTimeout((function(){e.submitLoading=!1,e.cancelModal(),e.goPageFirst(1)}),1500)})).catch((function(t){e.submitLoading=!1}))}}))},getUpccTemplateList:function(){var e=this;Object(l["c"])({pageNum:-1,pageSize:-1}).then((function(t){if(!t||"0000"!=t.code)throw t;e.upccTemplateList=t.data.records})).catch((function(e){}))}}},o=c,p=(a("fd72"),a("2877")),s=Object(p["a"])(o,i,r,!1,null,"660ce219",null);t["default"]=s.exports},"6e08":function(e,t,a){"use strict";a.d(t,"c",(function(){return n})),a.d(t,"b",(function(){return l})),a.d(t,"a",(function(){return c})),a.d(t,"d",(function(){return o}));var i=a("66df"),r="/pms/api/v1/upccTemplate",n=function(e){return i["a"].request({url:r+"/getUpccTemplate",data:e,method:"post"})},l=function(e){return i["a"].request({url:r+"/delUpccTemplate",data:e,method:"delete"})},c=function(e){return i["a"].request({url:r+"/newUpccTemplate",data:e,method:"post"})},o=function(e){return i["a"].request({url:r+"/updateUpccTemplate",data:e,method:"PUT"})}},e83d:function(e,t,a){},fd72:function(e,t,a){"use strict";a("e83d")}}]);