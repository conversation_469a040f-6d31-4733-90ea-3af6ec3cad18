(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d07893e4"],{"00b4":function(e,t,n){"use strict";n("ac1f");var a=n("23e7"),s=n("c65b"),r=n("1626"),i=n("825a"),o=n("577e"),c=function(){var e=!1,t=/[ac]/;return t.exec=function(){return e=!0,/./.exec.apply(this,arguments)},!0===t.test("abc")&&e}(),l=/./.test;a({target:"RegExp",proto:!0,forced:!c},{test:function(e){var t=i(this),n=o(e),a=t.exec;if(!r(a))return s(l,t,n);var c=s(a,t,n);return null!==c&&(i(c),!0)}})},"028c":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i}));var a=n("66df"),s="/sys/api/v1",r=function(e){return a["a"].request({url:s+"/notice/query",data:e,method:"post"})},i=function(e){return a["a"].request({url:s+"/notice/edit",data:e,method:"post"})}},"0af8":function(e,t,n){e.exports=n.p+"img/news.b451579a.png"},"129f":function(e,t,n){"use strict";e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!==e&&t!==t}},"271a":function(e,t,n){"use strict";var a=n("cb2d"),s=n("e330"),r=n("577e"),i=n("d6d6"),o=URLSearchParams,c=o.prototype,l=s(c.getAll),d=s(c.has),u=new o("a=1");!u.has("a",2)&&u.has("a",void 0)||a(c,"has",(function(e){var t=arguments.length,n=t<2?void 0:arguments[1];if(t&&void 0===n)return d(this,e);var a=l(this,e);i(t,1);var s=r(n),o=0;while(o<a.length)if(a[o++]===s)return!0;return!1}),{enumerable:!0,unsafe:!0})},"30af":function(e,t,n){"use strict";var a=n("44c9"),s=n.n(a);t["default"]=s.a},"37e3":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAAAsTAAALEwEAmpwYAAABkElEQVRoge2XO0vEQBSFx04bn41Y+db9AwqCuPprBEUQLCy2sfAfaC9WImgtVnaKNq74KHZlwWbFatFKGz0DKdbkjplNbnID3gMfLMnk3HPI7ECMUakKpRKogAvQBJ8BzeCavTcrlO1PLYMb8O3JNShLBA1rCJwa/+BhTsBg7qkDjYOnmIA+1MB0ztnNFHiLCdYCDwGtmLWvYCKv8D3g0RHkC+yBOdDV9oz9PQ/2gzXUs/egO48Cu44At8ZvK9hTqOrw2Mkg7y8NgHdi8CXo7cDHrr0ifOxW62PMG1HFMXQ0gdeYof8b2xxBXXohBm6m8Nsi/BopMzpVIobZ7ZTmlfeDD8I3k2N1gxh0xOB7TPiuM/hGdEgMWmXwXSN8Dxh8IzonBi0w+C4SvmcMvhHdEYMmGXxnCN8qg29EDWLQMIPvCOGbyUmkBRzKrID9SHkmzPPGZlhJUqAI4dtLdCzp0GG0gDRaQBotII0WkEYLSKMFpNEC0mgBabSANP+vQJE+6mtJCpRBvSDhl5IUUKk89AMN8S3IhBaCgwAAAABJRU5ErkJggg=="},4104:function(e,t,n){},"44c9":function(e,t){},"4b58":function(e,t,n){e.exports=n.p+"img/background.16a8bd5c.jpeg"},"519f":function(e,t,n){},5352:function(e,t,n){"use strict";n("e260"),n("f6d6");var a=n("23e7"),s=n("cfe9"),r=n("157a"),i=n("d066"),o=n("c65b"),c=n("e330"),l=n("83ab"),d=n("f354"),u=n("cb2d"),f=n("edd0"),p=n("6964"),h=n("d44e"),m=n("dcc3"),g=n("69f3"),v=n("19aa"),b=n("1626"),w=n("1a2d"),y=n("0366"),x=n("f5df"),C=n("825a"),S=n("861d"),k=n("577e"),$=n("7c73"),I=n("5c6c"),_=n("9a1f"),O=n("35a1"),P=n("4754"),B=n("d6d6"),A=n("b622"),T=n("addb"),L=A("iterator"),U="URLSearchParams",j=U+"Iterator",N=g.set,R=g.getterFor(U),z=g.getterFor(j),E=r("fetch"),F=r("Request"),M=r("Headers"),q=F&&F.prototype,D=M&&M.prototype,W=s.TypeError,H=s.encodeURIComponent,V=String.fromCharCode,G=i("String","fromCodePoint"),Y=parseInt,Q=c("".charAt),J=c([].join),K=c([].push),X=c("".replace),Z=c([].shift),ee=c([].splice),te=c("".split),ne=c("".slice),ae=c(/./.exec),se=/\+/g,re="�",ie=/^[0-9a-f]+$/i,oe=function(e,t){var n=ne(e,t,t+2);return ae(ie,n)?Y(n,16):NaN},ce=function(e){for(var t=0,n=128;n>0&&0!==(e&n);n>>=1)t++;return t},le=function(e){var t=null;switch(e.length){case 1:t=e[0];break;case 2:t=(31&e[0])<<6|63&e[1];break;case 3:t=(15&e[0])<<12|(63&e[1])<<6|63&e[2];break;case 4:t=(7&e[0])<<18|(63&e[1])<<12|(63&e[2])<<6|63&e[3];break}return t>1114111?null:t},de=function(e){e=X(e,se," ");var t=e.length,n="",a=0;while(a<t){var s=Q(e,a);if("%"===s){if("%"===Q(e,a+1)||a+3>t){n+="%",a++;continue}var r=oe(e,a+1);if(r!==r){n+=s,a++;continue}a+=2;var i=ce(r);if(0===i)s=V(r);else{if(1===i||i>4){n+=re,a++;continue}var o=[r],c=1;while(c<i){if(a++,a+3>t||"%"!==Q(e,a))break;var l=oe(e,a+1);if(l!==l){a+=3;break}if(l>191||l<128)break;K(o,l),a+=2,c++}if(o.length!==i){n+=re;continue}var d=le(o);null===d?n+=re:s=G(d)}}n+=s,a++}return n},ue=/[!'()~]|%20/g,fe={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},pe=function(e){return fe[e]},he=function(e){return X(H(e),ue,pe)},me=m((function(e,t){N(this,{type:j,target:R(e).entries,index:0,kind:t})}),U,(function(){var e=z(this),t=e.target,n=e.index++;if(!t||n>=t.length)return e.target=null,P(void 0,!0);var a=t[n];switch(e.kind){case"keys":return P(a.key,!1);case"values":return P(a.value,!1)}return P([a.key,a.value],!1)}),!0),ge=function(e){this.entries=[],this.url=null,void 0!==e&&(S(e)?this.parseObject(e):this.parseQuery("string"==typeof e?"?"===Q(e,0)?ne(e,1):e:k(e)))};ge.prototype={type:U,bindURL:function(e){this.url=e,this.update()},parseObject:function(e){var t,n,a,s,r,i,c,l=this.entries,d=O(e);if(d){t=_(e,d),n=t.next;while(!(a=o(n,t)).done){if(s=_(C(a.value)),r=s.next,(i=o(r,s)).done||(c=o(r,s)).done||!o(r,s).done)throw new W("Expected sequence with length 2");K(l,{key:k(i.value),value:k(c.value)})}}else for(var u in e)w(e,u)&&K(l,{key:u,value:k(e[u])})},parseQuery:function(e){if(e){var t,n,a=this.entries,s=te(e,"&"),r=0;while(r<s.length)t=s[r++],t.length&&(n=te(t,"="),K(a,{key:de(Z(n)),value:de(J(n,"="))}))}},serialize:function(){var e,t=this.entries,n=[],a=0;while(a<t.length)e=t[a++],K(n,he(e.key)+"="+he(e.value));return J(n,"&")},update:function(){this.entries.length=0,this.parseQuery(this.url.query)},updateURL:function(){this.url&&this.url.update()}};var ve=function(){v(this,be);var e=arguments.length>0?arguments[0]:void 0,t=N(this,new ge(e));l||(this.size=t.entries.length)},be=ve.prototype;if(p(be,{append:function(e,t){var n=R(this);B(arguments.length,2),K(n.entries,{key:k(e),value:k(t)}),l||this.length++,n.updateURL()},delete:function(e){var t=R(this),n=B(arguments.length,1),a=t.entries,s=k(e),r=n<2?void 0:arguments[1],i=void 0===r?r:k(r),o=0;while(o<a.length){var c=a[o];if(c.key!==s||void 0!==i&&c.value!==i)o++;else if(ee(a,o,1),void 0!==i)break}l||(this.size=a.length),t.updateURL()},get:function(e){var t=R(this).entries;B(arguments.length,1);for(var n=k(e),a=0;a<t.length;a++)if(t[a].key===n)return t[a].value;return null},getAll:function(e){var t=R(this).entries;B(arguments.length,1);for(var n=k(e),a=[],s=0;s<t.length;s++)t[s].key===n&&K(a,t[s].value);return a},has:function(e){var t=R(this).entries,n=B(arguments.length,1),a=k(e),s=n<2?void 0:arguments[1],r=void 0===s?s:k(s),i=0;while(i<t.length){var o=t[i++];if(o.key===a&&(void 0===r||o.value===r))return!0}return!1},set:function(e,t){var n=R(this);B(arguments.length,1);for(var a,s=n.entries,r=!1,i=k(e),o=k(t),c=0;c<s.length;c++)a=s[c],a.key===i&&(r?ee(s,c--,1):(r=!0,a.value=o));r||K(s,{key:i,value:o}),l||(this.size=s.length),n.updateURL()},sort:function(){var e=R(this);T(e.entries,(function(e,t){return e.key>t.key?1:-1})),e.updateURL()},forEach:function(e){var t,n=R(this).entries,a=y(e,arguments.length>1?arguments[1]:void 0),s=0;while(s<n.length)t=n[s++],a(t.value,t.key,this)},keys:function(){return new me(this,"keys")},values:function(){return new me(this,"values")},entries:function(){return new me(this,"entries")}},{enumerable:!0}),u(be,L,be.entries,{name:"entries"}),u(be,"toString",(function(){return R(this).serialize()}),{enumerable:!0}),l&&f(be,"size",{get:function(){return R(this).entries.length},configurable:!0,enumerable:!0}),h(ve,U),a({global:!0,constructor:!0,forced:!d},{URLSearchParams:ve}),!d&&b(M)){var we=c(D.has),ye=c(D.set),xe=function(e){if(S(e)){var t,n=e.body;if(x(n)===U)return t=e.headers?new M(e.headers):new M,we(t,"content-type")||ye(t,"content-type","application/x-www-form-urlencoded;charset=UTF-8"),$(e,{body:I(0,k(n)),headers:I(0,t)})}return e};if(b(E)&&a({global:!0,enumerable:!0,dontCallGetSet:!0,forced:!0},{fetch:function(e){return E(e,arguments.length>1?xe(arguments[1]):{})}}),b(F)){var Ce=function(e){return v(this,q),new F(e,arguments.length>1?xe(arguments[1]):{})};q.constructor=Ce,Ce.prototype=q,a({global:!0,constructor:!0,dontCallGetSet:!0,forced:!0},{Request:Ce})}}e.exports={URLSearchParams:ve,getState:R}},5494:function(e,t,n){"use strict";var a=n("83ab"),s=n("e330"),r=n("edd0"),i=URLSearchParams.prototype,o=s(i.forEach);a&&!("size"in i)&&r(i,"size",{get:function(){var e=0;return o(this,(function(){e++})),e},configurable:!0,enumerable:!0})},"59ed3":function(e,t,n){e.exports=n.p+"img/cmLink-header.f47723c5.png"},"5d38":function(e,t,n){"use strict";var a=n("7cd1"),s=n("5f69"),r=n("2877"),i=Object(r["a"])(s["default"],a["a"],a["b"],!1,null,null,null);t["default"]=i.exports},"5f69":function(e,t,n){"use strict";var a=n("7e63"),s=n.n(a);t["default"]=s.a},"631c":function(e,t,n){"use strict";var a=n("dc67"),s=n("30af"),r=n("2877"),i=Object(r["a"])(s["default"],a["a"],a["b"],!1,null,null,null);t["default"]=i.exports},"771d":function(e,t,n){"use strict";n("4104")},"7cd1":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return s}));var a=function(){var e=this;e._self._c,e._self._setupProxy;return e._m(0)},s=[function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticStyle:{padding:"10px 10px"}},[t("h4",{staticStyle:{"text-align":"left"}},[e._v("确保口令满足以下通用原则")]),t("div",{staticStyle:{"text-indent":"2em",display:"flex","flex-wrap":"wrap","font-size":"8px",color:"#757575",padding:"8px 0"}},[t("span",{staticStyle:{padding:"5px 0"}},[e._v("\n        1、口令至少由8位及以上大写字母、小写字母、数字与特殊符号等4类中3类混合、随机组成，尽量不要以姓名、电话号码以及出生日期等作为口令或者口令的组成部分；\n      ")]),t("span",{staticStyle:{padding:"5px 0"}},[e._v("\n        2、口令应与用户名无相关性，口令中不得包含用户名的完整字符串、大小写变位或形似变换的字符串，如teleadmin:teleadmin、teleadmin:teleadmin2017、teleadmin:TeleAdmin、teleadmin:te!e@dmin等；\n      ")]),t("span",{staticStyle:{padding:"5px 0"}},[e._v("\n        3、应更换系统或设备的出厂默认口令，如huawei:huawei@123，oracle数据库中SYS:CHANGE_ON_INSTALL,某移动定制版光猫默认帐号CMCCAdmin:aDm8H%MdA等；\n      ")]),t("span",{staticStyle:{padding:"5px 0"}},[e._v("\n        4、口令设置应避免3位以上（含3位）键盘排序密码，如qwe（键盘第1行前三个字母）、asd（键盘第2行前三个字母）、qaz（键盘第1列三个字母）、1qaz（键盘第1列数字加前三个字母）、！QAZ（键盘第1列特殊字符加前三个字母）等；\n      ")]),t("span",{staticStyle:{padding:"5px 0"}},[e._v("\n        5、口令中不能出现3位以上（含三位）连续字母、数字、特殊字符，如ABC、Abc、123、！@#等；\n      ")]),t("span",[e._v("\n        6、口令中不能出现3位以上（含三位）重复字母、数字、特殊字符，如AAA、Aaa、111、###等。\n      ")]),t("span",[e._v("\n\t\t\t7、当前密码不能与近10次使用密码重复。\n\t\t")])]),t("h4",{staticStyle:{"text-align":"left"}},[e._v("避免以下易猜解口令规则")]),t("div",{staticStyle:{"text-indent":"2em",display:"flex","flex-wrap":"wrap","font-size":"8px",color:"#757575",padding:"8px 0"}},[t("span",{staticStyle:{padding:"5px 0"}},[e._v("\n        1、省份、地市名称、邮箱、电话区号、邮政编码及缩写和简单数字或shift键+简单数字，如BJYD123、HBYD!@#等；\n      ")]),t("span",{staticStyle:{padding:"5px 0"}},[e._v("\n        2、单位名称、专业名称、系统名称、厂家名称（含缩写）和简单数字，如HBnmc123、HBsmc_123等；\n      ")]),t("span",{staticStyle:{padding:"5px 0"}},[e._v("\n        3、维护人员名字全拼大小写缩写等变形+设备IP地址（一位或两位）或出生年月日等，如维护人员张三，维护设备地址,出生日期为19951015，则其可能的弱口令为zhangsan100、zhangsan101，zhangsan10100，zhangsan10101，zhangsan19951015，ZS19951015等；\n      ")])])])}]},"7e63":function(e,t){},"841c":function(e,t,n){"use strict";var a=n("c65b"),s=n("d784"),r=n("825a"),i=n("7234"),o=n("1d80"),c=n("129f"),l=n("577e"),d=n("dc4a"),u=n("14c3");s("search",(function(e,t,n){return[function(t){var n=o(this),s=i(t)?void 0:d(t,e);return s?a(s,t,n):new RegExp(t)[e](l(n))},function(e){var a=r(this),s=l(e),i=n(t,a,s);if(i.done)return i.value;var o=a.lastIndex;c(o,0)||(a.lastIndex=0);var d=u(a,s);return c(a.lastIndex,o)||(a.lastIndex=o),null===d?-1:d.index}]}))},"88a7":function(e,t,n){"use strict";var a=n("cb2d"),s=n("e330"),r=n("577e"),i=n("d6d6"),o=URLSearchParams,c=o.prototype,l=s(c.append),d=s(c["delete"]),u=s(c.forEach),f=s([].push),p=new o("a=1&a=2&b=3");p["delete"]("a",1),p["delete"]("b",void 0),p+""!=="a=2"&&a(c,"delete",(function(e){var t=arguments.length,n=t<2?void 0:arguments[1];if(t&&void 0===n)return d(this,e);var a=[];u(this,(function(e,t){f(a,{key:t,value:e})})),i(t,1);var s,o=r(e),c=r(n),p=0,h=0,m=!1,g=a.length;while(p<g)s=a[p++],m||s.key===o?(m=!0,d(this,s.key)):h++;while(h<g)s=a[h++],s.key===o&&s.value===c||l(this,s.key,s.value)}),{enumerable:!0,unsafe:!0})},9861:function(e,t,n){"use strict";n("5352")},addb:function(e,t,n){"use strict";var a=n("f36a"),s=Math.floor,r=function(e,t){var n=e.length;if(n<8){var i,o,c=1;while(c<n){o=c,i=e[c];while(o&&t(e[o-1],i)>0)e[o]=e[--o];o!==c++&&(e[o]=i)}}else{var l=s(n/2),d=r(a(e,0,l),t),u=r(a(e,l),t),f=d.length,p=u.length,h=0,m=0;while(h<f||m<p)e[h+m]=h<f&&m<p?t(d[h],u[m])<=0?d[h++]:u[m++]:h<f?d[h++]:u[m++]}return e};e.exports=r},b2f9:function(e,t,n){"use strict";n("519f")},b308:function(e,t,n){},bad6:function(e,t,n){},dc67:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return s}));var a=function(){var e=this;e._self._c,e._self._setupProxy;return e._m(0)},s=[function(){var e=this,t=e._self._c;e._self._setupProxy;return t("div",{staticStyle:{padding:"10px 10px"}},[t("h4",{staticStyle:{"text-align":"left"}},[e._v("Password character policy:")]),t("div",{staticStyle:{"text-indent":"2em",display:"flex","flex-wrap":"wrap","font-size":"8px",color:"#757575",padding:"8px 0"}},[t("span",{staticStyle:{padding:"5px 0"}},[e._v("\n     1. The password must be 8 characters or more and contain at least one uppercase character, at least one lowercase character, at least one number and at least one special symbol;\n    ")]),t("span",{staticStyle:{padding:"5px 0"}},[e._v("\n      2. The password shall not contain any three identical consecutive (ABC, Abc, 123, !@# etc) and repetitive characters (AAA, Aaa, 111, ### etc)\n    ")])])])}]},e49c:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"login"},[t("header",{staticClass:"login-header"},[t("div",{staticClass:"left-box"},[t("div",{staticClass:"cmlink"},[t("img",{attrs:{src:e.cmlink_src,width:"100%",height:"100%"}})]),t("div",{staticClass:"line"}),"zh-CN"===this.$i18n.locale?t("div",{staticClass:"name"},[e._v("全球卡合作伙伴平台")]):t("div",{staticClass:"name"},[e._v("Global Data SIM Partner Portal")])]),t("div",{staticClass:"right-box"},[t("p",{attrs:{id:"en"},on:{click:function(t){return e.getLanguage(2)}}},[e._v("English")]),t("p",{staticClass:"line"},[e._v("|")]),t("p",{attrs:{id:"zh"},on:{click:function(t){return e.getLanguage(1)}}},[e._v("简体中文")])])]),t("section",{staticClass:"login-section"},[t("div",{staticClass:"trending-box"},[t("div",{staticClass:"trending"},[t("div",{staticClass:"global"},[t("img",{attrs:{src:e.global_src,alt:"news",width:"78%",height:"78%"}})]),t("div",{staticClass:"parallelogram"},[t("div",{staticClass:"text-box"},[t("div",{ref:"animateBox",staticClass:"text-box-child"},[t("p",{ref:"marqueeOld",class:e.inforWidth>e.boxWidth-60?"marquee":"marquees"},[e._v(e._s(e.trending))])])]),t("div",{staticClass:"blueline"})])]),t("div",{staticClass:"onebox"}),t("div",{staticClass:"twobox"}),t("div",{staticClass:"threebox"}),t("div",{staticClass:"greenline"})])]),e.noforget?t("div",{staticClass:"login-con"},[t("div",{staticClass:"card"},["en-US"===this.$i18n.locale?t("div",{staticClass:"login-box"},[t("Tabs",{staticStyle:{padding:"20px 0px 0px 0px"},attrs:{value:"name1",id:"loginTabs"},on:{"on-click":e.clicdTabs}},[t("TabPane",{attrs:{label:"Log In",name:"name1"}},[e.tabOne?t("div",{staticClass:"form-con"},[t("login-form",{attrs:{refresh:e.refresh,language:e.language},on:{"on-success-valid":e.handleSubmit}})],1):e._e()])],1),t("div",{staticStyle:{display:"flex","justify-content":"flex-end","margin-right":"12%"}},[e.ssoSwitchConfig?t("Button",{attrs:{type:"text",shape:"circle"},on:{click:e.handleSSO}},[e._v("SSO")]):e._e(),t("Button",{attrs:{type:"text",shape:"circle"},on:{click:e.goto}},[e._v("Forget Password")])],1)],1):t("div",{staticClass:"login-box"},[t("Tabs",{staticStyle:{padding:"20px 0px 0px 0px"},attrs:{value:"name1",id:"loginTabs"},on:{"on-click":e.clicdTabs}},[t("TabPane",{attrs:{label:"登录",name:"name1"}},[e.tabOne?t("div",{staticClass:"form-con"},[t("login-form",{attrs:{refresh:e.refresh,language:e.language},on:{"on-success-valid":e.handleSubmit}})],1):e._e()])],1),t("div",{staticStyle:{display:"flex","justify-content":"flex-end","margin-right":"16%"}},[e.ssoSwitchConfig?t("Button",{attrs:{type:"text",shape:"circle"},on:{click:e.handleSSO}},[e._v("SSO登录")]):e._e(),t("Button",{attrs:{type:"text",shape:"circle"},on:{click:e.goto}},[e._v("忘记密码")])],1)],1)])]):e._e(),e.isCood?t("div",{staticClass:"login-cood"},[t("div",{staticClass:"card"},["en-US"===this.$i18n.locale?t("div",{staticClass:"relogin-box"},[t("Tabs",{staticStyle:{padding:"20px 0px 0px 0px"},attrs:{value:"name1",id:"loginTabs"},on:{"on-click":e.clicdTabs}},[t("TabPane",{attrs:{label:"Verification code verification",name:"name1"}},[e.tabOne?t("div",{staticClass:"form-con"},[t("login-cood",{attrs:{language:e.language,refresh:e.refresh,userInfo:e.userInfo},on:{"on-success-valid":e.handleSubmitCood,"parent-method":e.parentMethod}})],1):e._e()])],1)],1):t("div",{staticClass:"relogin-box"},[t("Tabs",{staticStyle:{padding:"20px 0px 0px 0px"},attrs:{value:"name1",id:"loginTabs"},on:{"on-click":e.clicdTabs}},[t("TabPane",{attrs:{label:"验证码校验",name:"name1"}},[e.tabOne?t("div",{staticClass:"form-con"},[t("login-cood",{attrs:{language:e.language,refresh:e.refresh,userInfo:e.userInfo},on:{"on-success-valid":e.handleSubmitCood,"parent-method":e.parentMethod}})],1):e._e()])],1)],1)])]):e._e(),e.isforget?t("div",{staticClass:"login-conPwd"},[t("div",{staticClass:"card"},["en-US"===this.$i18n.locale?t("div",{staticClass:"relogin-box"},[t("Tabs",{staticStyle:{padding:"20px 0px 0px 0px"},attrs:{value:"name1",id:"loginTabs"},on:{"on-click":e.clicdTabs}},[t("TabPane",{attrs:{label:"Forgot Password",name:"name1"}},[e.tabOne?t("div",{staticClass:"form-con"},[t("pwd-reset",{attrs:{language:e.language,time:e.time,refresh:e.refresh},on:{"on-success-valid":e.handleSubmitPwd,"on-cancel-valid":e.cancel,"on-send-code":e.doSendMsgCodePwd,timeChange:e.timeChangePwd}})],1):e._e()])],1)],1):t("div",{staticClass:"relogin-box"},[t("Tabs",{staticStyle:{padding:"20px 0px 0px 0px"},attrs:{value:"name1",id:"loginTabs"},on:{"on-click":e.clicdTabs}},[t("TabPane",{attrs:{label:"忘记密码",name:"name1"}},[e.tabOne?t("div",{staticClass:"form-con"},[t("pwd-reset",{attrs:{language:e.language,time:e.time,refresh:e.refresh},on:{"on-success-valid":e.handleSubmitPwd,"on-cancel-valid":e.cancel,"on-send-code":e.doSendMsgCodePwd,timeChange:e.timeChangePwd}})],1):e._e()])],1)],1)])]):e._e()])},s=[],r=n("3835"),i=n("b85c"),o=n("5530"),c=(n("14d9"),n("d3b7"),n("ac1f"),n("3ca3"),n("5319"),n("841c"),n("498a"),n("ddb0"),n("9861"),n("88a7"),n("271a"),n("5494"),function(){var e=this,t=e._self._c;return t("Form",{ref:"loginForm",staticStyle:{"margin-top":"10px"},attrs:{model:e.form,rules:e.rules},nativeOn:{keydown:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;t.preventDefault()}}},["en-US"===this.$i18n.locale?t("div",[t("FormItem",{staticStyle:{width:"82%"},attrs:{prop:"username",rules:[{required:!0,message:"Username Cannot Be Empty",trigger:"blur"}]}},[t("Input",{attrs:{placeholder:"Username",clearable:""},model:{value:e.form.username,callback:function(t){e.$set(e.form,"username",t)},expression:"form.username"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"md-person"}})],1)])],1),t("FormItem",{staticStyle:{width:"82%"},attrs:{prop:"password",rules:[{required:!0,message:"Password Cannot Be Empty",trigger:"blur"}]}},[t("Input",{attrs:{type:"password",placeholder:"Password",clearable:""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"md-lock"}})],1)])],1)],1):t("div",[t("FormItem",{staticStyle:{width:"82%"},attrs:{prop:"username",rules:[{required:!0,message:"账号不能为空",trigger:"blur"}]}},[t("Input",{attrs:{placeholder:"用户名",clearable:""},model:{value:e.form.username,callback:function(t){e.$set(e.form,"username",t)},expression:"form.username"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"md-person"}})],1)])],1),t("FormItem",{staticStyle:{width:"82%"},attrs:{prop:"password",rules:[{required:!0,message:"密码不能为空",trigger:"blur"}]}},[t("Input",{attrs:{type:"password",placeholder:"请输入密码",clearable:""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"md-lock"}})],1)])],1)],1),t("FormItem",["en-US"===this.$i18n.locale?t("Button",{staticStyle:{width:"82%"},attrs:{disabled:e.isSubmitting,type:"primary"},on:{click:e.handleSubmit}},[e._v("Log In")]):t("Button",{staticStyle:{width:"82%"},attrs:{disabled:e.isSubmitting,type:"primary"},on:{click:e.handleSubmit}},[e._v("登录")])],1)],1)}),l=[],d=n("c7eb"),u=n("1da1"),f=n("2f62"),p={name:"LoginForm",props:{refresh:{type:Boolean,default:!1},language:"",rules:{}},data:function(){return{lock_src:n("37e3"),form:{username:"",password:"",type:1},isSubmitting:!1}},computed:{},activated:function(){},mounted:function(){this.$i18n.locale},watch:{},methods:Object(o["a"])(Object(o["a"])({},Object(f["b"])(["iistoken"])),{},{handleSubmit:function(){var e=this;return Object(u["a"])(Object(d["a"])().mark((function t(){return Object(d["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isSubmitting){t.next=2;break}return t.abrupt("return");case 2:return e.isSubmitting=!0,t.next=5,e.handleLogin();case 5:e.isSubmitting=!1;case 6:case"end":return t.stop()}}),t)})))()},handleLogin:function(){var e=this;return Object(u["a"])(Object(d["a"])().mark((function t(){return Object(d["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.$refs.loginForm.validate(function(){var t=Object(u["a"])(Object(d["a"])().mark((function t(n){return Object(d["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:n&&e.$emit("on-success-valid",{username:e.form.username,password:e.form.password});case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}());case 3:t.next=8;break;case 5:t.prev=5,t.t0=t["catch"](0),console.error("表单验证失败:",t.t0);case 8:return t.prev=8,e.isSubmitting=!1,t.finish(8);case 11:case"end":return t.stop()}}),t,null,[[0,5,8,11]])})))()}}),created:function(){var e=!1;this.iistoken(e)}},h=p,m=(n("e96e"),n("2877")),g=Object(m["a"])(h,c,l,!1,null,"12800493",null),v=g.exports,b=v,w=function(){var e=this,t=e._self._c;return t("Form",{ref:"loginForm",attrs:{model:e.form,rules:e.rules},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSubmit.apply(null,arguments)}}},[t("FormItem",{attrs:{prop:"username"}},[t("Input",{attrs:{placeholder:"请输入手机号码",clearable:""},model:{value:e.form.username,callback:function(t){e.$set(e.form,"username",t)},expression:"form.username"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"md-call"}})],1)])],1),t("FormItem",{attrs:{prop:"smsCode"}},[t("div",{staticStyle:{display:"flex","flex-wrap":"nowrap"}},[t("Input",{ref:"smsCode",attrs:{placeholder:"请输入短信验证码",maxlength:6,clearable:""},model:{value:e.form.smsCode,callback:function(t){e.$set(e.form,"smsCode",t)},expression:"form.smsCode"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"ios-mail"}})],1)]),t("input",{ref:"sessionId",attrs:{name:"sessionId",type:"hidden"}}),t("Button",{staticStyle:{width:"30%",margin:"0 0 0 10px"},attrs:{id:"msgo",type:"success",disabled:e.disabled,long:""},on:{click:e.handlePhone}},[e._v("发送验证码")])],1)]),t("FormItem",[t("Button",{attrs:{type:"primary",long:""},on:{click:e.handleSubmit}},[e._v("登录")])],1)],1)},y=[],x=(n("a9e3"),n("00b4"),n("f121"),{name:"LoginMsg",props:{usernameRules:{type:Array,default:function(){return[{required:!0,message:"手机号不能为空",trigger:"blur"}]}},smsCodeRules:{type:Array,default:function(){return[{required:!0,message:"请输入短信验证码",trigger:"blur"},{max:6,message:"请输入短信验证码",trigger:"blur"}]}},refresh:{type:Boolean,default:!1},time:{type:Number,default:!1}},data:function(){return{form:{username:"",password:"",type:2,smsCode:""},disabled:!1}},computed:{rules:function(){return{username:this.usernameRules,smsCode:this.smsCodeRules}}},watch:{time:function(e,t){0!==e&&(this.disabled=!0),this.showtime(e)}},activated:function(){},mounted:function(){},methods:{returnBak:function(){this.$emit("on-cancel-valid",!1)},handleSubmit:function(){var e=this;this.$refs.loginForm.validate((function(t){t&&e.$emit("on-success-valid",{username:e.form.username,password:e.form.password,type:e.form.type,smsCode:e.form.smsCode})}))},handlePhone:function(){if(/^[0-9]*$/.test(this.form.username)&&/^\d{4,16}$/.test(this.form.username)){this.$emit("on-send-code",{phoneNumber:this.form.username,type:1}),this.disabled=!0;try{document.getElementById("msgo").innerHTML="正在发送"}catch(e){}}else this.$Notice.warning({title:"号码格式错误",desc:"号码格式错误，请输入正确的手机号码"})},showtime:function(e){var t=this;if(0!==e)setTimeout((function(){try{var n=e-1;document.getElementById("msgo").innerHTML=" ("+n+")秒",t.$emit("timeChange",e-1)}catch(a){}}),1e3);else{this.disabled=!1;try{document.getElementById("msgo").innerHTML="发送验证码"}catch(n){}}}}}),C=x,S=Object(m["a"])(C,w,y,!1,null,null,null),k=S.exports,$=k,I=function(){var e=this,t=e._self._c;return t("Form",{ref:"loginCood",staticClass:"loginCood-box",attrs:{model:e.form,rules:e.rules},nativeOn:{keydown:function(t){if(!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter"))return null;t.preventDefault()}}},["en-US"===this.$i18n.locale?t("div",[t("div",{staticClass:"flex-box"},[e.showTip?t("FormItem",{staticClass:"h3-boxOne"},[t("h3",[e._v("We have sent the verification code to your email or mobile phone. Please check and input.")])]):e._e()],1),t("div",{staticClass:"smsCode-box"},[t("FormItem",{staticClass:"smsCode-input",attrs:{prop:"smsCode",rules:[{required:!0,len:6,message:e.$t("sys.smsCode"),trigger:"blur"},{pattern:/^[0-9]\d*$/,message:"Please Enter A 6-digit Verification Code",trigger:"blur"}]}},[t("Input",{attrs:{placeholder:"Verification Code",maxlength:6,clearable:""},model:{value:e.form.smsCode,callback:function(t){e.$set(e.form,"smsCode",t)},expression:"form.smsCode"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"md-checkmark-circle"}})],1)])],1),t("FormItem",{staticClass:"smscodess"},[t("Button",{staticStyle:{float:"left"},attrs:{disabled:e.disabled,shape:"circle"},on:{click:e.getCode}},[t("span",[e._v(e._s(e.valiBtn))])])],1)],1)]):t("div",[e.showTip?t("div",{staticClass:"flex-box"},[t("FormItem",{staticClass:"h3-boxOne"},[t("h3",[e._v("我们已将登录验证码发送至您的邮箱或手机号，请您查收并输入。")])])],1):e._e(),t("div",{staticClass:"smsCode-box"},[t("FormItem",{staticClass:"smsCode-input2",attrs:{prop:"smsCode",rules:[{required:!0,len:6,message:e.$t("sys.smsCode"),trigger:"blur"},{pattern:/^[0-9]\d*$/,message:"请输入6位数字验证码",trigger:"blur"}]}},[t("Input",{attrs:{placeholder:"请输入验证码",maxlength:6,clearable:""},model:{value:e.form.smsCode,callback:function(t){e.$set(e.form,"smsCode",t)},expression:"form.smsCode"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"md-checkmark-circle"}})],1)])],1),t("FormItem",{staticClass:"smscodess"},[t("Button",{staticStyle:{float:"left"},attrs:{disabled:e.disabled,shape:"circle"},on:{click:e.getCode}},[t("span",[e._v(e._s(e.valiBtn))])])],1)],1)]),t("div",{staticClass:"flex-box2"},[t("div",{staticClass:"h3-boxthree"},["en-US"===this.$i18n.locale?t("Button",{attrs:{shape:"circle",long:""},on:{click:e.back}},[e._v("Return")]):t("Button",{attrs:{shape:"circle",long:""},on:{click:e.back}},[e._v("返回")])],1),t("div",{staticClass:"h3-boxfour"},["en-US"===this.$i18n.locale?t("Button",{attrs:{disabled:e.isSubmitting,type:"primary",shape:"circle",long:""},on:{click:e.handleSubmitCood}},[e._v("Log In")]):t("Button",{attrs:{disabled:e.isSubmitting,type:"primary",shape:"circle",long:""},on:{click:e.handleSubmitCood}},[e._v("登录")])],1)])])},_=[],O=(n("d9e2"),n("66df")),P="/rcs/api/v1",B=function(){return O["a"].request({url:P+"/passport/captcha",method:"get"})},A=function(e){return O["a"].request({url:"/auth/code/getIsOpen",method:"post",data:e})},T=function(e){return O["a"].request({url:"/sys/api/v1/user/getVerifyCode",params:e,method:"get"})},L=function(e){return O["a"].request({url:"/sys/api/v1/user/userForgetpasswd/sendVerifyCode",params:e,method:"put"})},U={name:"loginCood",props:{refresh:{type:Boolean,default:!1},language:"",userInfo:{type:Object,default:{}}},data:function(){return{lock_src:n("37e3"),form:{smsCode:""},rules:{},valiBtn:"发送验证码",disabled:!1,showTip:!1,isSubmitting:!1,isSendingCode:!1,codeTimer:null}},computed:{},activated:function(){},created:function(){var e=!1;this.iistoken(e),this.getCode()},mounted:function(){var e=this.$i18n.locale;"en-US"===e&&(this.valiBtn="Send Verification Code")},watch:{language:{handler:function(e,t){this.$i18n.locale=e,this.valiBtn="en-US"===e?"Send Verification Code":"发送验证码"}}},methods:Object(o["a"])(Object(o["a"])({},Object(f["b"])(["iistoken"])),{},{handleSubmitCood:function(){var e=this;return Object(u["a"])(Object(d["a"])().mark((function t(){return Object(d["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isSubmitting&&!e.isSendingCode){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,e.isSubmitting=!0,t.next=6,e.validateAndSubmit();case 6:t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](2),console.error("表单验证或提交失败:",t.t0);case 11:return t.prev=11,e.clearCodeTimer(),e.isSubmitting=!1,t.finish(11);case 15:case"end":return t.stop()}}),t,null,[[2,8,11,15]])})))()},validateAndSubmit:function(){var e=this;return Object(u["a"])(Object(d["a"])().mark((function t(){return Object(d["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.$refs.loginCood.validate(function(){var t=Object(u["a"])(Object(d["a"])().mark((function t(n){return Object(d["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:n&&e.$emit("on-success-valid",{username:e.userInfo.username,password:e.userInfo.password,code:e.form.smsCode});case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}());case 3:t.next=8;break;case 5:throw t.prev=5,t.t0=t["catch"](0),new Error("表单验证失败: "+t.t0.message);case 8:case"end":return t.stop()}}),t,null,[[0,5]])})))()},getCode:function(){var e=this;return Object(u["a"])(Object(d["a"])().mark((function t(){var n;return Object(d["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.isSendingCode){t.next=2;break}return t.abrupt("return");case 2:return e.clearCodeTimer(),e.showTip=!0,e.isSendingCode=!0,e.valiBtn="en-US"===e.$i18n.locale?"Sending...":"发送中...",t.prev=6,t.next=9,T({username:e.userInfo.username,language:e.$i18n.locale});case 9:n=t.sent,"0000"===n.code?(e.$Notice.success({title:e.$t("stock.Code"),desc:n.data}),e.startCodeTimer(60)):e.handleError(),t.next=16;break;case 13:t.prev=13,t.t0=t["catch"](6),e.handleError();case 16:return t.prev=16,e.isSendingCode=!1,t.finish(16);case 19:case"end":return t.stop()}}),t,null,[[6,13,16,19]])})))()},handleError:function(){this.valiBtn="发送验证码","en-US"===this.$i18n.locale&&(this.valiBtn="Send Verification Code")},startCodeTimer:function(e){var t=this;this.disabled=!0,this.clearCodeTimer();var n=e;this.codeTimer=setInterval((function(){n--,t.$i18n?t.valiBtn="en-US"===t.$i18n.locale?"Retry after ".concat(n," seconds"):"".concat(n,"秒后重试"):(console.warn("$i18n is not available"),t.valiBtn="".concat(n,"秒后重试")),n<=0&&(t.clearCodeTimer(),t.updateButtonText(),t.disabled=!1,t.showTip=!1)}),1e3)},updateButtonText:function(){"en-US"===this.$i18n.locale?this.valiBtn="Send Verification Code":this.valiBtn="发送验证码"},clearCodeTimer:function(){this.codeTimer&&(clearInterval(this.codeTimer),this.codeTimer=null)},refreshCode:function(){var e=this,t=this.$refs.codeImg;B().then((function(n){t.src="data:image/png;base64,"+n.data.image,e.form.sessionId=n.data.sessionId,e.$refs["sessionId"].value=n.data.sessionId})).catch((function(e){console.log(e)})).finally((function(){}))},back:function(){this.$emit("parent-method")}}),beforeDestroy:function(){this.clearCodeTimer()}},j=U,N=(n("771d"),Object(m["a"])(j,I,_,!1,null,"14f4d0de",null)),R=N.exports,z=R,E=function(){var e=this,t=e._self._c;return t("div",{staticClass:"pwd-reset"},[e.showRules?t("div",{staticClass:"view_right"},[t("Alert",{attrs:{type:"warning",closable:""},on:{"on-close":function(t){e.showRules=!1}}},["zh-CN"===this.$i18n.locale?t("div",[t("text-view")],1):e._e(),"en-US"===this.$i18n.locale?t("div",[t("text-viewEn")],1):e._e()])],1):e._e(),"en-US"===this.$i18n.locale?t("div",{staticClass:"view_out"},[t("Form",{ref:"forgetPwForm",attrs:{model:e.form,rules:e.rules},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSubmit.apply(null,arguments)}}},[t("FormItem",{attrs:{prop:"username"}},[t("div",{staticStyle:{display:"flex","flex-wrap":"nowrap"}},[t("Input",{attrs:{placeholder:"Please Enter User Name",clearable:""},model:{value:e.form.username,callback:function(t){e.$set(e.form,"username",t)},expression:"form.username"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"ios-person"}})],1)]),t("input",{ref:"captchaSessionId",attrs:{name:"captchaSessionId",type:"hidden"}}),t("Button",{staticStyle:{width:"50%",margin:"0 0 0 10px"},attrs:{id:"msg",type:"success",disabled:e.disabled,long:""},on:{click:e.handlePhone}},[e._v(e._s(e.valiBtn))])],1)]),t("FormItem",{attrs:{prop:"smsCode"}},[t("div",{staticStyle:{display:"flex","flex-wrap":"nowrap"}},[t("Input",{ref:"smsCode",attrs:{placeholder:"Please enter verification code",maxlength:6,clearable:""},model:{value:e.form.smsCode,callback:function(t){e.$set(e.form,"smsCode",t)},expression:"form.smsCode"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"ios-mail"}})],1)])],1)]),t("FormItem",{attrs:{prop:"password"}},[t("Input",{attrs:{type:"password",placeholder:"Please Enter A New Password",clearable:""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"md-lock"}})],1)])],1),t("FormItem",{attrs:{prop:"repassword"}},[t("Input",{attrs:{type:"password",placeholder:"Please enter password again",clearable:""},on:{"on-blur":e.checkPwd},model:{value:e.form.repassword,callback:function(t){e.$set(e.form,"repassword",t)},expression:"form.repassword"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"md-lock"}})],1)])],1),t("Alert",{attrs:{type:"warning","show-icon":""}},[e._v("\n\t\t\t\tWhen Setting The Password, You Need To Meet The Given Rules, Click\n\t\t\t\t"),t("a",{attrs:{href:"#"},on:{click:function(t){e.showRules=!0}}},[e._v("View ")]),e._v("\n\t\t\t\tLearn More\n\t\t\t")]),t("FormItem",[t("div",{staticStyle:{display:"flex","flex-wrap":"nowrap","justify-content":"space-between"}},[t("Button",{staticStyle:{width:"40%"},attrs:{type:"error",long:""},on:{click:e.returnBak}},[e._v("Return")]),t("Button",{staticStyle:{width:"40%"},attrs:{type:"primary",long:""},on:{click:e.handleSubmit}},[e._v("Reset password")])],1)])],1)],1):t("div",{staticClass:"view_out"},[t("Form",{ref:"forgetPwForm",attrs:{model:e.form,rules:e.rules},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSubmit.apply(null,arguments)}}},[t("FormItem",{attrs:{prop:"username"}},[t("div",{staticStyle:{display:"flex","flex-wrap":"nowrap"}},[t("Input",{attrs:{placeholder:"请输入用户名",clearable:""},model:{value:e.form.username,callback:function(t){e.$set(e.form,"username",t)},expression:"form.username"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"ios-person"}})],1)]),t("input",{ref:"captchaSessionId",attrs:{name:"captchaSessionId",type:"hidden"}}),t("Button",{staticStyle:{width:"50%",margin:"0 0 0 10px"},attrs:{id:"msg",type:"success",disabled:e.disabled,long:""},on:{click:e.handlePhone}},[e._v(e._s(e.valiBtn))])],1)]),t("FormItem",{attrs:{prop:"smsCode"}},[t("div",{staticStyle:{display:"flex","flex-wrap":"nowrap"}},[t("Input",{ref:"smsCode",attrs:{placeholder:"请输入验证码",maxlength:6,clearable:""},model:{value:e.form.smsCode,callback:function(t){e.$set(e.form,"smsCode",t)},expression:"form.smsCode"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"ios-mail"}})],1)])],1)]),t("FormItem",{attrs:{prop:"password"}},[t("Input",{attrs:{type:"password",placeholder:"请输入新密码",clearable:""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"md-lock"}})],1)])],1),t("FormItem",{attrs:{prop:"repassword"}},[t("Input",{attrs:{type:"password",placeholder:"请再次输入新密码",clearable:""},on:{"on-blur":e.checkPwd},model:{value:e.form.repassword,callback:function(t){e.$set(e.form,"repassword",t)},expression:"form.repassword"}},[t("span",{attrs:{slot:"prepend"},slot:"prepend"},[t("Icon",{attrs:{size:20,type:"md-lock"}})],1)])],1),t("Alert",{attrs:{type:"warning","show-icon":""}},[e._v("\n\t\t\t\t密码设置时需要满足给定规则，点击\n\t\t\t\t"),t("a",{attrs:{href:"#"},on:{click:function(t){e.showRules=!0}}},[e._v("查看")]),e._v("\n\t\t\t\t了解详情\n\t\t\t")]),t("FormItem",[t("div",{staticStyle:{display:"flex","flex-wrap":"nowrap","justify-content":"space-between"}},[t("Button",{staticStyle:{width:"40%"},attrs:{type:"error",long:""},on:{click:e.returnBak}},[e._v("返回")]),t("Button",{staticStyle:{width:"40%"},attrs:{type:"primary",long:""},on:{click:e.handleSubmit}},[e._v("重置密码")])],1)])],1)],1)])},F=[],M=n("3108"),q=n("5d38"),D=n("631c"),W={name:"PwdReset",components:{TextView:q["default"],TextViewEn:D["default"]},props:{usernameRules:{type:Array,default:function(){return[{required:!0,message:"Login name can't be blank",trigger:"blur"}]}},smsCodeRules:{type:Array,default:function(){return[{required:!0,message:"Please enter verification code",trigger:"blur"},{max:6,message:"Verification code format incorrect",trigger:"blur"}]}},language:""},data:function(){return{form:{username:"",phoneNumber:"",smsCode:"",password:"",repassword:"",captchaSessionId:"",captcha:""},disabled:!1,checkPass:!1,showRules:!1,valiBtn:"下发验证码"}},computed:{rules:function(){var e=this,t=function(t,n,a){""!==e.form.repassword&&e.$refs.forgetPwForm.validateField("repassword"),a()},n=function(t,n,a){n!==e.form.password?a(new Error(e.$t("sys.inputTwoPw"))):a()},a=function(t,n,a){var s=/^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,}$/;if(0!=s.test(n))if(/(.)\1{2}/i.test(n))a(new Error(e.$t("address.appear")));else{var r=/((?:0(?=1)|1(?=2)|2(?=3)|3(?=4)|4(?=5)|5(?=6)|6(?=7)|7(?=8)|8(?=9)|9(?=0)){2}\d)/;r.test(n)?a(new Error(e.$t("address.allowed"))):a()}else a(new Error(e.$t("address.reset")))};return{username:this.usernameRules,smsCode:this.smsCodeRules,password:[{required:!0,message:"The new password cannot be empty",trigger:"blur"},{min:6,message:this.$t("sys.pwdLengthMsh")},{validator:a,trigger:"blur"},{validator:t,trigger:"blur"}],repassword:[{required:!0,message:"The new password cannot be empty",trigger:"blur"},{validator:a,trigger:"blur"},{validator:n,trigger:"blur"}]}}},activated:function(){},mounted:function(){var e=this.$i18n.locale;"en-US"===e&&(this.valiBtn="verification code sent")},watch:{time:function(e,t){0!==e&&(this.disabled=!0),this.showtime(e)},language:{handler:function(e,t){this.$i18n.locale=e,this.valiBtn="en-US"===e?"verification code sent":"下发验证码"}}},methods:{returnBak:function(){this.$emit("on-cancel-valid",!1)},checkPwd:function(){this.form.password!==this.form.repassword?this.$Notice.warning({title:this.$t("address.Operationreminder"),desc:this.$t("sys.pwNotMatch")}):this.checkPass=!0},handleSubmit:function(){var e=this;this.$refs.forgetPwForm.validate((function(t){t&&(e.checkPass?e.$emit("on-success-valid",{username:e.form.username,verifyCode:e.form.smsCode,passwd:e.form.password,confirmPasswd:e.form.repassword}):e.$Notice.warning({title:e.$t("address.Operationreminder"),desc:e.$t("sys.pwNotMatch")}))}))},handlePhone:function(){var e=this;this.form.username?(L({username:this.form.username}).then((function(t){if("0000"!==t.code)throw t;var n=t.data;e.$Notice.success({title:e.$t("address.Operationreminder"),desc:n}),e.tackBtn()})).catch((function(t){console.log(t),e.disabled=!1})).finally((function(){})),this.disabled=!0):this.$Notice.warning({title:this.$t("address.Operationreminder"),desc:this.$t("sys.inputName")})},tackBtn:function(){var e=this,t=60,n=this.$i18n.locale,a=setInterval((function(){0==t?(clearInterval(a),e.valiBtn="下发验证码","en-US"===n&&(e.valiBtn="verification code sent"),e.disabled=!1):(e.disabled=!0,e.valiBtn=t+"秒后重试","en-US"===n&&(e.valiBtn="Retry after "+t+" seconds"),t--)}),1e3)},showtime:function(e){var t=this;if(0!==e)setTimeout((function(){try{var n=e-1;console.log(n),document.getElementById("msg").innerHTML=" ("+n+')this.$t("sys.second")',t.$emit("timeChangePwd",e-1)}catch(a){}}),1e3);else{this.disabled=!1;try{document.getElementById("msg").innerHTML=this.$t("support.Verification_Code")}catch(n){}}},refreshCode:function(){var e=this,t=this.$refs.codeImg;Object(M["c"])({type:2}).then((function(n){t.src="data:image/png;base64,"+n.data.image,e.form.captchaSessionId=n.data.sessionId,e.$refs["captchaSessionId"].value=n.data.sessionId})).catch((function(e){console.log(e)})).finally((function(){e.loading=!1}))}}},H=W,V=(n("b2f9"),Object(m["a"])(H,E,F,!1,null,null,null)),G=V.exports,Y=G,Q=n("4b58"),J=n.n(Q),K=n("6dfa"),X=n("028c"),Z={data:function(){return{time:0,timePwd:0,src:J.a,cmlink_src:n("59ed3"),global_src:n("0af8"),refresh:!1,tabOne:!0,tabTwo:!1,reSetPwd:!1,noforget:!0,isforget:!1,isCood:!1,ssoSwitchConfig:!1,title:"登录",corpId:"",language:"",trending:"",boxWidth:"",inforWidth:"",queryParams:"",userInfo:""}},components:{LoginForm:b,LoginMsg:$,PwdReset:Y,LoginCood:z},methods:Object(o["a"])(Object(o["a"])({},Object(f["b"])(["handleLogin","handleLogOut","handleSSOLogin"])),{},{reSetPwdModal:function(){this.reSetPwd=!0},timeChange:function(e){this.time=e},timeChangePwd:function(e){this.timePwd=e},clicdTabs:function(e){"name1"===e&&(this.tabOne=!0)},handleSubmit:function(e){var t=this;this.handleLogOut().then((function(){A(e).then((function(n){"1"==n.data.userDetails.needVerifyCode?t.handleLogin(e).then((function(e){if("0000"!==e.code)throw e;t.$store.dispatch("changeAuthenStatus",!1),1!==t.$store.state.user.isUpdatePassword?(Object(K["F"])({userName:t.$store.state.user.userName}).then((function(e){"0000"==e.code&&(t.corpId=e.data,sessionStorage.setItem("corpId",t.corpId),t.corpId?Object(K["s"])({corpId:t.corpId}).then((function(e){if("0000"==e.code){var n=e.data[0];sessionStorage.setItem("modeLength",e.data.length),e.data.length>1?t.$router.push({name:"channelCooperationMode",query:{modeList:e.data}}):(sessionStorage.setItem("cooperationMode",n),t.$router.push({name:t.$config.homeName}))}})).catch((function(e){console.error(e)})).finally((function(){})):t.$router.push({name:t.$config.homeName}))})).catch((function(e){console.error(e)})).finally((function(){})),e.pwdBeOverdue&&(t.$Notice.warning({title:"密码过期提醒",name:"rePwd",render:function(e){return e("div",["你的密码即将过期，请点击",e("a",{style:{marginRight:"10px",marginLeft:"10px",color:"#ff0000"},on:{click:function(){t.$router.push({name:"pwd_mngr"}),t.$Notice.close("rePwd")}}},"修改密码"),"前往设置",e("p")])},duration:0}),t.$router.push({name:t.$config.homeName}))):t.$router.push({name:"pwd_mngr"})}),(function(e){return t.refresh=!t.refresh})):(t.noforget=!1,t.isCood=!0,t.userInfo=e)}))}))},handleSubmitCood:function(e){var t=this;this.handleLogOut().then((function(){t.handleLogin(e).then((function(e){if("0000"!==e.code)throw e;t.$store.dispatch("changeAuthenStatus",!1),1!==t.$store.state.user.isUpdatePassword?(Object(K["F"])({userName:t.$store.state.user.userName}).then((function(e){"0000"==e.code&&(t.corpId=e.data,sessionStorage.setItem("corpId",t.corpId),t.corpId?Object(K["s"])({corpId:t.corpId}).then((function(e){if("0000"==e.code){var n=e.data[0];sessionStorage.setItem("modeLength",e.data.length),e.data.length>1?t.$router.push({name:"channelCooperationMode",query:{modeList:e.data}}):(sessionStorage.setItem("cooperationMode",n),t.$router.push({name:t.$config.homeName}))}})).catch((function(e){console.error(e)})).finally((function(){})):t.$router.push({name:t.$config.homeName}))})).catch((function(e){console.error(e)})).finally((function(){})),e.pwdBeOverdue&&(t.$Notice.warning({title:"密码过期提醒",name:"rePwd",render:function(e){return e("div",["你的密码即将过期，请点击",e("a",{style:{marginRight:"10px",marginLeft:"10px",color:"#ff0000"},on:{click:function(){t.$router.push({name:"pwd_mngr"}),t.$Notice.close("rePwd")}}},"修改密码"),"前往设置",e("p")])},duration:0}),t.$router.push({name:t.$config.homeName}))):t.$router.push({name:"pwd_mngr"})}),(function(e){return t.refresh=!t.refresh}))}))},handleSSO:function(){var e=this.$getRedirectUrl(),t=this.getUrlWithoutParams(),n=e+t;this.redirectOnce(n)},ssoLoigin:function(e){var t=this,n={ticket:e,service:this.getUrlWithoutParams()};this.handleLogOut().then((function(){t.handleSSOLogin(n).then((function(e){if("0000"!==e.code)throw e;sessionStorage.setItem("corpId",""),t.$store.dispatch("changeAuthenStatus",!1),1!==t.$store.state.user.isUpdatePassword?(Object(K["F"])({userName:t.$store.state.user.userName}).then((function(e){"0000"==e.code&&t.$router.push({name:t.$config.homeName})})).catch((function(e){console.error(e)})).finally((function(){})),e.pwdBeOverdue&&(t.$Notice.warning({title:"密码过期提醒",name:"rePwd",render:function(e){return e("div",["你的密码即将过期，请点击",e("a",{style:{marginRight:"10px",marginLeft:"10px",color:"#ff0000"},on:{click:function(){t.$router.push({name:"pwd_mngr"}),t.$Notice.close("rePwd")}}},"修改密码"),"前往设置",e("p")])},duration:0}),t.$router.push({name:t.$config.homeName}))):t.$router.push({name:"pwd_mngr"})})).catch((function(e){t.$router.push({name:"login"}),t.refresh=!t.refresh})).finally((function(){}))}))},parseUrlParams:function(){var e,t=new URLSearchParams(window.location.search),n={},a=Object(i["a"])(t.entries());try{for(a.s();!(e=a.n()).done;){var s=Object(r["a"])(e.value,2),o=s[0],c=s[1];n[o]=c.trim()}}catch(l){a.e(l)}finally{a.f()}this.queryParams=n,this.queryParams.ticket&&this.ssoLoigin(this.queryParams.ticket)},getUrlWithoutParams:function(){var e=window.location.href,t=e.indexOf("?");return-1!==t?e.substring(0,t):e},redirectOnce:function(e){"#redirected"!==window.location.hash&&window.location.replace("".concat(e,"#redirected"))},cancel:function(e){this.clicdTabs("name1"),this.isforget=!1,this.noforget=!0},parentMethod:function(){this.noforget=!0,this.isCood=!1},doSendMsgCode:function(e){var t=this;Object(M["b"])(e).then((function(e){if("0000"!==e.code)throw e;t.$Notice.success({title:"操作提醒",desc:"短信验证码已发送至你的手机，请注意查收！"}),t.time=t.$config.sendsmsCodeTimeLimit})).catch((function(e){t.time=0,t.refresh=!t.refresh})).finally((function(){}))},doSendMsgCodePwd:function(e){var t=this;Object(M["b"])(e).then((function(e){if("0000"!==e.code)throw e;t.$Notice.success({title:t.$t("stock.Code"),desc:"验证码已发送至你的手机，请注意查收！"}),t.timePwd=t.$config.sendsmsCodeTimeLimit})).catch((function(e){t.timePwd=0,t.refresh=!t.refresh})).finally((function(){}))},goto:function(){this.noforget=!1,this.isforget=!0},handleSubmitPwd:function(e){var t=this;Object(M["f"])(e).then((function(e){if("0000"!==e.code)throw e;t.$Notice.success({title:"操作提醒",desc:"密码修改成功，即将跳转到登录页面"});var n=t;setTimeout((function(){n.noforget=!0,n.isforget=!1}),3e3)})).catch((function(e){t.refresh=!t.refresh})).finally((function(){}))},error:function(e){this.$Notice.error({title:"操作提醒",desc:e||"服务器内部异常，请稍候再试"})},getLanguage:function(e){var t=document.getElementById("zh"),n=document.getElementById("en");1===e?(localStorage.setItem("local","zh-CN"),this.$i18n.locale="zh-CN",this.language=this.$i18n.locale,t.style.color="rgb(228,0,119)",n.style.color="#000"):(localStorage.setItem("local","en-US"),this.$i18n.locale="en-US",this.language=this.$i18n.locale,t.style.color="#000",n.style.color="rgb(228,0,119)")},getAnnouncement:function(){var e=this;Object(X["b"])().then((function(t){if("0000"!==t.code)throw t;e.trending=t.data.notice,e.$nextTick((function(){var t=e.$refs.animateBox;e.boxWidth=t.offsetWidth;var n=e.$refs.marqueeOld;e.inforWidth=n.offsetWidth,e.$refs.marqueeOld.style.animationDuration=.5*e.trending.length+"s"}))})).catch((function(t){e.refresh=!e.refresh})).finally((function(){}))},getSSOSwitchConfig:function(){var e=this;Object(M["a"])().then((function(t){if("0000"!==t.code)throw t;e.ssoSwitchConfig=t.data})).catch((function(t){e.refresh=!e.refresh})).finally((function(){}))}}),mounted:function(){this.getAnnouncement(),this.getSSOSwitchConfig(),this.parseUrlParams();var e=this.$i18n.locale;this.language=this.$i18n.locale;var t=document.getElementById("zh"),n=document.getElementById("en");"en-US"===e?(this.title="Log in",t.style.color="#000",n.style.color="rgb(228,0,119)"):(t.style.color="rgb(228,0,119)",n.style.color="#000")}},ee=Z,te=(n("f3f2"),Object(m["a"])(ee,a,s,!1,null,"747c391b",null));t["default"]=te.exports},e96e:function(e,t,n){"use strict";n("b308")},f354:function(e,t,n){"use strict";var a=n("d039"),s=n("b622"),r=n("83ab"),i=n("c430"),o=s("iterator");e.exports=!a((function(){var e=new URL("b?a=1&b=2&c=3","https://a"),t=e.searchParams,n=new URLSearchParams("a=1&a=2&b=3"),a="";return e.pathname="c%20d",t.forEach((function(e,n){t["delete"]("b"),a+=n+e})),n["delete"]("a",2),n["delete"]("b",void 0),i&&(!e.toJSON||!n.has("a",1)||n.has("a",2)||!n.has("a",void 0)||n.has("b"))||!t.size&&(i||!r)||!t.sort||"https://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[o]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("https://тест").host||"#%D0%B1"!==new URL("https://a#б").hash||"a1c3"!==a||"x"!==new URL("https://x",void 0).host}))},f3f2:function(e,t,n){"use strict";n("bad6")},f6d6:function(e,t,n){"use strict";var a=n("23e7"),s=n("e330"),r=n("23cb"),i=RangeError,o=String.fromCharCode,c=String.fromCodePoint,l=s([].join),d=!!c&&1!==c.length;a({target:"String",stat:!0,arity:1,forced:d},{fromCodePoint:function(e){var t,n=[],a=arguments.length,s=0;while(a>s){if(t=+arguments[s++],r(t,1114111)!==t)throw new i(t+" is not a valid code point");n[s]=t<65536?o(t):o(55296+((t-=65536)>>10),t%1024+56320)}return l(n,"")}})}}]);