<template>
  <!-- 购买套餐 -->
  <ContentWrap>
    <el-card>
      <div class="search-container">
        <el-form :model="searchForm" inline>
          <el-form-item label="ICCID：">
            <el-input 
              v-model="searchForm.iccid" 
              placeholder="请输入ICCID" 
              clearable
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item label="套餐类型：">
            <el-select 
              v-model="searchForm.packageType" 
              placeholder="请选择套餐类型"
              clearable
              style="width: 150px;"
            >
              <el-option :value="1" label="基础套餐" />
              <el-option :value="2" label="高级套餐" />
              <el-option :value="3" label="企业套餐" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button 
              v-if="hasPermission('search')"
              type="primary" 
              :loading="searchLoading"
              @click="handleSearch"
            >
              <Icon icon="ep:search" class="mr-5px" />
              查询可购套餐
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 可购套餐列表 -->
      <div style="margin-top: 20px;">
        <h3>可购套餐列表</h3>
        <el-table :data="packageData" v-loading="loading" border>
          <el-table-column prop="packageName" label="套餐名称" min-width="150" />
          <el-table-column prop="packageType" label="套餐类型" min-width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="getTypeTag(row.packageType)">
                {{ getTypeText(row.packageType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="dataAmount" label="流量(GB)" min-width="100" align="right">
            <template #default="{ row }">
              {{ (row.dataAmount / 1024).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="voiceMinutes" label="通话(分钟)" min-width="100" align="right" />
          <el-table-column prop="smsCount" label="短信(条)" min-width="100" align="right" />
          <el-table-column prop="price" label="价格(元)" min-width="100" align="right" />
          <el-table-column prop="validDays" label="有效期(天)" min-width="100" align="center" />
          <el-table-column label="操作" min-width="120" align="center" fixed="right">
            <template #default="{ row }">
              <el-button 
                v-if="hasPermission('buy')"
                type="success" 
                size="small"
                @click="handleBuy(row)"
              >
                购买
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 购买历史 -->
      <div style="margin-top: 40px;">
        <h3>购买历史</h3>
        <el-table :data="historyData" v-loading="historyLoading" border>
          <el-table-column prop="orderNo" label="订单号" min-width="150" />
          <el-table-column prop="iccid" label="ICCID" min-width="180" />
          <el-table-column prop="packageName" label="套餐名称" min-width="150" />
          <el-table-column prop="price" label="价格(元)" min-width="100" align="right" />
          <el-table-column prop="status" label="状态" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusTag(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="buyTime" label="购买时间" min-width="160" />
        </el-table>
      </div>
      
      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right;">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

// 权限检查函数
const hasPermission = (permission: string): boolean => {
  console.log(`🔍 [购买套餐权限检查] ${permission}: 允许访问`)
  return true
}

// 响应式数据
const loading = ref(false)
const historyLoading = ref(false)
const searchLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const searchForm = reactive({
  iccid: '',
  packageType: null as number | null
})

const packageData = ref<any[]>([])
const historyData = ref<any[]>([])

// 方法
const getTypeTag = (type: number): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    1: 'primary',
    2: 'success',
    3: 'warning'
  }
  return typeMap[type] || 'info'
}

const getTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '基础套餐',
    2: '高级套餐',
    3: '企业套餐'
  }
  return typeMap[type] || '未知'
}

const getStatusTag = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    'success': 'success',
    'pending': 'warning',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'success': '成功',
    'pending': '处理中',
    'failed': '失败'
  }
  return statusMap[status] || '未知'
}

const handleSearch = () => {
  if (!searchForm.iccid) {
    ElMessage.warning('请输入ICCID')
    return
  }
  getPackageData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  getHistoryData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getHistoryData()
}

const handleBuy = async (row: any) => {
  if (!searchForm.iccid) {
    ElMessage.warning('请先输入ICCID')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要为 ${searchForm.iccid} 购买套餐 "${row.packageName}" 吗？价格：${row.price}元`,
      '确认购买',
      {
        type: 'warning'
      }
    )
    
    // TODO: 实现购买API调用
    ElMessage.success('购买成功')
    getHistoryData()
  } catch (error) {
    // 用户取消操作
  }
}

// 获取可购套餐数据
const getPackageData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    packageData.value = [
      {
        id: 1,
        packageName: '基础套餐A',
        packageType: 1,
        dataAmount: 2048, // 2GB
        voiceMinutes: 100,
        smsCount: 50,
        price: 29.00,
        validDays: 30
      },
      {
        id: 2,
        packageName: '高级套餐B',
        packageType: 2,
        dataAmount: 5120, // 5GB
        voiceMinutes: 300,
        smsCount: 100,
        price: 59.00,
        validDays: 30
      }
    ]
  } catch (error) {
    ElMessage.error('获取套餐数据失败')
  } finally {
    loading.value = false
  }
}

// 获取购买历史数据
const getHistoryData = async () => {
  try {
    historyLoading.value = true
    // TODO: 实现API调用
    // 模拟数据
    historyData.value = [
      {
        orderNo: 'BUY20240101001',
        iccid: '89860000000000000001',
        packageName: '基础套餐A',
        price: 29.00,
        status: 'success',
        buyTime: '2024-01-01 10:00:00'
      }
    ]
    total.value = 1
  } catch (error) {
    ElMessage.error('获取购买历史失败')
  } finally {
    historyLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  getHistoryData()
})
</script>

<style scoped>
.search-container {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

h3 {
  color: #409eff;
  margin-bottom: 15px;
}
</style>
