(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1fdfb292"],{"317b":function(t,e,a){},"6e38":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("Card",{staticStyle:{width:"100%",padiing:"16px"}},[e("Form",{ref:"searchObj",attrs:{model:t.searchObj,"label-width":100,rules:t.rule}},[e("FormItem",{attrs:{label:"终端厂商名称"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入终端厂商",readonly:""},model:{value:t.searchObj.corpName,callback:function(e){t.$set(t.searchObj,"corpName",e)},expression:"searchObj.corpName"}})],1),"1"===t.TabFlag||""===t.TabFlag?e("FormItem",{attrs:{label:"选择月份"}},[e("DatePicker",{staticStyle:{width:"200px"},attrs:{type:"month",format:"yyyy-MM",placeholder:"请选择月份",clearable:!0},on:{"on-change":t.handleDateChange,"on-clear":t.hanldeDateClear},model:{value:t.searchObj.searchMonth,callback:function(e){t.$set(t.searchObj,"searchMonth",e)},expression:"searchObj.searchMonth"}})],1):t._e(),"2"===t.TabFlag?e("FormItem",{attrs:{label:"选择开始月份"}},[e("DatePicker",{staticStyle:{width:"200px"},attrs:{type:"month",format:"yyyy-MM",placeholder:"请选择月份",clearable:!0},on:{"on-change":t.startMonthChange,"on-clear":t.startMonthDateClear},model:{value:t.searchObj.startMonth,callback:function(e){t.$set(t.searchObj,"startMonth",e)},expression:"searchObj.startMonth"}})],1):t._e(),"2"===t.TabFlag?e("FormItem",{attrs:{label:"选择结束月份"}},[e("DatePicker",{staticStyle:{width:"200px"},attrs:{type:"month",format:"yyyy-MM",placeholder:"请选择月份",clearable:!0},on:{"on-change":t.endMonthChange,"on-clear":t.endMonthDateClear},model:{value:t.searchObj.endMonth,callback:function(e){t.$set(t.searchObj,"endMonth",e)},expression:"searchObj.endMonth"}})],1):t._e(),e("Button",{staticStyle:{margin:"0 2px"},attrs:{type:"primary",loading:t.searchLoading},on:{click:t.searchDetails}},[t._v("\n              搜索\n          ")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],staticStyle:{margin:"0 2px"},attrs:{icon:"ios-cloud-download-outline",type:"success",loading:t.exportLoading},on:{click:t.exportDetails}},[t._v("\n            导出\n          ")])],1),e("div",[e("Tabs",{attrs:{value:"1"},on:{"on-click":t.switchTab}},[e("Tab-pane",{attrs:{label:"套餐计费",name:"1"}},[e("div",[e("Table",{ref:"selection",attrs:{columns:t.columns,data:t.tableData,ellipsis:!0,loading:t.tableLoading}}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,"page-size":t.pageSize,current:t.page,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.loadByPage}})],1)]),e("Tab-pane",{attrs:{label:"流量计费",name:"2"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],staticStyle:{"margin-bottom":"10px"},attrs:{type:"success",loading:t.totalLoading,icon:"ios-cloud-download-outline"},on:{click:function(e){return t.exportTotal()}}},[t._v("导出总流量明细")]),e("div",[e("Table",{attrs:{columns:t.columns1,data:t.tableData1,ellipsis:!0,loading:t.tableLoading},scopedSlots:t._u([{key:"action",fn:function(a){var n=a.row;a.index;return[e("a",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.showDetail(n)}}},[t._v("账单详情")]),t._v(" |\n\t\t\t\t\t    "),e("a",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.showFlowDetail(n)}}},[t._v("流量明细")])]}}])}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.flowtotal,"page-size":t.flowpageSize,current:t.flowpage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.flowpage=e},"on-change":t.flowloadByPage}})],1)],1),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v("导出提示")]),e("FormItem",{attrs:{label:"你本次导出任务ID为:"}},[e("span",{staticStyle:{width:"100px"}},[t._v(t._s(t.taskId))])]),e("FormItem",{attrs:{label:"你本次导出的文件名为:"}},[e("span",[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v("请前往下载管理-下载列表查看及下载。")])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v("立即前往")])],1)])],1)],1)],1)},o=[],r=(a("14d9"),a("e9c4"),a("b64b"),a("d3b7"),a("3ca3"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("f7fa")),i={components:{},data:function(){return{taskId:"",taskName:"",searchObj:{id:"",manufacturerName:"",searchMonth:"",endMonth:"",startMonth:""},tableData:[],TabFlag:"1",tableData1:[],selection:[],selectionIds:[],tableLoading:!1,searchLoading:!1,exportLoading:!1,totalLoading:!1,exportModal:!1,total:0,pageSize:10,page:1,flowtotal:0,flowpageSize:10,flowpage:1,modeType:"",columns:[{title:"日期",key:"useDate",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"VIMSI",key:"vimsi",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"计费模式",key:"settleType",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"套餐名称/流量方向",key:"itemName",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"数量",key:"count",align:"center",minWidth:100,tooltip:!0,tooltipMaxWidth:2e3},{title:"单价",key:"price",align:"center",minWidth:100,tooltip:!0,tooltipMaxWidth:2e3},{title:"总价",key:"totalPrice",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"币种",key:"currency",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3}],columns1:[{title:"账单月份",key:"statTime",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"企业/渠道商",key:"corpName",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"国家/地区",key:"countryCn",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"流量总量(G)",key:"flowByteTotal",align:"center",minWidth:100,tooltip:!0,tooltipMaxWidth:2e3},{title:"单价/G",key:"amount",align:"center",minWidth:100,tooltip:!0,tooltipMaxWidth:2e3},{title:"币种",key:"currencyCodeName",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"账单金额",key:"totalPrice",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"账单详情",slot:"action",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3}],rule:{startMonth:[{required:!0,message:"请选择开始月份",trigger:"change"}],endMonth:[{required:!0,message:"请选择结束月份",trigger:"change"}],month:[{required:!0,message:"请选择月份",trigger:"change"}]}}},methods:{goPageFirst:function(t){var e=this;e.tableLoading=!0;var a=10,n=t;Object(r["o"])({id:this.searchObj.corpId,yearMonth:this.searchObj.searchMonth,pageNumber:n,pageSize:a}).then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.tableData=a.records,e.total=a.totalCount,e.tableLoading=!1,e.searchLoading=!1})).catch((function(t){e.tableLoading=!1,e.searchLoading=!1}))},flowgoPageFirst:function(t){var e=this,a=10,n=t,o=null,i=null;if(this.searchObj.startMonth){var l=this.searchObj.startMonth.split("-");o=l[0]+l[1]+""}if(this.searchObj.endMonth){var s=this.searchObj.endMonth.split("-");i=s[0]+s[1]+""}if(null==o||null===i)return this.$Modal.warning({title:"请选择开始月份和结束月份"}),void(e.searchLoading=!1);e.tableLoading=!0,Object(r["s"])({corpId:this.searchObj.corpId,startMonth:o,endMonth:i,pageNum:n,pageSize:a}).then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.tableData1=a,e.flowtotal=t.count,e.tableLoading=!1,e.searchLoading=!1})).catch((function(t){e.tableLoading=!1,e.searchLoading=!1}))},init:function(){for(var t=1;t<=10;t++)this.tableData.push({createTime:"2021/03/01 08:00:00",VIMSI:"VIMSI"+t,paymentMode:t%3!="1"?"1":"2",mode:t%3!="1"?"套餐"+t:"中国大陆",count:t%3!="1"?"3":"2",price:t%4!="1"?t%4=="2"?"99.00":"149.00":"199.00",totalPrice:t%4!="1"?t%4=="2"?"99.00":"149.00":"199.00",currency:"CNY"});this.total=this.tableData.length},loadByPage:function(t){this.page=t,this.goPageFirst(t)},flowloadByPage:function(t){this.flowpage=t,this.flowgoPageFirst(t)},exportDetails:function(){var t=this,e=this;if("1"===this.TabFlag)e.exportLoading=!0,Object(r["i"])({id:e.searchObj.corpId,yearMonth:e.searchObj.searchMonth}).then((function(t){var a=t.data,n=new Date,o=n.getFullYear(),r=n.getMonth()+1,i=n.getDate(),l=o+"-"+r+"-"+i,s=l+".txt";if("download"in document.createElement("a")){var c=document.createElement("a"),h=URL.createObjectURL(a);c.download=s,c.href=h,c.click(),URL.revokeObjectURL(h)}else navigator.msSaveBlob(a,s);e.exportLoading=!1})).catch((function(t){e.exportLoading=!1,e.exporting=!1}));else{var a=null,n=null;if(this.searchObj.startMonth){var o=this.searchObj.startMonth.split("-");a=o[0]+o[1]+""}if(this.searchObj.endMonth){var i=this.searchObj.endMonth.split("-");n=i[0]+i[1]+""}if(null==a||null===n)return void this.$Modal.warning({title:"请选择开始月份和结束月份"});e.exportLoading=!0,Object(r["m"])({corpId:this.searchObj.corpId,startMonth:a,endMonth:n,userId:this.$store.state.user.userId,roleId:this.$store.state.user.roleId,pageNum:this.page,pageSize:10}).then((function(e){e&&"0000"==e.code&&(t.exportModal=!0,t.taskId=e.data.taskId,t.taskName=e.data.taskName),t.exportLoading=!1})).catch((function(e){return t.exportLoading=!1}))}},exportTotal:function(){var t=this,e=null,a=null;if(this.searchObj.startMonth){var n=this.searchObj.startMonth.split("-");e=n[0]+n[1]+""}if(this.searchObj.endMonth){var o=this.searchObj.endMonth.split("-");a=o[0]+o[1]+""}null!=e&&null!==a?(this.totalLoading=!0,Object(r["j"])({corpId:this.searchObj.corpId,startMonth:e,endMonth:a,userId:this.$store.state.user.userId,roleId:this.$store.state.user.roleId,pageNum:this.page,pageSize:10}).then((function(e){e&&"0000"==e.code&&(t.exportModal=!0,t.taskId=e.data.taskId,t.taskName=e.data.taskName),t.totalLoading=!1})).catch((function(e){return t.totalLoading=!1}))):this.$Modal.warning({title:"请选择开始月份和结束月份"})},cancelModal:function(){this.exportModal=!1},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModal=!1},searchDetails:function(){this.searchLoading=!0,this.page=1,"1"===this.TabFlag||void 0===this.TabFlag?this.goPageFirst(1):this.flowgoPageFirst(1)},handleDateChange:function(t){this.searchObj.searchMonth=t},startMonthChange:function(t){this.searchObj.startMonth=t},endMonthChange:function(t){this.searchObj.endMonth=t},hanldeDateClear:function(){this.searchObj.searchMonth=""},startMonthDateClear:function(){this.searchObj.startMonth=""},endMonthDateClear:function(){this.searchObj.endMonth=""},switchTab:function(t){this.TabFlag=t,"1"===t?this.goPageFirst(1):this.flowgoPageFirst(1),console.log(this.searchObj.startMonth),console.log(this.searchObj.endMonth)},showDetail:function(t){this.$router.push({name:"billInfo",query:{billInfo:encodeURIComponent(JSON.stringify(t))}})},showFlowDetail:function(t){this.$router.push({name:"flowInfo",query:{flowInfo:encodeURIComponent(JSON.stringify(t)),corpName:encodeURIComponent(this.searchObj.corpName),month:encodeURIComponent(this.searchObj.searchMonth)}})}},mounted:function(){var t=JSON.parse(decodeURIComponent(this.$route.query.manufacturer));this.searchObj=t,this.modeType=t.settleType,this.goPageFirst(1)}},l=i,s=(a("7a9e"),a("2877")),c=Object(s["a"])(l,n,o,!1,null,null,null);e["default"]=c.exports},"7a9e":function(t,e,a){"use strict";a("317b")},f7fa:function(t,e,a){"use strict";a.d(e,"p",(function(){return r})),a.d(e,"r",(function(){return i})),a.d(e,"a",(function(){return l})),a.d(e,"u",(function(){return s})),a.d(e,"d",(function(){return c})),a.d(e,"f",(function(){return h})),a.d(e,"o",(function(){return u})),a.d(e,"i",(function(){return d})),a.d(e,"b",(function(){return p})),a.d(e,"v",(function(){return m})),a.d(e,"e",(function(){return g})),a.d(e,"h",(function(){return f})),a.d(e,"g",(function(){return b})),a.d(e,"s",(function(){return M})),a.d(e,"l",(function(){return y})),a.d(e,"k",(function(){return x})),a.d(e,"t",(function(){return w})),a.d(e,"m",(function(){return v})),a.d(e,"n",(function(){return j})),a.d(e,"j",(function(){return k})),a.d(e,"w",(function(){return O})),a.d(e,"c",(function(){return I})),a.d(e,"q",(function(){return L}));var n=a("66df"),o="/cms/api/v1",r=function(t){return n["a"].request({url:o+"/terminal/pages",params:t,method:"get"})},i=function(t){return n["a"].request({url:o+"/terminal/settleRule/queryList",params:t,method:"get"})},l=function(t){return n["a"].request({url:o+"/terminal",data:t,method:"post"})},s=function(t,e){return n["a"].request({url:o+"/terminal/"+t,data:e,method:"put"})},c=function(t,e){return n["a"].request({url:o+"/terminal/audit/"+t,params:e,method:"put"})},h=function(t){return n["a"].request({url:o+"/terminal",data:t,method:"delete"})},u=function(t){return n["a"].request({url:o+"/terminal/details",params:t,method:"get"})},d=function(t){return n["a"].request({url:o+"/terminal/details/export",params:t,responseType:"blob",method:"get"})},p=function(t){return n["a"].request({url:o+"/terminal/settleRule/add",data:t,method:"post"})},m=function(t){return n["a"].request({url:o+"/terminal/settleRule/update",data:t,method:"put"})},g=function(t){return n["a"].request({url:"/pms/api/v1/cardPool/checkPackage",params:t,method:"get"})},f=function(t){return n["a"].request({url:o+"/terminal/settleRule/delete/"+t,method:"delete"})},b=function(t){return n["a"].request({url:o+"/terminal/settleRule/deleteBatch",data:t,method:"post"})},M=function(t){return n["a"].request({url:"/stat/cdr/flow/get/list",params:t,method:"get"})},y=function(t){return n["a"].request({url:"/stat/cdr/flow/export/details",params:t,method:"get"})},x=function(t){return n["a"].request({url:"/stat/cdr/flow/export/info",params:t,method:"get"})},w=function(t){return n["a"].request({url:"/stat/cdr/flow/get/info",params:t,method:"get"})},v=function(t){return n["a"].request({url:"/stat/cdr/flow/export/list",params:t,method:"get"})},j=function(t){return n["a"].request({url:"/stat/cdr/flow/get/details",params:t,method:"get"})},k=function(t){return n["a"].request({url:"/stat/cdr/flow/export/info/all",params:t,method:"get"})},O=function(t){return n["a"].request({url:o+"/terminal/plmnlist/update",data:t,method:"post",headers:{"Content-Type":"application/json;charset=UTF-8"}})},I=function(t){return n["a"].request({url:o+"/terminal/plmnlist/createByFile",data:t,method:"post",contentType:"multipart/form-data"})},L=function(t){return n["a"].request({url:o+"/terminal/plmnlist/get",params:t,method:"get"})}}}]);