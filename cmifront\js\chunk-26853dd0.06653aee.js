(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-26853dd0"],{"1e7c":function(t,o,e){"use strict";e.r(o);e("caad"),e("2532");var s=function(){var t=this,o=t._self._c;return o("div",{staticClass:"total"},[o("Divider",[o("h1",{staticClass:"dividerBox"},[t._v(t._s(t.welcome))])]),o("h2",{staticClass:"hbox"},[t._v(t._s(t.$t("selectCooperationMode")))]),o("div",{staticClass:"typeBox"},[t.cooperationModeList.includes("1")?o("div",{staticClass:"box",on:{click:function(o){return t.routeMode("1")}}},[o("p",{staticClass:"fontBox"},[t._v(t._s(t.$t("consignmentSalesModel")))])]):t._e(),t.cooperationModeList.includes("2")?o("div",{staticClass:"box",on:{click:function(o){return t.routeMode("2")}}},[o("p",{staticClass:"fontBox"},[t._v(t._s(t.$t("A2Zmode")))])]):t._e(),t.cooperationModeList.includes("3")?o("div",{staticClass:"box",on:{click:function(o){return t.routeMode("3")}}},[o("p",{staticClass:"fontBox"},[t._v(t._s(t.$t("resourceMode")))])]):t._e()])],1)},i=[],n=(e("14d9"),{data:function(){return{welcome:"欢迎使用CMI全球卡业务网站",cooperationModeList:""}},mounted:function(){var t=this.$i18n.locale;"en-US"===t&&(this.welcome="Welcome to Global Data SIM Portal"),this.cooperationModeList=this.$route.query.modeList,document.querySelector("body").setAttribute("style","background-color:#F5F7F9; overflow: auto !important")},methods:{routeMode:function(t){sessionStorage.setItem("cooperationMode",t),this.$router.push({path:"/home"})}}}),c=n,a=(e("4408"),e("2877")),r=Object(a["a"])(c,s,i,!1,null,null,null);o["default"]=r.exports},4408:function(t,o,e){"use strict";e("bee9")},bee9:function(t,o,e){}}]);