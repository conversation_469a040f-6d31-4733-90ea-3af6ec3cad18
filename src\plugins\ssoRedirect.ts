/**
 * SSO 重定向插件
 * 从原 cmi-web 项目的 redirectPlugin.js 迁移而来
 */

import type { App } from 'vue'

interface SSORedirectOptions {
  devLocations: string[]
  testLocations: string[]
  productionLocation: string
  productionRedirect: string
  devRedirect: string
  testRedirect: string
}

/**
 * 获取当前环境对应的 SSO 重定向 URL
 * @param options SSO 配置选项
 * @returns 重定向 URL
 */
export const getRedirectUrl = (options: SSORedirectOptions): string => {
  const currentUrl = window.location.href
  const {
    devLocations,
    testLocations,
    productionLocation,
    productionRedirect,
    devRedirect,
    testRedirect
  } = options

  // 判断是否属于测试环境
  const isTestEnv = testLocations.some(location => currentUrl.includes(location))

  // 判断是否属于开发环境
  const isDevEnv = devLocations.some(location => currentUrl.includes(location))

  if (isTestEnv) {
    console.log('测试环境')
    return testRedirect
  } else if (currentUrl.includes(productionLocation)) {
    console.log('生产环境')
    return productionRedirect
  } else if (isDevEnv) {
    console.log('开发环境')
    return devRedirect
  } else {
    console.log('默认生产环境')
    return productionRedirect
  }
}

/**
 * 获取不带参数的 URL
 * @returns 清理后的 URL
 */
export const getUrlWithoutParams = (): string => {
  const url = window.location.href
  const index = url.indexOf('?')
  if (index !== -1) {
    return url.substring(0, index)
  } else {
    return url
  }
}

/**
 * 重定向只执行一次
 * @param url 重定向目标 URL
 */
export const redirectOnce = (url: string): void => {
  // 检查URL的哈希部分
  if (window.location.hash !== '#redirected') {
    // 如果未设置，则设置并重定向
    window.location.replace(`${url}#redirected`)
  }
}

/**
 * 解析 URL 参数
 * @returns 参数对象
 */
export const parseUrlParams = (): Record<string, string> => {
  const urlParams = new URLSearchParams(window.location.search)
  const params: Record<string, string> = {}
  for (const [key, value] of urlParams.entries()) {
    params[key] = value.trim()
  }
  return params
}

/**
 * 执行 SSO 重定向
 * @param options SSO 配置选项
 */
export const performSSORedirect = (options: SSORedirectOptions): void => {
  const redirectUrl = getRedirectUrl(options)
  const paramsUrl = getUrlWithoutParams()
  const url = redirectUrl + paramsUrl
  redirectOnce(url)
}

/**
 * SSO 重定向插件
 */
export const ssoRedirectPlugin = {
  install(app: App, options: SSORedirectOptions) {
    // 将 SSO 相关方法添加到全局属性
    app.config.globalProperties.$getRedirectUrl = () => getRedirectUrl(options)
    app.config.globalProperties.$getUrlWithoutParams = getUrlWithoutParams
    app.config.globalProperties.$redirectOnce = redirectOnce
    app.config.globalProperties.$parseUrlParams = parseUrlParams
    app.config.globalProperties.$performSSORedirect = () => performSSORedirect(options)

    // 提供给组合式 API 使用的方法
    app.provide('ssoRedirect', {
      getRedirectUrl: () => getRedirectUrl(options),
      getUrlWithoutParams,
      redirectOnce,
      parseUrlParams,
      performSSORedirect: () => performSSORedirect(options)
    })
  }
}

// 默认配置
export const defaultSSOConfig: SSORedirectOptions = {
  devLocations: ['94.74.107.53', '119.8.233.55'],
  testLocations: ['10.1.60.109', '10.1.60.220', '10.1.60.127'],
  productionLocation: 'gdsadmin.cmlink.com',
  productionRedirect: 'https://cas.cmitry.com/cas/login?service=',
  devRedirect: 'https://fsuat.cmitry.com/cas/login?service=',
  testRedirect: 'http://10.1.60.109:5043/cas/login?service='
}
