"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceProfilesType = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var ServiceProfilesType;
(function (ServiceProfilesType) {
    ServiceProfilesType[ServiceProfilesType["Batch"] = 'Batch'] = "Batch";
    ServiceProfilesType[ServiceProfilesType["CardReader"] = 'CardReader'] = "CardReader";
    ServiceProfilesType[ServiceProfilesType["Communication"] = 'Communication'] = "Communication";
    ServiceProfilesType[ServiceProfilesType["Loyalty"] = 'Loyalty'] = "Loyalty";
    ServiceProfilesType[ServiceProfilesType["OneTimeRes"] = 'OneTimeRes'] = "OneTimeRes";
    ServiceProfilesType[ServiceProfilesType["Pin"] = 'PIN'] = "Pin";
    ServiceProfilesType[ServiceProfilesType["Reservation"] = 'Reservation'] = "Reservation";
    ServiceProfilesType[ServiceProfilesType["Sound"] = 'Sound'] = "Sound";
    ServiceProfilesType[ServiceProfilesType["StoredValue"] = 'StoredValue'] = "StoredValue";
    ServiceProfilesType[ServiceProfilesType["Synchro"] = 'Synchro'] = "Synchro";
})(ServiceProfilesType = exports.ServiceProfilesType || (exports.ServiceProfilesType = {}));
//# sourceMappingURL=serviceProfilesType.js.map