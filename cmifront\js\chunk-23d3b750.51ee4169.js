(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-23d3b750"],{1328:function(t,e,a){},"1f3b":function(t,e,a){},2274:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",[e("Card",[e("div",{staticClass:"search_head_i"},[e("div",{staticClass:"search_box"},[e("Button",{staticStyle:{margin:"0 4px"},on:{click:t.reBack}},[e("Icon",{attrs:{type:"ios-arrow-back"}}),t._v(" "+t._s(t.$t("support.back"))+"\n\t\t\t\t")],1)],1)]),e("div",[e("Table",{attrs:{columns:t.columns,data:t.tableData,ellipsis:!0,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(a){var i=a.row;a.index;return[e("a",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],staticStyle:{"margin-right":"10px"},attrs:{href:"#",type:"primary",size:"small"},on:{click:function(e){return t.packageModal(i)}}},[t._v(t._s(t.$t("stock.details")))]),"2"!=i.packageStatus&&"6"!=i.packageStatus||"3"==i.packageType?e("a",{directives:[{name:"has",rawName:"v-has",value:"recovery",expression:"'recovery'"}],staticStyle:{"margin-right":"10px"},attrs:{href:"#",type:"primary",size:"small",disabled:""}},[t._v(t._s(t.$t("support.Recycle")))]):e("a",{directives:[{name:"has",rawName:"v-has",value:"recovery",expression:"'recovery'"}],staticStyle:{"margin-right":"10px"},attrs:{href:"#",type:"primary",size:"small"},on:{click:function(e){return t.recovery(i)}}},[t._v(t._s(t.$t("support.Recycle")))]),"2"==i.packageStatus&&"3"!=i.packageType?e("a",{directives:[{name:"has",rawName:"v-has",value:"change",expression:"'change'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.changeVIMSI(i)}}},[t._v(t._s(t.$t("support.ReplaceVIMSI")))]):e("a",{directives:[{name:"has",rawName:"v-has",value:"change",expression:"'change'"}],staticStyle:{"margin-right":"10px"},attrs:{href:"#",type:"primary",size:"small",disabled:""}},[t._v(t._s(t.$t("support.ReplaceVIMSI")))])]}},{key:"activeAt",fn:function(a){var i=a.row;a.index;return[e("a",{staticStyle:{"margin-right":"10px"},attrs:{disabled:"1"!==i.packageStatus,href:"#",type:"primary",size:"small"},on:{click:function(e){return t.updateTime(i)}}},[t._v(t._s(i.activeAt))])]}}])})],1),e("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,"page-size":t.pageSize,current:t.pageP,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.pageP=e},"on-change":t.goPage}})],1)]),e("Modal",{attrs:{title:t.$t("packageInfo"),"footer-hide":!0,"mask-closable":!1,width:"zh-CN"===this.$i18n.locale?1e3:1200},model:{value:t.packageModalFlag,callback:function(e){t.packageModalFlag=e},expression:"packageModalFlag"}},[e("noDetails",{attrs:{obj:t.obj}})],1),e("Modal",{attrs:{title:t.$t("fuelPack.packageTtart"),width:"18%","mask-closable":!1},on:{"on-cancel":t.cancelTimeModal},model:{value:t.updateTimeModal,callback:function(e){t.updateTimeModal=e},expression:"updateTimeModal"}},[e("div",{staticStyle:{"margin-top":"5px"}},[e("Form",{ref:"updateTimeObj",staticStyle:{"align-items":"center","justify-content":"center"},attrs:{model:t.updateTimeObj,rules:t.updateTimerule,"label-position":"left","label-width":80}},[e("FormItem",{attrs:{label:t.$t("fuelPack.SelectDate")+":",prop:"date"}},[e("DatePicker",{attrs:{type:"date",format:"yyyy/MM/dd",placement:"bottom-start",placeholder:t.$t("fuelPack.PleaseSelectDate"),editable:!0},on:{"on-change":t.getTime},model:{value:t.updateTimeObj.date,callback:function(e){t.$set(t.updateTimeObj,"date",e)},expression:"updateTimeObj.date"}})],1)],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelTimeModal}},[t._v(t._s(t.$t("common.cancel")))]),e("Button",{attrs:{type:"primary",loading:t.updateTimeloading},on:{click:t.submit}},[t._v(t._s(t.$t("common.determine")))])],1)])],1)},o=[],l=(a("99af"),a("4de4"),a("14d9"),a("d3b7"),a("ac1f"),a("5319"),function(){var t=this,e=t._self._c;return e("div",[e("div",[e("Form",{ref:"detailsObj",attrs:{model:t.detailsObj,"label-width":100}},[e("Row",[e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:"套餐名称"}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.packageName,callback:function(e){t.$set(t.detailsObj,"packageName",e)},expression:"detailsObj.packageName"}})],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:"周期类型"}},[e("Select",{attrs:{disabled:""},model:{value:t.detailsObj.periodUnit,callback:function(e){t.$set(t.detailsObj,"periodUnit",e)},expression:"detailsObj.periodUnit"}},t._l(t.periodUnitList,(function(a){return e("Option",{key:a.value,attrs:{value:a.value}},[t._v(t._s(a.label))])})),1)],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:"持续周期"}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.keepPeroid,callback:function(e){t.$set(t.detailsObj,"keepPeroid",e)},expression:"detailsObj.keepPeroid"}})],1)],1)],1),e("Row",[e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:"套餐过期时间"}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.overdueTime,callback:function(e){t.$set(t.detailsObj,"overdueTime",e)},expression:"detailsObj.overdueTime"}})],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:"使用时间"}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.usingTime,callback:function(e){t.$set(t.detailsObj,"usingTime",e)},expression:"detailsObj.usingTime"}})],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:"激活类型"}},[e("Select",{attrs:{disabled:""},model:{value:t.detailsObj.activationType,callback:function(e){t.$set(t.detailsObj,"activationType",e)},expression:"detailsObj.activationType"}},t._l(t.activationTypeList,(function(a){return e("Option",{key:a.value,attrs:{value:a.value}},[t._v(t._s(a.label))])})),1)],1)],1)],1),e("Row",[e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:"套餐价格"}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.priceCNY,callback:function(e){t.$set(t.detailsObj,"priceCNY",e)},expression:"detailsObj.priceCNY"}})],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:"币种:"}},[e("span",[t._v("人民币")])])],1)],1),e("Row",[e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:"最新位置"}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.position,callback:function(e){t.$set(t.detailsObj,"position",e)},expression:"detailsObj.position"}})],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:"上报时间"}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.reportingTime,callback:function(e){t.$set(t.detailsObj,"reportingTime",e)},expression:"detailsObj.reportingTime"}})],1)],1)],1),e("Row",[e("Col",{attrs:{span:"24"}},[e("Tabs",{attrs:{value:t.checked},on:{"on-click":t.tagChange}},[e("TabPane",{attrs:{label:"流量使用详情",name:"trafficInfo"}},["trafficInfo"==t.checked?e("div",[e("Table",{attrs:{columns:t.columns,data:t.tableData,ellipsis:!0,loading:t.loading,"max-height":"500"}}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,current:t.currentpage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentpage=e},"on-change":function(e){return t.loadByPage(1)}}})],1):t._e()]),e("TabPane",{attrs:{label:"VIMSI位置更新详情",name:"vimsiInfo"}},["vimsiInfo"==t.checked?e("div",[e("Table",{attrs:{columns:t.columnsV,data:t.tableDataV,ellipsis:!0,loading:t.loading,"max-height":"500"}}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.totalV,current:t.currentpageV,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentpageV=e},"on-change":function(e){return t.loadByPage(2)}}})],1):t._e()])],1)],1)],1)],1)],1)])}),n=[],r={props:{id:String},data:function(){return{checked:"trafficInfo",detailsObj:{packageName:"",periodUnit:"",keepPeroid:"",effectiveDay:"",usingTime:"",activationType:"",priceCNY:"",priceHKD:"",priceUSD:"",position:"",reportingTime:""},activationTypeList:[{value:"V",label:"V卡"},{value:"H",label:"H卡"}],periodUnitList:[{value:"24小时",label:"24小时"},{value:"每天",label:"每天"},{value:"每周",label:"每周"},{value:"每月",label:"每月"}],total:0,loading:!1,pageSize:5,currentpage:1,columns:[{title:"IMSI",key:"IMSI",align:"center",minWidth:120},{title:"卡片类型",key:"cardType",align:"center",minWidth:120},{title:"开始时间",key:"startTime",align:"center",minWidth:130},{title:"结束时间",key:"endTime",align:"center",minWidth:130},{title:"国家/地区",key:"region",align:"center",minWidth:120},{title:"流量(G)",key:"flow",align:"center",minWidth:120}],tableData:[],totalV:0,pageSizeV:5,currentpageV:1,columnsV:[{title:"VIMSI号码",key:"VIMSI",align:"center"},{title:"位置上报时间",key:"upTime",align:"center"},{title:"位置上报地点",key:"upPlace",align:"center"}],tableDataV:[{VIMSI:"521",upTime:"2021-04-06 12:52:03",upPlace:"四川省成都市"},{VIMSI:"52456",upTime:"2021-04-07 10:24:23",upPlace:"四川省成都市"}]}},methods:{init:function(){this.loading=!0,this.tableData=[{IMSI:"123455",cardType:"V卡",startTime:"2021-01-20",endTime:"2021-01-23",region:"中国大陆",flow:"12"},{IMSI:"1234556",cardType:"V卡",startTime:"2021-01-20",endTime:"2021-01-23",region:"中国香港",flow:"30"},{IMSI:"1234558",cardType:"V卡",startTime:"2021-01-20",endTime:"2021-01-23",region:"泰国",flow:"10"},{IMSI:"1234559",cardType:"V卡",startTime:"2021-01-20",endTime:"2021-01-23",region:"新加坡",flow:"11"},{IMSI:"12345553",cardType:"V卡",startTime:"2021-01-20",endTime:"2021-01-23",region:"马来西亚",flow:"12"}],this.total=this.tableData.length,this.loading=!1,this.detailsObj={packageName:"测试套餐1",periodUnit:"每月",keepPeroid:"4",overdueTime:"2021-03-16 12:12:12",priceCNY:"49.99",priceHKD:"149.99",priceUSD:"24.99",usingTime:"2021-03-01 08:00:00",activationType:"V",position:"美国",reportingTime:"2021-03-01 23:00:00"}},loadByPage:function(t){},geiDetailsById:function(t){},tagChange:function(t){this.checked=t}},mounted:function(){this.init()},watch:{id:function(t,e){this.geiDetailsById(t)}}},s=r,c=a("2877"),u=Object(c["a"])(s,l,n,!1,null,"639b21a3",null),d=u.exports,p=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{padding:"0 16px"}},[e("Form",{ref:"detailsObj",attrs:{model:t.detailsObj,"label-width":"zh-CN"===this.$i18n.locale?150:140}},[e("Row",[e("Col",{attrs:{span:"8"}},["en-US"===this.$i18n.locale?e("FormItem",{attrs:{label:t.$t("support.mealname")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.packageNameEn,callback:function(e){t.$set(t.detailsObj,"packageNameEn",e)},expression:"detailsObj.packageNameEn"}})],1):e("FormItem",{attrs:{label:t.$t("support.mealname")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.packageName,callback:function(e){t.$set(t.detailsObj,"packageName",e)},expression:"detailsObj.packageName"}})],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("deposit.mealId")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.packageId,callback:function(e){t.$set(t.detailsObj,"packageId",e)},expression:"detailsObj.packageId"}})],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",[e("Button",{attrs:{type:"primary",disabled:1==t.$route.query.isBak},on:{click:t.showMoreInfo}},[t._v(t._s(t.$t("support.informationQuery")))])],1)],1)],1),e("Row",[e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("support.Periodtype")}},[e("Select",{attrs:{disabled:""},model:{value:t.detailsObj.periodUnit,callback:function(e){t.$set(t.detailsObj,"periodUnit",e)},expression:"detailsObj.periodUnit"}},t._l(t.periodUnitList,(function(a){return e("Option",{key:a.value,attrs:{value:a.value}},[t._v(t._s(a.label)+"\n\t\t\t\t\t")])})),1)],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("support.Continuouscycle")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.keepPeriod,callback:function(e){t.$set(t.detailsObj,"keepPeriod",e)},expression:"detailsObj.keepPeriod"}})],1)],1)],1),e("Row",[e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("support.time")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.expireTime,callback:function(e){t.$set(t.detailsObj,"expireTime",e)},expression:"detailsObj.expireTime"}})],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("deposit.mealprice")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.amount,callback:function(e){t.$set(t.detailsObj,"amount",e)},expression:"detailsObj.amount"}})],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("deposit.currency")}},[e("Select",{attrs:{disabled:""},model:{value:t.detailsObj.currencyCode,callback:function(e){t.$set(t.detailsObj,"currencyCode",e)},expression:"detailsObj.currencyCode"}},t._l(t.currencyList,(function(a){return e("Option",{key:a.value,attrs:{value:a.value}},[t._v(t._s(a.label)+"\n\t\t\t\t\t")])})),1)],1)],1)],1),"1"!=t.obj.packageStatus&&"5"!=t.obj.packageStatus?e("div",[e("Row",[e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("order.ActivationTime")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.activeTime,callback:function(e){t.$set(t.detailsObj,"activeTime",e)},expression:"detailsObj.activeTime"}})],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("support.Activationtype")}},[e("Select",{attrs:{disabled:""},model:{value:t.detailsObj.activeCategory,callback:function(e){t.$set(t.detailsObj,"activeCategory",e)},expression:"detailsObj.activeCategory"}},t._l(t.activeCategoryList,(function(a){return e("Option",{key:a.value,attrs:{value:a.value}},[t._v("\n\t\t\t\t\t\t\t"+t._s(a.label))])})),1)],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("support.Usedflow")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.usedFlowBytes,callback:function(e){t.$set(t.detailsObj,"usedFlowBytes",e)},expression:"detailsObj.usedFlowBytes"}})],1)],1)],1),e("Row",[e("Col",{attrs:{span:"8"}},["en-US"===this.$i18n.locale?e("FormItem",{attrs:{label:t.$t("order.LocationUpdate")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.currentLocationEn,callback:function(e){t.$set(t.detailsObj,"currentLocationEn",e)},expression:"detailsObj.currentLocationEn"}})],1):e("FormItem",{attrs:{label:t.$t("order.LocationUpdate")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.currentLocation,callback:function(e){t.$set(t.detailsObj,"currentLocation",e)},expression:"detailsObj.currentLocation"}})],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("support.Report_time")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.reportTime,callback:function(e){t.$set(t.detailsObj,"reportTime",e)},expression:"detailsObj.reportTime"}})],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("support.DataUsedDay")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.todayUsedFlow,callback:function(e){t.$set(t.detailsObj,"todayUsedFlow",e)},expression:"detailsObj.todayUsedFlow"}})],1)],1)],1),e("Row",[e("Col",{attrs:{span:"8"}},["3"!=t.obj.packageType?e("FormItem",{attrs:{label:t.$t("support.DataRestrictionType")}},[e("Select",{style:t.getStyle(1),attrs:{disabled:""},model:{value:t.detailsObj.flowLimitType,callback:function(e){t.$set(t.detailsObj,"flowLimitType",e)},expression:"detailsObj.flowLimitType"}},[e("Option",{attrs:{value:"1"}},[t._v(t._s(t.$t("support.DataRestrictionCycle")))]),e("Option",{attrs:{value:"2"}},[t._v(t._s(t.$t("support.DataRestrictionSingle")))])],1)],1):t._e()],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("support.InternetStatus")}},["3"!=t.obj.packageType?e("Select",{attrs:{disabled:""},model:{value:t.detailsObj.surfStatus,callback:function(e){t.$set(t.detailsObj,"surfStatus",e)},expression:"detailsObj.surfStatus"}},[e("Option",{attrs:{value:"1"}},[t._v(t._s(t.$t("support.Normal")))]),e("Option",{attrs:{value:"2"}},[t._v(t._s(t.$t("support.RestrictedSpeed")))])],1):e("Select",{attrs:{disabled:""},model:{value:t.detailsObj.surfStatus,callback:function(e){t.$set(t.detailsObj,"surfStatus",e)},expression:"detailsObj.surfStatus"}},[e("Option",{attrs:{value:"1"}},[t._v(t._s(t.$t("flow.Normal")))]),e("Option",{attrs:{value:"2"}},[t._v(t._s(t.$t("flow.Cardcycle")))]),e("Option",{attrs:{value:"3"}},[t._v(t._s(t.$t("flow.Stopdatalimit")))]),e("Option",{attrs:{value:"4"}},[t._v(t._s(t.$t("flow.Restrictedspeed")))]),e("Option",{attrs:{value:"5"}},[t._v(t._s(t.$t("flow.Totallimitcard")))]),e("Option",{attrs:{value:"6"}},[t._v(t._s(t.$t("flow.Datapoollimit")))]),e("Option",{attrs:{value:"7"}},[t._v(t._s(t.$t("flow.stoppoollimit")))])],1)],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("support.hightDataCap")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.flowLimitSumStr,callback:function(e){t.$set(t.detailsObj,"flowLimitSumStr",e)},expression:"detailsObj.flowLimitSumStr"}})],1)],1)],1),e("Row",[e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("support.ControlLogicLimit")}},["3"!=t.obj.packageType?e("Select",{style:t.getStyle(2),attrs:{disabled:""},model:{value:t.detailsObj.controlLogic,callback:function(e){t.$set(t.detailsObj,"controlLogic",e)},expression:"detailsObj.controlLogic"}},[e("Option",{attrs:{value:"1"}},[t._v(t._s(t.$t("support.RestrictedSpeedLimit")))]),e("Option",{attrs:{value:"2"}},[t._v(t._s(t.$t("support.ReleaseAfterLimit")))])],1):e("Select",{style:t.getStyle(2),attrs:{disabled:""},model:{value:t.detailsObj.controlLogic,callback:function(e){t.$set(t.detailsObj,"controlLogic",e)},expression:"detailsObj.controlLogic"}},[e("Option",{attrs:{value:"1"}},[t._v(t._s(t.$t("flow.Continuelimit")))]),e("Option",{attrs:{value:"2"}},[t._v(t._s(t.$t("flow.speedlimit")))]),e("Option",{attrs:{value:"3"}},[t._v(t._s(t.$t("flow.Stoplimit")))])],1)],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("support.UsedAddonPack")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.refuelUsedFlow,callback:function(e){t.$set(t.detailsObj,"refuelUsedFlow",e)},expression:"detailsObj.refuelUsedFlow"}})],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("support.ToppedAddonPack")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.refuelRechargeFlow,callback:function(e){t.$set(t.detailsObj,"refuelRechargeFlow",e)},expression:"detailsObj.refuelRechargeFlow"}})],1)],1)],1),e("Row",{directives:[{name:"show",rawName:"v-show",value:t.detailsObj.isUsing,expression:"detailsObj.isUsing"}]},[e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("support.Available")}},[e("Tooltip",{attrs:{placement:"top"}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.operatorName,callback:function(e){t.$set(t.detailsObj,"operatorName",e)},expression:"detailsObj.operatorName"}}),e("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v("\n\t\t\t\t\t\t\t"+t._s(t.detailsObj.operatorNametip)+"\n\t\t\t\t\t\t")])],1)],1)],1),e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:"APN"}},[e("Tooltip",{attrs:{placement:"top"}},["zh-CN"===t.$i18n.locale?e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.apnZh,callback:function(e){t.$set(t.detailsObj,"apnZh",e)},expression:"detailsObj.apnZh"}}):e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.apnEn,callback:function(e){t.$set(t.detailsObj,"apnEn",e)},expression:"detailsObj.apnEn"}}),"zh-CN"===t.$i18n.locale?e("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v("\n\t\t\t\t\t\t\t"+t._s(t.detailsObj.apnZhtip)+"\n\t\t\t\t\t\t")]):e("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v("\n\t\t\t\t\t\t\t"+t._s(t.detailsObj.apnEntip)+"\n\t\t\t\t\t\t")])],1)],1)],1)],1),e("Row",[t.detailsObj.supportHotspot?e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("support.isSupportHot")}},[e("Select",{attrs:{disabled:""},model:{value:t.detailsObj.supportHotspot,callback:function(e){t.$set(t.detailsObj,"supportHotspot",e)},expression:"detailsObj.supportHotspot"}},[e("Option",{attrs:{value:"1"}},[t._v(t._s(t.$t("order.yes")))]),e("Option",{attrs:{value:"2"}},[t._v(t._s(t.$t("order.no")))])],1)],1)],1):t._e(),t.shouldShowUpccRate?e("Col",{attrs:{span:"8"}},[e("FormItem",{attrs:{label:t.$t("support.internetTemplateSpeed")}},[e("Input",{attrs:{readonly:""},model:{value:t.detailsObj.upccRate,callback:function(e){t.$set(t.detailsObj,"upccRate",e)},expression:"detailsObj.upccRate"}})],1)],1):t._e()],1),e("Row",[e("Col",{attrs:{span:"24"}},[e("Tabs",{attrs:{value:t.checked},on:{"on-click":t.tagChange}},[e("TabPane",{attrs:{label:t.$t("support.usedetails"),name:"trafficInfo"}},["trafficInfo"==t.checked?e("div",[e("div",{staticStyle:{display:"flex",width:"100%"}},[e("Table",{staticStyle:{display:"flex",width:"100%"},attrs:{columns:t.columnsT,data:t.tableData1,ellipsis:!0,loading:t.loading,"max-height":"500"}}),e("div",{staticStyle:{"margin-left":"5px"}},[e("div",[e("Button",{attrs:{long:""},on:{click:function(e){return t.Flowdetails()}}},[t._v(t._s(t.$t("support.Flowdetails")))])],1),e("div",{staticStyle:{"margin-top":"25px"}},[e("Button",{attrs:{disabled:1==t.$route.query.isBak,long:""},on:{click:function(e){return t.CDRdetailsBtn()}}},[t._v(t._s(t.$t("support.cdrDetails")))])],1)])],1),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,current:t.currentpageT,"page-size":t.pageSize,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentpageT=e},"on-change":t.loadTraffic}})],1):t._e()]),e("TabPane",{attrs:{label:t.$t("support.VIMSILocation"),name:"vimsiInfo"}},["vimsiInfo"==t.checked?e("div",[e("Table",{attrs:{columns:t.columnsV,data:t.tableData,ellipsis:!0,loading:t.loading,"max-height":"500"}}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,current:t.currentpageV,"page-size":t.pageSize,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentpageV=e},"on-change":t.loadVimsi}})],1):t._e()]),e("TabPane",{attrs:{label:t.$t("support.IMSIdetails"),name:"vimsiAllocation"}},["vimsiAllocation"==t.checked?e("div",[e("Table",{attrs:{columns:t.columnsA,data:t.tableData,ellipsis:!0,loading:t.loading,"max-height":"500"},scopedSlots:t._u([{key:"flow",fn:function(a){var i=a.row;a.index;return["2"==t.detailsObj.packageStatus||"6"==t.detailsObj.packageStatus?e("a",{attrs:{href:"#",type:"primary",size:"small"},on:{click:function(e){return t.searchCardFlow("2",i.imsi)}}},[t._v(t._s(t.$t("support.cardtraffic")))]):e("a",{attrs:{href:"#",type:"primary",size:"small",disabled:""}},[t._v(t._s(t.$t("support.cardtraffic")))])]}}],null,!1,2147783868)}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,current:t.currentpageA,"page-size":t.pageSize,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentpageA=e},"on-change":t.loadAllocation}})],1):t._e()]),0!=t.appTableData.length?e("TabPane",{attrs:{label:t.$t("support.targetedAppDetails"),name:"AppInfo"}},["AppInfo"==t.checked?e("div",[e("Table",{attrs:{border:"",columns:t.columnsApp,data:t.appTableData,ellipsis:!0,loading:t.loading,"max-height":"500","span-method":t.handleSpan}})],1):t._e()]):t._e()],1)],1)],1)],1):t._e()],1),t.spinShow?e("Spin",{attrs:{size:"large",fix:""}}):t._e(),e("Modal",{attrs:{title:t.$t("support.cardflow"),"footer-hide":!0,"mask-closable":!1,width:"400px"},model:{value:t.cardFlowFlag,callback:function(e){t.cardFlowFlag=e},expression:"cardFlowFlag"}},[e("Form",{ref:"cardFlow",attrs:{"label-position":"left",model:t.cardFlow,"label-width":130}},[e("FormItem",{attrs:{label:t.$t("support.packageflow")}},[e("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:t.cardFlow.totalFlow,callback:function(e){t.$set(t.cardFlow,"totalFlow",e)},expression:"cardFlow.totalFlow"}})],1),e("FormItem",{attrs:{label:t.$t("support.remainingflow")}},[e("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:t.cardFlow.surplusFlow,callback:function(e){t.$set(t.cardFlow,"surplusFlow",e)},expression:"cardFlow.surplusFlow"}})],1),e("FormItem",{attrs:{label:t.$t("support.Usedtraffic")}},[e("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:t.cardFlow.usedFlow,callback:function(e){t.$set(t.cardFlow,"usedFlow",e)},expression:"cardFlow.usedFlow"}})],1)],1)],1),e("Modal",{attrs:{title:t.$t("support.cardflow"),"footer-hide":!0,"mask-closable":!1,width:"500px"},model:{value:t.cardFlowFlagEn,callback:function(e){t.cardFlowFlagEn=e},expression:"cardFlowFlagEn"}},[e("Form",{ref:"cardFlow",attrs:{"label-position":"left",model:t.cardFlow,"label-width":270}},[e("FormItem",{attrs:{label:t.$t("support.packageflow")}},[e("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:t.cardFlow.totalFlow,callback:function(e){t.$set(t.cardFlow,"totalFlow",e)},expression:"cardFlow.totalFlow"}})],1),e("FormItem",{attrs:{label:t.$t("support.remainingflow")}},[e("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:t.cardFlow.surplusFlow,callback:function(e){t.$set(t.cardFlow,"surplusFlow",e)},expression:"cardFlow.surplusFlow"}})],1),e("FormItem",{attrs:{label:t.$t("support.Usedtraffic")}},[e("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:t.cardFlow.usedFlow,callback:function(e){t.$set(t.cardFlow,"usedFlow",e)},expression:"cardFlow.usedFlow"}})],1)],1)],1),e("a",{ref:"downloadLink",staticStyle:{display:"none"}}),e("Modal",{attrs:{title:t.$t("support.Flowdetails"),"mask-closable":!0,width:"900px"},on:{"on-cancel":t.cancelModal},model:{value:t.flowdetailsFlag,callback:function(e){t.flowdetailsFlag=e},expression:"flowdetailsFlag"}},[e("Table",{attrs:{columns:t.columnFl,data:t.tableDataFlow,ellipsis:!0,loading:t.Flowloading,"max-height":"500"}}),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v(t._s(t.$t("support.close")))]),e("Button",{attrs:{type:"primary",loading:t.downloading},on:{click:t.exportFlow}},[t._v(t._s(t.$t("stock.exporttb")))])],1)],1),e("Modal",{attrs:{title:t.$t("support.cdrDetails"),"footer-hide":!0,"mask-closable":!1,width:"1600"},on:{"on-cancel":t.cancelModal},model:{value:t.CDRdetailsFlag,callback:function(e){t.CDRdetailsFlag=e},expression:"CDRdetailsFlag"}},[e("Form",{ref:"cardFlow",attrs:{"label-position":"left",model:t.cardFlow,"label-width":70}},[e("Row",[e("Col",{attrs:{span:"6"}},[e("FormItem",{attrs:{label:t.$t("support.coverageTime")}},[e("Select",{attrs:{clearable:""},model:{value:t.CDRCoverageTime,callback:function(e){t.CDRCoverageTime=e},expression:"CDRCoverageTime"}},t._l(t.CDRCoverageTimeList,(function(a){return e("Option",{key:a,attrs:{value:a}},[t._v(t._s(a)+"\n\t\t\t\t\t\t")])})),1)],1)],1),e("Col",{attrs:{span:"4"}},[e("Button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary"},on:{click:function(e){return t.CDRdetails()}}},[t._v(t._s(t.$t("flow.select")))])],1)],1)],1),e("Table",{attrs:{columns:t.columnCDR,data:t.tableDataCDR,ellipsis:!0,loading:t.CDRloading,"max-height":"500"}}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.CDRtotal,current:t.CDRcurrentpage,"page-size":t.CDRpageSize,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.CDRcurrentpage=e},"on-change":t.CDRChange}})],1),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancel},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v(t._s(t.$t("exportMS")))]),e("FormItem",{attrs:{label:t.$t("exportID")}},[e("span",{staticStyle:{width:"100px"}},[t._v(t._s(t.taskId))])]),e("FormItem",{attrs:{label:t.$t("exportFlie")}},[e("span",[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v(t._s(t.$t("downloadResult")))])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancel}},[t._v(t._s(t.$t("common.cancel")))]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v(t._s(t.$t("Goto")))])],1)]),e("Modal",{attrs:{title:t.$t("flow.Details"),"footer-hide":!0,"mask-closable":!1,width:"900"},model:{value:t.moreInfoFlag,callback:function(e){t.moreInfoFlag=e},expression:"moreInfoFlag"}},[e("Form",{ref:"tableDataMoreInfo",attrs:{model:t.tableDataMoreInfo,"label-width":"zh-CN"===this.$i18n.locale?150:140}},[e("div",[e("Row",{staticStyle:{display:"flex","flex-wrap":"wrap","flex-direction":"column"}},t._l(t.tableDataMoreInfo.consumptionList,(function(a,i){return e("div",{key:i,staticStyle:{display:"flex","flex-wrap":"wrap","margin-bottom":"10px"}},[e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:t.$t("support.usage2")}},[e("Input",{staticStyle:{width:"200px"},attrs:{value:a.consumption,readonly:""}})],1)],1),e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:t.$t("support.speedTemplate")}},[e("Input",{staticStyle:{width:"200px"},attrs:{value:a.templateName,readonly:""}})],1)],1)],1)})),0)],1),e("Row",t._l(t.tableDataMoreInfo.noLimitTemplate,(function(a,i){return e("div",{key:i,staticStyle:{display:"flex","flex-wrap":"wrap","margin-bottom":"10px"}},[e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:t.$t("support.unlimitedUsageTemplate"),prop:"noLimitTemplateId"}},[e("Input",{staticStyle:{width:"200px"},attrs:{readonly:"",value:a.templateName}})],1)],1)],1)})),0)],1)],1)],1)},m=[],g=a("c7eb"),f=a("1da1"),h=(a("caad"),a("a434"),a("a9e3"),a("b680"),a("25f0"),a("159b"),a("8ba4")),b=(a("e069"),a("c70b"),{props:{obj:Object},data:function(){return{cardFlowFlag:!1,cardFlowFlagEn:!1,flowdetailsFlag:!1,Flowloading:!1,exportModal:!1,downloading:!1,taskId:"",taskName:"",Flowtotal:0,FlowpageSize:5,Flowpage:1,CDRtotal:0,CDRpageSize:10,CDRpage:1,CDRcurrentpage:1,CDRCoverageTime:"",CDRCoverageTimeList:[],cardFlow:{totalFlow:"",surplusFlow:"",usedFlow:""},detailsObj:{packageName:"",packageNameEn:"",periodUnit:"",keepPeriod:"",expireTime:"",amount:"",currencyCode:"",activeTime:"",activeCategory:"",currentLocation:"",currentLocationEn:"",reportTime:"",supportHotspot:"",upccRate:""},periodUnitList:[{value:"1",label:this.$t("buymeal.hour")},{value:"2",label:this.$t("buymeal.day")},{value:"3",label:this.$t("buymeal.month")},{value:"4",label:this.$t("buymeal.year")}],activeCategoryList:[{value:"2",label:this.$t("support.Vcard")},{value:"1",label:this.$t("support.Hcard")}],currencyList:[{value:"156",label:this.$t("support.CNY")},{value:"840",label:this.$t("support.USD")},{value:"344",label:this.$t("support.HKD")}],checked:"trafficInfo",total:0,tableData:[],appTableData:[],tableData1:[],tableDataFlow:[],loading:!1,pageSize:5,currentpageT:1,currentpageV:1,currentpageA:1,columnsT:[],columnsV:[],columnsA:[],columnFl:[],columnsApp:[],packageUniqueId:"",spinShow:!1,moreInfoFlag:!1,columnMoreInfo:[],tableDataMoreInfo:{noLimitTemplate:[],consumptionList:[]},MoreInfoLoading:!1,CDRdetailsFlag:!1,columnCDR:[{title:"himsi",key:"himsi",align:"center",width:155,tooltip:!0},{title:"IMSI_with_usage",key:"imsi",align:"center",width:155,tooltip:!0},{title:"start_time",key:"startTime",align:"center",width:160,tooltip:!0},{title:"data_vol_total_kb",key:"dataVolTotal",align:"center",width:150},{title:"msisdn",key:"msisdn",align:"center"},{title:"mcc",key:"mcc",align:"center",width:80},{title:"mnc",key:"mnc",align:"center",width:80},{title:"tapcode",key:"tapcode",align:"center",maxWidth:100,tooltip:!0},{title:"sgsn_address",key:"sgsnAddress",align:"center",render:function(t,e){return t("span",e.row.sgsnAddress?e.row.sgsnAddress:"-")},width:120,tooltip:!0},{title:"data_vol_uplink_kb ",key:"dataVolUplink",align:"center",width:165},{title:"data_vol_downlink_kb",key:"dataVolDownlink",align:"center",width:165},{title:"apn",key:"apn",align:"center",maxWidth:90,tooltip:!0}],tableDataCDR:[],CDRloading:!1}},methods:{goDetailInfo:function(t){var e=this;this.spinShow=!0,Object(h["l"])({pageNumber:-1,pageSize:-1,imsi:this.$route.query.imsi,iccid:this.$route.query.iccid,expiredData:1,cooperationMode:sessionStorage.getItem("cooperationMode"),packageUniqueId:t}).then((function(t){if(!t||"0000"!=t.code)throw t;e.spinShow=!1,e.detailsObj=Object.assign({},t.data.records[0]),e.detailsObj.operatorNametip=e.detailsObj.operatorName,e.detailsObj.operatorName=e.detailsObj.operatorName&&e.detailsObj.operatorName.length>20?e.detailsObj.operatorName.substring(0,20)+"...":e.detailsObj.operatorName,e.detailsObj.apnEntip=e.detailsObj.apnEn,e.detailsObj.apnEn=e.detailsObj.apnEn&&e.detailsObj.apnEn.length>20?e.detailsObj.apnEn.substring(0,20)+"...":e.detailsObj.apnEn,e.detailsObj.apnZhtip=e.detailsObj.apnZh,e.detailsObj.apnZh=e.detailsObj.apnZh&&e.detailsObj.apnZh.length>20?e.detailsObj.apnZh.substring(0,20)+"...":e.detailsObj.apnZh})).catch((function(t){e.spinShow=!1,console.log(t)})).finally((function(){e.spinShow=!1}))},geiDetails:function(t){var e=this;this.packageId=t.packageId,this.packageUniqueId=t.packageUniqueId,this.goDetailInfo(t.packageUniqueId),this.$nextTick((function(){e.loadApp()})),this.getNewReportTime(),"1"!=t.packageStatus&&(this.currentpageT=1,this.currentpageV=1,this.loadColumns(),this.loadTraffic(this.currentpageT),this.checked="trafficInfo")},getNewReportTime:function(){var t=this;Object(h["p"])({imsi:this.$route.query.imsi,packageUniqueId:this.obj.packageUniqueId,pageNumber:1,pageSize:1}).then((function(e){if(!e||"0000"!=e.code)throw e;var a=e.data;if(null!=a.records&&a.records.length>0){var i=t.detailsObj.reportTime,o=a.records[0].reportTime;t.detailsObj.reportTime=o>=i?o:i}})).catch((function(t){console.log(t)})).finally((function(){}))},Flowdetails:function(){var t=this,e=this.obj.packageUniqueId;Object(h["e"])(e).then((function(e){e&&"0000"==e.code&&(t.tableDataFlow=e.data,t.flowdetailsFlag=!0)})).catch((function(t){console.log(t)}))},CDRdetailsBtn:function(){var t=this;return Object(f["a"])(Object(g["a"])().mark((function e(){return Object(g["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(h["i"])().then((function(e){e&&"0000"==e.code&&(t.CDRCoverageTimeList=e.data)})).catch((function(t){console.log(t)}));case 2:t.CDRdetails();case 3:case"end":return e.stop()}}),e)})))()},CDRdetails:function(){var t=this;return Object(f["a"])(Object(g["a"])().mark((function e(){var a;return Object(g["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a={coverHour:t.CDRCoverageTime,packageUniqueId:t.obj.packageUniqueId,pageSize:t.CDRpageSize,pageNum:t.CDRcurrentpage},e.next=3,Object(h["h"])(a).then((function(e){e&&"0000"==e.code&&(t.tableDataCDR=e.data.records,t.CDRdetailsFlag=!0,t.CDRtotal=Number(e.data.totalCount))})).catch((function(t){console.log(t)}));case 3:case"end":return e.stop()}}),e)})))()},CDRChange:function(t){this.CDRcurrentpage=t,this.CDRdetails()},cancelModal:function(){this.flowdetailsFlag=!1,this.CDRCoverageTime=""},cancel:function(){this.exportModal=!1},exportFlow:function(){var t=this,e=this.obj.packageUniqueId,a=this.$store.state.user.userId;this.downloading=!0,Object(h["d"])({packageUniqueId:e,userId:a}).then((function(e){e&&"0000"==e.code&&(t.exportModal=!0,t.taskId=e.data.taskId,t.taskName=e.data.taskName),t.downloading=!1})).catch((function(e){t.downloading=!1,t.exportModal=!1}))},getStyle:function(t){return 1===t&&"en-US"===this.$i18n.locale||2===t&&"en-US"===this.$i18n.locale?{width:"240px"}:void 0},handleSpan:function(t){var e=t.row,a=(t.column,t.rowIndex,t.columnIndex);if(1===a||2===a||4===a){var i=0==e.mergeCol?0:e.mergeCol,o=0==e.mergeCol?0:1;return[i,o]}},assembleData:function(t){var e=[];t.forEach((function(t){e.includes(t.groupId)||e.push(t.groupId)}));var a=[];e.forEach((function(t){a.push({groupId:t,num:0})})),t.forEach((function(t){a.forEach((function(e){t.groupId==e.groupId&&e.num++}))})),t.forEach((function(t){a.forEach((function(a){t.groupId==a.groupId&&(e.includes(t.groupId)?(t.mergeCol=a.num,e.splice(e.indexOf(a.groupId),1)):t.mergeCol=0)}))})),this.appTableData=t},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModal=!1},tagChange:function(t){this.resetState(),this.checked=t,"trafficInfo"===t&&this.loadTraffic(this.currentpageT),"vimsiInfo"===t&&this.loadVimsi(this.currentpageV),"vimsiAllocation"===t&&this.loadAllocation(this.currentpageA)},loadColumns:function(){var t=this;this.columnsT=[{title:"IMSI",key:"imsi",align:"center",minWidth:120},{title:this.$t("support.Cardtype"),key:"cardType",align:"center",minWidth:120},{title:this.$t("support.date"),key:"statTime",align:"center",minWidth:130},{title:this.$t("support.used_flow"),key:"flowByteTotal",align:"center",minWidth:120}],this.columnsV=[{title:this.$t("support.VIMSIphone"),key:"imsi",align:"center"},{title:this.$t("support.TimeLocation"),key:"reportTime",align:"center"},{title:this.$t("support.Location"),key:"mcc",align:"center",render:function(e,a){var i=a.row,o="zh-CN"===t.$i18n.locale?i.mcc:"en-US"===t.$i18n.locale?i.mccEn:"";return e("label",o)}}],this.columnsA=[{title:this.$t("support.VIMSIphone"),key:"imsi",align:"center"},{title:this.$t("support.imsiType"),key:"internetType",align:"center",render:function(e,a){var i=a.row,o="1"==i.internetType?t.$t("support.Hcard"):"2"==i.internetType?t.$t("support.Vcard"):"";return e("label",o)}},{title:this.$t("support.useCountry"),key:"mcc",align:"center",tooltip:!0,render:function(e,a){var i=a.row,o="zh-CN"===t.$i18n.locale?i.mcc:"en-US"===t.$i18n.locale?i.mccEn:"";return e("label",o)}},{title:this.$t("support.imsiResource"),key:"supplierName",align:"center",tooltip:!0,render:function(t,e){var a=e.row,i=a.supplierName?a.supplierName:"\\";return t("label",i)}}],this.columnsApp=[{title:this.$t("directionalApp.application"),key:"appName",align:"center"},{title:this.$t("deposit.charge_type"),key:"type",align:"center",render:function(e,a){var i=a.row,o="1"==i.type?t.$t("flow.Restricted"):"2"==i.type?t.$t("directionalApp.freeFlow"):"";return e("label",o)}},{title:this.$t("flow.totalusage"),key:"totleFlow",align:"center"},{title:this.$t("support.Usedflow"),key:"usedFlow",align:"center"},{title:this.$t("fuelPack.onlinestatus"),key:"surfStatus",align:"center",render:function(e,a){var i=a.row,o="1"==i.surfStatus?t.$t("support.Normal"):"2"==i.surfStatus?t.$t("flow.Restricted"):"";return e("label",o)}}],this.columnFl=[{title:"IMSI",key:"imsi",align:"center",minWidth:150},{title:this.$t("support.Cardtype"),key:"cardType",align:"center",minWidth:120},{title:this.$t("support.date"),key:"statTime",align:"center",minWidth:120},{title:this.$t("buymeal.Country"),key:"countryCn",align:"center",minWidth:120,render:function(e,a){var i=a.row,o="zh-CN"===t.$i18n.locale?i.countryCn.toString():"en-US"===t.$i18n.locale?i.countryEn.toString():" ";return e("label",o)}},{title:this.$t("support.operatorName"),key:"operatorName",align:"center",minWidth:120},{title:this.$t("support.usageTotal"),key:"flowByteTotal",align:"center",minWidth:120}]},searchCardFlow:function(t,e){var a=this;this.cardFlow={totalFlow:"",surplusFlow:"",usedFlow:""},"zh-CN"===this.$i18n.locale&&(this.cardFlowFlag=!1),"en-US"===this.$i18n.locale&&(this.cardFlowFlagEn=!1),Object(h["s"])({cardType:t,imsi:e,packageUniqueId:this.obj.packageUniqueId}).then((function(t){if(!t||"0000"!=t.code)throw a.$Notice.error({title:a.$t("address.Operationreminder"),desc:t.msg}),t;var e=t.data;a.cardFlow={totalFlow:Number(Number(e.totalFlow)/1024).toFixed(2),surplusFlow:Number(Number(e.surplusFlow)/1024).toFixed(2),usedFlow:Number(Number(e.usedFlow)/1024).toFixed(2)},"zh-CN"===a.$i18n.locale&&(a.cardFlowFlag=!0),"en-US"===a.$i18n.locale&&(a.cardFlowFlagEn=!0)})).catch((function(t){console.log(t)})).finally((function(){}))},loadTraffic:function(t){var e=this;this.currentpageT=t,this.loading=!0;var a=this.obj.packageUniqueId,i={endTime:this.obj.expireTime,isPage:0,pageNum:t,pageSize:this.pageSize,startTime:this.obj.activeTime};Object(h["n"])(a,i).then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.tableData1=a,e.total=t.count})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1}))},loadVimsi:function(t){var e=this;this.currentpageV=t,this.loading=!0,Object(h["p"])({imsi:this.$route.query.imsi,packageUniqueId:this.obj.packageUniqueId,pageNumber:t,pageSize:this.pageSize,iccid:this.$route.query.iccid}).then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.tableData=a.records,e.total=a.totalCount})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1}))},loadAllocation:function(t){var e=this;this.currentpageA=t,this.loading=!0,Object(h["f"])({imsi:this.$route.query.imsi,packageUniqueId:this.obj.packageUniqueId,mcc:"460",pageNumber:t,pageSize:this.pageSize,iccid:this.$route.query.iccid}).then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.tableData=a.records,e.total=a.totalCount})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1}))},loadApp:function(){var t=this;this.loading=!0,Object(h["g"])({imsi:this.$route.query.imsi,packageId:this.packageId,packageUniqueId:this.packageUniqueId}).then((function(e){if(!e||"0000"!=e.code)throw e;t.appTableData=e.data,t.assembleData(t.appTableData)})).catch((function(t){console.log(t)})).finally((function(){t.loading=!1}))},showMoreInfo:function(){var t=this;Object(h["j"])({packageUniqueId:this.packageUniqueId}).then((function(e){e&&"0000"==e.code&&(e.data.length>0?(t.tableDataMoreInfo.consumptionList=e.data.filter((function(t){return"999999999999999999"!=t.consumption})),t.tableDataMoreInfo.noLimitTemplate=e.data.filter((function(t){return"999999999999999999"==t.consumption})),t.moreInfoFlag=!0):t.$Message.info(t.$t("support.emptyData")))})).catch((function(t){console.log(t)}))},resetState:function(){this.currentpageT=1,this.currentpageV=1,this.currentpageA=1,this.total=0,this.tableData=[],this.tableData1=[],this.checked="trafficInfo"}},mounted:function(){},computed:{shouldShowUpccRate:function(){return null!==this.detailsObj.upccRate&&void 0!==this.detailsObj.upccRate&&""!==this.detailsObj.upccRate}},watch:{obj:function(t,e){this.geiDetails(t)}}}),v=b,w=(a("e137"),Object(c["a"])(v,p,m,!1,null,"50ce20a8",null)),y=w.exports,k=(a("c70b"),{components:{Details:d,noDetails:y},data:function(){return{row:null,columns:[],action:[],tableData:[],updateTimeObj:{date:""},loading:!1,pageP:1,total:0,pageSize:10,packageModalFlag:!1,updateTimeModal:!1,updateTimeloading:!1,obj:{},type:"",updateTimerule:{date:[{type:"date",required:!0,message:this.$t("fuelPack.PleaseSelectDate")}]}}},computed:{},methods:{init:function(){var t=this;this.columns=[{title:this.$t("support.mealname"),key:"packageName",tooltip:!0,align:"center",minWidth:150,render:function(e,a){var i=a.row,o="zh-CN"===t.$i18n.locale?i.packageName:"en-US"===t.$i18n.locale?i.packageNameEn:" ";return e("label",{style:{"word-break":"break-word"}},o)}},{title:this.$t("support.Packagestatus"),key:"packageStatus",align:"center",minWidth:130,render:function(e,a){var i=a.row,o="",l=(new Date).getTime();switch(i.packageStatus){case"1":o=t.$t("support.Unuse");break;case"2":var n=new Date(i.expireTime),r=n.getTime();o=l>r?t.$t("support.Used"):t.$t("support.activated");break;case"3":o=t.$t("support.Used");break;case"5":o=t.$t("support.Expired");break;case"6":n=new Date(i.effectiveDay),r=n.getTime();o=l>r?t.$t("support.Used"):t.$t("support.Activating");break;default:o=" "}return o="2"!==i.orderPackageStatus&&"3"!==i.orderPackageStatus&&"5"!==i.orderPackageStatus&&"6"!==i.orderPackageStatus||"3"!==i.orderStatus?o:t.$t("support.Refunded"),e("label",o)}},{title:this.$t("support.Activationtype"),key:"activeCategory",align:"center",minWidth:130,render:function(e,a){var i=a.row,o="2"==i.activeCategory?t.$t("support.Vcard"):"1"==i.activeCategory?t.$t("support.Hcard"):"-";return e("label",o)}},{title:this.$t("support.Ordertime"),tooltip:!0,tooltipTheme:"light",tooltipMaxWidth:600,key:"orderTime",align:"center",minWidth:150},{title:this.$t("support.activeTime"),tooltip:!0,tooltipTheme:"light",tooltipMaxWidth:600,key:"activeTime",align:"center",minWidth:150},{title:this.$t("flow.expirationDate"),tooltip:!0,tooltipTheme:"light",tooltipMaxWidth:600,key:"expireTime",align:"center",minWidth:150},{title:this.$t("support.latestActivationDate"),key:"effectiveDay",tooltip:!0,tooltipTheme:"light",tooltipMaxWidth:600,align:"center",minWidth:160},{title:this.$t("support.Ordernumber"),tooltip:!0,tooltipTheme:"light",tooltipMaxWidth:600,key:"orderNumber",align:"center",minWidth:160},{title:this.$t("order.order_money"),key:"amount",align:"center",minWidth:120},{title:this.$t("deposit.currency"),key:"currencyCode",align:"center",minWidth:120,render:function(e,a){var i=a.row,o="";switch(i.currencyCode){case"156":o=t.$t("support.CNY");break;case"840":o=t.$t("support.USD");break;case"344":o=t.$t("support.HKD");break;default:o=" "}return e("label",o)}},{title:this.$t("support.Orderchannel"),key:"orderChannel",align:"center",minWidth:120,render:function(e,a){var i=a.row,o="";switch(i.orderChannel){case"101":o=t.$t("order.WeChat");break;case"102":o="API";break;case"103":o=t.$t("order.Website");break;case"104":o=t.$t("order.BeijingMobile");break;case"105":o=t.$t("order.BulkOrder");break;case"106":o=t.$t("order.Trial");break;case"110":o=t.$t("order.Testing");break;case"111":o=t.$t("order.issuance");break;case"112":o=t.$t("order.Postpaid");break;case"113":o="WEB";break;case"114":o=t.$t("order.Datapool");break;case"115":o=t.$t("support.flowpoolApi");break;default:o=" "}return e("label",o)}},{title:this.$t("support.DataRestrictionType"),key:"flowLimitType",align:"center",minWidth:280,render:function(e,a){var i=a.row,o="1"===i.flowLimitType?t.$t("support.DataRestrictionCycle"):"2"===i.flowLimitType?t.$t("support.DataRestrictionSingle"):" ";return e("label",o)}},{title:this.$t("support.DataCap")+"(MB)",key:"flowLimitSum",align:"center",minWidth:150},{title:this.$t("fuelPack.onlinestatus"),key:"surfStatus",align:"center",minWidth:250,render:function(e,a){var i=a.row,o="",l="1"===i.surfStatus?t.$t("support.Normal"):"2"===i.surfStatus?t.$t("support.RestrictedSpeed"):" ",n="1"===i.surfStatus?t.$t("flow.Normal"):"2"===i.surfStatus?t.$t("flow.Cardcycle"):"3"===i.surfStatus?t.$t("flow.Stopdatalimit"):"4"===i.surfStatus?t.$t("flow.Restrictedspeed"):"5"===i.surfStatus?t.$t("flow.Totallimitcard"):"6"===i.surfStatus?t.$t("flow.Datapoollimit"):"7"===i.surfStatus?t.$t("flow.stoppoollimit"):" ";o="3"!=i.packageType?l:n;var r=""===o||null===o?0:o.length;if(r>20){var s=o.replace(/\|/g,"</br>");return o=o.substring(0,20)+"...",e("div",[e("Tooltip",{props:{placement:"bottom",transfer:!0},style:{cursor:"pointer"}},[o,e("label",{slot:"content",style:{whiteSpace:"normal"},domProps:{innerHTML:s}})])])}return o=o,e("label",o)}},{title:this.$t("fuelPack.activationdate"),slot:"activeAt",align:"center",minWidth:220}];var e=["view","recovery","change"],a=this.$route.meta.permTypes,i=e.filter((function(t){return a.indexOf(t)>-1}));if(i.length>0){var o=100+60*i.length;this.action.push({title:this.$t("support.action"),slot:"action",align:"center",width:o,fixed:"right"}),this.columns=this.columns.concat(this.action)}localStorage.setItem("MSISDN",this.$route.query.backmsisdn),localStorage.setItem("ICCID",this.$route.query.backiccid),localStorage.setItem("IMSI",this.$route.query.backimsi),this.goPageFirst(1)},goPageFirst:function(t){var e=this;this.pageP=t,this.loading=!0,Object(h["t"])({pageNumber:t,pageSize:this.pageSize,imsi:this.$route.query.imsi,iccid:this.$route.query.iccid,expiredData:1}).then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.tableData=a.records,e.total=a.totalCount})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1}))},recovery:function(t){var e=this,a=t.packageStatus,i=t.expireTime,o=t.effectiveDay;return"2"==a&&null!=i&&void 0!=i&&new Date(i)<new Date||"6"==a&&null!=o&&void 0!=o&&new Date(o)<new Date?(this.$Notice.error({title:this.$t("address.Operationreminder"),desc:this.$t("support.advance")}),!1):void this.$Modal.confirm({title:this.$t("support.Termination"),onOk:function(){Object(h["r"])({packageUniqueId:t.packageUniqueId}).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:e.$t("address.Operationreminder"),desc:e.$t("common.Successful")}),e.goPageFirst(e.pageP)})).catch((function(t){console.log(t)})).finally((function(){}))}})},changeVIMSI:function(t){var e=this;this.$Modal.confirm({title:this.$t("support.replacement"),onOk:function(){var a=e.$route.query.mcc;return"2"!=t.packageStatus?(e.$Notice.error({title:e.$t("address.Operationreminder"),desc:e.$t("support.operationFailed")}),!1):""==a||null==a?(e.$Notice.error({title:e.$t("address.Operationreminder"),desc:e.$t("support.VoperationFailed")}),!1):void Object(h["b"])({mcc:a,packageId:t.packageId,packageUniqueId:t.packageUniqueId}).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:e.$t("address.Operationreminder"),desc:e.$t("common.Successful")}),e.goPageFirst(e.pageP)})).catch((function(t){console.log(t)})).finally((function(){}))}})},packageModal:function(t){this.obj=Object.assign({},t),this.packageModalFlag=!0},searchByCondition:function(){this.goPageFirst(1)},goPage:function(t){this.goPageFirst(t)},reBack:function(){this.$router.push({name:"service_index"})},updateTime:function(t){this.updateTimeObj.packageUniqueId=t.packageUniqueId,this.updateTimeObj.date=t.activeAt,this.updateTimeObj.newActiveTime=t.activeAt,this.updateTimeModal=!0},cancelTimeModal:function(){this.updateTimeModal=!1,this.$refs["updateTimeObj"].resetFields()},getTime:function(t){this.updateTimeObj.newActiveTime=t},submit:function(){var t=this;this.$refs["updateTimeObj"].validate((function(e){e&&(t.updateTimeloading=!0,Object(h["v"])({packageUniqueId:t.updateTimeObj.packageUniqueId,newActiveTime:t.updateTimeObj.newActiveTime}).then((function(e){if(!e||"0000"!=e.code)throw e;t.goPageFirst(t.pageP),t.$Notice.success({title:t.$t("common.Successful"),desc:t.$t("common.Successful")}),t.cancelTimeModal()})).catch((function(t){return!1})).finally((function(){t.updateTimeloading=!1})))}))}},mounted:function(){this.init()},watch:{}}),$=k,I=(a("76ef"),Object(c["a"])($,i,o,!1,null,"bff3665a",null));e["default"]=I.exports},"76ef":function(t,e,a){"use strict";a("1f3b")},"8ba4":function(t,e,a){"use strict";a.d(e,"s",(function(){return l})),a.d(e,"k",(function(){return n})),a.d(e,"c",(function(){return r})),a.d(e,"t",(function(){return s})),a.d(e,"l",(function(){return c})),a.d(e,"o",(function(){return u})),a.d(e,"p",(function(){return d})),a.d(e,"f",(function(){return p})),a.d(e,"g",(function(){return m})),a.d(e,"r",(function(){return g})),a.d(e,"b",(function(){return f})),a.d(e,"m",(function(){return h})),a.d(e,"u",(function(){return b})),a.d(e,"q",(function(){return v})),a.d(e,"n",(function(){return w})),a.d(e,"e",(function(){return y})),a.d(e,"d",(function(){return k})),a.d(e,"v",(function(){return $})),a.d(e,"a",(function(){return I})),a.d(e,"h",(function(){return O})),a.d(e,"i",(function(){return j})),a.d(e,"j",(function(){return S}));var i=a("66df"),o="/cms/api/v1/customerService",l=function(t){return i["a"].request({url:o+"/queryPackageFlow",params:t,method:"get"})},n=function(t){return i["a"].request({url:o+"/package/current",params:t,method:"get"})},r=function(t){return i["a"].request({url:o+"/package/active",data:t,method:"post"})},s=function(t){return i["a"].request({url:o+"/package/purchased",params:t,method:"get"})},c=function(t){return i["a"].request({url:o+"/package/purchasedDetail",params:t,method:"get"})},u=function(t){return i["a"].request({url:o+"/luDetails/hOnly",params:t,method:"get"})},d=function(t){return i["a"].request({url:o+"/luDetails/v",params:t,method:"get"})},p=function(t){return i["a"].request({url:o+"/surf/getMcc/v",params:t,method:"get"})},m=function(t){return i["a"].request({url:"/cms/channelSelfServer/directionalAppSurfDetail",params:t,method:"get"})},g=function(t){return i["a"].request({url:o+"/recoveryPackage",data:t,method:"post"})},f=function(t){return i["a"].request({url:o+"/replaceVImsi",data:t,method:"post"})},h=function(t){return i["a"].request({url:"/sms/customer/list",data:t,method:"post"})},b=function(t){return i["a"].request({url:"/sms/customer/send",data:t,method:"post"})},v=function(t){return i["a"].request({url:"/stat/finance/card/flow/info",params:t,method:"post"})},w=function(t,e){return i["a"].request({url:"/stat/finance/get/flow/".concat(t),data:e,method:"post"})},y=function(t){return i["a"].request({url:"/stat/finance/get/flow/detail/".concat(t),method:"post"})},k=function(t){return i["a"].request({url:"/stat/finance/get/flow/detail/export/",method:"post",params:t})},$=function(t){return i["a"].request({url:"/cms/package/updatePackageActiveTime",params:t,method:"post"})},I=function(t){return i["a"].request({url:o+"/package/replaceIccid",data:t,method:"post"})},O=function(t){return i["a"].request({url:o+"/package/getCDR",method:"post",data:t})},j=function(t){return i["a"].request({url:o+"/package/getCoverHours",method:"post",data:t})},S=function(t){return i["a"].request({url:o+"/package/getConsumption",method:"post",params:t})}},b680:function(t,e,a){"use strict";var i=a("23e7"),o=a("e330"),l=a("5926"),n=a("408a"),r=a("1148"),s=a("d039"),c=RangeError,u=String,d=Math.floor,p=o(r),m=o("".slice),g=o(1..toFixed),f=function(t,e,a){return 0===e?a:e%2===1?f(t,e-1,a*t):f(t*t,e/2,a)},h=function(t){var e=0,a=t;while(a>=4096)e+=12,a/=4096;while(a>=2)e+=1,a/=2;return e},b=function(t,e,a){var i=-1,o=a;while(++i<6)o+=e*t[i],t[i]=o%1e7,o=d(o/1e7)},v=function(t,e){var a=6,i=0;while(--a>=0)i+=t[a],t[a]=d(i/e),i=i%e*1e7},w=function(t){var e=6,a="";while(--e>=0)if(""!==a||0===e||0!==t[e]){var i=u(t[e]);a=""===a?i:a+p("0",7-i.length)+i}return a},y=s((function(){return"0.000"!==g(8e-5,3)||"1"!==g(.9,0)||"1.25"!==g(1.255,2)||"1000000000000000128"!==g(0xde0b6b3a7640080,0)}))||!s((function(){g({})}));i({target:"Number",proto:!0,forced:y},{toFixed:function(t){var e,a,i,o,r=n(this),s=l(t),d=[0,0,0,0,0,0],g="",y="0";if(s<0||s>20)throw new c("Incorrect fraction digits");if(r!==r)return"NaN";if(r<=-1e21||r>=1e21)return u(r);if(r<0&&(g="-",r=-r),r>1e-21)if(e=h(r*f(2,69,1))-69,a=e<0?r*f(2,-e,1):r/f(2,e,1),a*=4503599627370496,e=52-e,e>0){b(d,0,a),i=s;while(i>=7)b(d,1e7,0),i-=7;b(d,f(10,i,1),0),i=e-1;while(i>=23)v(d,1<<23),i-=23;v(d,1<<i),b(d,1,1),v(d,2),y=w(d)}else b(d,0,a),b(d,1<<-e,0),y=w(d)+p("0",s);return s>0?(o=y.length,y=g+(o<=s?"0."+p("0",s-o)+y:m(y,0,o-s)+"."+m(y,o-s))):y=g+y,y}})},e137:function(t,e,a){"use strict";a("1328")}}]);