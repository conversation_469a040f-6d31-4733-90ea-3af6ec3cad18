(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a6062dfe"],{"504f":function(e,t,a){},"8ce8":function(e,t,a){"use strict";a("504f")},a77c:function(e,t,a){"use strict";a.d(t,"l",(function(){return o})),a.d(t,"f",(function(){return i})),a.d(t,"j",(function(){return c})),a.d(t,"d",(function(){return l})),a.d(t,"k",(function(){return s})),a.d(t,"e",(function(){return u})),a.d(t,"i",(function(){return d})),a.d(t,"c",(function(){return m})),a.d(t,"h",(function(){return h})),a.d(t,"a",(function(){return p})),a.d(t,"n",(function(){return g})),a.d(t,"m",(function(){return f})),a.d(t,"g",(function(){return b})),a.d(t,"b",(function(){return v}));var n=a("66df"),r="/cms/packageActive",o=function(e){return n["a"].request({url:r+"/globalPackage/pageList",data:e,method:"post"})},i=function(e){return n["a"].request({url:r+"/globalPackageSearchExport",data:e,method:"post",responseType:"blob"})},c=function(e){return n["a"].request({url:r+"/offlinePackage/pageList",data:e,method:"post"})},l=function(e){return n["a"].request({url:r+"/offlinePackageSearchExport",data:e,method:"post",responseType:"blob"})},s=function(e){return n["a"].request({url:r+"/onlinePackage/pageList",data:e,method:"post"})},u=function(e){return n["a"].request({url:r+"/onlinePackageSearchExport",data:e,method:"post",responseType:"blob"})},d=function(e){return n["a"].request({url:r+"/cooperationPackage/pageList",data:e,method:"post"})},m=function(e){return n["a"].request({url:r+"/cooperationPackageSearchExport",data:e,method:"post",responseType:"blob"})},h=function(e){return n["a"].request({url:r+"/activatedPackageStat",data:e,method:"post"})},p=function(e){return n["a"].request({url:r+"/usedPackageStat/export",params:e,method:"post",responseType:"blob"})},g=function(e){return n["a"].request({url:r+"/usedPackageStat",params:e,method:"post"})},f=function(e){return n["a"].request({url:r+"/UnactivatedPackage",params:e,method:"post"})},b=function(e){return n["a"].request({url:r+"/unactivatedPackageStat/export",params:e,method:"post",responseType:"blob"})},v=function(e){return n["a"].request({url:r+"/activatedPackageStatExport",data:e,method:"post",responseType:"blob"})}},aea1:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e._self._c;return t("div",[t("Card",[t("div",{staticClass:"search_head"},[t("Form",{ref:"formInline",attrs:{"label-width":90,model:e.formInline,rules:e.ruleInline,inline:""}},[t("FormItem",{attrs:{label:"查询时间段:",prop:"timeRangeArray"}},[t("DatePicker",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{width:"200px",margin:"0 10px 0 0"},attrs:{editable:!1,type:"daterange",placeholder:"选择时间段",clearable:""},on:{"on-change":e.handleDateChange,"on-clear":e.hanldeDateClear},model:{value:e.formInline.timeRangeArray,callback:function(t){e.$set(e.formInline,"timeRangeArray",t)},expression:"formInline.timeRangeArray"}})],1),t("FormItem",{attrs:{label:"查询地区名称:",prop:"localName"}},[t("Input",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{width:"200px","margin-right":"10px"},attrs:{clearable:"",placeholder:"输入地区名称..."},model:{value:e.formInline.localName,callback:function(t){e.$set(e.formInline,"localName",t)},expression:"formInline.localName"}}),t("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",icon:"md-search",loading:e.loading},on:{click:function(t){return e.searchByCondition("formInline")}}},[e._v("搜索")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],attrs:{type:"success",loading:e.downloading,icon:"ios-download"},on:{click:function(t){return e.downLoad("formInline")}}},[e._v("导出")])],1)],1)],1),t("div",[t("Table",{attrs:{columns:e.columns,data:e.tableData,ellipsis:!0,loading:e.loading}})],1),t("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[t("Page",{attrs:{total:e.total,current:e.page,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.page=t},"on-change":e.goPage}})],1)])],1)},r=[],o=a("3835"),i=(a("d3b7"),a("3ca3"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("a77c")),c={data:function(){return{formInline:{timeRangeArray:[],localName:""},ruleInline:{timeRangeArray:[{type:"array",required:!0,message:"请选择时间",trigger:"blur",fields:{0:{type:"date",required:!0,message:"请选择开始日期"},1:{type:"date",required:!0,message:"请选择结束日期"}}}]},downloading:!1,columns:[{title:"开始时间",key:"startDate",tooltip:!0,tooltipTheme:"light",tooltipMaxWidth:600,align:"center"},{title:"结束时间",key:"endDate",tooltip:!0,tooltipTheme:"light",tooltipMaxWidth:600,align:"center"},{title:"地区",key:"mccName",tooltip:!0,tooltipTheme:"light",tooltipMaxWidth:600,align:"center"},{title:"销量",key:"countNum",tooltip:!0,tooltipTheme:"light",tooltipMaxWidth:600,align:"center"}],tableData:[],loading:!1,page:1,total:0,searchBeginTime:"",searchEndTime:""}},computed:{},methods:{goPageFirst:function(e){var t=this;this.page=e,this.loading=!0;var a={page:e,pageSize:10,mccName:this.formInline.localName,startDate:this.searchBeginTime,endDate:this.searchEndTime};Object(i["h"])(a).then((function(e){if(!e||"0000"!=e.code)throw e;t.tableData=e.data,t.total=e.count})).catch((function(e){console.log(e)})).finally((function(){t.loading=!1}))},hanldeDateClear:function(){this.searchBeginTime="",this.searchEndTime=""},handleDateChange:function(e){var t=this.formInline.timeRangeArray[0]||"",a=this.formInline.timeRangeArray[1]||"";if(""!=t&&""!=a){var n=Object(o["a"])(e,2);this.searchBeginTime=n[0],this.searchEndTime=n[1]}},downLoad:function(e){var t=this;this.$refs[e].validate((function(e){e?(t.downloading=!0,Object(i["b"])({startDate:t.searchBeginTime,endDate:t.searchEndTime,mccName:t.formInline.localName,page:1,pageSize:-1}).then((function(e){var a=e.data,n="销售统计.csv";if("download"in document.createElement("a")){var r=document.createElement("a"),o=URL.createObjectURL(a);r.download=n,r.href=o,r.click(),URL.revokeObjectURL(o)}else navigator.msSaveBlob(a,n);t.downloading=!1})).catch((function(e){t.downloading=!1}))):t.$Message.error("参数校验不通过")}))},searchByCondition:function(e){var t=this;this.$refs[e].validate((function(e){e?t.goPageFirst(1):t.$Message.error("参数校验不通过")}))},goPage:function(e){this.goPageFirst(e)}},mounted:function(){},watch:{}},l=c,s=(a("8ce8"),a("2877")),u=Object(s["a"])(l,n,r,!1,null,null,null);t["default"]=u.exports}}]);