/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SaleProfile } from './saleProfile';
export declare class SaleTerminalData {
    'SaleCapabilities'?: Array<SaleTerminalData.SaleCapabilitiesEnum>;
    'SaleProfile'?: SaleProfile;
    'TerminalEnvironment'?: SaleTerminalData.TerminalEnvironmentEnum;
    'TotalsGroupID'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace SaleTerminalData {
    enum SaleCapabilitiesEnum {
        CashierDisplay,
        CashierError,
        CashierInput,
        CashierStatus,
        CustomerAssistance,
        CustomerDisplay,
        CustomerError,
        CustomerInput,
        EmvContactless,
        Icc,
        MagStripe,
        PoiReplication,
        PrinterDocument,
        PrinterReceipt,
        PrinterVoucher
    }
    enum TerminalEnvironmentEnum {
        Attended,
        SemiAttended,
        Unattended
    }
}
