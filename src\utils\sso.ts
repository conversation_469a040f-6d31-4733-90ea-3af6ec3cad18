/**
 * SSO 单点登录相关工具函数
 * 从原 cmi-web 项目迁移而来
 */

import { getSSOSwitchConfig, ssoLoginGDS, searchcorpid } from '@/api/cmi/auth'
import { ElMessage } from 'element-plus'
import router from '@/router'
import { parseUrlParams, performSSORedirect, defaultSSOConfig } from '@/plugins/ssoRedirect'

/**
 * 检查 SSO 开关配置
 * @returns Promise<boolean> 是否启用 SSO
 */
export const checkSSOConfig = async (): Promise<boolean> => {
  try {
    const res = await getSSOSwitchConfig()
    if ((res as any).code === '0000' && res.data) {
      return (res.data as any).ssoSwitchConfig === true
    }
    return false
  } catch (error) {
    console.error('获取 SSO 配置失败:', error)
    return false
  }
}

/**
 * 从 URL 参数中获取 SSO 相关信息
 * @returns object SSO 参数对象
 */
export const getSSOParamsFromURL = () => {
  const urlParams = new URLSearchParams(window.location.search)
  const corpId = urlParams.get('corpId') || sessionStorage.getItem('corpId') || ''

  return {
    corpId,
    queryParams: window.location.search
    // 可以根据需要添加其他 SSO 参数
  }
}

/**
 * 执行 SSO 登录
 * @param ssoData SSO 登录数据
 * @returns Promise<any> 登录结果
 */
export const performSSOLogin = async (ssoData: any) => {
  try {
    const res = await ssoLoginGDS(ssoData)
    if ((res as any).code === '0000') {
      // 清空 corpId
      sessionStorage.setItem('corpId', '')
      return res
    } else {
      throw new Error((res as any).msg || 'SSO 登录失败')
    }
  } catch (error) {
    console.error('SSO 登录失败:', error)
    throw error
  }
}

/**
 * 检查用户是否为超管
 * @param userName 用户名
 * @returns Promise<boolean> 是否为超管
 */
export const checkSuperAdmin = async (userName: string): Promise<boolean> => {
  try {
    const res = await searchcorpid({ userName })
    return (res as any).code === '0000'
  } catch (error) {
    console.error('检查超管权限失败:', error)
    return false
  }
}

/**
 * 处理 SSO 登录流程
 * @param userStore 用户状态管理实例
 * @returns Promise<boolean> 是否成功处理 SSO 登录
 */
export const handleSSOLoginFlow = async (userStore: any): Promise<boolean> => {
  try {
    // 1. 检查 SSO 开关配置
    const ssoEnabled = await checkSSOConfig()
    if (!ssoEnabled) {
      return false
    }

    // 2. 获取 SSO 参数
    const ssoParams = getSSOParamsFromURL()
    if (!ssoParams.corpId && !ssoParams.queryParams) {
      return false
    }

    // 3. 执行 SSO 登录
    const loginResult = await performSSOLogin(ssoParams)

    // 4. 处理登录结果
    if ((loginResult as any).code === '0000') {
      const userDetails = loginResult.data.userDetails
      const oauth2AccessToken = loginResult.data.oauth2AccessToken

      // 设置用户信息
      userStore.setToken(oauth2AccessToken.access_token)
      userStore.setUserId(userDetails.id)
      userStore.setUserName(userDetails.username)

      // 处理权限信息
      const privList = userDetails.pagePrivileges || []
      const access = privList.map((priv: any) => priv.access)
      const btnPrivs = privList.map((priv: any) => ({
        url: priv.url,
        priv: priv.buttons
      }))

      userStore.setPermissions(access)
      // 可以添加按钮权限的处理逻辑

      // 检查密码是否需要更新
      if (userDetails.rePassword === 1) {
        ElMessage.warning('密码已过期，请修改密码')
        router.push({ name: 'pwd_mngr' })
        return true
      }

      // 检查是否为超管
      const isSuperAdmin = await checkSuperAdmin(userDetails.username)
      if (isSuperAdmin) {
        router.push({ name: 'home' })
      } else {
        router.push({ name: 'home' })
      }

      ElMessage.success('SSO 登录成功')
      return true
    }

    return false
  } catch (error) {
    console.error('SSO 登录流程失败:', error)
    ElMessage.error('SSO 登录失败')
    return false
  }
}

/**
 * 初始化 SSO 检查
 * 在应用启动时调用，检查是否需要进行 SSO 登录
 */
export const initSSOCheck = async (userStore: any) => {
  // 检查 URL 中是否有 SSO 相关参数
  const urlParams = new URLSearchParams(window.location.search)
  const hasCorpId = urlParams.has('corpId')

  if (hasCorpId) {
    // 如果有 corpId 参数，尝试 SSO 登录
    const ssoSuccess = await handleSSOLoginFlow(userStore)
    if (ssoSuccess) {
      return true
    }
  }

  return false
}

/**
 * 处理 SSO 重定向登录
 * 点击 SSO 登录按钮时调用
 */
export const handleSSORedirect = (): void => {
  performSSORedirect(defaultSSOConfig)
}

/**
 * 处理 ticket 登录
 * 当 URL 中包含 ticket 参数时调用
 * @param ticket SSO ticket
 * @param userStore 用户状态管理实例
 */
export const handleTicketLogin = async (ticket: string, userStore: any): Promise<boolean> => {
  try {
    const service = window.location.href.split('?')[0] // 获取不带参数的 URL
    const ssoData = {
      ticket,
      service
    }

    // 先执行登出清理
    await userStore.cmiLogout()

    // 调用 SSO 登录
    const res = await userStore.cmiSSOLogin(ssoData)

    if ((res as any).code === '0000') {
      // 清空 corpId
      sessionStorage.setItem('corpId', '')

      // 检查密码是否需要更新
      if (userStore.getIsUpdatePassword === 1) {
        ElMessage.warning('密码已过期，请修改密码')
        router.push({ name: 'pwd_mngr' })
        return true
      }

      // 检查是否为超管
      try {
        const isSuperAdmin = await checkSuperAdmin(userStore.getUserName)
        if (isSuperAdmin) {
          router.push({ name: 'home' })
        } else {
          router.push({ name: 'home' })
        }
      } catch (error) {
        console.error('检查超管权限失败:', error)
        router.push({ name: 'home' })
      }

      ElMessage.success('SSO 登录成功')
      return true
    }

    return false
  } catch (error) {
    console.error('Ticket 登录失败:', error)
    ElMessage.error('SSO 登录失败')
    return false
  }
}

/**
 * 检查并处理 URL 中的 ticket 参数
 * 在页面加载时调用
 * @param userStore 用户状态管理实例
 */
export const checkAndHandleTicket = async (userStore: any): Promise<boolean> => {
  const params = parseUrlParams()
  if (params.ticket) {
    return await handleTicketLogin(params.ticket, userStore)
  }
  return false
}
