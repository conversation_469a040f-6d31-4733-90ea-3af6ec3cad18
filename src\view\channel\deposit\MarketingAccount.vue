<template>
  <!-- 营销账户详情  -->
  <div class="marketing-account-container">
    <ElCard class="marketing-card">
      <div class="action-header">
        <ElButton 
          v-if="checkPermission(['marketingAccountFlow'])"
          type="primary" 
          @click="showAccountFlow"
          class="flow-button"
        >
          营销账户流水
        </ElButton>
      </div>
      
      <!-- 数据列表 -->
      <div class="table-container">
        <ElTable 
          :data="tableData" 
          v-loading="loading"
          border
          style="width: 100%"
        >
          <ElTableColumn 
            prop="name"
            label="营销活动"
            min-width="150"
            align="center"
          />
          <ElTableColumn 
            prop="beginTime"
            label="活动开始时间"
            min-width="150"
            align="center"
          />
          <ElTableColumn 
            prop="endTime"
            label="活动结束时间"
            min-width="150"
            align="center"
          />
          <ElTableColumn 
            prop="status"
            label="活动状态"
            min-width="120"
            align="center"
          >
            <template #default="{ row }">
              <ElTag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn 
            prop="totalAmount"
            label="总金额"
            min-width="120"
            align="center"
          />
          <ElTableColumn 
            prop="usedAmount"
            label="已使用金额"
            min-width="120"
            align="center"
          >
            <template #default="{ row }">
              <ElLink 
                v-if="row.usedAmount != null && row.usedAmount !== ''"
                @click="showUsedAmount(row)"
                type="primary"
              >
                {{ row.usedAmount }}
              </ElLink>
              <span v-else>-</span>
            </template>
          </ElTableColumn>
          <ElTableColumn 
            prop="balance"
            label="余额"
            min-width="120"
            align="center"
          />
        </ElTable>
      </div>
      
      <div class="pagination-container">
        <ElPagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="goPage"
          @size-change="handleSizeChange"
        />
      </div>
      
      <div class="back-button-container">
        <ElButton @click="goBack" :icon="ArrowLeft">
          返回
        </ElButton>
      </div>
    </ElCard>

    <!-- 营销账户流水弹窗 -->
    <ElDialog 
      :title="flowTitle" 
      v-model="accountFlowModel" 
      :close-on-click-modal="false"
      width="90%"
      class="flow-dialog"
    >
      <div class="flow-search">
        <ElDatePicker
          v-model="dateRange"
          type="daterange"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          placeholder="请选择日期"
          style="width: 200px"
          @change="handleDateChange"
          @clear="handleDateClear"
        />
        <ElButton 
          v-if="checkPermission(['searchFlow'])"
          type="primary" 
          :loading="searchLoading" 
          @click="searchAccountFlow"
          :icon="Search"
          style="margin: 0 20px;"
        >
          搜索
        </ElButton>
        <ElButton 
          v-if="checkPermission(['exportFlow'])"
          type="success" 
          :loading="exportLoading"
          @click="exportFile('1')"
          :icon="Download"
        >
          导出
        </ElButton>
      </div>
      
      <div class="flow-table">
        <ElTable 
          :data="flowData" 
          v-loading="flowLoading"
          border
          style="width: 100%"
        >
          <ElTableColumn 
            prop="consumptionDate"
            label="消费时间"
            min-width="170"
            align="center"
          />
          <ElTableColumn 
            prop="amount"
            label="金额"
            min-width="120"
            align="center"
          />
          <ElTableColumn 
            prop="type"
            label="费用类型"
            min-width="200"
            align="center"
          >
            <template #default="{ row }">
              {{ getChargeTypeText(row.type) }}
            </template>
          </ElTableColumn>
        </ElTable>
      </div>
      
      <div class="flow-pagination">
        <ElPagination
          :current-page="flowCurrentPage"
          :page-size="pageSize"
          :total="flowTotal"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="goFlowPage"
          @size-change="handleFlowSizeChange"
        />
      </div>
      
      <template #footer>
        <ElButton @click="cancelModel" :icon="ArrowLeft">
          返回
        </ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElCard, ElButton, ElTable, ElTableColumn, ElPagination, ElDialog, ElDatePicker, ElTag, ElLink } from 'element-plus'
import { ArrowLeft, Search, Download } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'

defineOptions({
  name: 'MarketingAccountDetail'
})

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const flowLoading = ref(false)
const searchLoading = ref(false)
const exportLoading = ref(false)
const accountFlowModel = ref(false)

const flowTitle = ref('')
const dateRange = ref([])
const startTime = ref('')
const endTime = ref('')

const currentPage = ref(1)
const flowCurrentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const flowTotal = ref(0)

const tableData = ref([])
const flowData = ref([])

// 权限检查函数
const checkPermission = (permissions: string[]): boolean => {
  return true
}

// 状态类型映射
const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    '0': 'info',     // 待开始
    '1': 'success',  // 已开始
    '2': 'danger',   // 已结束
    '3': 'warning',  // 已废弃
    '4': 'danger'    // 提前终止
  }
  return statusMap[status] || 'info'
}

// 状态文本映射
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '0': '待开始',
    '1': '已开始',
    '2': '已结束',
    '3': '已废弃',
    '4': '提前终止'
  }
  return statusMap[status] || '-'
}

// 费用类型文本映射
const getChargeTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    '1': '营销返利增加',
    '2': '分销套餐订购',
    '3': '流量包订购',
    '4': '套餐取消',
    '5': '流量包退订',
    '6': '重置营销预算',
    '7': '冲抵营销返利金额'
  }
  return typeMap[type] || '-'
}

// 方法
const showAccountFlow = () => {
  accountFlowModel.value = true
  flowTitle.value = '营销账户流水'
}

const showUsedAmount = (row: any) => {
  console.log('显示已使用金额详情:', row)
}

const goBack = () => {
  router.push('/channel/deposit')
}

const goPage = (page: number) => {
  currentPage.value = page
  loadData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

const handleDateChange = (dates: string[]) => {
  if (dates && dates.length === 2) {
    startTime.value = dates[0]
    endTime.value = dates[1]
  }
}

const handleDateClear = () => {
  startTime.value = ''
  endTime.value = ''
  dateRange.value = []
}

const searchAccountFlow = () => {
  flowCurrentPage.value = 1
  loadFlowData()
}

const goFlowPage = (page: number) => {
  flowCurrentPage.value = page
  loadFlowData()
}

const handleFlowSizeChange = (size: number) => {
  pageSize.value = size
  flowCurrentPage.value = 1
  loadFlowData()
}

const cancelModel = () => {
  accountFlowModel.value = false
}

const exportFile = (type: string) => {
  exportLoading.value = true
  console.log('导出文件类型:', type)
  setTimeout(() => {
    exportLoading.value = false
    ElMessage.success('导出成功')
  }, 2000)
}

// 加载主表格数据
const loadData = async () => {
  try {
    loading.value = true
    console.log('加载营销账户数据')
    
    // 模拟数据
    tableData.value = [
      {
        name: '营销活动1',
        beginTime: '2024-01-01',
        endTime: '2024-12-31',
        status: '1',
        totalAmount: '10000.00',
        usedAmount: '5000.00',
        balance: '5000.00'
      }
    ]
    total.value = 1
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载流水数据
const loadFlowData = async () => {
  try {
    flowLoading.value = true
    console.log('加载流水数据')
    
    // 模拟数据
    flowData.value = [
      {
        consumptionDate: '2024-01-15',
        amount: '1000.00',
        type: '1'
      }
    ]
    flowTotal.value = 1
  } catch (error) {
    console.error('加载流水数据失败:', error)
    ElMessage.error('加载流水数据失败')
  } finally {
    flowLoading.value = false
  }
}

// 组件挂载时初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.marketing-account-container {
  padding: 20px;
}

.marketing-card {
  margin-bottom: 20px;
}

.action-header {
  margin: 30px 0;
}

.flow-button {
  margin: 0;
}

.table-container {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}

.back-button-container {
  text-align: center;
  margin-top: 20px;
}

/* 弹窗样式 */
.flow-search {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.flow-table {
  margin-top: 20px;
}

.flow-pagination {
  margin-top: 15px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .flow-search {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .pagination-container,
  .flow-pagination {
    text-align: center;
  }
}
</style>
