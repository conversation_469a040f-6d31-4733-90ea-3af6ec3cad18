<template>
  <!-- 营销账户详情 -->
  <ContentWrap>
    <el-card>
      <div class="search-header">
        <el-button 
          v-if="hasPermission('marketingAccountFlow')"
          type="primary" 
          style="margin: 30px 0;"
          @click="showAccountFlow"
        >
          营销账户流水
        </el-button>
      </div>
      
      <!-- 数据列表 -->
      <div style="margin-top: 20px;">
        <el-table :data="tableData" v-loading="loading" border>
          <el-table-column prop="packageName" label="套餐名称" min-width="150" />
          <el-table-column prop="totalAmount" label="总金额" min-width="120" align="center" />
          <el-table-column prop="usedAmount" label="已使用金额" min-width="120" align="center">
            <template #default="{ row }">
              <el-link 
                v-if="row.usedAmount != null && row.usedAmount !== ''" 
                @click="showUsedAmount(row)"
                type="primary"
              >
                {{ row.usedAmount }}
              </el-link>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="remainingAmount" label="剩余金额" min-width="120" align="center" />
          <el-table-column prop="status" label="状态" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="160" />
        </el-table>
      </div>
      
      <!-- 分页 -->
      <div style="margin-top: 15px; text-align: right;">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
      
      <div style="text-align: center; margin-top: 20px;">
        <el-button @click="goBack">
          <Icon icon="ep:back" class="mr-5px" />
          返回
        </el-button>
      </div>
    </el-card>

    <!-- 营销账户流水弹窗 -->
    <el-dialog
      v-model="accountFlowVisible"
      :title="flowTitle"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="flow-search">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px; margin-right: 20px;"
        />
        <el-button 
          v-if="hasPermission('searchFlow')"
          type="primary" 
          :loading="searchLoading"
          @click="searchAccountFlow"
        >
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button 
          v-if="hasPermission('exportFlow')"
          type="success" 
          :loading="exportLoading"
          @click="exportFlow"
        >
          <Icon icon="ep:download" class="mr-5px" />
          导出
        </el-button>
      </div>
      
      <div style="margin-top: 20px;">
        <el-table :data="flowData" v-loading="flowLoading" border>
          <el-table-column prop="transactionTime" label="交易时间" min-width="160" />
          <el-table-column prop="transactionType" label="交易类型" min-width="120" />
          <el-table-column prop="amount" label="金额" min-width="120" align="right" />
          <el-table-column prop="balance" label="余额" min-width="120" align="right" />
          <el-table-column prop="description" label="描述" min-width="200" />
        </el-table>
      </div>
      
      <!-- 流水分页 -->
      <div style="margin-top: 15px; text-align: right;">
        <el-pagination
          v-model:current-page="flowCurrentPage"
          v-model:page-size="pageSize"
          :total="flowTotal"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handleFlowPageChange"
        />
      </div>
      
      <template #footer>
        <el-button @click="accountFlowVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { useRouter } from 'vue-router'

// 权限检查函数
const hasPermission = (permission: string): boolean => {
  return true // TODO: 实现权限检查逻辑
}

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const exportLoading = ref(false)
const flowLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const flowCurrentPage = ref(1)
const flowTotal = ref(0)

const accountFlowVisible = ref(false)
const flowTitle = ref('营销账户流水')
const dateRange = ref<[string, string]>(['', ''])

const tableData = ref([])
const flowData = ref([])

// 方法
const showAccountFlow = () => {
  accountFlowVisible.value = true
  getFlowData()
}

const showUsedAmount = (row: any) => {
  ElMessage.info(`已使用金额详情: ${row.usedAmount}`)
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '1': 'success',
    '0': 'danger',
    '2': 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '1': '正常',
    '0': '停用',
    '2': '待审核'
  }
  return statusMap[status] || '未知'
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getTableData()
}

const handleFlowPageChange = (page: number) => {
  flowCurrentPage.value = page
  getFlowData()
}

const searchAccountFlow = () => {
  flowCurrentPage.value = 1
  getFlowData()
}

const exportFlow = async () => {
  try {
    exportLoading.value = true
    // TODO: 实现导出功能
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const goBack = () => {
  router.back()
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    tableData.value = [
      {
        packageName: '基础套餐',
        totalAmount: 10000.00,
        usedAmount: 3000.00,
        remainingAmount: 7000.00,
        status: '1',
        createTime: '2024-01-01 10:00:00'
      }
    ]
    total.value = 1
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 获取流水数据
const getFlowData = async () => {
  try {
    flowLoading.value = true
    // TODO: 实现API调用
    // 模拟数据
    flowData.value = [
      {
        transactionTime: '2024-01-01 10:00:00',
        transactionType: '充值',
        amount: 1000.00,
        balance: 10000.00,
        description: '账户充值'
      }
    ]
    flowTotal.value = 1
  } catch (error) {
    ElMessage.error('获取流水数据失败')
  } finally {
    flowLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.search-header {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 20px;
}

.flow-search {
  margin-bottom: 20px;
}
</style>
