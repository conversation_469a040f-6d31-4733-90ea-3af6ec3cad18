# 环境
VITE_NODE_ENV=production

# 接口前缀
VITE_API_BASE_PATH=/api/

# 打包路径
VITE_BASE_PATH=/cmifront/

# 是否删除debugger
VITE_DROP_DEBUGGER=true

# 是否删除console.log
VITE_DROP_CONSOLE=true

# 是否sourcemap
VITE_SOURCEMAP=false

# 输出路径
VITE_OUT_DIR=cmifront

# 标题
VITE_APP_TITLE=CMI

# 是否包分析
VITE_USE_BUNDLE_ANALYZER=true

# 是否全量引入element-plus样式
VITE_USE_ALL_ELEMENT_PLUS_STYLE=false

# 是否开启mock
VITE_USE_MOCK=true

# 是否切割css
VITE_USE_CSS_SPLIT=true

# 是否使用在线图标
VITE_USE_ONLINE_ICON=false

# 是否隐藏全局设置按钮
VITE_HIDE_GLOBAL_SETTING=false
