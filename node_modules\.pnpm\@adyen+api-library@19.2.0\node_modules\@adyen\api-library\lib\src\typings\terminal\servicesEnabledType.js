"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServicesEnabledType = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var ServicesEnabledType;
(function (ServicesEnabledType) {
    ServicesEnabledType[ServicesEnabledType["CardAcquisition"] = 'CardAcquisition'] = "CardAcquisition";
    ServicesEnabledType[ServicesEnabledType["Loyalty"] = 'Loyalty'] = "Loyalty";
    ServicesEnabledType[ServicesEnabledType["Payment"] = 'Payment'] = "Payment";
})(ServicesEnabledType = exports.ServicesEnabledType || (exports.ServicesEnabledType = {}));
//# sourceMappingURL=servicesEnabledType.js.map