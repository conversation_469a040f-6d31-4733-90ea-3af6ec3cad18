(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-aae304a4"],{5744:function(t,e,o){"use strict";o("a2b6")},a2b6:function(t,e,o){},f3c4:function(t,e,o){"use strict";o.r(e);var n=function(){var t=this,e=t._self._c;return e("div",[e("Card",[e("div",{staticClass:"search_head_i"},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"marketingAccountFlow",expression:"'marketingAccountFlow'"},{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{margin:"30px 0"},attrs:{type:"primary"},on:{click:t.showAccountFlow}},[t._v(t._s(t.$t("deposit.marketingAccountFlow")))])],1),e("div",{staticStyle:{"margin-top":"20px"}},[e("Table",{attrs:{columns:t.columns,data:t.tableData,ellipsis:!0,loading:t.loading},scopedSlots:t._u([{key:"usedAmount",fn:function(o){var n=o.row;o.index;return[null!=n.usedAmount&&""!==n.usedAmount?e("a",{on:{click:function(e){return t.shoWusedAmount(n)}}},[t._v(t._s(n.usedAmount))]):e("span",[t._v("\\")])]}}])})],1),e("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[e("Page",{staticStyle:{margin:"10px 0"},attrs:{total:t.total,current:t.currentPage,"page-size":t.pageSize,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("div",{staticStyle:{"text-align":"center"}},[e("Button",{attrs:{icon:"ios-arrow-back"},on:{click:t.reback}},[t._v(t._s(t.$t("support.back")))])],1)]),e("Modal",{attrs:{title:t.flowTitle,"mask-closable":!1,width:"1380","footer-hide":!0},on:{"on-cancel":t.cancelModel},model:{value:t.accountFlowModel,callback:function(e){t.accountFlowModel=e},expression:"accountFlowModel"}},[e("div",[e("DatePicker",{staticStyle:{width:"200px"},attrs:{type:"daterange",format:"yyyy-MM-dd",placement:"bottom-end",placeholder:t.$t("flow.PleaseChoosedate")},on:{"on-change":t.handleDateChange,"on-clear":t.hanldeDateClear},model:{value:t.date,callback:function(e){t.date=e},expression:"date"}}),e("Button",{directives:[{name:"has",rawName:"v-has",value:"searchFlow",expression:"'searchFlow'"}],staticStyle:{margin:"0 20px"},attrs:{type:"primary",icon:"md-search",loading:t.searchloading},on:{click:t.searchAccountFlow}},[t._v(t._s(t.$t("support.search")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"exportFlow",expression:"'exportFlow'"}],attrs:{type:"success",icon:"ios-cloud-download-outline",loading:t.exportloading},on:{click:function(e){return t.exportFile("1")}}},[t._v(t._s(t.$t("stock.exporttb")))])],1),e("div",{staticStyle:{"margin-top":"20px"}},[e("Table",{attrs:{columns:"1"==t.cooperationMode?t.flowColumns1:t.flowColumns2,data:t.flowData,ellipsis:!0,loading:t.flowloading}})],1),e("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[e("Page",{staticStyle:{margin:"10px 0"},attrs:{total:t.flowTotal,current:t.flowCurrentPage,"page-size":t.pageSize,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.flowCurrentPage=e},"on-change":t.goFlowPage}})],1),e("div",{staticStyle:{"text-align":"center"}},[e("Button",{attrs:{icon:"ios-arrow-back"},on:{click:t.cancelModel}},[t._v(t._s(t.$t("support.back")))])],1)]),e("Modal",{attrs:{title:t.flowTitle,"mask-closable":!1,width:"1380","footer-hide":!0},on:{"on-cancel":t.cancelModel},model:{value:t.rebateFlowModel,callback:function(e){t.rebateFlowModel=e},expression:"rebateFlowModel"}},[e("div",[e("DatePicker",{staticStyle:{width:"200px"},attrs:{type:"daterange",format:"yyyy-MM-dd",placement:"bottom-end",placeholder:t.$t("flow.PleaseChoosedate")},on:{"on-change":t.handleDateChange,"on-clear":t.hanldeDateClear},model:{value:t.date,callback:function(e){t.date=e},expression:"date"}}),e("Button",{directives:[{name:"has",rawName:"v-has",value:"searchRebate",expression:"'searchRebate'"}],staticStyle:{margin:"0 20px"},attrs:{type:"primary",icon:"md-search",loading:t.searchloading},on:{click:t.searchAccountFlow}},[t._v(t._s(t.$t("support.search")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"exportRebate",expression:"'exportRebate'"}],attrs:{type:"success",icon:"ios-cloud-download-outline",loading:t.exportloading},on:{click:function(e){return t.exportFile("2")}}},[t._v(t._s(t.$t("stock.exporttb")))])],1),e("div",{staticStyle:{"margin-top":"20px"}},[e("Table",{attrs:{columns:"1"==t.cooperationMode?t.flowColumns3:t.flowColumns4,data:t.flowData,ellipsis:!0,loading:t.flowloading}})],1),e("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[e("Page",{staticStyle:{margin:"10px 0"},attrs:{total:t.flowTotal,current:t.flowCurrentPage,"page-size":t.pageSize,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.flowCurrentPage=e},"on-change":t.goFlowPage}})],1),e("div",{staticStyle:{"text-align":"center"}},[e("Button",{attrs:{icon:"ios-arrow-back"},on:{click:t.cancelModel}},[t._v(t._s(t.$t("support.back")))])],1)]),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.exportcancelModal},model:{value:t.exportModalr,callback:function(e){t.exportModalr=e},expression:"exportModalr"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex","flex-wrap":"wrap"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v(t._s(t.$t("exportMS")))]),e("FormItem",{attrs:{label:t.$t("exportID")}},[e("span",[t._v(t._s(t.taskId))])]),e("FormItem",{attrs:{label:t.$t("exportFlie")}},[e("span",{staticClass:"task-name"},[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v(t._s(t.$t("downloadResult")))])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.exportcancelModal}},[t._v(t._s(t.$t("common.cancel")))]),e("Button",{attrs:{type:"primary"},on:{click:t.Gotor}},[t._v(t._s(t.$t("Goto")))])],1)])],1)},a=[],i=o("5530"),r=o("3835"),c=(o("99af"),o("4de4"),o("14d9"),o("b0c0"),o("a9e3"),o("d3b7"),o("ac1f"),o("4d90"),o("5319"),o("66df")),s="cms/cms-channel-market-billflow",l=function(t){return c["a"].request({url:"cms/cms_channel_marketing_rebate/getMarketingDeatil",data:t,method:"post"})},d=function(t){return c["a"].request({url:s+"/selectMarketingAccountFlow",params:t,method:"get"})},u=function(t){return c["a"].request({url:s+"/selectMarketingAccountFlowRebate",params:t,method:"get"})},p=function(t){return c["a"].request({url:s+"/MarketingAccountFlowOut",params:t,method:"get"})},m=function(t){return c["a"].request({url:s+"/MarketingAccountFlowOutRebate",params:t,method:"get"})},h={components:{},data:function(){var t=this;return{taskId:"",taskName:"",corpId:"",cooperationMode:"",modelType:"",activityId:"",flowTitle:"",startTime:"",endTime:"",adminOrChannel:!1,date:[],currentPage:1,flowCurrentPage:1,pageSize:10,total:0,flowTotal:0,loading:!1,flowloading:!1,searchloading:!1,exportloading:!1,accountFlowModel:!1,rebateFlowModel:!1,exportModalr:!1,columns:[{title:this.$t("deposit.marketingActivities"),key:"name",minWidth:150,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.name||void 0===o.name||""===o.name?"\\":o.name;return t("span",n)}},{title:this.$t("deposit.eventStartTime"),key:"beginTime",align:"center",minWidth:150,render:function(e,o){var n=o.row,a=null===n.beginTime||void 0===n.beginTime||""===n.beginTime?"\\":t.formatDateToDash(n.beginTime);return e("span",a)}},{title:this.$t("deposit.eventEndTime"),key:"endTime",align:"center",minWidth:150,render:function(e,o){var n=o.row,a=null===n.endTime||void 0===n.endTime||""===n.endTime?"\\":t.formatDateToDash(n.endTime);return e("span",a)}},{title:this.$t("deposit.campaignStatus"),key:"status",minWidth:120,align:"center",tooltip:!0,render:function(e,o){var n=o.row,a="0"==n.status?t.$t("deposit.toBeStarted"):"1"==n.status?t.$t("deposit.started"):"2"==n.status?t.$t("deposit.ended"):"3"==n.status?t.$t("deposit.obsolete"):"4"==n.status?t.$t("deposit.earlyTerminate"):"\\";return e("label",{style:{"word-break":"break-word"}},a)}},{title:this.$t("deposit.rebateAmount"),key:"rebateAmount",minWidth:130,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.rebateAmount||void 0===o.rebateAmount||""===o.rebateAmount?"\\":o.rebateAmount;return t("span",n)}},{title:this.$t("deposit.usedAmount"),slot:"usedAmount",minWidth:120,align:"center",tooltip:!0},{title:this.$t("deposit.rebateBalance"),key:"remainAmount",minWidth:150,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.remainAmount||void 0===o.remainAmount||""===o.remainAmount?"\\":o.remainAmount;return t("span",n)}},{title:this.$t("deposit.arrivalTime"),key:"createTime",minWidth:160,align:"center",tooltip:!0,render:function(e,o){var n=o.row,a=null===n.createTime||void 0===n.createTime||""===n.createTime?"\\":t.formatDateToDash(n.createTime);return e("span",a)}},{title:this.$t("deposit.effectiveTime"),key:"effectiveTime",minWidth:160,align:"center",tooltip:!0,render:function(e,o){var n=o.row,a=null===n.effectiveTime||void 0===n.effectiveTime||""===n.effectiveTime?"\\":t.formatDateToDash(n.effectiveTime);return e("span",a)}},{title:this.$t("deposit.expirationTime"),key:"expireTime",minWidth:160,align:"center",tooltip:!0,render:function(e,o){var n=o.row,a=null===n.expireTime||void 0===n.expireTime||""===n.expireTime?"\\":t.formatDateToDash(n.expireTime);return e("span",a)}}],flowColumns1:[{title:this.$t("deposit.spendTime"),key:"consumptionDate",minWidth:170,align:"center",tooltip:!0,render:function(e,o){var n=o.row,a=null===n.consumptionDate||void 0===n.consumptionDate||""===n.consumptionDate?"\\":t.formatDate(n.consumptionDate);return e("span",a)}},{title:this.$t("deposit.charge_type"),key:"type",align:"center",minWidth:250,render:function(e,o){var n=o.row,a="1"==n.type?t.$t("deposit.increaseMarketinRebates"):"2"==n.type?t.$t("deposit.distributionPackageOrdering"):"3"==n.type?t.$t("support.fuelPackpackageOrder"):"4"==n.type?t.$t("support.packageCancellation"):"5"==n.type?t.$t("support.fuelPackUnsubscribe"):"6"==n.type?t.$t("deposit.ResetMarketingBudget"):"7"==n.type?t.$t("deposit.OffsetMarketingRebateAmount"):"\\";return e("label",{style:{"word-break":"break-word"}},a)}},{title:this.$t("deposit.connectedActivities"),key:"activity",align:"center",minWidth:150,render:function(t,e){var o=e.row,n=null===o.activity||void 0===o.activity||""===o.activity?"\\":o.activity;return t("span",n)}},{title:this.$t("deposit.currency"),key:"currency",minWidth:120,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.currency||void 0===o.currency||""===o.currency?"\\":o.currency;return t("span",n)}},{title:this.$t("deposit.consumptionAmount"),key:"amount",minWidth:180,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.amount||void 0===o.amount||""===o.amount?"\\":o.amount;return t("span",n)}},{title:this.$t("deposit.accountTotalBalance"),key:"totalAmount",minWidth:250,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.totalAmount||void 0===o.totalAmount||""===o.totalAmount?"\\":o.totalAmount;return t("span",n)}},{title:this.$t("deposit.totalOrderNumber"),key:"totalOrderId",minWidth:200,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.totalOrderId||void 0===o.totalOrderId||""===o.totalOrderId?"\\":o.totalOrderId;return t("span",n)}}],flowColumns2:[{title:this.$t("deposit.settlementDate"),key:"balanceDate",minWidth:150,align:"center",tooltip:!0,render:function(e,o){var n=o.row,a=null===n.balanceDate||void 0===n.balanceDate||""===n.balanceDate?"\\":t.formatDate(n.balanceDate);return e("span",a)}},{title:this.$t("deposit.charge_type"),key:"type",align:"center",minWidth:200,render:function(e,o){var n=o.row,a="1"==n.type?t.$t("deposit.increaseMarketinRebates"):"2"==n.type?t.$t("deposit.dataUsageSettlemtn"):"3"==n.type?t.$t("deposit.ResetMarketingBudget"):"\\";return e("label",{style:{"word-break":"break-word"}},a)}},{title:this.$t("deposit.connectedActivities"),key:"activity",align:"center",minWidth:150,render:function(t,e){var o=e.row,n=null===o.activity||void 0===o.activity||""===o.activity?"\\":o.activity;return t("span",n)}},{title:this.$t("deposit.currency"),key:"currency",minWidth:120,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.currency||void 0===o.currency||""===o.currency?"\\":o.currency;return t("span",n)}},{title:this.$t("deposit.consumptionAmount"),key:"amount",minWidth:150,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.amount||void 0===o.amount||""===o.amount?"\\":o.amount;return t("span",n)}},{title:this.$t("deposit.accountTotalBalance"),key:"totalAmount",minWidth:220,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.totalAmount||void 0===o.totalAmount||""===o.totalAmount?"\\":o.totalAmount;return t("span",n)}}],flowColumns3:[{title:this.$t("deposit.spendTime"),key:"consumptionDate",minWidth:170,align:"center",tooltip:!0,render:function(e,o){var n=o.row,a=null===n.consumptionDate||void 0===n.consumptionDate||""===n.consumptionDate?"\\":t.formatDate(n.consumptionDate);return e("span",a)}},{title:this.$t("deposit.charge_type"),key:"type",align:"center",minWidth:250,render:function(e,o){var n=o.row,a="1"==n.type?t.$t("deposit.increaseMarketinRebates"):"2"==n.type?t.$t("deposit.distributionPackageOrdering"):"3"==n.type?t.$t("support.fuelPackpackageOrder"):"4"==n.type?t.$t("support.packageCancellation"):"5"==n.type?t.$t("support.fuelPackUnsubscribe"):"6"==n.type?t.$t("deposit.ResetMarketingBudget"):"7"==n.type?t.$t("deposit.OffsetMarketingRebateAmount"):"\\";return e("label",{style:{"word-break":"break-word"}},a)}},{title:this.$t("deposit.connectedActivities"),key:"activity",align:"center",minWidth:150,render:function(t,e){var o=e.row,n=null===o.activity||void 0===o.activity||""===o.activity?"\\":o.activity;return t("span",n)}},{title:this.$t("deposit.currency"),key:"currency",minWidth:120,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.currency||void 0===o.currency||""===o.currency?"\\":o.currency;return t("span",n)}},{title:this.$t("deposit.consumptionAmount"),key:"amount",minWidth:180,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.amount||void 0===o.amount||""===o.amount?"\\":o.amount;return t("span",n)}},{title:this.$t("deposit.singleActivityBalance"),key:"deposit",minWidth:250,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.deposit||void 0===o.deposit||""===o.deposit?"\\":o.deposit;return t("span",n)}},{title:this.$t("deposit.totalOrderNumber"),key:"totalOrderId",minWidth:200,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.totalOrderId||void 0===o.totalOrderId||""===o.totalOrderId?"\\":o.totalOrderId;return t("span",n)}}],flowColumns4:[{title:this.$t("deposit.settlementDate"),key:"balanceDate",minWidth:150,align:"center",tooltip:!0,render:function(e,o){var n=o.row,a=null===n.balanceDate||void 0===n.balanceDate||""===n.balanceDate?"\\":t.formatDate(n.balanceDate);return e("span",a)}},{title:this.$t("deposit.charge_type"),key:"type",align:"center",minWidth:200,render:function(e,o){var n=o.row,a="1"==n.type?t.$t("deposit.increaseMarketinRebates"):"2"==n.type?t.$t("deposit.dataUsageSettlemtn"):"3"==n.type?t.$t("deposit.ResetMarketingBudget"):"\\";return e("label",{style:{"word-break":"break-word"}},a)}},{title:this.$t("deposit.connectedActivities"),key:"activity",align:"center",minWidth:150,render:function(t,e){var o=e.row,n=null===o.activity||void 0===o.activity||""===o.activity?"\\":o.activity;return t("span",n)}},{title:this.$t("deposit.currency"),key:"currency",minWidth:120,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.currency||void 0===o.currency||""===o.currency?"\\":o.currency;return t("span",n)}},{title:this.$t("deposit.consumptionAmount"),key:"amount",minWidth:150,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.amount||void 0===o.amount||""===o.amount?"\\":o.amount;return t("span",n)}},{title:this.$t("deposit.singleActivityBalance"),key:"deposit",minWidth:200,align:"center",tooltip:!0,render:function(t,e){var o=e.row,n=null===o.deposit||void 0===o.deposit||""===o.deposit?"\\":o.deposit;return t("span",n)}}],tableData:[],flowData:[]}},mounted:function(){var t=sessionStorage.getItem("corpId");if(""==t||"null"==t||"undefined"==t){this.adminOrChannel=!0,this.corpId=this.$route.query.corpId,this.cooperationMode=this.$route.query.type;try{var e=this.$route.query.searchObj;e&&localStorage.setItem("searchObj",e)}catch(o){console.warn("Failed to handle searchObj",o)}}else this.adminOrChannel=!1,this.corpId=sessionStorage.getItem("corpId"),this.cooperationMode=sessionStorage.getItem("cooperationMode");this.goPageFirst(1)},methods:{goPage:function(t){this.goPageFirst(t)},goPageFirst:function(t){var e=this;this.tableData=[],this.loading=!0,l({pageNum:t,pageSize:this.pageSize,corpId:this.corpId,cooperationMode:this.cooperationMode}).then((function(o){if(!o||"0000"!=o.code)throw o;e.total=Number(o.count),e.tableData=o.data,e.currentPage=t})).catch((function(t){})).finally((function(){e.loading=!1}))},reback:function(){var t="";try{var e=localStorage.getItem("searchObj");e&&"undefined"!==e&&"null"!==e&&(t=e)}catch(o){console.warn("Failed to get searchObj from localStorage",o)}this.adminOrChannel?this.$router.push({path:"/channelDetails",query:{id:this.corpId,searchObj:t}}):this.$router.push({path:"/deposit"})},cancelModel:function(){this.flowTitle="",this.accountFlowModel=!1,this.rebateFlowModel=!1,this.startTime="",this.endTime="",this.date=[],this.flowData=[],this.hanldeDateClear()},formatDate:function(t){if(t){var e=new Date(t.replace(/-/g,"/")),o=e.getFullYear(),n=e.getMonth()+1,a=e.getDate(),i=String(e.getHours()).padStart(2,"0"),r=String(e.getMinutes()).padStart(2,"0"),c=String(e.getSeconds()).padStart(2,"0");if("1"==this.cooperationMode){var s="zh-CN"===this.$i18n.locale?"".concat(o,"年").concat(n,"月").concat(a,"日 ").concat(i,":").concat(r,":").concat(c):"en-US"===this.$i18n.locale?"".concat(o,"-").concat(n,"-").concat(a," ").concat(i,":").concat(r,":").concat(c):"";return s}var l="zh-CN"===this.$i18n.locale?"".concat(o,"年").concat(n,"月").concat(a,"日"):"en-US"===this.$i18n.locale?"".concat(o,"-").concat(n,"-").concat(a):"";return l}return""},formatDateToDash:function(t){var e=t.replace(/年|月|日/g,"-").split("-").filter(Boolean),o=Object(r["a"])(e,3),n=o[0],a=o[1],i=o[2],c="zh-CN"===this.$i18n.locale?t:"".concat(n,"-").concat(parseInt(a,10),"-").concat(parseInt(i,10));return c},showAccountFlow:function(){this.modelType="1";var t=this.$t("deposit.marketingAccountFlow")+"（"+this.$t("support.distribution")+"）",e=this.$t("deposit.marketingAccountFlow")+"（"+this.$t("support.atoz")+"）",o=this.$t("deposit.marketingAccountFlow")+"（"+this.$t("support.resourceCooperation")+"）";this.flowTitle="1"==this.cooperationMode?t:"2"==this.cooperationMode?e:o,this.accountFlowModel=!0},shoWusedAmount:function(t){this.modelType="2";var e=this.$t("deposit.marketingActivityDetails")+"（"+this.$t("support.distribution")+"）",o=this.$t("deposit.marketingActivityDetails")+"（"+this.$t("support.atoz")+"）",n=this.$t("deposit.marketingActivityDetails")+"（"+this.$t("support.resourceCooperation")+"）";this.flowTitle="1"==this.cooperationMode?e:"2"==this.cooperationMode?o:n,this.activityId=t.activityId,this.rebateFlowModel=!0},handleDateChange:function(t){Array.isArray(t)&&(this.startTime=t[0],this.endTime=t[1])},hanldeDateClear:function(){this.startTime="",this.endTime="",this.date=[]},searchAccountFlow:function(){if(""==this.startTime&&""==this.endTime)return this.$Notice.warning({desc:this.$t("deposit.startEndDate")}),void(this.flowData=[]);this.goFlowPageFirst(1)},goFlowPage:function(t){this.goFlowPageFirst(t)},goFlowPageFirst:function(t){var e=this;this.flowData=[],this.flowloading=!0;var o={beginDate:this.startTime,endDate:this.endTime,pageNum:t,pageSize:this.pageSize,corpId:this.corpId,cooperationMode:this.cooperationMode},n="1"===this.modelType?Object(i["a"])({},o):Object(i["a"])(Object(i["a"])({},o),{},{activityId:this.activityId}),a="1"==this.modelType?d:u;a(n).then((function(o){if(!o||"0000"!=o.code)throw o;e.flowTotal=Number(o.count),e.flowData=o.data,e.flowCurrentPage=t})).catch((function(t){})).finally((function(){e.flowloading=!1}))},exportFile:function(t){var e=this;if(""==this.startTime&&""==this.endTime)return this.$Notice.warning({desc:this.$t("deposit.startEndDate")}),void(this.flowData=[]);this.exportloading=!0;var o={beginDate:this.startTime,endDate:this.endTime,corpId:this.corpId,cooperationMode:this.cooperationMode},n="1"===t?Object(i["a"])({},o):Object(i["a"])(Object(i["a"])({},o),{},{activityId:this.activityId}),a="1"===t?p:m;a(n).then((function(t){t.code&&"0000"==t.code&&(e.exportModalr=!0,e.taskId=t.data.taskId,e.taskName=t.data.taskName)})).catch((function(t){console.error(t)})).finally((function(){e.exportloading=!1}))},exportcancelModal:function(){this.exportModalr=!1},Gotor:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModalr=!1,this.cancelModel()}}},g=h,y=(o("5744"),o("2877")),f=Object(y["a"])(g,n,a,!1,null,"1c27c377",null);e["default"]=f.exports}}]);