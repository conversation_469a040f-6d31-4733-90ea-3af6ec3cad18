<template>
  <!-- 套餐管理 -->
  <ContentWrap>
    <el-card>
      <div class="search-container">
        <el-form :model="searchForm" inline>
          <el-form-item label="套餐名称：">
            <el-input
              v-model="searchForm.packageName"
              placeholder="请输入套餐名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="套餐类型：">
            <el-select
              v-model="searchForm.packageType"
              placeholder="请选择类型"
              clearable
              style="width: 150px"
            >
              <el-option :value="1" label="基础套餐" />
              <el-option :value="2" label="高级套餐" />
              <el-option :value="3" label="企业套餐" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态：">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <el-option :value="1" label="上架" />
              <el-option :value="0" label="下架" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="hasPermission('search')"
              type="primary"
              :loading="searchLoading"
              @click="handleSearch"
            >
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button v-if="hasPermission('add')" type="success" @click="handleAdd">
              <Icon icon="ep:plus" class="mr-5px" />
              新增套餐
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <div style="margin-top: 20px">
        <el-table :data="tableData" v-loading="loading" border>
          <el-table-column prop="packageName" label="套餐名称" min-width="150" />
          <el-table-column prop="packageType" label="套餐类型" min-width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="getTypeTag(row.packageType)">
                {{ getTypeText(row.packageType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="dataAmount" label="流量(GB)" min-width="100" align="right">
            <template #default="{ row }">
              {{ (row.dataAmount / 1024).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="voiceMinutes" label="通话(分钟)" min-width="100" align="right" />
          <el-table-column prop="smsCount" label="短信(条)" min-width="100" align="right" />
          <el-table-column prop="monthlyFee" label="月费(元)" min-width="100" align="right" />
          <el-table-column prop="validDays" label="有效期(天)" min-width="100" align="center" />
          <el-table-column prop="status" label="状态" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusTag(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="150" align="center" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="hasPermission('edit')"
                type="primary"
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                v-if="hasPermission('delete')"
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="pagination.pageSizes"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { hasPermi } from '@/components/Permission'
import type {
  PackageInfo,
  PackageSearchForm,
  PaginationConfig,
  PackageType
} from '@/types/channel'

// 组件选项
defineOptions({
  name: 'ChannelPackageManagement'
})

// 权限检查函数 - 使用项目统一的权限系统
const hasPermission = (permission: string): boolean => {
  return hasPermi(`channel:package:${permission}`)
}

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)

// 分页配置
const pagination = reactive<PaginationConfig>({
  currentPage: 1,
  pageSize: 20,
  total: 0,
  pageSizes: [10, 20, 50, 100]
})

// 搜索表单 - 使用类型定义
const searchForm = reactive<PackageSearchForm>({
  packageName: '',
  packageType: null,
  status: null
})

// 表格数据 - 使用具体类型
const tableData = ref<PackageInfo[]>([])

// 方法
const getTypeTag = (type: number): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    1: 'primary',
    2: 'success',
    3: 'warning'
  }
  return typeMap[type] || 'info'
}

const getTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '基础套餐',
    2: '高级套餐',
    3: '企业套餐'
  }
  return typeMap[type] || '未知'
}

const getStatusTag = (status: number): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  return status === 1 ? 'success' : 'danger'
}

const getStatusText = (status: number) => {
  return status === 1 ? '上架' : '下架'
}

const handleSearch = () => {
  pagination.currentPage = 1
  getTableData()
}

const handlePageChange = (page: number) => {
  pagination.currentPage = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  getTableData()
}

const handleAdd = () => {
  ElMessage.info('新增套餐功能开发中...')
}

const handleEdit = (row: PackageInfo) => {
  ElMessage.info(`编辑套餐: ${row.packageName}`)
}

const handleDelete = async (row: PackageInfo) => {
  try {
    await ElMessageBox.confirm(`确定要删除套餐 "${row.packageName}" 吗？`, '确认删除', {
      type: 'warning'
    })

    // TODO: 实现删除套餐API调用
    ElMessage.success('删除成功')
    getTableData()
  } catch (error) {
    // 用户取消操作 - 这里捕获用户取消确认框的情况
    console.log('用户取消删除套餐操作')
  }
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        packageName: '基础套餐A',
        packageType: 1,
        dataAmount: 2048, // 2GB
        voiceMinutes: 100,
        smsCount: 50,
        monthlyFee: 29.0,
        validDays: 30,
        status: 1
      },
      {
        id: 2,
        packageName: '高级套餐B',
        packageType: 2,
        dataAmount: 5120, // 5GB
        voiceMinutes: 300,
        smsCount: 100,
        monthlyFee: 59.0,
        validDays: 30,
        status: 1
      }
    ]
    pagination.total = 2
  } catch (error) {
    ElMessage.error('获取套餐数据失败')
    console.error('获取套餐数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.search-container {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}
</style>
