# 环境
VITE_NODE_ENV=development

# 接口前缀
VITE_API_BASE_PATH=/cmiweb

# 打包路径
VITE_BASE_PATH=/

# 标题
VITE_APP_TITLE=CMI

# 是否全量引入element-plus样式
VITE_USE_ALL_ELEMENT_PLUS_STYLE=true

# 是否开启mock
VITE_USE_MOCK=true

# 是否使用在线图标
VITE_USE_ONLINE_ICON=true

# 是否隐藏全局设置按钮
VITE_HIDE_GLOBAL_SETTING=false

# CMI 项目特定配置
# Cookie过期时间（天）
VITE_COOKIE_EXPIRES=1

# 是否使用国际化
VITE_USE_I18N=true

# 默认首页路由名称
VITE_HOME_NAME=home

# HSS开户信息默认值
VITE_KEY_TYPE=ClearKey

# CMI 测试环境 IP（为空时使用默认配置）
VITE_CMI_TEST_IP=http://***********:5042/api
