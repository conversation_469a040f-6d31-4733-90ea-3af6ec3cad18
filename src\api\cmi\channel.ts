import request from '@/axios'

const servicePre = '/cms'

// ==================== 通用接口 ====================

// 根据账户查询corpid
export const searchCorpId = (params: any) => {
  return request.get({
    url: '/sys/api/v1/channel/userName',
    params
  })
}

// 查询渠道商合作模式
export const getChannelCooperationMode = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getChannelCooperationMode`,
    params
  })
}

// 获取合作模式（返回 data: ["1","2","3"]）
export const getChannelCooperationModeForLogin = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getChannelCooperationModeForLogin`,
    params
  }).then(res => {
    return res?.data?.data || [];
  });
}

// ==================== 充值管理相关接口 ====================

// 获取账户管理信息（返回 data: [ {...} ]）
export const getAccountManagement = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getAccountManagement`,
    params
  }).then(res => {
    // 只返回第一个账户对象，便于前端直接使用
    return res?.data?.data?.[0] || {};
  });
}

// 获取营销账户详情
export const getMarketingAccountDetails = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getMarketingAccountDetails`,
    params
  })
}

// ==================== 库存管理相关接口 ====================

// 获取库存任务列表
export const getStockTaskList = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getStockTaskList`,
    params
  })
}

// 获取库存卡片列表
export const getStockCardList = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getStockCardList`,
    params
  })
}

// ==================== 订单管理相关接口 ====================

// 获取订单列表
export const getOrderList = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getOrderList`,
    params
  })
}

// 获取订单详情
export const getOrderDetail = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getOrderDetail`,
    params
  })
}

// ==================== 流量池管理相关接口 ====================

// 获取流量池列表
export const getFlowPoolList = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getFlowPoolList`,
    params
  })
}

// 设置流量池阈值
export const setFlowPoolThreshold = (data: any) => {
  return request.post({
    url: `${servicePre}/channel/setFlowPoolThreshold`,
    data
  })
}

// ==================== AQ码管理相关接口 ====================

// 获取AQ码列表
export const getAQCodeList = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getAQCodeList`,
    params
  })
}

// 生成AQ码
export const generateAQCode = (data: any) => {
  return request.post({
    url: `${servicePre}/channel/generateAQCode`,
    data
  })
}

// ==================== 燃料包管理相关接口 ====================

// 获取燃料包列表
export const getFuelPackList = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getFuelPackList`,
    params
  })
}

// 创建燃料包
export const createFuelPack = (data: any) => {
  return request.post({
    url: `${servicePre}/channel/createFuelPack`,
    data
  })
}

// 更新燃料包
export const updateFuelPack = (data: any) => {
  return request.put({
    url: `${servicePre}/channel/updateFuelPack`,
    data
  })
}

// 删除燃料包
export const deleteFuelPack = (id: string) => {
  return request.delete({
    url: `${servicePre}/channel/deleteFuelPack/${id}`
  })
}

// ==================== 套餐管理相关接口 ====================

// 获取套餐列表
export const getPackageList = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getPackageList`,
    params
  })
}

// 创建套餐
export const createPackage = (data: any) => {
  return request.post({
    url: `${servicePre}/channel/createPackage`,
    data
  })
}

// ==================== 购买套餐相关接口 ====================

// 获取可购套餐列表
export const getAvailablePackages = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getAvailablePackages`,
    params
  })
}

// 购买套餐
export const buyPackage = (data: any) => {
  return request.post({
    url: `${servicePre}/channel/buyPackage`,
    data
  })
}

// 获取购买历史
export const getBuyHistory = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getBuyHistory`,
    params
  })
}

// ==================== 服务支持相关接口 ====================

// 获取工单列表
export const getTicketList = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getTicketList`,
    params
  })
}

// 创建工单
export const createTicket = (data: any) => {
  return request.post({
    url: `${servicePre}/channel/createTicket`,
    data
  })
}

// 更新工单
export const updateTicket = (data: any) => {
  return request.put({
    url: `${servicePre}/channel/updateTicket`,
    data
  })
}

// ==================== 地址管理相关接口 ====================

// 获取地址列表
export const getAddressList = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getAddressList`,
    params
  })
}

// 创建地址
export const createAddress = (data: any) => {
  return request.post({
    url: `${servicePre}/channel/createAddress`,
    data
  })
}

// 更新地址
export const updateAddress = (data: any) => {
  return request.put({
    url: `${servicePre}/channel/updateAddress`,
    data
  })
}

// 删除地址
export const deleteAddress = (id: string) => {
  return request.delete({
    url: `${servicePre}/channel/deleteAddress/${id}`
  })
}

// 设置默认地址
export const setDefaultAddress = (id: string) => {
  return request.put({
    url: `${servicePre}/channel/setDefaultAddress/${id}`
  })
}

// ==================== 渠道计费查询相关接口 ====================

// 获取渠道账单列表
export const getChannelBillList = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getChannelBillList`,
    params
  })
}

// 导出账单文件
export const exportBillFile = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/exportBillFile`,
    params,
    responseType: 'blob'
  })
}

// 导出发票
export const exportInvoice = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/exportInvoice`,
    params,
    responseType: 'blob'
  })
}

// 线上支付
export const onlinePayment = (data: any) => {
  return request.post({
    url: `${servicePre}/channel/onlinePayment`,
    data
  })
}

// 线下支付
export const offlinePayment = (data: any) => {
  return request.post({
    url: `${servicePre}/channel/offlinePayment`,
    data
  })
}

// 撤销支付
export const revokePayment = (data: any) => {
  return request.post({
    url: `${servicePre}/channel/revokePayment`,
    data
  })
}

// ==================== 支付订单相关接口 ====================

// 获取支付订单列表
export const getPaymentOrderList = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getPaymentOrderList`,
    params
  })
}

// 取消支付订单
export const cancelPaymentOrder = (id: string) => {
  return request.put({
    url: `${servicePre}/channel/cancelPaymentOrder/${id}`
  })
}

// 删除支付订单
export const deletePaymentOrder = (id: string) => {
  return request.delete({
    url: `${servicePre}/channel/deletePaymentOrder/${id}`
  })
}

// ==================== 资源合作相关接口 ====================

// 获取资源合作列表
export const getResourceCooperationList = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getResourceCooperationList`,
    params
  })
}

// 创建资源合作
export const createResourceCooperation = (data: any) => {
  return request.post({
    url: `${servicePre}/channel/createResourceCooperation`,
    data
  })
}

// ==================== 子渠道提供商相关接口 ====================

// 获取子渠道提供商列表
export const getSubChannelProviderList = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getSubChannelProviderList`,
    params
  })
}

// 创建子渠道提供商
export const createSubChannelProvider = (data: any) => {
  return request.post({
    url: `${servicePre}/channel/createSubChannelProvider`,
    data
  })
}

// 子渠道提供商充值
export const rechargeSubChannelProvider = (data: any) => {
  return request.post({
    url: `${servicePre}/channel/rechargeSubChannelProvider`,
    data
  })
}

// ==================== 白卡订单相关接口 ====================

// 获取白卡订单列表
export const getWhiteCardOrderList = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/getWhiteCardOrderList`,
    params
  })
}

// 处理白卡订单
export const processWhiteCardOrder = (data: any) => {
  return request.post({
    url: `${servicePre}/channel/processWhiteCardOrder`,
    data
  })
}

// 取消白卡订单
export const cancelWhiteCardOrder = (id: string) => {
  return request.put({
    url: `${servicePre}/channel/cancelWhiteCardOrder/${id}`
  })
}

// 退款白卡订单
export const refundWhiteCardOrder = (data: any) => {
  return request.post({
    url: `${servicePre}/channel/refundWhiteCardOrder`,
    data
  })
}

// 导出白卡订单
export const exportWhiteCardOrders = (params: any) => {
  return request.get({
    url: `${servicePre}/channel/exportWhiteCardOrders`,
    params,
    responseType: 'blob'
  })
}
