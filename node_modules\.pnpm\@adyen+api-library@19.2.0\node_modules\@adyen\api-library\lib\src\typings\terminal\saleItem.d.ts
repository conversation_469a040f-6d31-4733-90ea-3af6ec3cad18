/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
export declare class SaleItem {
    'AdditionalProductInfo'?: string;
    'EanUpc'?: string;
    'ItemAmount': number;
    'ItemID': number;
    'ProductCode': string;
    'ProductLabel'?: string;
    'Quantity'?: number;
    'SaleChannel'?: string;
    'TaxCode'?: string;
    'UnitOfMeasure'?: SaleItem.UnitOfMeasureEnum;
    'UnitPrice'?: number;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace SaleItem {
    enum UnitOfMeasureEnum {
        Case,
        Centilitre,
        Centimetre,
        Foot,
        Gram,
        Inch,
        <PERSON>logram,
        Kilometre,
        <PERSON>tre,
        Meter,
        Mile,
        Other,
        <PERSON><PERSON>ce,
        Pint,
        Pound,
        Quart,
        UkGallon,
        UsGallon,
        Yard
    }
}
