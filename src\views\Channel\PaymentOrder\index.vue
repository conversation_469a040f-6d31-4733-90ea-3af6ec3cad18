<template>
  <!-- 支付订单管理 -->
  <ContentWrap>
    <el-card>
      <div class="search-container">
        <el-form :model="searchForm" inline>
          <el-form-item>
            <el-select
              v-model="searchForm.paymentStatus"
              placeholder="请选择支付状态"
              clearable
              filterable
              class="search-input"
              @change="handlePaymentStatusChange"
            >
              <el-option
                v-for="item in paymentStatusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="searchForm.thirdOrderNo"
              placeholder="请输入第三方订单号"
              clearable
              class="search-input"
            />
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="searchForm.thirdMchorderNo"
              placeholder="请输入第三方商户订单号"
              clearable
              class="search-input"
            />
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="searchForm.createDate"
              type="daterange"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="search-date-picker"
              @change="handleDateChange"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="hasPermission('search')"
              type="primary"
              :loading="searchLoading"
              @click="handleSearch"
              class="search-button"
            >
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <div class="table-container">
        <el-table :data="tableData" v-loading="loading" border>
          <el-table-column prop="orderNo" label="订单号" min-width="180" />
          <el-table-column prop="thirdOrderNo" label="第三方订单号" min-width="180" />
          <el-table-column prop="thirdMchorderNo" label="第三方商户订单号" min-width="180" />
          <el-table-column prop="amount" label="金额" min-width="120" align="right">
            <template #default="scope">
              {{ scope.row ? (scope.row.amount || 0).toFixed(2) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="paymentStatus" label="支付状态" min-width="120" align="center">
            <template #default="scope">
              <el-tag :type="getPaymentStatusTag(scope.row?.paymentStatus)" v-if="scope.row">
                {{ getPaymentStatusText(scope.row?.paymentStatus) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="paymentMethod" label="支付方式" min-width="120" align="center">
            <template #default="scope">
              {{ getPaymentMethodText(scope.row?.paymentMethod) }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="160" />
          <el-table-column prop="expireTime" label="过期时间" min-width="160" />
          <el-table-column label="剩余时间" min-width="120" align="center">
            <template #default="scope">
              <span v-if="scope.row && scope.row.timer && scope.row.timer !== '已过期'" class="timer-active">
                {{ scope.row.timer }}
              </span>
              <span v-else-if="scope.row && scope.row.timer === '已过期'" class="timer-expired"> 已过期 </span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="200" align="center" fixed="right">
            <template #default="scope">
              <div v-if="scope.row" class="action-buttons">
                <!-- 未过期且有剩余时间的订单 -->
                <div v-if="scope.row.timer && scope.row.timer !== '已过期'">
                  <div class="payment-timer-info">
                    支付剩余时间：<span class="timer-active">{{ scope.row.timer }}</span>
                  </div>
                  <el-button
                    v-if="hasPermission('pay')"
                    type="primary"
                    size="small"
                    @click="handlePay(scope.row)"
                    class="action-button"
                  >
                    支付
                  </el-button>
                  <el-button
                    v-if="hasPermission('cancel')"
                    type="danger"
                    size="small"
                    @click="handleCancel(scope.row)"
                    class="action-button"
                  >
                    取消
                  </el-button>
                </div>
                <!-- 已过期或已关闭的订单 -->
                <el-button
                  v-else-if="
                    ['EXPIRED', 'CLOSED'].includes(scope.row.paymentStatus) && hasPermission('delete')
                  "
                  type="danger"
                  size="small"
                  @click="handleDelete(scope.row)"
                >
                  删除
                </el-button>
                <!-- 其他状态 -->
                <span v-else class="no-action">无可用操作</span>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

// 权限检查函数
const hasPermission = (permission: string): boolean => {
  console.log(`🔍 [支付订单权限检查] ${permission}: 允许访问`)
  return true
}

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const searchForm = reactive({
  paymentStatus: '',
  thirdOrderNo: '',
  thirdMchorderNo: '',
  createDate: [] as string[]
})

const paymentStatusList = [
  { value: 'PENDING', label: '待支付' },
  { value: 'PAID', label: '已支付' },
  { value: 'EXPIRED', label: '已过期' },
  { value: 'CLOSED', label: '已关闭' },
  { value: 'REFUNDED', label: '已退款' }
]

const tableData = ref<any[]>([])
let timerInterval: NodeJS.Timeout | null = null

// 方法
const getPaymentStatusTag = (
  status: string
): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    PENDING: 'warning',
    PAID: 'success',
    EXPIRED: 'info',
    CLOSED: 'danger',
    REFUNDED: 'primary'
  }
  return statusMap[status] || 'info'
}

const getPaymentStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    PENDING: '待支付',
    PAID: '已支付',
    EXPIRED: '已过期',
    CLOSED: '已关闭',
    REFUNDED: '已退款'
  }
  return statusMap[status] || '未知'
}

const getPaymentMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    ALIPAY: '支付宝',
    WECHAT: '微信支付',
    BANK: '银行卡',
    BALANCE: '余额支付'
  }
  return methodMap[method] || '未知'
}

const handlePaymentStatusChange = (value: string) => {
  console.log('支付状态变更:', value)
}

const handleDateChange = (value: string[]) => {
  console.log('日期范围变更:', value)
}

const handleSearch = () => {
  currentPage.value = 1
  getTableData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getTableData()
}

const handlePay = (row: any) => {
  ElMessage.info(`跳转支付页面: ${row.orderNo}`)
  // TODO: 跳转到支付页面
}

const handleCancel = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要取消订单 ${row.orderNo} 吗？`, '确认取消', {
      type: 'warning'
    })

    ElMessage.success('订单取消成功')
    getTableData()
  } catch (error) {
    // 用户取消操作
  }
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除订单 ${row.orderNo} 吗？`, '确认删除', {
      type: 'warning'
    })

    ElMessage.success('订单删除成功')
    getTableData()
  } catch (error) {
    // 用户取消操作
  }
}

// 更新倒计时
const updateTimer = () => {
  tableData.value.forEach((row) => {
    if (row.expireTime && row.paymentStatus === 'PENDING') {
      const now = new Date().getTime()
      const expireTime = new Date(row.expireTime).getTime()
      const diff = expireTime - now

      if (diff > 0) {
        const hours = Math.floor(diff / (1000 * 60 * 60))
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
        const seconds = Math.floor((diff % (1000 * 60)) / 1000)
        row.timer = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      } else {
        row.timer = '已过期'
        row.paymentStatus = 'EXPIRED'
      }
    }
  })
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    const now = new Date()
    const expireTime1 = new Date(now.getTime() + 2 * 60 * 60 * 1000) // 2小时后过期
    const expireTime2 = new Date(now.getTime() - 1 * 60 * 60 * 1000) // 1小时前过期

    tableData.value = [
      {
        id: 1,
        orderNo: 'PAY20240101001',
        thirdOrderNo: 'ALI20240101001',
        thirdMchorderNo: 'MCH20240101001',
        amount: 100.0,
        paymentStatus: 'PENDING',
        paymentMethod: 'ALIPAY',
        createTime: '2024-01-01 10:00:00',
        expireTime: expireTime1.toISOString(),
        timer: ''
      },
      {
        id: 2,
        orderNo: 'PAY20240101002',
        thirdOrderNo: 'WX20240101002',
        thirdMchorderNo: 'MCH20240101002',
        amount: 200.0,
        paymentStatus: 'EXPIRED',
        paymentMethod: 'WECHAT',
        createTime: '2024-01-01 09:00:00',
        expireTime: expireTime2.toISOString(),
        timer: '已过期'
      }
    ]
    total.value = 2

    // 启动倒计时
    updateTimer()
  } catch (error) {
    ElMessage.error('获取支付订单数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getTableData()
  // 每秒更新倒计时
  timerInterval = setInterval(updateTimer, 1000)
})

onUnmounted(() => {
  if (timerInterval) {
    clearInterval(timerInterval)
  }
})
</script>

<style scoped>
.search-container {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

/* 搜索表单样式 */
.search-input {
  width: 300px;
  margin-right: 10px;
}

.search-date-picker {
  width: 300px;
}

.search-button {
  margin-left: 10px;
}

/* 表格容器样式 */
.table-container {
  margin-top: 20px;
}

/* 分页容器样式 */
.pagination-container {
  margin-top: 20px;
  text-align: right;
}

/* 计时器样式 */
.timer-active {
  color: var(--el-color-danger);
  font-weight: bold;
}

.timer-expired {
  color: var(--el-text-color-disabled);
}

/* 支付计时器信息样式 */
.payment-timer-info {
  margin-bottom: 10px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

/* 操作按钮样式 */
.action-button {
  margin: 0 5px 5px 0;
}

/* 无操作状态样式 */
.no-action {
  color: var(--el-text-color-disabled);
}
</style>
