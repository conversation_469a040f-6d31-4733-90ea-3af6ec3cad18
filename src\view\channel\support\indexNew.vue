<template>
	<div class="support-container">
		<ElCard class="support-card">
			<!-- 搜索表单 -->
			<div class="search-form">
				<ElForm inline>
					<ElFormItem label="MSISDN" label-width="80px">
						<ElInput 
							v-model.trim="msisdn" 
							placeholder="请输入MSISDN" 
							clearable 
							style="width: 200px" 
						/>
					</ElFormItem>
					<ElFormItem label="ICCID" label-width="80px">
						<ElInput 
							v-model.trim="iccid" 
							placeholder="请输入ICCID" 
							clearable 
							style="width: 200px" 
						/>
					</ElFormItem>
					<ElFormItem label="IMSI" label-width="80px">
						<ElInput 
							v-model.trim="imsi" 
							placeholder="请输入IMSI" 
							clearable 
							style="width: 200px" 
						/>
					</ElFormItem>
					<ElFormItem>
						<ElButton 
							:disabled="cooperationMode === '3'" 
							type="primary" 
							@click="search" 
							:loading="loading"
							:icon="Search"
						>
							搜索
						</ElButton>
					</ElFormItem>
				</ElForm>
			</div>
			
			<!-- 主要内容区域 -->
			<div class="content-area">
				<!-- 表格区域 -->
				<div class="table-section">
					<ElTable 
						:data="tableData" 
						v-loading="loading"
						border
						style="width: 100%"
						class="support-table"
					>
						<ElTableColumn 
							prop="msisdn"
							label="MSISDN"
							min-width="120"
							align="center"
						/>
						<ElTableColumn 
							prop="iccid"
							label="ICCID"
							min-width="150"
							align="center"
						/>
						<ElTableColumn 
							prop="imsi"
							label="IMSI"
							min-width="150"
							align="center"
						/>
						<ElTableColumn 
							prop="status"
							label="状态"
							min-width="100"
							align="center"
						>
							<template #default="{ row }">
								<ElTag :type="getStatusType(row.status)">
									{{ getStatusText(row.status) }}
								</ElTag>
							</template>
						</ElTableColumn>
						<ElTableColumn 
							prop="location"
							label="位置"
							min-width="120"
							align="center"
						/>
						<ElTableColumn 
							prop="lastUpdateTime"
							label="最后更新时间"
							min-width="160"
							align="center"
						/>
					</ElTable>
				</div>
				
				<!-- 右侧操作按钮 -->
				<div class="action-section" v-if="showList">
					<div class="action-buttons">
						<ElButton 
							v-if="checkPermission(['currentPackage']) && showList.mcc"
							type="primary"
							style="width: 180px; margin-bottom: 10px;"
							@click="goToLocationPackage"
						>
							位置套餐
						</ElButton>
						<ElButton 
							v-else-if="checkPermission(['currentPackage'])"
							type="primary"
							style="width: 180px; margin-bottom: 10px;"
							disabled
						>
							位置套餐
						</ElButton>
						
						<ElButton 
							v-if="checkPermission(['purchasedPackage'])"
							type="success"
							style="width: 180px; margin-bottom: 10px;"
							@click="goToPurchasedPackage"
						>
							已购套餐
						</ElButton>
						
						<ElButton 
							v-if="checkPermission(['useList'])"
							type="info"
							style="width: 180px; margin-bottom: 10px;"
							@click="goToUseList"
						>
							使用列表
						</ElButton>
						
						<ElButton 
							v-if="checkPermission(['detailsList'])"
							type="warning"
							style="width: 180px; margin-bottom: 10px;"
							@click="goToDetailsList"
						>
							详情列表
						</ElButton>
						
						<ElButton 
							v-if="checkPermission(['localSearch'])"
							type="default"
							style="width: 180px; margin-bottom: 10px;"
							@click="goToLocalSearch"
						>
							本地搜索
						</ElButton>
					</div>
				</div>
			</div>
			
			<!-- 位置记录弹窗 -->
			<ElDialog 
				v-model="localUpModal" 
				:close-on-click-modal="false"
				width="850px"
				class="location-dialog"
			>
				<template #header>
					<span>位置记录</span>
				</template>
				
				<div class="location-content">
					<ElTable 
						:data="locationData" 
						v-loading="locationLoading"
						border
						style="width: 100%"
					>
						<ElTableColumn 
							prop="updateTime"
							label="更新时间"
							min-width="160"
							align="center"
						/>
						<ElTableColumn 
							prop="location"
							label="位置"
							min-width="120"
							align="center"
						/>
						<ElTableColumn 
							prop="mcc"
							label="MCC"
							min-width="80"
							align="center"
						/>
						<ElTableColumn 
							prop="mnc"
							label="MNC"
							min-width="80"
							align="center"
						/>
					</ElTable>
				</div>
				
				<template #footer>
					<ElButton @click="cancelModal" :icon="ArrowLeft">
						关闭
					</ElButton>
				</template>
			</ElDialog>
		</ElCard>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElCard, ElForm, ElFormItem, ElInput, ElButton, ElTable, ElTableColumn, ElDialog, ElTag } from 'element-plus'
import { Search, ArrowLeft } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

defineOptions({
  name: 'ChannelSupportManagement'
})

const router = useRouter()

// 响应式数据
const loading = ref(false)
const locationLoading = ref(false)
const localUpModal = ref(false)

const cooperationMode = ref('')
const msisdn = ref('')
const iccid = ref('')
const imsi = ref('')

const tableData = ref([])
const locationData = ref([])
const showList = ref(null)

// 权限检查函数
const checkPermission = (permissions: string[]): boolean => {
  return true
}

// 状态类型映射
const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': 'success',  // 激活
    '2': 'warning',  // 待激活
    '3': 'danger',   // 停用
    '4': 'info'      // 其他
  }
  return statusMap[status] || 'info'
}

// 状态文本映射
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': '激活',
    '2': '待激活',
    '3': '停用',
    '4': '其他'
  }
  return statusMap[status] || '-'
}

// 方法
const search = () => {
  if (!msisdn.value && !iccid.value && !imsi.value) {
    ElMessage.warning('请至少输入一个查询条件')
    return
  }
  
  loading.value = true
  loadData()
}

const goToLocationPackage = () => {
  router.push({
    name: 'splocation_package',
    query: {
      imsi: showList.value?.imsi,
      backimsi: imsi.value,
      backiccid: iccid.value,
      backmsisdn: msisdn.value,
      mcc: showList.value?.mcc,
      localName: showList.value?.local,
      localNameEn: showList.value?.localEn
    }
  })
}

const goToPurchasedPackage = () => {
  router.push('/channel/support/purchasedPackage')
}

const goToUseList = () => {
  router.push('/channel/support/useList')
}

const goToDetailsList = () => {
  router.push('/channel/support/detailsList')
}

const goToLocalSearch = () => {
  router.push('/channel/support/localSearch')
}

const cancelModal = () => {
  localUpModal.value = false
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    console.log('搜索支持数据:', { msisdn: msisdn.value, iccid: iccid.value, imsi: imsi.value })
    
    // 模拟数据
    tableData.value = [
      {
        msisdn: '1234567890',
        iccid: '89860000000000000001',
        imsi: '460000000000001',
        status: '1',
        location: '中国',
        lastUpdateTime: '2024-01-15 10:30:00'
      }
    ]
    
    // 设置显示列表数据
    if (tableData.value.length > 0) {
      showList.value = {
        imsi: tableData.value[0].imsi,
        mcc: '460',
        local: '中国',
        localEn: 'China'
      }
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时初始化
onMounted(() => {
  cooperationMode.value = sessionStorage.getItem('cooperationMode') || ''
})
</script>

<style scoped>
.support-container {
  padding: 20px;
}

.support-card {
  margin-bottom: 20px;
}

.search-form {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 15px;
  margin-right: 20px;
}

.content-area {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.table-section {
  flex: 1;
}

.support-table {
  margin-top: 20px;
}

.action-section {
  min-width: 200px;
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  height: fit-content;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.location-content {
  margin: 20px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-area {
    flex-direction: column;
  }

  .action-section {
    min-width: auto;
  }

  .action-buttons {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .action-buttons .el-button {
    width: auto !important;
    margin-bottom: 0 !important;
  }
}

@media (max-width: 768px) {
  .search-form :deep(.el-form-item) {
    margin-bottom: 15px;
    margin-right: 0;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .el-button {
    width: 100% !important;
  }
}
</style>
