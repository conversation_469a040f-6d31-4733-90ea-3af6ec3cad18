<template>
  <!-- 流量详情列表 -->
  <ContentWrap>
    <!-- 加载状态 -->
    <div v-if="!isComponentMounted" class="loading-container">
      <ElSkeleton :rows="5" animated />
    </div>

    <!-- 主要内容 -->
    <ElCard v-else>
      <div class="search-header">
        <ElButton @click="goBack" type="default" style="margin-bottom: 20px">
          <Icon icon="ep:arrow-left" />
          返回
        </ElButton>
        <h3>流量使用详情</h3>
      </div>

      <!-- 搜索表单 -->
      <ElForm :model="searchForm" inline style="margin-bottom: 20px">
        <ElFormItem label="时间范围">
          <ElDatePicker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </ElFormItem>
        <ElFormItem label="流量类型">
          <ElSelect v-model="searchForm.flowType" placeholder="请选择" clearable>
            <ElOption label="全部" value="" />
            <ElOption label="基础流量" value="1" />
            <ElOption label="加油包流量" value="2" />
            <ElOption label="套餐流量" value="3" />
          </ElSelect>
        </ElFormItem>
        <ElFormItem>
          <ElButton type="primary" @click="handleSearch">查询</ElButton>
          <ElButton @click="handleReset">重置</ElButton>
        </ElFormItem>
      </ElForm>

      <!-- 流量详情列表 -->
      <div style="margin-top: 20px">
        <ElTable :data="streamList" v-loading="loading" border>
          <ElTableColumn prop="date" label="日期" min-width="120" />
          <ElTableColumn prop="flowType" label="流量类型" min-width="120" align="center">
            <template #default="scope">
              <ElTag :type="getFlowTypeTag(scope.row.flowType)">
                {{ getFlowTypeText(scope.row.flowType) }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="totalFlow" label="总流量(MB)" min-width="120" align="right" />
          <ElTableColumn prop="usedFlow" label="已用流量(MB)" min-width="120" align="right" />
          <ElTableColumn
            prop="remainingFlow"
            label="剩余流量(MB)"
            min-width="120"
            align="right"
          />
          <ElTableColumn prop="usageRate" label="使用率" min-width="100" align="center">
            <template #default="scope">
              <ElProgress
                :percentage="scope.row.usageRate"
                :color="getProgressColor(scope.row.usageRate)"
                :stroke-width="8"
              />
            </template>
          </ElTableColumn>
          <ElTableColumn prop="status" label="状态" min-width="100" align="center">
            <template #default="scope">
              <ElTag :type="getStatusTag(scope.row.status)">
                {{ getStatusText(scope.row.status) }}
              </ElTag>
            </template>
          </ElTableColumn>
        </ElTable>
      </div>

      <!-- 分页 -->
      <div style="margin-top: 15px; text-align: right">
        <ElPagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </ElCard>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { useRouter, useRoute } from 'vue-router'
import { useComponentSafety } from '@/composables/useComponentSafety'

const router = useRouter()
const route = useRoute()

// 使用组件安全性管理
const { isComponentMounted, isLoading, safeNavigate, safeLoadData, initComponentSafety } =
  useComponentSafety('流量详情')

// 响应式数据
const loading = isLoading
const streamList = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 搜索表单
const searchForm = reactive({
  dateRange: [],
  flowType: ''
})

// 获取流量类型标签
const getFlowTypeTag = (type: string) => {
  const typeMap = {
    '1': 'primary',
    '2': 'success',
    '3': 'warning'
  }
  return typeMap[type] || 'info'
}

// 获取流量类型文本
const getFlowTypeText = (type: string) => {
  const typeMap = {
    '1': '基础流量',
    '2': '加油包流量',
    '3': '套餐流量'
  }
  return typeMap[type] || '未知'
}

// 获取状态标签
const getStatusTag = (status: string) => {
  const statusMap = {
    '1': 'success',
    '2': 'warning',
    '3': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    '1': '正常',
    '2': '即将耗尽',
    '3': '已耗尽'
  }
  return statusMap[status] || '未知'
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

// 获取流量详情数据
const getStreamData = () => {
  return safeLoadData(async () => {
    try {
      console.log('📊 [流量详情] 开始获取流量数据')

      const corpId = route.query.corpId
      if (!corpId) {
        throw new Error('未获取到企业ID')
      }

      // TODO: 实现真实API调用
      // const response = await getFlowDetails({
      //   corpId,
      //   page: currentPage.value,
      //   pageSize: pageSize.value,
      //   ...searchForm
      // })

      // 使用模拟数据
      streamList.value = [
        {
          date: '2024-01-15',
          flowType: '1',
          totalFlow: 1024,
          usedFlow: 512,
          remainingFlow: 512,
          usageRate: 50,
          status: '1'
        },
        {
          date: '2024-01-14',
          flowType: '2',
          totalFlow: 2048,
          usedFlow: 1638,
          remainingFlow: 410,
          usageRate: 80,
          status: '2'
        },
        {
          date: '2024-01-13',
          flowType: '3',
          totalFlow: 512,
          usedFlow: 512,
          remainingFlow: 0,
          usageRate: 100,
          status: '3'
        }
      ]
      total.value = 3

      console.log('✅ [流量详情] 数据更新完成:', {
        streamList: streamList.value,
        total: total.value
      })

      return true
    } catch (error) {
      console.error('❌ [流量详情] API调用失败:', error)
      ElMessage.error('获取流量数据失败')
      return false
    }
  }, '获取流量数据失败')
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  getStreamData()
}

// 重置搜索
const handleReset = () => {
  searchForm.dateRange = []
  searchForm.flowType = ''
  currentPage.value = 1
  getStreamData()
}

// 返回上一页
const goBack = () => {
  safeNavigate(
    router,
    {
      path: '/newcmi/channel/deposit'
    },
    '返回失败'
  )
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getStreamData()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  getStreamData()
}

// 在组件挂载后自动加载数据
initComponentSafety(async () => {
  await getStreamData()
})
</script>

<style scoped>
.loading-container {
  padding: 20px;
}

.search-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-header h3 {
  margin: 0;
  color: #409eff;
}
</style>
