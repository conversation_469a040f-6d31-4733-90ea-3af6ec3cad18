(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-879c9ab8"],{"00b4":function(t,e,a){"use strict";a("ac1f");var n=a("23e7"),o=a("c65b"),r=a("1626"),i=a("825a"),c=a("577e"),s=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),l=/./.test;n({target:"RegExp",proto:!0,forced:!s},{test:function(t){var e=i(this),a=c(t),n=e.exec;if(!r(n))return o(l,e,a);var s=o(n,e,a);return null!==s&&(i(s),!0)}})},"5ad0":function(t,e,a){},c69c:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t._self._c;return e("Card",[e("div",{staticClass:"search_head_i"},[e("div",{staticClass:"search_box"},[e("Button",{attrs:{disabled:"false"==t.show||"1"==t.cooperationMode||"3"==t.cooperationMode,type:"primary",icon:"md-add"},on:{click:function(e){return t.add()}}},[t._v(t._s(t.$t("support.create")))])],1)]),e("Table",{ref:"selection",staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:t.columns,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(a){var n=a.row;a.index;return[e("Button",{staticStyle:{"margin-right":"10px"},attrs:{disabled:"false"==t.show,type:"error",size:"small"},on:{click:function(e){return t.deleteItem(n.id)}}},[t._v(t._s(t.$t("address.Delete")))])]}}])}),e("div",{staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Modal",{attrs:{title:t.$t("support.newAddonPack"),"footer-hide":!0,"mask-closable":!1,width:"400px"},on:{"on-cancel":t.cancelModal},model:{value:t.newGaspackFlag,callback:function(e){t.newGaspackFlag=e},expression:"newGaspackFlag"}},[e("div",{staticStyle:{padding:"0 16px"}},[e("Form",{ref:"addObj",attrs:{model:t.addObj,"label-width":100,rules:t.ruleValidate}},[e("FormItem",{staticStyle:{margin:"20px 0 35px 0"},attrs:{label:t.$t("support.AddonAmount"),prop:"flowValue"}},[e("Input",{attrs:{placeholder:t.$t("support.inputFlowValue")},model:{value:t.addObj.flowValue,callback:function(e){t.$set(t.addObj,"flowValue",e)},expression:"addObj.flowValue"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("MB")])])],1)],1),e("div",{staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v(t._s(t.$t("common.cancel")))]),t._v("      \n\t\t\t\t"),e("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],staticStyle:{"margin-left":"8px"},attrs:{type:"primary",loading:t.submitFlag},on:{click:t.submit}},[t._v(t._s(t.$t("support.determine")))])],1)],1)])],1)},o=[],r=(a("d9e2"),a("d3b7"),a("ac1f"),a("00b4"),a("d2d0")),i={data:function(){var t=this,e=function(t,e,a){if(e){var n=/^[1-9]\d*$/;return n.test(e)}a()},a=function(t,e,a){e&&e>104857600?a(new Error(t.message)):a()};return{total:0,currentPage:1,page:0,cooperationMode:"",loading:!1,searchloading:!1,newGaspackFlag:!1,submitFlag:!1,show:"",data:[],columns:[{title:this.$t("support.AddonID"),key:"id",minWidth:120,align:"center",tooltip:!0},{title:this.$t("support.AddonName"),key:"nameCn",minWidth:120,align:"center",tooltip:!0,render:function(e,a){var n=a.row,o="zh-CN"===t.$i18n.locale?n.nameCn:"en-US"===t.$i18n.locale?n.nameEn:"";return e("label",o)}},{title:this.$t("support.flowValue")+"(MB)",key:"flowValue",minWidth:120,align:"center",tooltip:!0},{title:this.$t("address.action"),slot:"action",minWidth:100,align:"center",fixed:"right"}],addObj:{flowValue:""},ruleValidate:{flowValue:[{required:!0,type:"string",message:this.$t("support.addAmountMandatory")},{validator:e,message:this.$t("flow.Pleaseinteger")},,{validator:a,message:this.$t("support.cannotExceed")}]}}},mounted:function(){this.cooperationMode=sessionStorage.getItem("cooperationMode"),this.show=this.$route.query.showFalg,this.cooperationMode&&2!=this.cooperationMode||(console.log(this.cooperationMode),this.goPageFirst(1))},methods:{goPageFirst:function(t){var e=this;this.loading=!0;var a=this;Object(r["f"])({pageNo:t,pageSize:10,corpId:sessionStorage.getItem("corpId"),cooperationMode:sessionStorage.getItem("cooperationMode")}).then((function(n){"0000"==n.code&&(a.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t,e.data=n.data,e.total=n.count)})).catch((function(t){console.error(t)})).finally((function(){a.loading=!1,e.searchloading=!1}))},goPage:function(t){this.goPageFirst(t)},deleteItem:function(t){var e=this,a=sessionStorage.getItem("cooperationMode");this.$Modal.confirm({title:this.$t("flow.Confirmdelete"),onOk:function(){Object(r["d"])(t,a).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:e.$t("address.Operationreminder"),desc:e.$t("common.Successful")}),e.goPageFirst(e.page)})).catch((function(t){}))}})},cancelModal:function(){this.$refs.addObj.resetFields(),this.newGaspackFlag=!1},add:function(){this.newGaspackFlag=!0},submit:function(){var t=this;this.$refs["addObj"].validate((function(e){e&&(t.submitFlag=!0,Object(r["b"])({flowValue:t.addObj.flowValue,corpId:sessionStorage.getItem("corpId"),cooperationMode:sessionStorage.getItem("cooperationMode")}).then((function(e){if("0000"===e.code){e.data;t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("common.Successful")}),t.submitFlag=!1,t.goPageFirst(1),t.newGaspackFlag=!1,t.cancelModal()}})).catch((function(e){t.submitFlag=!1,console.log(e)})).finally((function(){t.submitFlag=!1})))}))}}},c=i,s=(a("f3a0"),a("2877")),l=Object(s["a"])(c,n,o,!1,null,"f528ecac",null);e["default"]=l.exports},d2d0:function(t,e,a){"use strict";a.d(e,"e",(function(){return r})),a.d(e,"a",(function(){return i})),a.d(e,"i",(function(){return c})),a.d(e,"c",(function(){return s})),a.d(e,"g",(function(){return l})),a.d(e,"h",(function(){return d})),a.d(e,"f",(function(){return u})),a.d(e,"d",(function(){return p})),a.d(e,"b",(function(){return f}));a("99af");var n=a("66df"),o="/pms/api/v1/channelBuiltPackage",r=function(t){return n["a"].request({url:o+"/getChannelList",data:t,method:"post"})},i=function(t){return n["a"].request({url:o+"/add",data:t,method:"post"})},c=function(t){return n["a"].request({url:o+"/update",data:t,method:"post"})},s=function(t){return n["a"].request({url:o+"/batchDelete",data:t,method:"delete"})},l=function(t){return n["a"].request({url:"/pms/api/v1/upccTemplate/packageGetUpcc",params:t,method:"get"})},d=function(t){return n["a"].request({url:"/cms/channel/distributors/judgeChannelCreatePackage",params:t,method:"get"})},u=function(t){return n["a"].request({url:"/pms/refuelPackage/channelSelf/get",data:t,method:"post"})},p=function(t,e){return n["a"].request({url:"/pms/refuelPackage/channelSelf/del/".concat(t,"/").concat(e),method:"delete"})},f=function(t){return n["a"].request({url:"/pms/refuelPackage/channelSelf/add",data:t,method:"post"})}},f3a0:function(t,e,a){"use strict";a("5ad0")}}]);