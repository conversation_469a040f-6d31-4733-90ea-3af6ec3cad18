/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { POIProfile } from './pOIProfile';
import { TerminalEnvironmentType } from './terminalEnvironmentType';
export declare class POITerminalData {
    'POICapabilities': Array<POITerminalData.POICapabilitiesEnum>;
    'POIProfile'?: POIProfile;
    'POISerialNumber': string;
    'TerminalEnvironment': TerminalEnvironmentType;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace POITerminalData {
    enum POICapabilitiesEnum {
        CashHandling,
        CashierDisplay,
        CashierError,
        CashierInput,
        CustomerDisplay,
        CustomerError,
        CustomerInput,
        EmvContactless,
        Icc,
        MagStripe,
        PrinterDocument,
        PrinterReceipt,
        PrinterVoucher
    }
}
