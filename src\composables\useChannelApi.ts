/**
 * Channel 模块 API 调用 Composable
 * 提供统一的 API 调用和错误处理功能
 */

import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { PaginationConfig, BaseSearchForm } from '@/types/channel'

/**
 * API 调用状态
 */
interface ApiState {
  loading: boolean
  error: Error | null
  data: any
}

/**
 * 使用异步数据获取
 * @param apiFunction API 调用函数
 * @param options 配置选项
 */
export function useAsyncData<T = any>(
  apiFunction: () => Promise<T>,
  options: {
    immediate?: boolean
    onSuccess?: (data: T) => void
    onError?: (error: Error) => void
    successMessage?: string
    errorMessage?: string
  } = {}
) {
  const {
    immediate = true,
    onSuccess,
    onError,
    successMessage,
    errorMessage = '操作失败，请重试'
  } = options

  const state = reactive<ApiState>({
    loading: false,
    error: null,
    data: null
  })

  /**
   * 执行 API 调用
   */
  const execute = async (): Promise<T | null> => {
    try {
      state.loading = true
      state.error = null
      
      const result = await apiFunction()
      state.data = result
      
      if (successMessage) {
        ElMessage.success(successMessage)
      }
      
      onSuccess?.(result)
      return result
    } catch (error) {
      const err = error as Error
      state.error = err
      
      console.error('API调用失败:', err)
      ElMessage.error(errorMessage)
      
      onError?.(err)
      return null
    } finally {
      state.loading = false
    }
  }

  // 立即执行
  if (immediate) {
    execute()
  }

  return {
    ...state,
    execute,
    refresh: execute
  }
}

/**
 * 使用分页数据
 * @param apiFunction 分页 API 调用函数
 * @param searchForm 搜索表单
 */
export function usePaginatedData<T = any>(
  apiFunction: (params: any) => Promise<{ list: T[]; total: number }>,
  searchForm: BaseSearchForm
) {
  const loading = ref(false)
  const tableData = ref<T[]>([])
  
  const pagination = reactive<PaginationConfig>({
    currentPage: 1,
    pageSize: 20,
    total: 0,
    pageSizes: [10, 20, 50, 100]
  })

  /**
   * 获取数据
   */
  const fetchData = async () => {
    try {
      loading.value = true
      
      const params = {
        ...searchForm,
        pageNum: pagination.currentPage,
        pageSize: pagination.pageSize
      }
      
      const response = await apiFunction(params)
      
      tableData.value = response.list
      pagination.total = response.total
    } catch (error) {
      console.error('获取数据失败:', error)
      ElMessage.error('获取数据失败，请重试')
      
      // 重置数据
      tableData.value = []
      pagination.total = 0
    } finally {
      loading.value = false
    }
  }

  /**
   * 搜索
   */
  const handleSearch = () => {
    pagination.currentPage = 1
    fetchData()
  }

  /**
   * 页码变化
   */
  const handlePageChange = (page: number) => {
    pagination.currentPage = page
    fetchData()
  }

  /**
   * 页大小变化
   */
  const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.currentPage = 1
    fetchData()
  }

  /**
   * 刷新数据
   */
  const refresh = () => {
    fetchData()
  }

  return {
    loading,
    tableData,
    pagination,
    fetchData,
    handleSearch,
    handlePageChange,
    handleSizeChange,
    refresh
  }
}

/**
 * 使用操作确认
 * @param options 配置选项
 */
export function useConfirmOperation(options: {
  title?: string
  type?: 'warning' | 'info' | 'success' | 'error'
  successMessage?: string
  errorMessage?: string
}) {
  const {
    title = '确认操作',
    type = 'warning',
    successMessage = '操作成功',
    errorMessage = '操作失败'
  } = options

  /**
   * 执行确认操作
   * @param message 确认消息
   * @param operation 操作函数
   */
  const executeWithConfirm = async (
    message: string,
    operation: () => Promise<void>
  ): Promise<boolean> => {
    try {
      await ElMessageBox.confirm(message, title, { type })
      
      await operation()
      
      ElMessage.success(successMessage)
      return true
    } catch (error) {
      if (error === 'cancel') {
        // 用户取消操作
        console.log('用户取消操作')
        return false
      }
      
      console.error('操作失败:', error)
      ElMessage.error(errorMessage)
      return false
    }
  }

  return {
    executeWithConfirm
  }
}

/**
 * 使用表单操作
 * @param options 配置选项
 */
export function useFormOperation<T = any>(options: {
  createApi?: (data: T) => Promise<any>
  updateApi?: (data: T) => Promise<any>
  onSuccess?: () => void
}) {
  const { createApi, updateApi, onSuccess } = options
  
  const loading = ref(false)

  /**
   * 保存数据
   * @param data 表单数据
   * @param isEdit 是否为编辑模式
   */
  const save = async (data: T, isEdit = false): Promise<boolean> => {
    try {
      loading.value = true
      
      if (isEdit && updateApi) {
        await updateApi(data)
        ElMessage.success('更新成功')
      } else if (!isEdit && createApi) {
        await createApi(data)
        ElMessage.success('创建成功')
      } else {
        throw new Error('未配置相应的 API 函数')
      }
      
      onSuccess?.()
      return true
    } catch (error) {
      console.error('保存失败:', error)
      ElMessage.error('保存失败，请重试')
      return false
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    save
  }
}
