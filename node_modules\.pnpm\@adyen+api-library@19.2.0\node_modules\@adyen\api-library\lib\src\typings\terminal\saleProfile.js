"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SaleProfile = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
class SaleProfile {
    static getAttributeTypeMap() {
        return SaleProfile.attributeTypeMap;
    }
}
exports.SaleProfile = SaleProfile;
SaleProfile.discriminator = undefined;
SaleProfile.attributeTypeMap = [
    {
        "name": "GenericProfile",
        "baseName": "GenericProfile",
        "type": "SaleProfile.GenericProfileEnum"
    },
    {
        "name": "ServiceProfiles",
        "baseName": "ServiceProfiles",
        "type": "Array<SaleProfile.ServiceProfilesEnum>"
    }
];
(function (SaleProfile) {
    let GenericProfileEnum;
    (function (GenericProfileEnum) {
        GenericProfileEnum[GenericProfileEnum["Basic"] = 'Basic'] = "Basic";
        GenericProfileEnum[GenericProfileEnum["Extended"] = 'Extended'] = "Extended";
        GenericProfileEnum[GenericProfileEnum["Standard"] = 'Standard'] = "Standard";
    })(GenericProfileEnum = SaleProfile.GenericProfileEnum || (SaleProfile.GenericProfileEnum = {}));
    let ServiceProfilesEnum;
    (function (ServiceProfilesEnum) {
        ServiceProfilesEnum[ServiceProfilesEnum["Batch"] = 'Batch'] = "Batch";
        ServiceProfilesEnum[ServiceProfilesEnum["CardReader"] = 'CardReader'] = "CardReader";
        ServiceProfilesEnum[ServiceProfilesEnum["Communication"] = 'Communication'] = "Communication";
        ServiceProfilesEnum[ServiceProfilesEnum["Loyalty"] = 'Loyalty'] = "Loyalty";
        ServiceProfilesEnum[ServiceProfilesEnum["OneTimeRes"] = 'OneTimeRes'] = "OneTimeRes";
        ServiceProfilesEnum[ServiceProfilesEnum["Pin"] = 'PIN'] = "Pin";
        ServiceProfilesEnum[ServiceProfilesEnum["Reservation"] = 'Reservation'] = "Reservation";
        ServiceProfilesEnum[ServiceProfilesEnum["Sound"] = 'Sound'] = "Sound";
        ServiceProfilesEnum[ServiceProfilesEnum["StoredValue"] = 'StoredValue'] = "StoredValue";
        ServiceProfilesEnum[ServiceProfilesEnum["Synchro"] = 'Synchro'] = "Synchro";
    })(ServiceProfilesEnum = SaleProfile.ServiceProfilesEnum || (SaleProfile.ServiceProfilesEnum = {}));
})(SaleProfile = exports.SaleProfile || (exports.SaleProfile = {}));
//# sourceMappingURL=saleProfile.js.map