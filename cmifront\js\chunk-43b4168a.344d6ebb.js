(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-43b4168a"],{"129f":function(e,t,r){"use strict";e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!==e&&t!==t}},"1c31":function(e,t,r){"use strict";r.d(t,"t",(function(){return o})),r.d(t,"s",(function(){return i})),r.d(t,"a",(function(){return s})),r.d(t,"b",(function(){return u})),r.d(t,"c",(function(){return c})),r.d(t,"d",(function(){return l})),r.d(t,"e",(function(){return d})),r.d(t,"f",(function(){return m})),r.d(t,"g",(function(){return p})),r.d(t,"h",(function(){return f})),r.d(t,"i",(function(){return h})),r.d(t,"j",(function(){return g})),r.d(t,"k",(function(){return b})),r.d(t,"l",(function(){return v})),r.d(t,"x",(function(){return y})),r.d(t,"m",(function(){return k})),r.d(t,"n",(function(){return x})),r.d(t,"o",(function(){return D})),r.d(t,"p",(function(){return w})),r.d(t,"q",(function(){return q})),r.d(t,"v",(function(){return P})),r.d(t,"r",(function(){return T})),r.d(t,"w",(function(){return F})),r.d(t,"u",(function(){return I}));var a=r("66df"),n="/stat",o=function(e){return a["a"].request({url:"/cms/api/v1/packageCard/countReuseExport",data:e,responseType:"blob",method:"post"})},i=function(e){return a["a"].request({url:"/cms/api/v1/packageCard/countReuse",data:e,method:"post"})},s=function(e){return a["a"].request({url:n+"/activereport/detailDownload",params:e,responseType:"blob",method:"get"})},u=function(e){return a["a"].request({url:n+"/activereport/pageList",data:e,method:"post"})},c=function(e){return a["a"].request({url:n+"/cardReport",params:e,method:"get"})},l=function(e){return a["a"].request({url:n+"/cardReport/export",params:e,responseType:"blob",method:"get"})},d=function(e){return a["a"].request({url:n+"/offline/export",params:e,responseType:"blob",method:"get"})},m=function(e){return a["a"].request({url:n+"/offline/import",data:e,method:"post"})},p=function(e){return a["a"].request({url:n+"/offline/pageList",data:e,method:"post"})},f=function(e){return a["a"].request({url:n+"/operatorsettle/detailDownload",params:e,responseType:"blob",method:"get"})},h=function(e){return a["a"].request({url:n+"/operatorsettle/pageList",data:e,method:"post"})},g=function(e){return a["a"].request({url:n+"/postpaidsettle/detailDownload",params:e,responseType:"blob",method:"get"})},b=function(e){return a["a"].request({url:n+"/postpaidsettle/pageList",data:e,method:"post"})},v=function(e){return a["a"].request({url:n+"/rate",params:e,method:"get"})},y=function(e){return a["a"].request({url:n+"/rate",data:e,method:"post"})},k=function(e){return a["a"].request({url:n+"/rate/export",params:e,responseType:"blob",method:"get"})},x=function(e){return a["a"].request({url:n+"/report/package/analysis/export",params:e,responseType:"blob",method:"get"})},D=function(e){return a["a"].request({url:n+"/report/package/analysis/search",data:e,method:"post"})},w=function(e){return a["a"].request({url:n+"/terminalsettle/detailDownload",params:e,responseType:"blob",method:"get"})},q=function(e){return a["a"].request({url:n+"/terminalsettle/pageList",data:e,method:"post"})},P=function(e){return a["a"].request({url:"/charging/cost/supplierCostQuery",data:e,method:"post"})},T=function(e){return a["a"].request({url:"/charging/cost/supplierCostExport",data:e,responseType:"blob",method:"post"})},F=function(e){return a["a"].request({url:"/cms/esim/getEsimcardStats",params:e,method:"get"})},I=function(e){return a["a"].request({url:"/cms/esim/exportEsimcardStats",params:e,method:"get"})}},"841c":function(e,t,r){"use strict";var a=r("c65b"),n=r("d784"),o=r("825a"),i=r("7234"),s=r("1d80"),u=r("129f"),c=r("577e"),l=r("dc4a"),d=r("14c3");n("search",(function(e,t,r){return[function(t){var r=s(this),n=i(t)?void 0:l(t,e);return n?a(n,t,r):new RegExp(t)[e](c(r))},function(e){var a=o(this),n=c(e),i=r(t,a,n);if(i.done)return i.value;var s=a.lastIndex;u(s,0)||(a.lastIndex=0);var l=d(a,n);return u(a.lastIndex,s)||(a.lastIndex=s),null===l?-1:l.index}]}))},b35e:function(e,t,r){"use strict";r("d9e2");t["a"]={methods:{validateDate:function(e,t,r){var a=this.form.endDate||this.form.endTime,n=this.form.startDate||this.form.startTime;a&&n?"startDate"===e.field||"startTime"===e.field?this.$time(t,">",a)?r(new Error("开始时间不能大于结束时间")):r():this.$time(t,"<",a)?r(new Error("结束时间不能小于开始时间")):r():r()}}}},b6a0:function(e,t,r){"use strict";r.r(t);r("ac1f"),r("841c");var a=function(){var e=this,t=e._self._c;return t("Card",[t("div",{staticStyle:{display:"flex",width:"100%"}},[t("Form",{ref:"form",attrs:{"label-width":0,model:e.form,rules:e.rule,inline:""}},[t("FormItem",{attrs:{prop:"packagename"}},[t("Input",{staticStyle:{width:"200px","text-align":"left",margin:"0 10px"},attrs:{placeholder:"输入套餐名称",clearable:""},model:{value:e.form.packagename,callback:function(t){e.$set(e.form,"packagename",t)},expression:"form.packagename"}})],1),t("FormItem",{attrs:{prop:"corpname"}},[t("Input",{staticStyle:{width:"200px","text-align":"left",margin:"0 10px"},attrs:{placeholder:"输入渠道名称",clearable:""},model:{value:e.form.corpname,callback:function(t){e.$set(e.form,"corpname",t)},expression:"form.corpname"}})],1),t("FormItem",{attrs:{prop:"dimension"}},[t("Select",{staticStyle:{width:"200px","text-align":"left",margin:"0 10px"},attrs:{clearable:!0,placeholder:"请选择统计维度"},on:{"on-change":function(t){e.date="",e.resetField(["startDate","endDate"])}},model:{value:e.form.dimension,callback:function(t){e.$set(e.form,"dimension",t)},expression:"form.dimension"}},e._l(e.cycleList,(function(r,a){return t("Option",{key:a,attrs:{value:r.id}},[e._v(e._s(r.value))])})),1)],1),t("FormItem",{attrs:{prop:"endDate"}},["2"!=e.form.dimension?t("FormItem",{attrs:{prop:"startDate"}},[t("DatePicker",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{format:"yyyyMMdd",editable:!1,type:"daterange",placeholder:"选择时间段",clearable:""},on:{"on-change":e.checkDatePicker},model:{value:e.date,callback:function(t){e.date=t},expression:"date"}})],1):e._e(),"2"==e.form.dimension?t("FormItem",{attrs:{prop:"startDate"}},[t("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择开始月份",editable:!1},on:{"on-change":function(t){return e.checkDatePicker(t,1)}}})],1):e._e(),"2"==e.form.dimension?t("FormItem",{attrs:{prop:"endDate"}},[t("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择结束月份",editable:!1},on:{"on-change":function(t){return e.checkDatePicker(t,2)}}})],1):e._e()],1),t("FormItem",[t("Button",{attrs:{type:"primary",icon:"md-search",size:"large"},on:{click:function(t){return e.search()}}},[e._v("搜索")]),t("Button",{staticStyle:{"margin-left":"20px"},attrs:{type:"success",icon:"ios-cloud-download-outline",size:"large"},on:{click:function(t){return e.exportTable()}}},[e._v("导出")])],1)],1)],1),t("Table",{staticStyle:{width:"100%","margin-top":"50px"},attrs:{columns:e.columns12,data:e.data,loading:e.loading}}),t("div",{staticStyle:{"margin-left":"38%","margin-top":"100px","margin-bottom":"160px"}},[t("Page",{attrs:{total:e.total,current:e.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1),e.data1.length?t("Table",{staticStyle:{width:"100%","margin-top":"50px"},attrs:{columns:e.columns12,data:e.data1,loading:e.loading}}):e._e()],1)},n=[],o=r("5530"),i=(r("caad"),r("14d9"),r("d3b7"),r("2532"),r("3ca3"),r("159b"),r("ddb0"),r("2b3d"),r("bf19"),r("9861"),r("88a7"),r("271a"),r("5494"),r("1c31")),s=r("b35e"),u={mixins:[s["a"]],data:function(){return{date:"",loading:!1,form:{corpname:"",dimension:"",endDate:"",packagename:"",startDate:""},rule:{startDate:[{required:!0,message:"请选择时间"}],endDate:[{required:!0,message:"请选择时间"}],dimension:[{required:!0,message:"请选择维度"}]},total:0,currentPage:1,cycleList:[{id:1,value:"日"},{id:2,value:"月"}],typeList:[{value:"1",label:"普通卡（实体卡）"},{value:"2",label:"Esim卡"},{value:"3",label:"贴片卡"}],sellList:[{value:"102",label:"API"},{value:"103",label:"官网（H5）"},{value:"104",label:"北京移动"},{value:"105",label:"批量售卖"},{value:"106",label:"推广活动"},{value:"110",label:"测试渠道"},{value:"111",label:"合作发卡"},{value:"112",label:"后付费发卡"},{value:"113",label:"WEB"},{value:"114",label:"流量池WEB"}],columns12:[{title:"渠道名称",key:"corpname",align:"center",render:function(e,t){return e("span","-"===t.row.corpname?"合计":t.row.corpname)}},{title:"套餐名称",key:"packagename",align:"center"},{title:"货币",key:"currencycode",align:"center",render:function(e,t){var r=t.row,a="156"==r.currencycode?"人民币":"840"==r.currencycode?"美元":"344"==r.currencycode?"港币":"-";return e("label",a)}},{title:"时间",key:"statTime",align:"center"},{title:"使用量",key:"usevolume",align:"center"},{title:"当前货币收入",key:"salesincome",align:"center"},{title:"港币收入",key:"hkdincome",align:"center"}],data:[],data1:[],rules:{}}},created:function(){this.rule.startDate.push({validator:this.validateDate,trigger:"change"}),this.rule.endDate.push({validator:this.validateDate,trigger:"change"})},mounted:function(){},methods:{resetField:function(e){this.$refs["form"].fields.forEach((function(t){console.log(t.prop),e.includes(t.prop)&&t.resetField()}))},checkDatePicker:function(e,t){Array.isArray(e)?(this.form.startDate=e[0],this.form.endDate=e[1]):1===t?this.form.startDate=e:this.form.endDate=e},goPageFirst:function(e){var t=this;0===e&&(this.currentPage=1);var r=this,a=this.currentPage,n=10;this.$refs["form"].validate((function(s){s?(t.loading=!0,Object(i["k"])(Object(o["a"])({pageNum:a,pageSize:n},t.form)).then((function(a){if("0000"==a.code)if(r.loading=!1,t.page=e,t.total=a.data.total,t.data=a.data.record,a.data.records1[0]){var n=a.data.records1[0],o={};for(var i in n)if(Object.hasOwnProperty.call(n,i)){var s=n[i];o[i]=s||"-"}t.data1=[o]}else t.data1=[]})).catch((function(e){console.error(e)})).finally((function(){t.loading=!1}))):t.$Message.error("参数校验不通过")}))},goPage:function(e){this.goPageFirst(e)},search:function(){this.goPageFirst(0)},exportTable:function(){var e=this;this.$refs["form"].validate((function(t){t&&Object(i["j"])(Object(o["a"])({},e.form)).then((function(e){var t=e.data,r="后付费报表.csv";if("download"in document.createElement("a")){var a=document.createElement("a"),n=URL.createObjectURL(t);a.download=r,a.href=n,a.click(),URL.revokeObjectURL(n)}else navigator.msSaveBlob(t,r)})).catch((function(){return e.downloading=!1}))}))},details:function(e){this.$router.push({path:"/channel/detailsList"})}}},c=u,l=r("2877"),d=Object(l["a"])(c,a,n,!1,null,null,null);t["default"]=d.exports}}]);