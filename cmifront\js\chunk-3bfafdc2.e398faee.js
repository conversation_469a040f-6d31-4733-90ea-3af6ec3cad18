(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3bfafdc2"],{"507b":function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e._self._c;return a("Card",[a("div",{staticClass:"search_head_i"},[a("div",{staticClass:"search_box"},[a("span",{staticClass:"search_box_label"},[e._v("渠道商简称")]),e._v("  \n      "),a("Select",{staticStyle:{width:"200px"},attrs:{filterable:"",clearable:"",placeholder:"请选择渠道商简称"},model:{value:e.searchForm.corpId,callback:function(a){e.$set(e.searchForm,"corpId",a)},expression:"searchForm.corpId"}},e._l(e.corpListFilter,(function(t){return a("Option",{key:t.corpId,attrs:{value:t.corpId}},[e._v(e._s(t.corpName))])})),1)],1),a("div",{staticClass:"search_box"},[e._v("\n              \n      "),a("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",loading:e.searchLoading},on:{click:e.handleSearch}},[e._v("搜索")]),e._v("\n              \n      "),a("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{margin:"0 20px"},attrs:{type:"info",icon:"md-add"},on:{click:e.handleAdd}},[e._v("新增")])],1)]),a("Table",{attrs:{columns:e.columns,data:e.tableData,loading:e.loading},scopedSlots:e._u([{key:"action",fn:function(t){var r=t.row;return[a("Button",{directives:[{name:"has",rawName:"v-has",value:"edit",expression:"'edit'"}],attrs:{type:"success",size:"small",ghost:""},on:{click:function(a){return e.handleEdit(r)}}},[e._v("编辑")]),a("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],staticStyle:{"margin-left":"20px"},attrs:{disabled:"-9999"==r.corpId,type:"error",size:"small",ghost:""},on:{click:function(a){return e.handleDelete(r)}}},[e._v("删除")])]}}])}),a("div",{staticStyle:{"margin-top":"15px"}},[a("Page",{attrs:{total:e.pagination.total,current:e.pagination.current,"page-size":e.pagination.pageSize,"show-sizer":"","show-total":"","show-elevator":""},on:{"on-change":e.handlePageChange,"on-page-size-change":e.handlePageSizeChange}})],1),a("Modal",{attrs:{title:e.modalTitle,"mask-closable":!1,width:"800"},on:{"on-cancel":e.handleModalCancel},model:{value:e.modalVisible,callback:function(a){e.modalVisible=a},expression:"modalVisible"}},[a("Form",{ref:"formRef",staticStyle:{"margin-bottom":"30px"},attrs:{model:e.formData,rules:e.ruleValidate,"label-width":120}},[e.formData.id?[e.isDefaultAddress?[a("Row",{attrs:{type:"flex",align:"middle"}},[a("Col",{attrs:{span:"12"}},[a("FormItem",{attrs:{label:"渠道商默认地址"}})],1),a("Col",{attrs:{span:"12"}},[a("FormItem",{attrs:{prop:"defaultAddress","label-width":0,rules:{required:!0,message:"请选择GTP话单地址",trigger:"change"}}},[a("RadioGroup",{staticClass:"radio-group",model:{value:e.formData.defaultAddress,callback:function(a){e.$set(e.formData,"defaultAddress",a)},expression:"formData.defaultAddress"}},[a("Radio",{attrs:{label:"1"}},[e._v("ARCH")]),a("Radio",{attrs:{label:"2"}},[e._v("GTP Proxy")])],1)],1)],1)],1)]:[a("Row",{attrs:{type:"flex",align:"middle"}},[a("Col",{attrs:{span:"12"}},[a("FormItem",{attrs:{label:"渠道商简称",prop:"corpId",rules:{required:!0,message:"请选择渠道商简称",trigger:"change"}}},[a("Select",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择渠道商简称",disabled:""},model:{value:e.formData.corpId,callback:function(a){e.$set(e.formData,"corpId",a)},expression:"formData.corpId"}},e._l(e.corpList,(function(t){return a("Option",{key:t.corpId,attrs:{value:t.corpId}},[e._v(e._s(t.corpName))])})),1)],1)],1),a("Col",{attrs:{span:"12"}},[a("FormItem",{staticClass:"no-label-form-item",attrs:{prop:"address",rules:{required:!0,message:"请选择GTP话单地址",trigger:"change"}}},[a("RadioGroup",{staticClass:"radio-group",model:{value:e.formData.address,callback:function(a){e.$set(e.formData,"address",a)},expression:"formData.address"}},[a("Radio",{attrs:{label:"1"}},[e._v("ARCH")]),a("Radio",{attrs:{label:"2"}},[e._v("GTP Proxy")])],1)],1)],1)],1)]]:[a("Row",{attrs:{type:"flex",align:"middle"}},[a("Col",{attrs:{span:"11"}},[a("FormItem",{attrs:{label:"渠道商默认地址"}})],1),a("Col",{attrs:{span:"13"}},[a("FormItem",{attrs:{prop:"defaultAddress","label-width":128}},[a("RadioGroup",{staticClass:"radio-group",model:{value:e.formData.defaultAddress,callback:function(a){e.$set(e.formData,"defaultAddress",a)},expression:"formData.defaultAddress"}},[a("Radio",{attrs:{label:"1"}},[e._v("ARCH")]),a("Radio",{attrs:{label:"2"}},[e._v("GTP Proxy")])],1)],1)],1)],1),a("Divider"),e._l(e.formData.addresses,(function(t,r){return a("div",{key:r},[a("Row",{attrs:{type:"flex",align:"middle"}},[a("Col",{attrs:{span:"12"}},[a("FormItem",{attrs:{label:"渠道商简称",prop:"addresses."+r+".corpId",rules:{required:!0,message:"请选择渠道商简称",trigger:"change"}}},[a("Select",{staticStyle:{width:"200px"},attrs:{filterable:"",clearable:"",placeholder:"请选择渠道商简称"},model:{value:t.corpId,callback:function(a){e.$set(t,"corpId",a)},expression:"item.corpId"}},e._l(e.availableCorps,(function(t){return a("Option",{key:t.corpId,attrs:{value:t.corpId,disabled:e.isCorpSelected(t.corpId,r)}},[e._v(e._s(t.corpName))])})),1)],1)],1),a("Col",{attrs:{span:"10"}},[a("div",{staticClass:"radio-address-wrapper"},[a("FormItem",{attrs:{prop:"addresses."+r+".address",rules:{required:!0,message:"请选择GTP话单地址",trigger:"change"}}},[a("RadioGroup",{staticClass:"radio-group",model:{value:t.address,callback:function(a){e.$set(t,"address",a)},expression:"item.address"}},[a("Radio",{attrs:{label:"1"}},[e._v("ARCH")]),a("Radio",{attrs:{label:"2"}},[e._v("GTP Proxy")])],1)],1)],1)]),a("Col",{attrs:{span:"2"}},[a("FormItem",{attrs:{"label-width":0}},[a("Button",{staticClass:"delete-btn",attrs:{type:"error",ghost:"",shape:"circle",size:"small",icon:"md-remove"},on:{click:function(a){return e.removeAddress(r)}}})],1)],1)],1)],1)})),a("div",{staticClass:"add-btn-wrapper"},[a("Button",{attrs:{type:"success",ghost:"",shape:"circle",size:"small",icon:"md-add"},on:{click:e.addAddress}})],1)]],2),a("div",{staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[a("Button",{attrs:{type:"primary",loading:e.submitLoading,icon:"md-checkmark"},on:{click:e.handleCustomOk}},[e._v("确定")]),e._v("\n              \n      "),a("Button",{attrs:{icon:"ios-arrow-back"},on:{click:e.handleModalCancel}},[e._v("返回")])],1)],1)],1)},s=[],o=t("2909"),d=t("c7eb"),i=t("1da1"),n=(t("d9e2"),t("99af"),t("4de4"),t("d81d"),t("14d9"),t("a434"),t("a9e3"),t("d3b7"),t("25f0"),t("6062"),t("1e70"),t("79a4"),t("c1a1"),t("8b00"),t("a4e7"),t("1e5a"),t("72c3"),t("3ca3"),t("ddb0"),t("66df")),l="cms",c=function(e){return n["a"].request({url:l+"/gtpConfig/corpList",method:"get",data:e})},u=function(e){return n["a"].request({url:l+"/gtpConfig/page",method:"post",data:e})},p=function(e){return n["a"].request({url:l+"/gtpConfig/add",method:"post",data:e})},m=function(e){return n["a"].request({url:l+"/gtpConfig/update",method:"post",data:e})},f=function(e){return n["a"].request({url:l+"/gtpConfig/delete?id=".concat(e),method:"post"})},h={name:"GTPAddressConfig",data:function(){return{searchForm:{corpId:""},searchLoading:!1,loading:!1,columns:[{title:"渠道商简称",key:"corpName",minWidth:150,tooltip:!0,align:"center",render:function(e,a){var t=a.row,r="-9999"==t.corpId?"默认":t.corpName;return e("label",r)}},{title:"话单地址",key:"address",minWidth:150,tooltip:!0,align:"center",render:function(e,a){var t=a.row,r=1==t.address?"#2b85e4":2==t.address?"#e4aa21":"",s=1==t.address?"ARCH":2==t.address?"GTP Proxy":"";return e("label",{style:{color:r}},s)}},{title:"操作",slot:"action",minWidth:200,align:"center"}],tableData:[],pagination:{current:1,pageSize:10,total:0},corpList:[],corpListFilter:[],modalVisible:!1,modalTitle:"GTP话单地址配置新增",isDefaultAddress:!1,submitLoading:!1,formData:{id:"",defaultAddress:"",corpId:"",address:"",addresses:[{corpId:"",address:""}]},ruleValidate:{defaultAddress:[{required:!0,message:"请选择默认地址",trigger:"change"}],corpId:[{required:!0,message:"请选择渠道商简称",trigger:"change"}],address:[{required:!0,message:"请选择GTP话单地址",trigger:"change"}],addresses:[{required:!0,type:"array",min:1,message:"请至少添加一个渠道商地址"}]}}},computed:{availableCorps:function(){return this.corpList},canAddMore:function(){var e=new Set(this.formData.addresses.map((function(e){return e.corpId})).filter(Boolean));return this.corpList.length>e.size}},mounted:function(){this.loadCorpList(),this.loadTableData(),this.loadFilterCorpList()},methods:{loadFilterCorpList:function(){var e=this;u({pageSize:-1,pageNum:-1}).then((function(a){"0000"===a.code&&("-9999"==a.data[0].corpId&&(a.data[0].corpName="默认"),e.corpListFilter=a.data)}))},loadCorpList:function(){var e=this;return Object(i["a"])(Object(d["a"])().mark((function a(){var t;return Object(d["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,c();case 3:t=a.sent,"0000"===t.code&&(e.corpList=t.data),a.next=9;break;case 7:a.prev=7,a.t0=a["catch"](0);case 9:case"end":return a.stop()}}),a,null,[[0,7]])})))()},loadTableData:function(){var e=this;return Object(i["a"])(Object(d["a"])().mark((function a(){var t;return Object(d["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.loading=!0,a.prev=1,a.next=4,u({corpId:e.searchForm.corpId,pageSize:e.pagination.pageSize,pageNum:e.pagination.current});case 4:t=a.sent,"0000"===t.code&&(e.tableData=t.data,e.pagination.total=Number(t.count)),a.next=10;break;case 8:a.prev=8,a.t0=a["catch"](1);case 10:return a.prev=10,e.loading=!1,a.finish(10);case 13:case"end":return a.stop()}}),a,null,[[1,8,10,13]])})))()},handleSearch:function(){this.pagination.current=1,this.loadTableData()},handlePageChange:function(e){this.pagination.current=e,this.loadTableData()},handlePageSizeChange:function(e){this.pagination.pageSize=e,this.pagination.current=1,this.loadTableData()},handleAdd:function(){this.resetForm(),this.modalTitle="GTP话单地址配置新增",this.modalVisible=!0,this.tableData[0].address&&(this.formData.defaultAddress=this.tableData[0].address.toString())},handleEdit:function(e){this.resetForm(),this.modalTitle="GTP话单地址配置修改","-9999"===e.corpId?(this.isDefaultAddress=!0,this.formData.id=e.id,this.formData.defaultAddress=e.address.toString()):(this.formData.id=e.id,this.formData.corpId=e.corpId,this.formData.address=e.address.toString()),this.modalVisible=!0},handleDelete:function(e){var a=this;this.$Modal.confirm({title:"确认删除？",onOk:function(){var t=Object(i["a"])(Object(d["a"])().mark((function t(){var r;return Object(d["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,f(e.id);case 3:r=t.sent,"0000"===r.code&&(a.$Message.success("删除成功"),a.loadTableData(),a.loadFilterCorpList()),t.next=9;break;case 7:t.prev=7,t.t0=t["catch"](0);case 9:case"end":return t.stop()}}),t,null,[[0,7]])})));function r(){return t.apply(this,arguments)}return r}()})},handleCustomOk:function(){var e=this;return Object(i["a"])(Object(d["a"])().mark((function a(){var t,r;return Object(d["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,e.$refs.formRef.validate();case 2:if(t=a.sent,t){a.next=5;break}return a.abrupt("return");case 5:if(e.submitLoading=!0,a.prev=6,e.formData.id){a.next=13;break}return r=[{corpId:"-9999",address:e.formData.defaultAddress}].concat(Object(o["a"])(e.formData.addresses.map((function(e){return{corpId:e.corpId,address:e.address}})))),a.next=11,p(r);case 11:a.next=16;break;case 13:return r={id:e.formData.id,corpId:e.isDefaultAddress?"-9999":e.formData.corpId,address:e.isDefaultAddress?e.formData.defaultAddress:e.formData.address},a.next=16,m(r);case 16:e.$Message.success("操作成功"),e.modalVisible=!1,e.loadTableData(),e.loadFilterCorpList(),a.next=24;break;case 22:a.prev=22,a.t0=a["catch"](6);case 24:return a.prev=24,e.submitLoading=!1,a.finish(24);case 27:case"end":return a.stop()}}),a,null,[[6,22,24,27]])})))()},handleModalCancel:function(){this.modalVisible=!1,this.resetForm()},resetForm:function(){this.formData={id:"",defaultAddress:"",corpId:"",address:"",addresses:[{corpId:"",address:""}]},this.isDefaultAddress=!1,this.$refs.formRef&&this.$refs.formRef.resetFields()},addAddress:function(){this.canAddMore?this.formData.addresses.push({corpId:"",address:""}):this.$Message.warning("渠道商简称已全部用尽，无剩余可选")},removeAddress:function(e){this.formData.addresses.length<=1?this.$Message.warning("至少保留一组渠道商简称地址信息"):this.formData.addresses.splice(e,1)},isCorpSelected:function(e,a){return this.formData.addresses.some((function(t,r){return r!==a&&t.corpId===e}))}}},g=h,b=(t("8798"),t("2877")),v=Object(b["a"])(g,r,s,!1,null,"02a9e89e",null);a["default"]=v.exports},8798:function(e,a,t){"use strict";t("bd61")},bd61:function(e,a,t){}}]);