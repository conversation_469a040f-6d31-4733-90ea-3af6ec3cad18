(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6822bcd8"],{"279e":function(e,t,r){"use strict";r("cb20")},"3e33":function(e,t,r){"use strict";r.r(t);r("caad");var n=function(){var e=this,t=e._self._c;return t("Card",{staticStyle:{width:"100%",padiing:"16px"}},[t("Form",{ref:"searchForm",attrs:{model:e.searchObj,inline:""},nativeOn:{submit:function(e){e.preventDefault()}}},[t("FormItem",[t("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入渠道商名称",clearable:""},model:{value:e.searchObj.wholesalerName,callback:function(t){e.$set(e.searchObj,"wholesalerName",t)},expression:"searchObj.wholesalerName"}})],1),t("FormItem",[t("Select",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择渠道商状态",clearable:!0},model:{value:e.searchObj.purchaseStatus,callback:function(t){e.$set(e.searchObj,"purchaseStatus",t)},expression:"searchObj.purchaseStatus"}},e._l(e.purchaseStatusList,(function(r){return t("Option",{key:r.value,attrs:{value:r.value}},[e._v(e._s(r.label)+"\n\t\t\t\t")])})),1)],1),t("FormItem",[t("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{margin:"0 2px"},attrs:{type:"primary"},on:{click:e.searchChannel}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"ios-search"}}),e._v(" 搜索\n\t\t\t\t")],1)]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{margin:"0 2px"},attrs:{type:"info"},on:{click:function(t){return e.channelCommon(null,"Add")}}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"md-add"}}),e._v(" 新增\n\t\t\t\t")],1)]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"batchDelete",expression:"'batchDelete'"}],staticStyle:{margin:"0 2px"},attrs:{type:"error"},on:{click:function(t){return e.deleteList(null)}}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"ios-trash"}}),e._v(" 批量删除\n\t\t\t\t")],1)])],1)],1),t("div",[t("Table",{ref:"selection",attrs:{columns:e.columns,data:e.tableData,ellipsis:!0,loading:e.tableLoading},on:{"on-selection-change":e.handleRowChange},scopedSlots:e._u([{key:"action",fn:function(r){var n=r.row;r.index;return[t("Button",{staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.channelCommon(n.corpId,"Info")}}},[e._v("详情")]),4==n.checkStatus?t("Button",{staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small",disabled:""},on:{click:function(t){return e.channelCommon(n.corpId,"Update")}}},[e._v("编辑")]):t("Button",{staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small"},on:{click:function(t){return e.channelCommon(n.corpId,"Update")}}},[e._v("编辑")]),5==n.checkStatus||4==n.checkStatus?t("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],attrs:{type:"error",size:"small",disabled:""},on:{click:function(t){return e.deleteList(n.corpId)}}},[e._v("删除")]):t("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],attrs:{type:"error",size:"small"},on:{click:function(t){return e.deleteList(n.corpId)}}},[e._v("删除")])]}},{key:"approval",fn:function(r){var n=r.row;r.index;return[[1,4,5].includes(+n.checkStatus)?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small"},on:{click:function(t){return e.cooperativeApproval(n,2)}}},[e._v("通过")]):e._e(),[1,4,5].includes(+n.checkStatus)?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],attrs:{type:"error",size:"small"},on:{click:function(t){return e.cooperativeApproval(n,3)}}},[e._v("不通过")]):e._e()]}}])}),t("Page",{staticStyle:{margin:"15px 0"},attrs:{total:e.total,"page-size":e.pageSize,current:e.page,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.page=t},"on-change":e.loadByPage}})],1),t("Modal",{attrs:{"footer-hide":!0,"mask-closable":!1},model:{value:e.approvalCrossModal,callback:function(t){e.approvalCrossModal=t},expression:"approvalCrossModal"}},[t("div",{staticClass:"approvalMoal"},[t("p",[e._v("企业是否可用")]),t("Button",{on:{click:function(t){return e.hasCompanyExit(0)}}},[e._v("不可用")]),t("Button",{staticStyle:{"margin-left":"30px"},attrs:{type:"primary"},on:{click:function(t){return e.hasCompanyExit(1)}}},[e._v("可用")])],1)])],1)},a=[],o=(r("d81d"),r("14d9"),r("e9c4"),r("b680"),r("b64b"),r("d3b7"),r("ac1f"),r("5319"),r("159b"),r("e3b7")),i={components:{},data:function(){return{approvalCurrentCropId:"",searchObj:{wholesalerName:"",purchaseStatus:""},tableData:[],selection:[],selectionIds:[],tableLoading:!1,total:0,pageSize:10,page:1,columns:[{type:"selection",minWidth:60,align:"center"},{title:"渠道商名称",key:"corpName",align:"center",minWidth:120,tooltip:!0},{title:"创建时间",key:"createTime",align:"center",minWidth:150,tooltip:!0},{title:"EBS Code",key:"ebsCode",align:"center",minWidth:120,tooltip:!0},{title:"总额度",key:"totalDeposit",align:"center",minWidth:120,tooltip:!0,render:function(e,t){var r=t.row,n="1"==r.channelType?r.totalDeposit:"2"==r.channelType?r.deposit:"0.00";return e("label",n)}},{title:"可用额度",key:"deposit",align:"center",minWidth:120,tooltip:!0,render:function(e,t){var r=t.row,n=(parseFloat(r.deposit)+parseFloat(r.creditAmount)).toFixed(2);return e("label",n)}},{title:"已用额度",key:"usedAmount",align:"center",minWidth:100,tooltip:!0,render:function(e,t){var r=t.row;return e("label",r.usedAmount)}},{title:"代销渠道类型",key:"channelType",align:"center",minWidth:120,tooltip:!0,render:function(e,t){var r=t.row,n="1"==r.channelType?"押金模式":"2"==r.channelType?"预存模式":"";return e("label",n)}},{title:"A2Z总信用额度",key:"a2zTotalDeposit",align:"center",minWidth:120,tooltip:!0},{title:"已用信用额度",key:"a2zUsedDeposit",align:"center",minWidth:120,tooltip:!0},{title:"A2Z渠道类型",key:"a2zChannelType",align:"center",minWidth:120,tooltip:!0,render:function(e,t){var r=t.row,n="1"==r.a2zChannelType?"押金模式":"2"==r.a2zChannelType?"预存模式":"";return e("label",n)}},{title:"币种",key:"currencyCode",align:"center",minWidth:100,tooltip:!0,render:function(e,t){var r=t.row,n="156"==r.currencyCode?"人民币":"840"==r.currencyCode?"美元":"344"==r.currencyCode?"港币":"获取失败";return e("label",n)}},{title:"订购状态",key:"isSub",align:"center",minWidth:120,render:function(e,t){var r=t.row,n="1"==r.isSub?"#00cc66":"#ff0000",a="1"==r.isSub?"允许订购":"不允许订购";return e("label",{style:{color:n}},a)}},{title:"操作",slot:"action",minWidth:180,align:"center"},{title:"审批状态",key:"checkStatus",align:"center",minWidth:120,render:function(e,t){var r=t.row,n={1:{color:"#27A1FF",text:"新建待审批"},2:{color:"#00cc66",text:"通过"},3:{color:"#ff0000",text:"不通过"},4:{color:"#ff0000",text:"删除待审批"},5:{color:"#ffa554",text:"修改待审批"}};return e("label",{style:{color:n[r.checkStatus].color}},n[r.checkStatus].text)}},{title:"审批操作",slot:"approval",minWidth:150,align:"center"}],typeList:[{label:"批发商",value:"1"},{label:"酬金渠道商",value:"2"},{label:"能力开放渠道商",value:"3"}],purchaseStatusList:[{label:"允许订购",value:"1"},{label:"不允许订购",value:"2"}],approvalStatusList:[{label:"待审批",value:"0"},{label:"已审批",value:"1"},{label:"审批未通过",value:"-1"}],rechargeFlag:!1,approvalCrossModal:!1}},mounted:function(){var e=null===JSON.parse(localStorage.getItem("searchObj"))?"":JSON.parse(localStorage.getItem("searchObj"));e&&(this.searchObj.wholesalerName=void 0===e.wholesalerName?"":e.wholesalerName,this.searchObj.purchaseStatus=void 0===e.purchaseStatus?"":e.purchaseStatus),this.init(),localStorage.removeItem("searchObj")},methods:{init:function(){this.getDistributorsList(0)},formatMockData:function(e){function t(e){return e.replace(/\_(\w)/g,(function(e,t){return t.toUpperCase()}))}var r=[];return e.forEach((function(e){var n={};Object.keys(e).forEach((function(r){n[t(r)]=e[r].value})),r.push(n)})),console.log(r[0]),r},getDistributorsList:function(e){var t=this;0===e&&(this.page=1),Object(o["t"])({corpName:this.searchObj.wholesalerName,isSub:this.searchObj.purchaseStatus,pageNumber:this.page,pageSize:10,isNeedAuth:!0}).then((function(e){if("0000"===e.code){var r=[];e.data.records.map((function(e,t){e.authObj?(r.push(e.authObj),e.authObj.totalDeposit=e.totalDeposit,e.authObj.deposit=e.deposit,e.authObj.usedAmount=e.usedAmount,e.authObj.a2zUsedDeposit=e.a2zUsedDeposit):r.push(e)})),t.tableData=r,t.total=e.data.totalCount}}))},loadByPage:function(e){this.page=e,this.getDistributorsList(e)},searchChannel:function(){this.getDistributorsList(0)},channelAdd:function(){this.$router.push({name:"channel"})},channelCommon:function(e,t){this.$router.push({name:"channel"+t,query:{id:e,type:t,searchObj:encodeURIComponent(JSON.stringify(this.searchObj))}})},handleRowChange:function(e){var t=this;this.selection=e,this.selectionIds=[],e.map((function(e,r){t.selectionIds.push(e.corpId)}))},deleteList:function(e){var t=this;if(e)var r=[e];else{var n=this.selection.length;if(n<1)return void this.$Message.warning("请至少选择一条记录")}this.$Modal.confirm({title:"确认删除？",onOk:function(){Object(o["k"])({corpIds:e?r:t.selectionIds}).then((function(e){"0000"===e.code?(t.init(),t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.selection=[],t.selectionIds=[]):t.$Notice.error({title:"操作提示",desc:"操作失败"})}))}})},cooperativeApproval:function(e,t){var r=this;this.approvalCurrentCropId=e.corpId,Object(o["f"])({corpId:this.approvalCurrentCropId,status:t}).then((function(t){"0000"===t.code&&(e.checkStatus,r.init())}))},hasCompanyExit:function(e){e||Object(o["f"])({corpId:this.approvalCurrentCropId,status:"3"}),this.approvalCrossModal=!1}}},u=i,c=(r("279e"),r("2877")),s=Object(c["a"])(u,n,a,!1,null,null,null);t["default"]=s.exports},b680:function(e,t,r){"use strict";var n=r("23e7"),a=r("e330"),o=r("5926"),i=r("408a"),u=r("1148"),c=r("d039"),s=RangeError,l=String,p=Math.floor,d=a(u),h=a("".slice),f=a(1..toFixed),m=function(e,t,r){return 0===t?r:t%2===1?m(e,t-1,r*e):m(e*e,t/2,r)},v=function(e){var t=0,r=e;while(r>=4096)t+=12,r/=4096;while(r>=2)t+=1,r/=2;return t},g=function(e,t,r){var n=-1,a=r;while(++n<6)a+=t*e[n],e[n]=a%1e7,a=p(a/1e7)},b=function(e,t){var r=6,n=0;while(--r>=0)n+=e[r],e[r]=p(n/t),n=n%t*1e7},y=function(e){var t=6,r="";while(--t>=0)if(""!==r||0===t||0!==e[t]){var n=l(e[t]);r=""===r?n:r+d("0",7-n.length)+n}return r},k=c((function(){return"0.000"!==f(8e-5,3)||"1"!==f(.9,0)||"1.25"!==f(1.255,2)||"1000000000000000128"!==f(0xde0b6b3a7640080,0)}))||!c((function(){f({})}));n({target:"Number",proto:!0,forced:k},{toFixed:function(e){var t,r,n,a,u=i(this),c=o(e),p=[0,0,0,0,0,0],f="",k="0";if(c<0||c>20)throw new s("Incorrect fraction digits");if(u!==u)return"NaN";if(u<=-1e21||u>=1e21)return l(u);if(u<0&&(f="-",u=-u),u>1e-21)if(t=v(u*m(2,69,1))-69,r=t<0?u*m(2,-t,1):u/m(2,t,1),r*=4503599627370496,t=52-t,t>0){g(p,0,r),n=c;while(n>=7)g(p,1e7,0),n-=7;g(p,m(10,n,1),0),n=t-1;while(n>=23)b(p,1<<23),n-=23;b(p,1<<n),g(p,1,1),b(p,2),k=y(p)}else g(p,0,r),g(p,1<<-t,0),k=y(p)+d("0",c);return c>0?(a=k.length,k=f+(a<=c?"0."+d("0",c-a)+k:h(k,0,a-c)+"."+h(k,a-c))):k=f+k,k}})},cb20:function(e,t,r){},e3b7:function(e,t,r){"use strict";r.d(t,"t",(function(){return i})),r.d(t,"s",(function(){return u})),r.d(t,"k",(function(){return c})),r.d(t,"u",(function(){return s})),r.d(t,"n",(function(){return l})),r.d(t,"p",(function(){return p})),r.d(t,"d",(function(){return d})),r.d(t,"a",(function(){return h})),r.d(t,"f",(function(){return f})),r.d(t,"x",(function(){return m})),r.d(t,"w",(function(){return v})),r.d(t,"v",(function(){return g})),r.d(t,"r",(function(){return b})),r.d(t,"A",(function(){return y})),r.d(t,"l",(function(){return k})),r.d(t,"m",(function(){return w})),r.d(t,"e",(function(){return S})),r.d(t,"z",(function(){return x})),r.d(t,"g",(function(){return C})),r.d(t,"j",(function(){return O})),r.d(t,"o",(function(){return I})),r.d(t,"i",(function(){return q})),r.d(t,"h",(function(){return j})),r.d(t,"y",(function(){return N})),r.d(t,"q",(function(){return D})),r.d(t,"c",(function(){return _})),r.d(t,"b",(function(){return z}));var n=r("66df"),a="pms",o="cms",i=function(e){return n["a"].request({url:o+"/channel/distributors/detail",data:e,method:"post"})},u=function(e){return n["a"].request({url:o+"/channel/distributors/info",params:e,method:"get"})},c=function(e){return n["a"].request({url:o+"/channel/distributors/delete",data:e,method:"delete"})},s=function(e){return n["a"].request({url:o+"/channel/distributors/record",data:e,method:"post"})},l=function(e){return n["a"].request({url:o+"/channel/distributors/record/export/"+e.corpId,method:"get",responseType:"blob"})},p=function(e){return n["a"].request({url:o+"/channel/distributors/remunerate/export",params:e,method:"post",responseType:"blob"})},d=function(e){return n["a"].request({url:o+"/channel/newChannel",data:e,method:"post"})},h=function(e){return n["a"].request({url:o+"/channel/updateChannel",data:e,method:"put"})},f=function(e){return n["a"].request({url:o+"/channel/approvalChannel",params:e,method:"put"})},m=function(e){return n["a"].request({url:a+"/packageGroup/queryPackageGroupRelation",params:e,method:"get"})},v=function(e){return n["a"].request({url:a+"/packageGroup/queryPackageGroupDetail",params:e,method:"get"})},g=function(e){return n["a"].request({url:a+"/packageGroup/purchasePart",params:e,method:"get"})},b=function(e){return n["a"].request({url:a+"/packageGroup/queryPackageList",params:e,method:"get"})},y=function(e){return n["a"].request({url:a+"/update",params:e,method:"put"})},k=function(e){return n["a"].request({url:a+"/packageGroup/deleteBatchPackageGroup",data:e,method:"delete"})},w=function(e){return n["a"].request({url:a+"/packageGroup/deletePackageGroup",params:e,method:"delete"})},S=function(e){return n["a"].request({url:a+"/packageGroup/newPackageGroup",data:e,method:"post"})},x=function(e){return n["a"].request({url:a+"/packageGroup/updatePackageGroup",data:e,method:"put"})},C=function(e){return n["a"].request({url:a+"/packageGroup/approvalPackageGroup",params:e,method:"put"})},O=function(e){return n["a"].request({url:a+"/packageGroup/create/byFile",data:e,method:"post",contentType:"multipart/form-data"})},I=function(e){return n["a"].request({url:a+"/packageGroup/packageGroupDetailExport",params:e,method:"get",responseType:"blob"})},q=function(e){return n["a"].request({url:o+"/channel/distributors/channelBill",data:e,method:"post"})},j=function(e){return n["a"].request({url:o+"/channel/distributors/channelBill/export",data:e,method:"post"})},N=function(e){return n["a"].request({url:o+"/channel/getCorpFlowDetail",params:e,method:"get"})},D=function(e){return n["a"].request({url:o+"/channel/corpFlowDetailExport",params:e,method:"get"})},_=function(e){return n["a"].request({url:o+"/channel/distributors/card/suspend",params:e,method:"get"})},z=function(e){return n["a"].request({url:o+"/channel/distributors/card/recover",params:e,method:"get"})}}}]);