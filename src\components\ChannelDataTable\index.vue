<template>
  <div class="data-table-container">
    <!-- 表格 -->
    <el-table 
      :data="data" 
      v-loading="loading" 
      :border="border"
      style="width: 100%"
    >
      <el-table-column
        v-for="column in columns"
        :key="column.prop || column.label"
        :prop="column.prop"
        :label="column.label"
        :min-width="column.minWidth"
        :align="column.align"
        :fixed="column.fixed"
        :type="column.type"
      >
        <template v-if="column.slot" #default="scope">
          <slot 
            :name="column.slot" 
            :row="scope.row" 
            :column="scope.column" 
            :index="scope.$index"
          />
        </template>
        
        <template v-else-if="column.formatter" #default="scope">
          {{ column.formatter(scope.row, scope.column, scope.row[column.prop || ''], scope.$index) }}
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div v-if="pagination" class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="pagination.pageSizes"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { DataTableProps, PaginationConfig, TableColumnConfig } from '@/types/channel'

// 组件选项
defineOptions({
  name: 'ChannelDataTable'
})

// Props 定义
interface Props extends DataTableProps {
  data: any[]
  loading?: boolean
  columns: TableColumnConfig[]
  pagination?: PaginationConfig
  border?: boolean
}

withDefaults(defineProps<Props>(), {
  loading: false,
  border: true
})

// Emits 定义
const emit = defineEmits<{
  'page-change': [page: number]
  'size-change': [size: number]
}>()

// 方法
const handlePageChange = (page: number) => {
  emit('page-change', page)
}

const handleSizeChange = (size: number) => {
  emit('size-change', size)
}
</script>

<style scoped>
.data-table-container {
  width: 100%;
}

.pagination-container {
  margin-top: var(--el-space-lg);
  text-align: right;
}

/* 确保表格在加载时不会闪烁 */
:deep(.el-table) {
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pagination-container {
    text-align: center;
  }
  
  :deep(.el-pagination) {
    justify-content: center;
  }
}
</style>
