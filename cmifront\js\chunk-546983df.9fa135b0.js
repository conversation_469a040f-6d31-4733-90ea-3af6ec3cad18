(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-546983df"],{"129f":function(t,e,a){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},"5d26":function(t,e,a){},"841c":function(t,e,a){"use strict";var i=a("c65b"),n=a("d784"),r=a("825a"),s=a("7234"),o=a("1d80"),l=a("129f"),c=a("577e"),u=a("dc4a"),d=a("14c3");n("search",(function(t,e,a){return[function(e){var a=o(this),n=s(e)?void 0:u(e,t);return n?i(n,e,a):new RegExp(e)[t](c(a))},function(t){var i=r(this),n=c(t),s=a(e,i,n);if(s.done)return s.value;var o=i.lastIndex;l(o,0)||(i.lastIndex=0);var u=d(i,n);return l(i.lastIndex,o)||(i.lastIndex=o),null===u?-1:u.index}]}))},b344:function(t,e,a){"use strict";a.r(e);a("ac1f"),a("841c"),a("498a");var i=function(){var t=this,e=t._self._c;return e("Card",[e("div",{staticClass:"search_head_i"},[e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("申请账号:")]),e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入申请账号",clearable:""},model:{value:t.accountNumber,callback:function(e){t.accountNumber="string"===typeof e?e.trim():e},expression:"accountNumber "}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("申请时间:")]),e("DatePicker",{staticStyle:{width:"200px",margin:"0 10px 0 0"},attrs:{format:"yyyy-MM-dd",editable:!1,type:"daterange",placeholder:"请选择申请时间",clearable:""},on:{"on-change":t.getTime}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("审批结果:")]),e("Select",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"选择审批结果",filterable:""},model:{value:t.approveResultCode,callback:function(e){t.approveResultCode=e},expression:"approveResultCode"}},[e("Option",{attrs:{value:"1"}},[t._v("待审批")]),e("Option",{attrs:{value:"2"}},[t._v("通过")]),e("Option",{attrs:{value:"3"}},[t._v("不通过")])],1)],1),t._v("    \n\t\t"),e("div",{staticStyle:{padding:"0 5px"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.search()}}},[t._v("搜索")])],1)]),e("Table",{ref:"selection",staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:t.columns,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(a){var i=a.row;a.index;return["1"==i.authStatus?e("div",[e("Button",{directives:[{name:"has",rawName:"v-has",value:"pass",expression:"'pass'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",ghost:"",size:"small"},on:{click:function(e){return t.examine(i.id,2)}}},[t._v("通过")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"nopass",expression:"'nopass'"}],attrs:{type:"error",ghost:"",size:"small"},on:{click:function(e){return t.examine(i.id,3)}}},[t._v("不通过")])],1):t._e()]}}])}),e("div",{staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1)],1)},n=[],r=(a("d3b7"),a("25f0"),a("4d90"),a("66df")),s="/sys/api/user",o=function(t){return r["a"].request({url:s+"/privilege/getPage",data:t,method:"post"})},l=function(t){return r["a"].request({url:s+"/privilege/authPrivilegeReply",data:t,method:"post"})},c={data:function(){return{total:0,currentPage:1,page:0,accountNumber:"",startTime:"",endTime:"",approveResultCode:"",loading:!1,searchloading:!1,data:[],columns:[{title:"申请账号",key:"userName",minWidth:120,align:"center",tooltip:!0},{title:"申请时间",key:"replyTime",minWidth:150,align:"center",tooltip:!0,render:function(t,e){var a=e.row,i="";if(a.replyTime){var n=new Date(a.replyTime);i=n.getFullYear()+"年"+(n.getMonth()+1)+"月"+n.getDate()+"日 "+n.getHours().toString().padStart(2,"0")+":"+n.getMinutes().toString().padStart(2,"0")+":"+n.getSeconds().toString().padStart(2,"0")}else i="";return t("label",i)}},{title:"审批时间",key:"authTime",minWidth:150,align:"center",tooltip:!0,render:function(t,e){var a=e.row,i="";if(a.authTime){var n=new Date(a.authTime);i=n.getFullYear()+"年"+(n.getMonth()+1)+"月"+n.getDate()+"日 "+n.getHours().toString().padStart(2,"0")+":"+n.getMinutes().toString().padStart(2,"0")+":"+n.getSeconds().toString().padStart(2,"0")}else i="";return t("label",i)}},{title:"审批账号",key:"approver",minWidth:120,align:"center",tooltip:!0},{title:"申请页面",key:"pageNum",minWidth:120,align:"center",tooltip:!0,render:function(t,e){var a=e.row,i="1"==a.pageNum?"个人订单管理":"2"==a.pageNum?"认证信息":"";return t("label",i)}},{title:"申请原因",key:"replyReason",minWidth:120,align:"center",tooltip:!0},{title:"浏览时间",key:"availableTime",minWidth:120,align:"center",tooltip:!0,render:function(t,e){var a=e.row,i=a.availableTime+"小时";return t("label",i)}},{title:"审批结果",key:"authStatus",align:"center",minWidth:120,render:function(t,e){var a=e.row,i="1"==a.authStatus?"#2b85e4":"2"==a.authStatus?"#19be6b":"3"==a.authStatus?"#ff0000":"",n="1"==a.authStatus?"待审核":"2"==a.authStatus?"通过":"3"==a.authStatus?"不通过":"";return t("label",{style:{color:i}},n)}},{title:"审核操作",slot:"action",minWidth:180,align:"center",fixed:"right"}]}},mounted:function(){this.goPageFirst(1)},methods:{goPageFirst:function(t){var e=this;this.loading=!0;var a=this;o({pageNum:t,pageSize:10,username:this.accountNumber,startTime:this.startTime?this.startTime+" 00:00:00":"",endTime:this.endTime?this.endTime+" 23:59:59":"",authStatus:this.approveResultCode}).then((function(i){"0000"==i.code&&(a.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t,e.data=i.data.records,e.total=i.data.total)})).catch((function(t){console.error(t)})).finally((function(){a.loading=!1,e.searchloading=!1}))},search:function(){this.searchloading=!0,this.goPageFirst(1)},goPage:function(t){this.goPageFirst(t)},getTime:function(t,e){this.startTime=t[0],this.endTime=t[1]},examine:function(t,e){var a=this;this.$Modal.confirm({title:"2"==e?"确认执行审核通过？":"确认执行审核不通过？",onOk:function(){l({id:t,authStatus:e}).then((function(t){if(!t||"0000"!=t.code)throw t;a.$Notice.success({title:"操作提示",desc:"操作成功"}),a.goPageFirst(a.page)})).catch((function(t){a.goPageFirst(a.page)}))}})}}},u=c,d=(a("f321"),a("2877")),h=Object(d["a"])(u,i,n,!1,null,"38821ab8",null);e["default"]=h.exports},f321:function(t,e,a){"use strict";a("5d26")}}]);