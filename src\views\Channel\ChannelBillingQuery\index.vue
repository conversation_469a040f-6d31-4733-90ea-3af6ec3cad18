<template>
  <!-- 渠道商账单查询 -->
  <ContentWrap>
    <el-card>
      <div class="search-container">
        <el-form :model="searchForm" inline ref="searchFormRef" :rules="searchRules">
          <el-form-item label="开始月份：" prop="beginMonth">
            <el-date-picker
              v-model="searchForm.beginMonth"
              type="month"
              format="YYYY-MM"
              value-format="YYYY-MM"
              placeholder="请选择开始月份"
              style="width: 200px"
              @change="handleStartChange"
            />
          </el-form-item>
          <el-form-item label="结束月份：" prop="endMonth">
            <el-date-picker
              v-model="searchForm.endMonth"
              type="month"
              format="YYYY-MM"
              value-format="YYYY-MM"
              placeholder="请选择结束月份"
              style="width: 200px"
              @change="handleEndChange"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="hasPermission('search')"
              type="primary"
              :loading="searchLoading"
              @click="handleSearch"
            >
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button
              v-if="hasPermission('bill_search')"
              type="info"
              :loading="flowLoading"
              :disabled="cooperationMode === '1'"
              @click="handleUsageBilling"
            >
              <Icon icon="ep:view" class="mr-5px" />
              数据使用量
            </el-button>
            <el-button
              v-if="hasPermission('imsi_search')"
              type="success"
              :loading="imsiLoading"
              @click="handleImsiView"
            >
              <Icon icon="ep:magic-stick" class="mr-5px" />
              IMSI费用
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <div style="margin-top: 20px">
        <el-table :data="tableData" v-loading="loading" border>
          <el-table-column prop="billMonth" label="账单月份" min-width="120" align="center" />
          <el-table-column prop="channelType" label="渠道类型" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getChannelTypeTag(row?.channelType)" v-if="row">
                {{ getChannelTypeText(row?.channelType) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="accountingType" label="核算类型" min-width="100" align="center">
            <template #default="scope">
              <el-tag :type="getAccountingTypeTag(scope.row?.accountingType)" v-if="scope.row">
                {{ getAccountingTypeText(scope.row?.accountingType) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="totalAmount" label="总金额" min-width="120" align="right">
            <template #default="scope">
              {{ scope.row ? (scope.row.totalAmount || 0).toFixed(2) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="realIncome" label="应缴金额" min-width="120" align="right">
            <template #default="scope">
              {{ scope.row ? (scope.row.realIncome || 0).toFixed(2) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="chargeStatus" label="缴费状态" min-width="100" align="center">
            <template #default="scope">
              <el-tag :type="getChargeStatusTag(scope.row?.chargeStatus)" v-if="scope.row">
                {{ getChargeStatusText(scope.row?.chargeStatus) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="160" />
          <el-table-column label="操作" min-width="400" align="center" fixed="right">
            <template #default="scope">
              <div v-if="scope.row" class="action-buttons">
                <el-button
                  v-if="hasPermission('info')"
                  type="primary"
                  size="small"
                  @click="handleShowInfo(scope.row)"
                >
                  详情
                </el-button>
                <el-button
                  v-if="hasPermission('bill_export')"
                  type="info"
                  size="small"
                  @click="handleExportBillFile(scope.row)"
                >
                  账单文件下载
                </el-button>
                <el-button
                  v-if="hasPermission('invoice')"
                  type="success"
                  size="small"
                  :disabled="!scope.row.invoicePath"
                  @click="handleExportInvoice(scope.row)"
                >
                  发票下载
                </el-button>
                <el-button
                  v-if="hasPermission('onlinePayment')"
                  type="primary"
                  size="small"
                  :disabled="isPaymentDisabled(scope.row, 'online')"
                  @click="handlePaymentMethods(scope.row)"
                >
                  线上支付
                </el-button>
                <el-button
                  v-if="hasPermission('payment')"
                  type="info"
                  size="small"
                  :disabled="isPaymentDisabled(scope.row, 'offline')"
                  @click="handleBillsDelivery(scope.row, '1')"
                >
                  线下支付
                </el-button>
                <el-button
                  v-if="hasPermission('revoke')"
                  type="danger"
                  size="small"
                  :disabled="isRevokeDisabled(scope.row)"
                  @click="handleRevoke(scope.row)"
                >
                  撤销
                </el-button>
                <el-button
                  v-if="hasPermission('rePayment')"
                  type="warning"
                  size="small"
                  :disabled="isReUploadDisabled(scope.row)"
                  @click="handleBillsDelivery(scope.row, '2')"
                >
                  重新上传
                </el-button>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

// 权限检查函数
const hasPermission = (permission: string): boolean => {
  console.log(`🔍 [渠道账单查询权限检查] ${permission}: 允许访问`)
  return true
}

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const flowLoading = ref(false)
const imsiLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const cooperationMode = ref('1') // 合作模式

const searchFormRef = ref()
const searchForm = reactive({
  beginMonth: '',
  endMonth: ''
})

const searchRules = {
  beginMonth: [{ required: true, message: '请选择开始月份', trigger: 'change' }],
  endMonth: [{ required: true, message: '请选择结束月份', trigger: 'change' }]
}

const tableData = ref<any[]>([])

// 方法
const getChannelTypeTag = (type: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '1': 'primary',
    '2': 'success'
  }
  return typeMap[type] || 'info'
}

const getChannelTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    '1': '代销',
    '2': '直销'
  }
  return typeMap[type] || '未知'
}

const getAccountingTypeTag = (
  type: string
): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '0': 'primary',
    '1': 'success'
  }
  return typeMap[type] || 'info'
}

const getAccountingTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    '0': '预存',
    '1': '押金'
  }
  return typeMap[type] || '未知'
}

const getChargeStatusTag = (
  status: string
): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '0': 'warning',
    '1': 'primary',
    '2': 'success',
    '3': 'danger',
    '4': 'info',
    '5': 'success'
  }
  return statusMap[status] || 'info'
}

const getChargeStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '0': '未缴费',
    '1': '已提交',
    '2': '已缴费',
    '3': '已拒绝',
    '4': '已撤销',
    '5': '已完成'
  }
  return statusMap[status] || '未知'
}

// 判断支付按钮是否禁用
const isPaymentDisabled = (row: any, type: 'online' | 'offline') => {
  if (!row) return true

  if (type === 'online') {
    return (
      ['1', '2', '3', '5'].includes(row.chargeStatus) ||
      row.channelType === '2' ||
      !['0', '1'].includes(row.accountingType) ||
      row.realIncome === 0
    )
  } else {
    return !['0', '4'].includes(row.chargeStatus) || row.channelType === '2' || row.realIncome === 0
  }
}

const isRevokeDisabled = (row: any) => {
  if (!row) return true
  return row.chargeStatus !== '1' || row.channelType === '2' || row.realIncome === 0
}

const isReUploadDisabled = (row: any) => {
  if (!row) return true
  return !['3'].includes(row.chargeStatus) || row.channelType === '2' || row.realIncome === 0
}

const handleStartChange = (value: string) => {
  console.log('开始月份变更:', value)
}

const handleEndChange = (value: string) => {
  console.log('结束月份变更:', value)
}

const handleSearch = async () => {
  try {
    await searchFormRef.value?.validate()
    currentPage.value = 1
    getTableData()
  } catch (error) {
    console.log('表单验证失败')
  }
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getTableData()
}

const handleUsageBilling = () => {
  ElMessage.info('数据使用量功能开发中...')
}

const handleImsiView = () => {
  ElMessage.info('IMSI费用功能开发中...')
}

const handleShowInfo = (row: any) => {
  ElMessage.info(`查看详情: ${row.billMonth}`)
}

const handleExportBillFile = (row: any) => {
  ElMessage.info(`导出账单文件: ${row.billMonth}`)
}

const handleExportInvoice = (row: any) => {
  ElMessage.info(`导出发票: ${row.billMonth}`)
}

const handlePaymentMethods = (row: any) => {
  ElMessage.info(`线上支付: ${row.billMonth}`)
}

const handleBillsDelivery = (row: any, type: string) => {
  const action = type === '1' ? '线下支付' : '重新上传'
  ElMessage.info(`${action}: ${row.billMonth}`)
}

const handleRevoke = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要撤销 ${row.billMonth} 的账单吗？`, '确认撤销', {
      type: 'warning'
    })

    ElMessage.success('撤销成功')
    getTableData()
  } catch (error) {
    // 用户取消操作
  }
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        billMonth: '2024-01',
        channelType: '1',
        accountingType: '0',
        totalAmount: 10000.0,
        realIncome: 8500.0,
        chargeStatus: '0',
        invoicePath: '/path/to/invoice.pdf',
        createTime: '2024-01-01 10:00:00'
      },
      {
        id: 2,
        billMonth: '2024-02',
        channelType: '2',
        accountingType: '1',
        totalAmount: 15000.0,
        realIncome: 12000.0,
        chargeStatus: '2',
        invoicePath: null,
        createTime: '2024-02-01 10:00:00'
      }
    ]
    total.value = 2
  } catch (error) {
    ElMessage.error('获取账单数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.search-container {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: center;
}

.action-buttons .el-button {
  margin: 2px;
}
</style>
