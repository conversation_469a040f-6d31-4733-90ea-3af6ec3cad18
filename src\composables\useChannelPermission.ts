/**
 * Channel 模块权限管理 Composable
 * 提供统一的权限检查功能
 */

import { hasPermi } from '@/components/Permission'
import type { PermissionChecker } from '@/types/channel'

/**
 * 使用渠道权限管理
 * @param module 模块名称，如 'address', 'package', 'order' 等
 * @returns 权限检查函数和常用权限检查方法
 */
export function useChannelPermission(module: string) {
  /**
   * 检查指定权限
   * @param permission 权限名称
   * @returns 是否有权限
   */
  const hasPermission: PermissionChecker = (permission: string): boolean => {
    return hasPermi(`channel:${module}:${permission}`)
  }

  /**
   * 检查查看权限
   */
  const canView = (): boolean => hasPermission('view')

  /**
   * 检查搜索权限
   */
  const canSearch = (): boolean => hasPermission('search')

  /**
   * 检查新增权限
   */
  const canAdd = (): boolean => hasPermission('add')

  /**
   * 检查编辑权限
   */
  const canEdit = (): boolean => hasPermission('edit')

  /**
   * 检查删除权限
   */
  const canDelete = (): boolean => hasPermission('delete')

  /**
   * 检查导出权限
   */
  const canExport = (): boolean => hasPermission('export')

  /**
   * 检查导入权限
   */
  const canImport = (): boolean => hasPermission('import')

  /**
   * 批量检查权限
   * @param permissions 权限列表
   * @returns 权限检查结果对象
   */
  const checkPermissions = (permissions: string[]) => {
    const result: Record<string, boolean> = {}
    permissions.forEach(permission => {
      result[permission] = hasPermission(permission)
    })
    return result
  }

  return {
    hasPermission,
    canView,
    canSearch,
    canAdd,
    canEdit,
    canDelete,
    canExport,
    canImport,
    checkPermissions
  }
}

/**
 * 地址管理权限
 */
export function useAddressPermission() {
  const { hasPermission, canView, canSearch, canAdd, canEdit, canDelete } = useChannelPermission('address')
  
  /**
   * 检查设置默认地址权限
   */
  const canSetDefault = (): boolean => hasPermission('setDefault')

  return {
    hasPermission,
    canView,
    canSearch,
    canAdd,
    canEdit,
    canDelete,
    canSetDefault
  }
}

/**
 * 套餐管理权限
 */
export function usePackagePermission() {
  const { hasPermission, canView, canSearch, canAdd, canEdit, canDelete } = useChannelPermission('package')
  
  /**
   * 检查上架权限
   */
  const canPublish = (): boolean => hasPermission('publish')

  /**
   * 检查下架权限
   */
  const canUnpublish = (): boolean => hasPermission('unpublish')

  return {
    hasPermission,
    canView,
    canSearch,
    canAdd,
    canEdit,
    canDelete,
    canPublish,
    canUnpublish
  }
}

/**
 * 订单管理权限
 */
export function useOrderPermission() {
  const { hasPermission, canView, canSearch, canExport } = useChannelPermission('order')
  
  /**
   * 检查退订权限
   */
  const canUnsubscribe = (): boolean => hasPermission('unsubscribe')

  /**
   * 检查查看账单权限
   */
  const canViewBill = (): boolean => hasPermission('viewBill')

  /**
   * 检查查看流量详情权限
   */
  const canViewTrafficDetails = (): boolean => hasPermission('trafficDetails')

  return {
    hasPermission,
    canView,
    canSearch,
    canExport,
    canUnsubscribe,
    canViewBill,
    canViewTrafficDetails
  }
}

/**
 * 服务支持权限
 */
export function useSupportPermission() {
  const { hasPermission, canView, canSearch, canAdd, canEdit } = useChannelPermission('support')
  
  /**
   * 检查创建工单权限
   */
  const canCreate = (): boolean => hasPermission('create')

  /**
   * 检查关闭工单权限
   */
  const canClose = (): boolean => hasPermission('close')

  return {
    hasPermission,
    canView,
    canSearch,
    canAdd,
    canEdit,
    canCreate,
    canClose
  }
}

/**
 * 充值管理权限
 */
export function useDepositPermission() {
  const { hasPermission, canView } = useChannelPermission('deposit')
  
  /**
   * 检查充值权限
   */
  const canRecharge = (): boolean => hasPermission('recharge')

  /**
   * 检查查看营销账户详情权限
   */
  const canViewMarketingAccount = (): boolean => hasPermission('marketingAccountDetails')

  /**
   * 检查查看套餐详情权限
   */
  const canViewPackageDetails = (): boolean => hasPermission('Packagedetails')

  /**
   * 检查查看流量详情权限
   */
  const canViewStreamDetails = (): boolean => hasPermission('streamdetails')

  return {
    hasPermission,
    canView,
    canRecharge,
    canViewMarketingAccount,
    canViewPackageDetails,
    canViewStreamDetails
  }
}
