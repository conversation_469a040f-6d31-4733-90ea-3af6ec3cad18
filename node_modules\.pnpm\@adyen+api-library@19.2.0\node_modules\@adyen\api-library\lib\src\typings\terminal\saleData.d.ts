/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SaleTerminalData } from './saleTerminalData';
import { SaleToAcquirerData } from './saleToAcquirerData';
import { SaleToIssuerData } from './saleToIssuerData';
import { SponsoredMerchant } from './sponsoredMerchant';
import { TransactionIdentification } from './transactionIdentification';
export declare class SaleData {
    'CustomerOrderID'?: string;
    'CustomerOrderReq'?: Array<SaleData.CustomerOrderReqEnum>;
    'OperatorID'?: string;
    'OperatorLanguage'?: string;
    'SaleReferenceID'?: string;
    'SaleTerminalData'?: SaleTerminalData;
    'SaleToAcquirerData'?: SaleToAcquirerData | string;
    'SaleToIssuerData'?: SaleToIssuerData;
    'SaleToPOIData'?: string;
    'SaleTransactionID': TransactionIdentification;
    'ShiftNumber'?: string;
    'SponsoredMerchant'?: Array<SponsoredMerchant>;
    'TokenRequestedType'?: SaleData.TokenRequestedTypeEnum;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace SaleData {
    enum CustomerOrderReqEnum {
        Both,
        Closed,
        Open
    }
    enum TokenRequestedTypeEnum {
        Customer,
        Transaction
    }
}
