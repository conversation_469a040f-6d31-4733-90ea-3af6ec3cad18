(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-55d74b39"],{"129f":function(t,e,a){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},"6e08":function(t,e,a){"use strict";a.d(e,"c",(function(){return s})),a.d(e,"b",(function(){return o})),a.d(e,"a",(function(){return n})),a.d(e,"d",(function(){return l}));var c=a("66df"),r="/pms/api/v1/upccTemplate",s=function(t){return c["a"].request({url:r+"/getUpccTemplate",data:t,method:"post"})},o=function(t){return c["a"].request({url:r+"/delUpccTemplate",data:t,method:"delete"})},n=function(t){return c["a"].request({url:r+"/newUpccTemplate",data:t,method:"post"})},l=function(t){return c["a"].request({url:r+"/updateUpccTemplate",data:t,method:"PUT"})}},"841c":function(t,e,a){"use strict";var c=a("c65b"),r=a("d784"),s=a("825a"),o=a("7234"),n=a("1d80"),l=a("129f"),i=a("577e"),p=a("dc4a"),u=a("14c3");r("search",(function(t,e,a){return[function(e){var a=n(this),r=o(e)?void 0:p(e,t);return r?c(r,e,a):new RegExp(e)[t](i(a))},function(t){var c=s(this),r=i(t),o=a(e,c,r);if(o.done)return o.value;var n=c.lastIndex;l(n,0)||(c.lastIndex=0);var p=u(c,r);return l(c.lastIndex,n)||(c.lastIndex=n),null===p?-1:p.index}]}))},d63f:function(t,e,a){"use strict";a.r(e);a("ac1f"),a("841c");var c=function(){var t=this,e=t._self._c;return e("Card",[e("div",{staticClass:"search_head_i"},[e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("模板ID:")]),e("Input",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请输入模板ID"},model:{value:t.templateId,callback:function(e){t.templateId=e},expression:"templateId"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("模板名称:")]),e("Input",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"清输入模板名称"},model:{value:t.templateName,callback:function(e){t.templateName=e},expression:"templateName"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("模板描述:")]),e("Poptip",{attrs:{trigger:"focus"},scopedSlots:t._u([{key:"content",fn:function(){return[e("div",[t._v(t._s(t.formatTemplateDesc))])]},proxy:!0}])},[e("Input",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"请输入模板描述"},model:{value:t.templateDesc,callback:function(e){t.templateDesc=e},expression:"templateDesc"}})],1)],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("是否支持热点:")]),e("Select",{staticStyle:{width:"200px"},attrs:{clearable:!0},model:{value:t.supportHotspot,callback:function(e){t.supportHotspot=e},expression:"supportHotspot"}},[e("Option",{attrs:{value:"1"}},[t._v("是")]),e("Option",{attrs:{value:"2"}},[t._v("否")])],1)],1),e("div",{staticClass:"search_box",staticStyle:{width:"120px","padding-left":"20px"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.search()}}},[t._v("搜索")]),t._v("\n\t\t\t    "),e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{"margin-left":"10px"},attrs:{type:"info",icon:"md-add",loading:t.addloading},on:{click:function(e){return t.addUpcc()}}},[t._v("新增")])],1)]),e("Table",{ref:"selection",staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:t.columns,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(a){var c=a.row;a.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",ghost:""},on:{click:function(e){return t.updateUpcc(c)}}},[t._v("编辑")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"error",ghost:""},on:{click:function(e){return t.deleteUpcc(c)}}},[t._v("删除")])]}}])}),e("div",{staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Modal",{attrs:{title:t.title,"mask-closable":!0,width:"500px"},on:{"on-cancel":t.cancelModal},model:{value:t.upccModal,callback:function(e){t.upccModal=e},expression:"upccModal"}},[e("Form",{ref:"upccform",staticStyle:{"font-size":"600"},attrs:{model:t.upccform,rules:t.rule,"label-width":120}},[e("FormItem",{attrs:{label:"模板名称",prop:"templateName"}},[e("Input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入模板名称",maxlength:"1000",clearable:""},model:{value:t.upccform.templateName,callback:function(e){t.$set(t.upccform,"templateName",e)},expression:"upccform.templateName"}})],1),e("FormItem",{attrs:{label:"模板描述",prop:"templateDesc"}},[e("Input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入模板描述",maxlength:"3000",type:"textarea",rows:5,clearable:""},model:{value:t.upccform.templateDesc,callback:function(e){t.$set(t.upccform,"templateDesc",e)},expression:"upccform.templateDesc"}})],1),e("FormItem",{attrs:{label:"签约模板ID",prop:"signId"}},[e("Input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入模板ID",clearable:""},model:{value:t.upccform.signId,callback:function(e){t.$set(t.upccform,"signId",e)},expression:"upccform.signId"}})],1),e("FormItem",{attrs:{label:"是否支持热点",prop:"supportHotspot"}},[e("Select",{staticStyle:{width:"300px"},attrs:{disabled:t.title==t.name2,clearable:""},model:{value:t.upccform.supportHotspot,callback:function(e){t.$set(t.upccform,"supportHotspot",e)},expression:"upccform.supportHotspot"}},[e("Option",{attrs:{value:"1"}},[t._v("是")]),e("Option",{attrs:{value:"2"}},[t._v("否")])],1)],1),e("FormItem",{attrs:{label:"模板速率",prop:"rate"}},[e("Input",{staticStyle:{width:"300px"},attrs:{placeholder:"请输入模板速率",maxlength:"4",clearable:""},model:{value:t.upccform.rate,callback:function(e){t.$set(t.upccform,"rate",e)},expression:"upccform.rate"}},[e("Select",{staticStyle:{width:"70px"},attrs:{slot:"append"},slot:"append",model:{value:t.upccform.unit,callback:function(e){t.$set(t.upccform,"unit",e)},expression:"upccform.unit"}},[e("Option",{attrs:{value:"1"}},[t._v("Kb/s")]),e("Option",{attrs:{value:"2"}},[t._v("Mb/s")])],1)],1)],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("返回")]),e("Button",{attrs:{type:"primary",loading:t.besureLoading},on:{click:t.besure}},[t._v("确定")])],1)],1)],1)},r=[],s=(a("fb6a"),a("d3b7"),a("5319"),a("6e08")),o={data:function(){return{total:0,currentPage:1,page:0,templateId:"",templateName:"",templateDesc:"",supportHotspot:"",title:"",name1:"新增UPCC模板",name2:"修改UPCC模板",upccform:{templateName:"",templateDesc:"",signId:"",supportHotspot:"",rate:"",unit:"2"},rule:{templateName:[{required:!0,message:"模板名称不可为空",trigger:"change"},{pattern:/^[^\s]+(\s+[^\s]+)*$/,trigger:"blur",message:"模板名称有空格"}],templateDesc:[{required:!0,message:"模板描述不可为空",trigger:"blur"}],signId:[{required:!0,message:"签约模板ID不可为空",trigger:"blur"},{pattern:/^[^\s]+(\s+[^\s]+)*$/,trigger:"blur",message:"签约模板ID有空格"}],supportHotspot:[{required:!0,message:"是否支持热点不可为空",trigger:"blur"}],rate:[{required:!0,message:"模板速率不可为空"},{pattern:/^[0-9]+$/,message:"请输入非负纯数字",trigger:"blur"}]},loading:!1,searchloading:!1,addloading:!1,besureLoading:!1,upccModal:!1,data:[],columns:[{title:"模板ID",key:"templateId",minWidth:120,align:"center",tooltip:!0},{title:"模板名称",key:"templateName",minWidth:220,align:"center",tooltip:!0},{title:"模板描述",key:"templateDesc",minWidth:220,align:"center",render:function(t,e){var a=e.row,c=""===a.templateDesc?"":a.templateDesc;return c.length>20?(c=c.substring(0,20)+"...",t("div",[t("Tooltip",{props:{placement:"bottom",transfer:!0},style:{cursor:"pointer"}},[c,t("label",{slot:"content",style:{whiteSpace:"normal",wordBreak:"break-all"}},a.templateDesc)])])):(c=c,t("label",c))}},{title:"是否支持热点",key:"supportHotspot",minWidth:110,align:"center",render:function(t,e){var a=e.row,c="1"==a.supportHotspot?"是":"2"==a.supportHotspot?"否":"";return t("label",c)}},{title:"签约模板ID",key:"signId",minWidth:150,align:"center",render:function(t,e){var a=e.row,c=a.signId,r=""===c||null===c?0:c.length;if(r>20){for(var s="",o=0;o<=a.signId.length;)s=s+c.slice(o,o+17)+",",o+=18;return c=c.substring(0,20)+"...",t("div",[t("Tooltip",{props:{placement:"bottom",transfer:!0},style:{cursor:"pointer"}},[c,t("label",{slot:"content",style:{whiteSpace:"normal"},domProps:{innerHTML:s.replace(/\,/g,"</br>")}})])])}return t("label",c)}},{title:"模板速率",key:"rate",minWidth:100,align:"center",render:function(t,e){var a=e.row,c="1"==a.unit?a.rate+"Kb/s":a.rate+"Mb/s";return t("label",c)}},{title:"操作",slot:"action",minWidth:180,align:"center",fixed:"right"}]}},mounted:function(){this.goPageFirst(1)},computed:{formatTemplateDesc:function(){return""===this.templateDesc?"请输入模板描述":this.templateDesc}},methods:{goPageFirst:function(t){var e=this;this.loading=!0;var a=this;Object(s["c"])({pageNum:t,pageSize:10,templateId:this.templateId,templateName:this.templateName,templateDesc:this.templateDesc,supportHotspot:this.supportHotspot}).then((function(c){"0000"==c.code&&(a.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t,e.data=c.data.records,e.total=c.data.total)})).catch((function(t){console.error(t)})).finally((function(){a.loading=!1,e.searchloading=!1}))},search:function(){this.searchloading=!0,this.goPageFirst(1)},goPage:function(t){this.goPageFirst(t)},addUpcc:function(){this.title=this.name1,this.upccModal=!0},updateUpcc:function(t){this.title=this.name2,this.upccModal=!0,this.upccform.templateName=t.templateName,this.upccform.templateDesc=t.templateDesc,this.upccform.signId=t.signId,this.upccform.supportHotspot=t.supportHotspot,this.upccform.rate=t.rate,this.upccform.unit=t.unit,this.templateIds=t.templateId},deleteUpcc:function(t){var e=this;this.$Modal.confirm({title:"确认删除？",onOk:function(){Object(s["b"])({templateId:t.templateId,signId:t.signId,templateName:t.templateName,templateDesc:t.templateDesc,supportHotspot:t.supportHotspot}).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.page=1,e.goPageFirst(1)})).catch((function(t){console.error(t)}))}})},besure:function(){var t=this;this.$refs["upccform"].validate((function(e){e&&(t.title==t.name1?(t.besureLoading=!0,Object(s["a"])({templateName:t.upccform.templateName,templateDesc:t.upccform.templateDesc,signId:t.upccform.signId,supportHotspot:t.upccform.supportHotspot,rate:t.upccform.rate,unit:t.upccform.unit}).then((function(e){if("0000"===e.code){e.data;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.besureLoading=!1,t.goPageFirst(1),t.upccModal=!1,t.cancelModal()}})).catch((function(e){t.besureLoading=!1,console.log(e)})).finally((function(){t.besureLoading=!1}))):(t.besureLoading=!0,Object(s["d"])({templateName:t.upccform.templateName,templateDesc:t.upccform.templateDesc,templateId:t.templateIds,signId:t.upccform.signId,supportHotspot:t.upccform.supportHotspot,rate:t.upccform.rate,unit:t.upccform.unit}).then((function(e){if("0000"===e.code){e.data;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.besureLoading=!1,t.goPageFirst(1),t.upccModal=!1,t.cancelModal()}})).catch((function(e){t.besureLoading=!1,console.log(e)})).finally((function(){t.besureLoading=!1}))))}))},cancelModal:function(){this.upccModal=!1,this.upccform.templateName="",this.upccform.templateDesc="",this.upccform.signId="",this.upccform.supportHotspot="",this.upccform.rate="",this.upccform.unit="2",this.$refs.upccform.resetFields()}}},n=o,l=(a("f16a"),a("2877")),i=Object(l["a"])(n,c,r,!1,null,"3d8c873e",null);e["default"]=i.exports},f16a:function(t,e,a){"use strict";a("f82c")},f82c:function(t,e,a){}}]);