<template>
  <!-- 线下支付页面 -->
  <ContentWrap>
    <!-- 加载状态 -->
    <div v-if="!isComponentMounted" class="loading-container">
      <ElSkeleton :rows="5" animated />
    </div>

    <!-- 主要内容 -->
    <ElCard v-else>
      <div class="search-header">
        <ElButton @click="goBack" type="default" style="margin-bottom: 20px">
          <Icon icon="ep:arrow-left" />
          返回
        </ElButton>
        <h3>线下支付充值</h3>
      </div>

      <!-- 支付表单 -->
      <ElForm
        ref="paymentFormRef"
        :model="paymentForm"
        :rules="paymentRules"
        label-width="120px"
        style="max-width: 600px; margin-top: 20px"
      >
        <ElFormItem label="充值金额" prop="amount">
          <ElInput
            v-model="paymentForm.amount"
            placeholder="请输入充值金额"
            type="number"
            min="1"
            max="999999"
          >
            <template #append>元</template>
          </ElInput>
        </ElFormItem>

        <ElFormItem label="支付方式" prop="paymentMethod">
          <ElRadioGroup v-model="paymentForm.paymentMethod">
            <ElRadio label="bank_transfer">银行转账</ElRadio>
            <ElRadio label="alipay">支付宝</ElRadio>
            <ElRadio label="wechat">微信支付</ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <ElFormItem label="付款凭证" prop="voucher">
          <ElUpload
            ref="uploadRef"
            :file-list="fileList"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :before-upload="beforeUpload"
            :auto-upload="false"
            accept="image/*,.pdf"
            list-type="picture-card"
          >
            <Icon icon="ep:plus" />
          </ElUpload>
          <div class="upload-tip"> 支持jpg、png、pdf格式，文件大小不超过5MB </div>
        </ElFormItem>

        <ElFormItem label="备注信息" prop="remark">
          <ElInput
            v-model="paymentForm.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入备注信息（可选）"
            maxlength="200"
            show-word-limit
          />
        </ElFormItem>

        <ElFormItem>
          <ElButton type="primary" @click="handleSubmit" :loading="submitting">
            提交充值申请
          </ElButton>
          <ElButton @click="handleReset">重置</ElButton>
        </ElFormItem>
      </ElForm>

      <!-- 支付说明 -->
      <ElCard style="margin-top: 30px">
        <template #header>
          <span>支付说明</span>
        </template>
        <div class="payment-info">
          <h4>银行转账信息：</h4>
          <p><strong>收款户名：</strong>中国移动通信集团有限公司</p>
          <p><strong>收款账号：</strong>1234567890123456789</p>
          <p><strong>开户银行：</strong>中国工商银行北京分行</p>

          <h4 style="margin-top: 20px">注意事项：</h4>
          <ul>
            <li>请在转账时备注您的企业ID：{{ corpId }}</li>
            <li>充值申请提交后，我们将在1-3个工作日内处理</li>
            <li>如有疑问，请联系客服：400-123-4567</li>
            <li>请保留好付款凭证，以便查询和核对</li>
          </ul>
        </div>
      </ElCard>
    </ElCard>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { useRouter, useRoute } from 'vue-router'
import { useComponentSafety } from '@/composables/useComponentSafety'
import { offlinePayment } from '@/api/cmi/channel'

const router = useRouter()
const route = useRoute()

// 使用组件安全性管理
const { isComponentMounted, safeNavigate, initComponentSafety } = useComponentSafety('线下支付')

// 响应式数据
const paymentFormRef = ref()
const uploadRef = ref()
const submitting = ref(false)
const fileList = ref([])
const corpId = ref(route.query.corpId || '')

// 支付表单
const paymentForm = reactive({
  amount: '',
  paymentMethod: 'bank_transfer',
  voucher: '',
  remark: ''
})

// 表单验证规则
const paymentRules = {
  amount: [
    { required: true, message: '请输入充值金额', trigger: 'blur' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (value && (isNaN(value) || Number(value) <= 0)) {
          callback(new Error('请输入有效的充值金额'))
        } else if (value && Number(value) > 999999) {
          callback(new Error('充值金额不能超过999999元'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  paymentMethod: [{ required: true, message: '请选择支付方式', trigger: 'change' }]
}

// 文件上传处理
const handleFileChange = (file: any, fileList: any) => {
  console.log('📎 [线下支付] 文件变更:', { file, fileList })
}

const handleFileRemove = (file: any, fileList: any) => {
  console.log('🗑️ [线下支付] 文件移除:', { file, fileList })
}

const beforeUpload = (file: any) => {
  const isValidType = ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)
  const isValidSize = file.size / 1024 / 1024 < 5

  if (!isValidType) {
    ElMessage.error('只支持jpg、png、pdf格式的文件')
    return false
  }
  if (!isValidSize) {
    ElMessage.error('文件大小不能超过5MB')
    return false
  }
  return true
}

// 提交充值申请
const handleSubmit = async () => {
  try {
    // 表单验证
    const valid = await paymentFormRef.value?.validate()
    if (!valid) return

    const result = await ElMessageBox.confirm(
      `确定要提交充值申请吗？充值金额：${paymentForm.amount}元`,
      '确认提交',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (result === 'confirm') {
      submitting.value = true
      console.log('💰 [线下支付] 开始提交充值申请:', paymentForm)

      const response = await offlinePayment({
        corpId: corpId.value,
        amount: Number(paymentForm.amount),
        paymentMethod: paymentForm.paymentMethod,
        remark: paymentForm.remark,
        voucher: paymentForm.voucher
      })

      if (response?.success) {
        ElMessage.success('充值申请提交成功，请等待审核')
        // 返回上一页
        goBack()
      } else {
        ElMessage.error(response?.message || '充值申请提交失败')
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('❌ [线下支付] 提交失败:', error)
      ElMessage.error('充值申请提交失败')
    }
  } finally {
    submitting.value = false
  }
}

// 重置表单
const handleReset = () => {
  paymentFormRef.value?.resetFields()
  fileList.value = []
}

// 返回上一页
const goBack = () => {
  safeNavigate(
    router,
    {
      path: '/newcmi/channel/deposit'
    },
    '返回失败'
  )
}

// 在组件挂载后初始化
initComponentSafety(async () => {
  console.log('🎉 [线下支付] 页面初始化完成')
})
</script>

<style scoped>
.loading-container {
  padding: 20px;
}

.search-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-header h3 {
  margin: 0;
  color: #409eff;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.payment-info h4 {
  color: #409eff;
  margin-bottom: 10px;
}

.payment-info p {
  margin: 5px 0;
  line-height: 1.6;
}

.payment-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.payment-info li {
  margin: 5px 0;
  line-height: 1.6;
}
</style>
