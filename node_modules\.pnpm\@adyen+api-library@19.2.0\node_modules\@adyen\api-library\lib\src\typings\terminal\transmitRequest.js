"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransmitRequest = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
class TransmitRequest {
    static getAttributeTypeMap() {
        return TransmitRequest.attributeTypeMap;
    }
}
exports.TransmitRequest = TransmitRequest;
TransmitRequest.discriminator = undefined;
TransmitRequest.attributeTypeMap = [
    {
        "name": "DestinationAddress",
        "baseName": "DestinationAddress",
        "type": "string"
    },
    {
        "name": "MaximumTransmitTime",
        "baseName": "MaximumTransmitTime",
        "type": "number"
    },
    {
        "name": "Message",
        "baseName": "Message",
        "type": "any"
    },
    {
        "name": "WaitResponseFlag",
        "baseName": "WaitResponseFlag",
        "type": "boolean"
    }
];
//# sourceMappingURL=transmitRequest.js.map