/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { AlgorithmIdentifier } from './algorithmIdentifier';
import { SignerIdentifier } from './signerIdentifier';
export declare class Signer {
    'DigestAlgorithm': AlgorithmIdentifier;
    'Signature': any | null;
    'SignatureAlgorithm': AlgorithmIdentifier;
    'SignerIdentifier': SignerIdentifier;
    'Version'?: Signer.VersionEnum;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace Signer {
    enum VersionEnum {
        V0,
        V1,
        V2,
        V3,
        V4,
        V5
    }
}
