"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SoundFormatType = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var SoundFormatType;
(function (SoundFormatType) {
    SoundFormatType[SoundFormatType["MessageRef"] = 'MessageRef'] = "MessageRef";
    SoundFormatType[SoundFormatType["SoundRef"] = 'SoundRef'] = "SoundRef";
    SoundFormatType[SoundFormatType["Text"] = 'Text'] = "Text";
})(SoundFormatType = exports.SoundFormatType || (exports.SoundFormatType = {}));
//# sourceMappingURL=soundFormatType.js.map