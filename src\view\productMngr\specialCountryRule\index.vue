<template>
  <div class="list-container">
    <Card>
      <div class="search-box">
        <Form ref="searchForm" :model="searchForm" inline>
          <FormItem prop="ruleNameTitle">
            <span class="search_box_label">规则名称</span>
          </FormItem>
          <FormItem prop="ruleName">
            <Input v-model="searchForm.groupName" placeholder="规则名称" clearable />
          </FormItem>
          <FormItem prop="ruleNameTitle">
            <span class="search_box_label">国家</span>
          </FormItem>
          <FormItem prop="country">
            <Select
              v-model="searchForm.mcc"
              filterable
              clearable
              placeholder="选择国家"
              style="width: 200px"
            >
              <!-- <Option v-for="item in countryList" :value="item.mcc" :key="item.value">{{ item.countryEn }}</Option> -->
              <Option :value="item.mcc" v-for="(item,index) in countryList" :key="index">{{item.countryEn}}</Option>
            </Select>
          </FormItem>
          <FormItem prop="ruleNameTitle">
            <span class="search_box_label">渠道商简称</span>
          </FormItem>
          <FormItem prop="isActive">
            <Select v-model="searchForm.corpId" clearable placeholder="渠道商" filterable style="width: 210px;">
					<Option :value="item.corpId" v-for="(item,index) in channelList" :key="index">{{item.corpName}}
					</Option>
				</Select>
          </FormItem>
          <FormItem>
            <Button type="primary" style="margin-right: 10px" @click="handleSearch">
              <Icon type="ios-search" />&nbsp;搜索
            </Button>
            <Button type="info" icon="md-add" style="margin-right: 10px" @click="handleAdd" v-has="'add'">
              新增
            </Button>
          </FormItem>
        </Form>
      </div>
      <Table :columns="columns" :data="flattenedData" :loading="loading" border :span-method="handleSpan">
        <template slot-scope="{ row }" slot="poolId">
          <div class="merged-cell">
            <div v-for="(item, index) in getGroupData(row.detailId)">
              {{item.poolId}}
            </div>
          </div>
        </template>
        <template slot-scope="{ row }" slot="action">
          <Button type="primary" size="small" style="margin-right: 5px" @click="handleEdit(row)" v-has="'update'">编辑</Button>
          <Button type="error" size="small" @click="delSpecial(row)" v-has="'delete'">删除</Button>
        </template>
      </Table>
      <div class="page-box" style="text-align: left;">
        <Page
          :total="count"
          :current="current"
          :page-size="size"
          show-total
          show-elevator
          @on-change="handlePageChange"
        />
      </div>
    </Card>
  </div>
</template>

<script>
import { getCorpList } from "@/api/product/package/batch";
import { opsearchAll } from '@/api/operators';
import { getPageList, handleDelete } from '@/api/product/specialCountryRule';
import { Table } from "iview";
import { map } from "jquery";
import { get } from "jquery";
export default {
  name: 'SpecialCountryRuleList',
  data() {
    return {
      searchForm: {
        groupName: '',
        mcc: '',
        corpId: '',
        current: 1,
        size: 10
      },
      columns: [
        {
          title: '规则名称',
          key: 'groupName',
          width: 200,
          render: (h, params) => {
            return h('div', [
              h('Tooltip', {
                  props: {
                    placement: 'bottom',
                    transfer: true,
                    maxWidth: 500,
                  },
                  style: {
                    cursor: 'pointer',
                  }
                },
                [
                  h('span', {
                    style: {
                      display: "block",
                    }
                  }, params.row.groupName.length >50 ? params.row.groupName.substring(0, 50)+'...': params.row.groupName),
									h('ul', {
												slot: 'content',
												style: {
													whiteSpace: 'normal',
													wordBreak: 'break-all' //超出隐藏
												},
											}, params.row.groupName)
                ]
              )
            ]
            )}
        },
        {
          title: '国家',
          key: 'countryEn',
          width: 150
        },
        {
          title: '卡池ID',
          key: 'poolId',
          minWidth: 250,
          render: (h, params) => {
            let idss = '';
            if (params.row.poolId !== null) {
              idss = params.row.poolId[0];
            }
            if (params.row.poolId !== null && params.row.poolId.length > 1) {
              return h("div",[
                h('Tooltip', {
											props: {
												placement: 'bottom',
												transfer: true,
											},
											style: {
												cursor: 'pointer',
											}
										},
                    [
											h('span', {
												style: {
													display: "block",
												}
											},params.row.poolId[0]),
											h('span', {}, params.row.poolId[1]),
											h('span', {}, "…"),
											h('ul', {
												slot: 'content',
												style: {
													whiteSpace: 'normal',
													wordBreak: 'break-all' //超出隐藏
												},
											}, this.flattenedData[params.index].poolId.map(
											item => {
												return h('div', item)}))
										]
                  )
                ]
              );
            }else{
              idss = idss;
              return h(
                "span",idss
              );
            }
            
          }
        },
        {
          title: '备注中文',
          key: 'remarkCn',
          width: 200,
          render: (h, params) => {
            return h('div', [
              h('Tooltip', {
                  props: {
                    placement: 'bottom',
                    transfer: true,
                    maxWidth: 500,
                  },
                  style: {
                    cursor: 'pointer',
                  }
                },
                [
                  h('span', {
                    style: {
                      display: "block",
                    }
                  }, params.row.remarkCn.length >50 ? params.row.remarkCn.substring(0, 50)+'...': params.row.remarkCn),
									h('ul', {
												slot: 'content',
												style: {
													whiteSpace: 'normal',
													wordBreak: 'break-all' //超出隐藏
												},
											}, params.row.remarkCn)
                ]
              )
            ]
            )}
        },
        {
          title: '备注英文',
          key: 'remarkEn',
          width: 200,
          render: (h, params) => {
            return h('div', [
              h('Tooltip', {
                  props: {
                    placement: 'bottom',
                    transfer: true,
                    maxWidth: 500,
                  },
                  style: {
                    cursor: 'pointer',
                  }
                },
                [
                  h('span', {
                    style: {
                      display: "block",
                    }
                  }, params.row.remarkEn.length >50 ? params.row.remarkEn.substring(0, 50)+'...': params.row.remarkEn),
									h('ul', {
												slot: 'content',
												style: {
													whiteSpace: 'normal',
													wordBreak: 'break-all' //超出隐藏
												},
											}, params.row.remarkEn)
                ]
              )
            ]
            )
          }
        },
        {
          title: '关联渠道商',
          key: 'bingdingChannel',
          width: 200,
          render: (h, params) => {
            const row = params.row;
							let text = ""
							if (row.corps != null) {
								text = this.getChannelNameByCorpId(row.corps[0])
							}							
							if (row.corps!=null&& row.corps.length > 1) {
								return h('div', [
									h('Tooltip', {
											props: {
												placement: 'bottom',
												transfer: true,
											},
											style: {
												cursor: 'pointer',
											}
										},
										[
											h('span', {
												style: {
													display: "block",
												}
											}, this.getChannelNameByCorpId(row.corps[0])),
											h('span', {}, this.getChannelNameByCorpId(row.corps[1])),
											h('span', {}, "…"),
											h('ul', {
												slot: 'content',
												style: {
													whiteSpace: 'normal',
													wordBreak: 'break-all' //超出隐藏
												},
											}, this.flattenedData[params.index].corps.map(
											item => {
												return h('div', this.getChannelNameByCorpId(item))}))
										]
									)
								])
							} else {
								text = text;
								return h('label', text)
							};
          }
        },
        {
          title: '是否生效',
          key: 'status',
          width: 100,
          render: (h, params) => {
            return h('Tag', {
              props: {
                color: params.row.status === '0' ? 'default' : 'success'
              }
            }, params.row.status === '0' ? '否' : '是')
          }
        },
        {
          title: '操作',
          slot: 'action',
          width: 150,
          fixed: 'right'
        }
      ],
      tableData: [
      ],
      countryList: [],
      channelList: [],
      loading: false,
      count: 0,
      current: 1,
      size: 10
    }
  },
  created() {
    this.getList()
    this.getLocalList()
    this.getCorpList()
  },
  methods: {
    async getList() {
      this.current = this.current
      try {
        const res = await getPageList({
          ...this.searchForm,
        })
        this.tableData = res.data
        this.count = parseInt(res.count,10)
        this.size = 10
      } catch (error) {
        this.$Message.error('获取特殊规则列表失败')
      }
      this.loading = false
    },
    getChannelNameByCorpId(corpId){
      const foundItem = this.channelList.find(item => item.corpId === corpId);
      return foundItem ? foundItem.corpName : '';
    },
		getLocalList() { 
				getPageList(
          { baseInfo: true, 
            current: -1 , 
            size: -1 }
        ).then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
            var countryList = [];
            this.countryList = this.getUniqueCountries(list).sort((str1, str2) => str1.countryEn.localeCompare(str2.countryEn));
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			}	,
      getUniqueCountries(list) {
        var countryList = [];
        var uniqueCountries = new Set();

        list.forEach(item => {
        if (!uniqueCountries.has(item.countryEn)) {
          uniqueCountries.add(item.countryEn);
          countryList.push({
          mcc: item.mcc,
          countryEn: item.countryEn,
          });
        }
      });

  return countryList;
},
    //获取渠道商
    async getCorpList() {
				getCorpList({
					"type": 1,
					"status": 1,
					"checkStatus": 2
				}).then(res => {
					if (res && res.code == '0000') {
						this.channelList = res.data;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
    handleSearch() {
      this.current = 1 
      this.searchForm.current = 1 
      this.getList()
    },
    handlePageChange(page) {
      this.current = page
      this.searchForm.current = page
      this.getList()
    },
    handleAdd() {
      this.$router.push('/specialCountryRuleAdd')
    },
    handleEdit(row) {
      this.$router.push({
        name: 'specialCountryRuleEdit',
        query: { ruleId: row.groupId }
      })
    },
    delSpecial(row) {
				this.$Modal.confirm({
					title: '确认删除？',
					onOk: () => {
						handleDelete({groupId:row.groupId}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: '操作提示',
									desc: '操作成功'
								})
								if (this.tableData.length == 1 && this.current > 1) {
									this.handlePageChange(this.current-1);
								} else {
									this.handlePageChange(this.current);
								}
							} else {
								throw res
							}
						}).catch((err) => {

						})
					}
				});
			},
    handleSpan({ row, column, rowIndex, columnIndex }){
      if (column.key === 'groupName' || column.key === 'countryEn'|| column.key === 'bingdingChannel'|| column.slot === 'action'|| column.key === 'status') {
        if(rowIndex > 0 && this.flattenedData[rowIndex].groupId === this.flattenedData[rowIndex - 1].groupId){
          return {
            rowspan: 0, colspan: 0
          }
        }
        else{
          const rowspan = this.flattenedData.slice(rowIndex).findIndex(s => s.groupId !== row.groupId)
          return {
            rowspan: rowspan === -1 ? this.flattenedData.length -rowIndex + 1 : rowspan,
            colspan: 1
          }
        }  
      }else if(column.key === 'poolId'|| column.key === 'remarkCn'|| column.key === 'remarkEn'){
        if(rowIndex > 0 && this.flattenedData[rowIndex].detailId === this.flattenedData[rowIndex - 1].detailId){
          return {
            rowspan: 0, colspan: 0
          }
        }else{
          const rowspan = this.flattenedData.slice(rowIndex).findIndex(s => s.detailId !== row.detailId)
          return {
            rowspan: rowspan === -1 ? this.flattenedData.length -rowIndex + 1 : rowspan,
            colspan: 1
          }
        }  
      }
      else
      {
        return {
          rowspan: 1, colspan: 1 
        }
      }
    },
  },
  computed: {
    flattenedData() {
      const result = [];
      this.tableData.forEach(group => {

        group.details.forEach(detail => {          
          
          if (detail.cardPools && detail.cardPools.length > 0) {
            const ids = [];
            detail.cardPools.forEach(a => {
              if(a.rate !== null && a.rate !== 100 && a.rate !== 0){
                ids.push((a.poolId === "default" ? "默认" : a.poolId)+"("+a.rate+"%)");
              }else{
                ids.push(a.poolId === "default" ? "默认" : a.poolId);
              }
            });
            detail.cardPools.forEach(pool => {
              result.push({
                groupId: group.groupId,
                groupName: group.groupName,
                countryEn: group.countryEn,
                mcc: group.mcc,
                remarkCn: detail.remarkCn,
                remarkEn: detail.remarkEn,
                poolId: ids,
                poolName: pool.poolName,
                corps: group.corps,
                detailId: detail.detailId,
                status: group.status,
              });
            });
          } else {
            result.push({
              groupId: group.groupId,
              groupName: group.groupName,
              countryEn: group.countryEn,
              mcc: group.mcc,
              remarkCn: detail.remarkCn,
              remarkEn: detail.remarkEn,
              poolId: null,
              poolName: null,
              corps: group.corps,
              detailId: detail.detailId,
              status: group.status
            });
          }
        });
      });
      return result;
    },
  }
  
}
</script>

<style lang="less" scoped>
.list-container {
  padding: 20px;
  
  .search-box {
    margin-bottom: 20px;
  }
  
  .operation-box {
    margin-bottom: 20px;
  }
  
  .page-box {
    margin-top: 20px;
    text-align: right;
  }
}
	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 110px;
	}
</style> 