/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { LoyaltyRequest } from './loyaltyRequest';
import { PaymentRequest } from './paymentRequest';
import { ReversalRequest } from './reversalRequest';
export declare class TransactionToPerform {
    'LoyaltyRequest'?: LoyaltyRequest;
    'PaymentRequest'?: PaymentRequest;
    'ReversalRequest'?: ReversalRequest;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
