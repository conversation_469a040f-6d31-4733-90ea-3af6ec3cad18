(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-21556441"],{"00b4":function(t,e,r){"use strict";r("ac1f");var i=r("23e7"),o=r("c65b"),n=r("1626"),s=r("825a"),a=r("577e"),l=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),p=/./.test;i({target:"RegExp",proto:!0,forced:!l},{test:function(t){var e=s(this),r=a(t),i=e.exec;if(!n(i))return o(p,e,r);var l=o(i,e,r);return null!==l&&(s(l),!0)}})},"0a0e":function(t,e,r){},"129f":function(t,e,r){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},"466d":function(t,e,r){"use strict";var i=r("c65b"),o=r("d784"),n=r("825a"),s=r("7234"),a=r("50c4"),l=r("577e"),p=r("1d80"),u=r("dc4a"),c=r("8aa5"),d=r("14c3");o("match",(function(t,e,r){return[function(e){var r=p(this),o=s(e)?void 0:u(e,t);return o?i(o,e,r):new RegExp(e)[t](l(r))},function(t){var i=n(this),o=l(t),s=r(e,i,o);if(s.done)return s.value;if(!i.global)return d(i,o);var p=i.unicode;i.lastIndex=0;var u,m=[],f=0;while(null!==(u=d(i,o))){var h=l(u[0]);m[f]=h,""===h&&(i.lastIndex=c(o,a(i.lastIndex),p)),f++}return 0===f?null:m}]}))},"4ec9":function(t,e,r){"use strict";r("6f48")},"5c92":function(t,e,r){"use strict";r.r(e);r("ac1f"),r("5319"),r("841c"),r("498a");var i=function(){var t=this,e=t._self._c;return e("Card",[e("div",{staticStyle:{width:"100%","margin-top":"50px",margin:"auto"}},[e("div",{staticStyle:{display:"flex",width:"100%","align-items":"center"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v("国家名称")]),e("Input",{staticStyle:{width:"200px","margin-left":"10px"},attrs:{placeholder:"请输入国家名称",prop:"showTitle",clearable:""},model:{value:t.country,callback:function(e){t.country=e},expression:"country "}}),t._v("  \n      "),e("span",{staticStyle:{"font-weight":"bold"}},[t._v("大洲名称")]),e("Input",{staticStyle:{width:"200px","margin-left":"10px"},attrs:{placeholder:"请输入大洲名称",prop:"showTitle",clearable:""},model:{value:t.continent,callback:function(e){t.continent=e},expression:"continent"}}),e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{"margin-left":"20px"},attrs:{type:"primary",icon:"md-search",size:"large",loading:t.searchLoading},on:{click:function(e){return t.search()}}},[t._v("搜索")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{"margin-left":"20px"},attrs:{type:"success",icon:"md-add",size:"large"},on:{click:function(e){return t.address()}}},[t._v("新增国家")])],1),e("Table",{staticStyle:{width:"100%","margin-top":"50px"},attrs:{columns:t.columns12,data:t.talbedata,loading:t.loading},scopedSlots:t._u([{key:"Operators",fn:function(r){var i=r.row;r.index;return[e("a",{on:{click:function(e){return t.viewOperators(i)}}},[t._v("查看更多")])]}},{key:"action",fn:function(r){var i=r.row;r.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.updaterow(i)}}},[t._v("修改")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"error",size:"small",loading:t.deleteLoading},on:{click:function(e){return t.remove(i)}}},[t._v("删除")])]}}])}),e("div",{staticStyle:{"margin-left":"38%","margin-top":"100px","margin-bottom":"160px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Modal",{staticStyle:{padding:"10px","box-sizing":"border-box","max-height":"90vh",overflow:"auto"},attrs:{title:"添加国家","mask-closable":!1,width:"95%"},on:{"on-cancel":t.cancelModal},model:{value:t.addmodel,callback:function(e){t.addmodel=e},expression:"addmodel"}},[e("div",{staticStyle:{width:"100%","overflow-x":"auto"}},[e("RadioGroup",{on:{"on-change":t.noRepeat},model:{value:t.addPreSupplier,callback:function(e){t.addPreSupplier=e},expression:"addPreSupplier"}},[e("Form",{ref:"formmodel",staticStyle:{width:"90%"},attrs:{model:t.formmodel,rules:t.rules,"label-width":110}},[e("FormItem",{attrs:{label:"漫游是否已开通",prop:"openRoaming"}},[e("Select",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"请选择",clearable:""},model:{value:t.formmodel.openRoaming,callback:function(e){t.$set(t.formmodel,"openRoaming",e)},expression:"formmodel.openRoaming"}},t._l(t.openList,(function(r,i){return e("Option",{key:i,attrs:{value:r.id}},[t._v(t._s(r.value)+"\n                ")])})),1)],1),e("div",{staticStyle:{display:"flex"}},[e("FormItem",{attrs:{label:"国家名称",prop:"countryCn"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"简体名称",clearable:""},model:{value:t.formmodel.countryCn,callback:function(e){t.$set(t.formmodel,"countryCn",e)},expression:"formmodel.countryCn"}})],1),e("FormItem",{staticStyle:{"margin-left":"30px"},attrs:{prop:"countryTw","label-width":0}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"繁体名称",clearable:""},model:{value:t.formmodel.countryTw,callback:function(e){t.$set(t.formmodel,"countryTw",e)},expression:"formmodel.countryTw"}})],1),e("FormItem",{staticStyle:{"margin-left":"30px"},attrs:{prop:"countryEn","label-width":0}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"英文名称",clearable:""},model:{value:t.formmodel.countryEn,callback:function(e){t.$set(t.formmodel,"countryEn",e)},expression:"formmodel.countryEn"}})],1)],1),e("div",{staticStyle:{display:"flex"}},[e("FormItem",{attrs:{label:"所属大洲名称",prop:"continentCn"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"简体名称",clearable:""},model:{value:t.formmodel.continentCn,callback:function(e){t.$set(t.formmodel,"continentCn",e)},expression:"formmodel.continentCn"}})],1),e("FormItem",{staticStyle:{"margin-left":"30px"},attrs:{prop:"continentTw","label-width":0}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"繁体名称",clearable:""},model:{value:t.formmodel.continentTw,callback:function(e){t.$set(t.formmodel,"continentTw",e)},expression:"formmodel.continentTw"}})],1),e("FormItem",{staticStyle:{"margin-left":"30px"},attrs:{prop:"continentEn","label-width":0}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"英文名称",clearable:""},model:{value:t.formmodel.continentEn,callback:function(e){t.$set(t.formmodel,"continentEn",e)},expression:"formmodel.continentEn"}})],1)],1),e("FormItem",{attrs:{label:"MCC",prop:"mcc"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入MCC",prop:"showTitle",clearable:"",onkeyup:t.formmodel.mcc=t.formmodel.mcc.replace(/\s+/g,"")},model:{value:t.formmodel.mcc,callback:function(e){t.$set(t.formmodel,"mcc",e)},expression:"formmodel.mcc"}})],1),e("FormItem",{attrs:{label:"是否为热门国家",prop:"hotcountry"}},[e("Select",{staticStyle:{width:"200px"},attrs:{filterable:!0,clearable:"",placeholder:"请选择国家"},on:{"on-change":function(e){return t.getProviders(e)}},model:{value:t.formmodel.hotcountry,callback:function(e){t.$set(t.formmodel,"hotcountry",e)},expression:"formmodel.hotcountry"}},t._l(t.hotcountryList,(function(r,i){return e("Option",{key:i,attrs:{value:r.id}},[t._v(t._s(r.value))])})),1)],1),e("FormItem",{attrs:{label:"上传图片",rules:[{required:!0,message:"请上传图片",trigger:"blur"}]}},[e("div",{staticStyle:{display:"flex","flex-direction":"column",width:"200px",height:"100px"}},[null==t.formmodel.picture?e("Upload",{ref:"upload",attrs:{type:"drag",action:"#","before-upload":t.handleUpload,"on-preview":t.handlePreview,"show-upload-list":!1}},[e("div",{staticStyle:{padding:"20px",height:"100px",width:"100%"}},[e("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),e("p",[t._v("上传图片")])],1)]):e("div",{staticStyle:{display:"flex","flex-direction":"column",width:"100%",height:"100%",position:"relative"}},[e("Icon",{staticClass:"mediaShowDelSty",attrs:{type:"md-close-circle",color:"#ff3300",size:"22"},on:{click:function(e){return t.cancelSelected()}}}),e("img",{attrs:{src:t.pictureUrl,width:"100%",height:"100%"},on:{click:function(e){t.pictureShowFlag=!0}}})],1)],1)]),e("div",{staticClass:"section-box",staticStyle:{"margin-top":"30px"}},[t._l(t.formmodel.GTList,(function(r,i){return e("div",{key:i},[e("FormItem",{attrs:{label:"GT码 ",prop:"GTList."+i+".gtcode",rules:[{required:!0,message:"请输入GT码",trigger:"blur"}]}},[e("div",{staticClass:"gt-input-group"},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入GT码",clearable:"",onkeyup:r.gtcode=r.gtcode.replace(/\s+/g,"")},model:{value:r.gtcode,callback:function(e){t.$set(r,"gtcode",e)},expression:"item.gtcode"}}),e("Icon",{staticStyle:{cursor:"pointer"},attrs:{type:"ios-close-circle-outline",size:"30",color:"red"},on:{click:function(e){return t.removegt(i)}}})],1)])],1)})),e("FormItem",{staticStyle:{"margin-bottom":"20px"}},[e("Button",{attrs:{type:"primary",ghost:""},on:{click:function(e){return t.addgt()}}},[t._v("新增GT码")])],1)],2),e("div",{staticClass:"section-box"},[e("div",{staticClass:"mnc-table"},[e("div",{staticClass:"mnc-row header"},[e("div",{staticClass:"mnc-cell",staticStyle:{width:"700px"}}),e("div",{staticClass:"mnc-cell",staticStyle:{width:"60px"}}),e("div",{staticClass:"mnc-cell",staticStyle:{width:"115px"}},[e("FormItem",{attrs:{"label-width":30}},[e("Radio",{attrs:{label:t.formmodel.MNCList[0].suppliers[0].supplierId}},[t._v("\n                        H卡\n                      ")])],1)],1),t._l(9,(function(r){return e("div",{key:r,staticClass:"mnc-cell",staticStyle:{width:"120px"}},[e("FormItem",{staticStyle:{display:"flex","flex-direction":"column"},attrs:{"label-width":0}},[e("div",{staticStyle:{display:"flex","justify-content":"center"}},[e("Radio",{attrs:{label:t.formmodel.MNCList[0].suppliers[r].supplierId,disabled:!t.formmodel.MNCList[0].suppliers[r].supplierId},model:{value:t.formmodel.MNCList[0].suppliers[r].choose,callback:function(e){t.$set(t.formmodel.MNCList[0].suppliers[r],"choose",e)},expression:"formmodel.MNCList[0].suppliers[i].choose"}},[t._v("\n                          V"+t._s(r)+"卡\n                        ")])],1),e("Select",{staticStyle:{width:"100px"},attrs:{filterable:"",clearable:!0,transfer:""},on:{"on-clear":function(e){return t.clearSupplierId(0,r)},"on-change":function(e){return t.handleSupplierChange(e,r)}},model:{value:t.formmodel.MNCList[0].suppliers[r].supplierId,callback:function(e){t.$set(t.formmodel.MNCList[0].suppliers[r],"supplierId",e)},expression:"formmodel.MNCList[0].suppliers[i].supplierId"}},t._l(t.availableProviders(t.formmodel.MNCList[0].suppliers[r].supplierId),(function(r){return e("Option",{key:r.supplierId,attrs:{value:r.supplierId,title:r.supplierName}},[t._v(t._s(r.supplierName.length>20?r.supplierName.substring(0,20)+"…":r.supplierName)+"\n                        ")])})),1)],1)],1)}))],2),t._l(t.formmodel.MNCList,(function(r,i){return e("div",{key:i,staticClass:"mnc-row"},[e("FormItem",{attrs:{label:"MNC",prop:"MNCList."+i+".mnc",rules:t.rules.mnc}},[e("Input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入MNC",clearable:"",onkeyup:r.mnc=r.mnc.replace(/\s+/g,"")},model:{value:r.mnc,callback:function(e){t.$set(r,"mnc",e)},expression:"item.mnc"}})],1),e("FormItem",{attrs:{label:"运营商名称",prop:"MNCList."+i+".operatorId",rules:t.rules.operatorId,"label-width":90}},[e("Select",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入运营商名称",disabled:!r.mnc,clearable:"",filterable:"",transfer:""},on:{"on-change":function(){return t.updateSupportNetType()}},model:{value:r.operatorId,callback:function(e){t.$set(r,"operatorId",e)},expression:"item.operatorId"}},t._l(t.operatorIdList,(function(r,i){return e("Option",{key:r.id,attrs:{value:r.id,title:r.id}},[t._v(t._s(r.operatorName))])})),1)],1),e("FormItem",{attrs:{label:"TADIG",prop:"MNCList."+i+".tadig",rules:t.rules.tadigRules,"label-width":60}},[e("Input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入TADIG",maxlength:"64",clearable:"",disabled:!r.operatorId},model:{value:r.tadig,callback:function(e){t.$set(r,"tadig","string"===typeof e?e.trim():e)},expression:"item.tadig"}})],1),e("FormItem",{staticStyle:{margin:"0 10px",cursor:"pointer"},attrs:{"label-width":0}},[e("Icon",{attrs:{type:"ios-close-circle-outline",size:"30",color:"red"},on:{click:function(e){return t.removemnc(i)}}})],1),e("FormItem",{attrs:{"label-width":5}},[e("Select",{class:{"status-1":1===r.suppliers[0].status,"status-2":2===r.suppliers[0].status,"status-3":3===r.suppliers[0].status},staticStyle:{width:"100px"},attrs:{transfer:"",disabled:!r.operatorId},on:{"on-change":function(e){return t.changeSupplierStatus(i,0,e)}},model:{value:r.suppliers[0].status,callback:function(e){t.$set(r.suppliers[0],"status",e)},expression:"item.suppliers[0].status"}},t._l(t.mncStatusOptions,(function(r){return e("Option",{key:r.value,class:{"status-1":1===r.value,"status-2":2===r.value,"status-3":3===r.value},attrs:{value:r.value}},[t._v("\n                        "+t._s(r.label)+"\n                      ")])})),1)],1),t._l(9,(function(o){return e("FormItem",{key:o,attrs:{"label-width":20}},[e("Select",{class:{"status-1":1===r.suppliers[o].status,"status-2":2===r.suppliers[o].status,"status-3":3===r.suppliers[o].status},staticStyle:{width:"100px"},attrs:{transfer:"",disabled:!r.operatorId},on:{"on-change":function(e){return t.changeSupplierStatus(i,o,e)}},model:{value:r.suppliers[o].status,callback:function(e){t.$set(r.suppliers[o],"status",e)},expression:"item.suppliers[i].status"}},t._l(t.mncStatusOptions,(function(r){return e("Option",{key:r.value,class:{"status-1":1===r.value,"status-2":2===r.value,"status-3":3===r.value},attrs:{value:r.value}},[t._v("\n                        "+t._s(r.label)+"\n                      ")])})),1)],1)}))],2)}))],2),e("FormItem",[e("Button",{attrs:{type:"primary",ghost:""},on:{click:function(e){return t.addmnc()}}},[t._v("新增MNC")])],1)],1),e("div",{staticClass:"section-box"},t._l(t.formmodel.supportNetType,(function(r,i){return e("div",{key:i,staticClass:"mnc-row"},[e("FormItem",{attrs:{label:"运营商名称",prop:"supportNetType."+i+".operatorId"}},[e("Select",{staticStyle:{width:"150px"},attrs:{disabled:"",transfer:""},model:{value:r.operatorId,callback:function(e){t.$set(r,"operatorId",e)},expression:"itemNet.operatorId"}},t._l(t.operatorIdList,(function(r){return e("Option",{key:r.id,attrs:{value:r.id,title:r.id}},[t._v("\n                      "+t._s(r.operatorName)+"\n                    ")])})),1)],1),e("FormItem",{attrs:{label:"网络类型",prop:"supportNetType."+i+".supportNetType",rules:[{required:!0,type:"array",min:1,message:"请选择网络类型",trigger:"change"}]}},[e("CheckboxGroup",{on:{"on-change":function(e){return t.handleNetworkTypeChange(e,i)}},model:{value:r.supportNetType,callback:function(e){t.$set(r,"supportNetType",e)},expression:"itemNet.supportNetType"}},[e("Checkbox",{attrs:{label:"2"}},[t._v("2G")]),t._v("    \n                    "),e("Checkbox",{attrs:{label:"3"}},[t._v("3G")]),t._v("    \n                    "),e("Checkbox",{attrs:{label:"4"}},[t._v("4G")]),t._v("    \n                    "),e("Checkbox",{attrs:{label:"5"}},[t._v("5G")])],1)],1)],1)})),0)],1)],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","justify-content":"center",margin:"30px 0"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),t._v("  \n        "),e("Button",{attrs:{type:"primary",loading:t.addLoading},on:{click:t.addsave}},[t._v("确定")])],1)]),e("Modal",{staticStyle:{padding:"10px","box-sizing":"border-box","max-height":"90vh","overflow-y":"auto"},attrs:{title:"修改国家","mask-closable":!1,width:"95%"},on:{"on-cancel":t.updatecancelModal},model:{value:t.upmodel,callback:function(e){t.upmodel=e},expression:"upmodel"}},[e("div",{staticStyle:{width:"100%","overflow-x":"auto"}},[e("RadioGroup",{on:{"on-change":t.noRepeats},model:{value:t.upadatePreSupplier,callback:function(e){t.upadatePreSupplier=e},expression:"upadatePreSupplier"}},[e("Form",{ref:"upform",staticStyle:{width:"90%"},attrs:{model:t.upform,rules:t.upformrules,"label-width":110}},[e("FormItem",{attrs:{label:"漫游是否已开通",prop:"openRoaming"}},[e("Select",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"请选择",clearable:""},model:{value:t.upform.openRoaming,callback:function(e){t.$set(t.upform,"openRoaming",e)},expression:"upform.openRoaming"}},t._l(t.openList,(function(r,i){return e("Option",{key:i,attrs:{value:r.id}},[t._v(t._s(r.value)+"\n                ")])})),1)],1),e("div",{staticStyle:{display:"flex"}},[e("FormItem",{attrs:{label:"国家名称",prop:"countryCn"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"简体名称",clearable:""},model:{value:t.upform.countryCn,callback:function(e){t.$set(t.upform,"countryCn",e)},expression:"upform.countryCn"}})],1),e("FormItem",{staticStyle:{"margin-left":"30px"},attrs:{prop:"countryTw","label-width":0}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"繁体名称",clearable:""},model:{value:t.upform.countryTw,callback:function(e){t.$set(t.upform,"countryTw",e)},expression:"upform.countryTw"}})],1),e("FormItem",{staticStyle:{"margin-left":"30px"},attrs:{prop:"countryEn","label-width":0}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"英文名称",clearable:""},model:{value:t.upform.countryEn,callback:function(e){t.$set(t.upform,"countryEn",e)},expression:"upform.countryEn"}})],1)],1),e("div",{staticStyle:{display:"flex"}},[e("FormItem",{attrs:{label:"所属大洲名称",prop:"continentCn"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"简体名称",clearable:""},model:{value:t.upform.continentCn,callback:function(e){t.$set(t.upform,"continentCn",e)},expression:"upform.continentCn"}})],1),e("FormItem",{staticStyle:{"margin-left":"30px"},attrs:{prop:"continentTw","label-width":0}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"繁体名称",clearable:""},model:{value:t.upform.continentTw,callback:function(e){t.$set(t.upform,"continentTw",e)},expression:"upform.continentTw"}})],1),e("FormItem",{staticStyle:{"margin-left":"30px"},attrs:{prop:"continentEn","label-width":0}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"英文名称",clearable:""},model:{value:t.upform.continentEn,callback:function(e){t.$set(t.upform,"continentEn",e)},expression:"upform.continentEn"}})],1)],1),e("FormItem",{attrs:{label:"MCC",prop:"mcc"}},[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入MCC",prop:"showTitle",clearable:""},model:{value:t.upform.mcc,callback:function(e){t.$set(t.upform,"mcc",e)},expression:"upform.mcc"}})],1),e("FormItem",{attrs:{label:"是否为热门国家",rules:[{required:!0,message:"请选择是否为热门国家",trigger:"blur"}]}},[e("Select",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"请选择"},on:{"on-change":function(e){return t.getProviders(e)}},model:{value:t.hotCountry,callback:function(e){t.hotCountry=e},expression:"hotCountry"}},t._l(t.hotcountryList,(function(r,i){return e("Option",{key:i,attrs:{value:r.id}},[t._v(t._s(r.value))])})),1)],1),e("FormItem",{attrs:{label:"上传图片",rules:[{required:!0,message:"请上传图片",trigger:"blur"}]}},[e("div",{staticStyle:{display:"flex","flex-direction":"column",width:"200px",height:"100px"}},[null==t.upform.imagePath?e("Upload",{ref:"upload",attrs:{type:"drag",action:"#","before-upload":t.handleUpload,"on-preview":t.handlePreview,"show-upload-list":!1}},[e("div",{staticStyle:{padding:"20px",height:"100px",width:"100%"}},[e("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),e("p",[t._v("上传图片")])],1)]):e("div",{staticStyle:{display:"flex","flex-direction":"column",width:"100%",height:"100%",position:"relative"}},[e("Icon",{staticClass:"mediaShowDelSty",attrs:{type:"md-close-circle",color:"#ff3300",size:"22"},on:{click:function(e){return t.cancelSelected()}}}),e("img",{attrs:{src:t.pictureUrl,width:"100%",height:"100%"},on:{click:function(e){t.pictureShowFlag=!0}}})],1)],1)]),e("div",{staticClass:"section-box",staticStyle:{"margin-top":"30px"}},[t._l(t.upform.gt,(function(r,i){return e("div",{key:i},[e("FormItem",{attrs:{label:"GT码 ",prop:"gt."+i+".gtCode",rules:[{required:!0,message:"请输入GT码",trigger:"blur"}]}},[e("div",[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入GT码",clearable:"",onkeyup:r.gtCode=r.gtCode.replace(/\s+/g,"")},model:{value:r.gtCode,callback:function(e){t.$set(r,"gtCode",e)},expression:"item.gtCode"}}),e("Icon",{staticStyle:{cursor:"pointer"},attrs:{type:"ios-close-circle-outline",size:"30",color:"red"},on:{click:function(e){return t.upformremovegt(i)}}})],1)])],1)})),e("FormItem",{staticStyle:{"margin-bottom":"20px"}},[e("Button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.upformaddgt()}}},[t._v("新增GT码")])],1)],2),e("div",{staticClass:"section-box"},[e("div",{staticClass:"mnc-table"},[e("div",{staticClass:"mnc-row header"},[e("div",{staticClass:"mnc-cell",staticStyle:{width:"700px"}}),e("div",{staticClass:"mnc-cell",staticStyle:{width:"60px"}}),e("div",{staticClass:"mnc-cell",staticStyle:{width:"115px"}},[e("FormItem",{attrs:{"label-width":30}},[e("Radio",{attrs:{label:t.upform.mnc[0].suppliers[0].supplierId}},[t._v("\n                        H卡\n                      ")])],1)],1),t._l(9,(function(r){return e("div",{key:r,staticClass:"mnc-cell",staticStyle:{width:"120px"}},[e("FormItem",{staticStyle:{display:"flex","flex-direction":"column"},attrs:{"label-width":0}},[e("div",{staticStyle:{display:"flex","justify-content":"center"}},[e("Radio",{attrs:{label:t.upform.mnc[0].suppliers[r].supplierId,disabled:!t.upform.mnc[0].suppliers[r].supplierId},model:{value:t.upform.mnc[0].suppliers[r].choose,callback:function(e){t.$set(t.upform.mnc[0].suppliers[r],"choose",e)},expression:"upform.mnc[0].suppliers[i].choose"}},[t._v("\n                          V"+t._s(r)+"卡\n                        ")])],1),e("Select",{staticStyle:{width:"100px"},attrs:{filterable:"",clearable:!0,transfer:""},on:{"on-clear":function(e){return t.clearSupplierId(0,r)},"on-change":function(e){return t.handleSupplierChangeUpdate(e,r)}},model:{value:t.upform.mnc[0].suppliers[r].supplierId,callback:function(e){t.$set(t.upform.mnc[0].suppliers[r],"supplierId",e)},expression:"upform.mnc[0].suppliers[i].supplierId"}},t._l(t.availableProviderss(t.upform.mnc[0].suppliers[r].supplierId),(function(r){return e("Option",{key:r.supplierId,attrs:{value:r.supplierId,title:r.supplierName}},[t._v(t._s(r.supplierName.length>20?r.supplierName.substring(0,20)+"…":r.supplierName)+"\n                        ")])})),1)],1)],1)}))],2),t._l(t.upform.mnc,(function(r,i){return e("div",{key:i,staticClass:"mnc-row"},[e("FormItem",{attrs:{label:"MNC",prop:"mnc."+i+".mnc",rules:t.upformrules.mnc}},[e("Input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入MNC",clearable:"",onkeyup:r.mnc=r.mnc.replace(/\s+/g,"")},model:{value:r.mnc,callback:function(e){t.$set(r,"mnc",e)},expression:"item.mnc"}})],1),e("FormItem",{attrs:{label:"运营商名称",prop:"mnc."+i+".operatorId",rules:t.upformrules.operatorId,"label-width":90}},[e("Select",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入运营商名称",disabled:!r.mnc,clearable:"",filterable:"",transfer:""},on:{"on-change":function(){return t.updateSupportNetTypeUpdate()}},model:{value:r.operatorId,callback:function(e){t.$set(r,"operatorId",e)},expression:"item.operatorId"}},t._l(t.operatorIdList,(function(r,i){return e("Option",{key:r.id,attrs:{value:r.id,title:r.id}},[t._v(t._s(r.operatorName))])})),1)],1),e("FormItem",{attrs:{label:"TADIG",prop:"mnc."+i+".tadig",rules:t.upformrules.tadigRules,"label-width":60}},[e("Input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入TADIG",maxlength:"64",clearable:"",disabled:!r.operatorId},model:{value:r.tadig,callback:function(e){t.$set(r,"tadig","string"===typeof e?e.trim():e)},expression:"item.tadig"}})],1),e("FormItem",{staticStyle:{margin:"0 10px",cursor:"pointer"},attrs:{"label-width":0}},[e("Icon",{attrs:{type:"ios-close-circle-outline",size:"30",color:"red"},on:{click:function(e){return t.upformremovemnc(i)}}})],1),e("FormItem",{attrs:{"label-width":5}},[e("Select",{class:{"status-1":1===r.suppliers[0].status,"status-2":2===r.suppliers[0].status,"status-3":3===r.suppliers[0].status},staticStyle:{width:"100px"},attrs:{transfer:"",disabled:!r.operatorId},on:{"on-change":function(e){return t.changeSupplierStatusUpdate(i,0,e)}},model:{value:r.suppliers[0].status,callback:function(e){t.$set(r.suppliers[0],"status",e)},expression:"item.suppliers[0].status"}},t._l(t.mncStatusOptions,(function(r){return e("Option",{key:r.value,class:{"status-1":1===r.value,"status-2":2===r.value,"status-3":3===r.value},attrs:{value:r.value}},[t._v("\n                        "+t._s(r.label)+"\n                      ")])})),1)],1),t._l(9,(function(o){return e("FormItem",{key:o,attrs:{"label-width":20}},[e("Select",{class:{"status-1":1===r.suppliers[o].status,"status-2":2===r.suppliers[o].status,"status-3":3===r.suppliers[o].status},staticStyle:{width:"100px"},attrs:{transfer:"",disabled:!r.operatorId},on:{"on-change":function(e){return t.changeSupplierStatusUpdate(i,o,e)}},model:{value:r.suppliers[o].status,callback:function(e){t.$set(r.suppliers[o],"status",e)},expression:"item.suppliers[i].status"}},t._l(t.mncStatusOptions,(function(r){return e("Option",{key:r.value,class:{"status-1":1===r.value,"status-2":2===r.value,"status-3":3===r.value},attrs:{value:r.value}},[t._v("\n                        "+t._s(r.label)+"\n                      ")])})),1)],1)}))],2)}))],2),e("FormItem",[e("Button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.upformaddmnc()}}},[t._v("新增MNC")])],1)],1),e("div",{staticClass:"section-box"},t._l(t.upform.supportNetType,(function(r,i){return e("div",{key:i,staticClass:"mnc-row"},[e("FormItem",{attrs:{label:"运营商名称",prop:"supportNetType."+i+".operatorId"}},[e("Select",{staticStyle:{width:"150px"},attrs:{disabled:"",transfer:""},model:{value:r.operatorId,callback:function(e){t.$set(r,"operatorId",e)},expression:"itemNet.operatorId"}},t._l(t.operatorIdList,(function(r){return e("Option",{key:r.id,attrs:{value:r.id,title:r.id}},[t._v("\n                      "+t._s(r.operatorName)+"\n                    ")])})),1)],1),e("FormItem",{attrs:{label:"网络类型",prop:"supportNetType."+i+".supportNetType",rules:[{required:!0,type:"array",min:1,message:"请至少选择一种网络类型",trigger:"change"}]}},[e("CheckboxGroup",{on:{"on-change":function(e){return t.handleNetworkTypeChange(e,i)}},model:{value:r.supportNetType,callback:function(e){t.$set(r,"supportNetType",e)},expression:"itemNet.supportNetType"}},[e("Checkbox",{attrs:{label:"2"}},[t._v("2G")]),t._v("    \n                    "),e("Checkbox",{attrs:{label:"3"}},[t._v("3G")]),t._v("    \n                    "),e("Checkbox",{attrs:{label:"4"}},[t._v("4G")]),t._v("    \n                    "),e("Checkbox",{attrs:{label:"5"}},[t._v("5G")])],1)],1)],1)})),0)],1)],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","justify-content":"center",margin:"30px 0"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.updatecancelModal}},[t._v("取消")]),t._v("  \n        "),e("Button",{attrs:{type:"primary",loading:t.updateLoading},on:{click:t.updatesave}},[t._v("确定")])],1)]),e("Modal",{attrs:{title:"View Image"},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[t.visible?e("img",{staticStyle:{width:"100%"},attrs:{src:"https://o5wwk8baw.qnssl.com/"+t.imgName+"/large"}}):t._e()]),e("Modal",{attrs:{title:"封面预览","footer-hide":!0,width:"532px"},model:{value:t.pictureShowFlag,callback:function(e){t.pictureShowFlag=e},expression:"pictureShowFlag"}},[e("div",{staticStyle:{display:"flex","justify-content":"center","align-items":"center",width:"500px"}},[e("img",{attrs:{src:t.pictureUrl,width:"100%"}})])]),e("Modal",{attrs:{title:"查看更多"},model:{value:t.model1,callback:function(e){t.model1=e},expression:"model1"}},t._l(t.gtList,(function(r,i){return e("div",{key:i,attrs:{value:r.gtCode}},[e("span",{staticStyle:{"margin-top":"20px"}},[t._v(t._s(r.gtCode))])])})),0),e("Modal",{attrs:{title:"查看更多"},model:{value:t.model2,callback:function(e){t.model2=e},expression:"model2"}},t._l(t.operatorList,(function(r,i){return e("div",{key:i,attrs:{value:r.operatorName}},[e("span",{staticStyle:{"margin-top":"20px"}},[t._v(t._s(r.operatorName))])])})),0)],1)])},o=[],n=r("5530"),s=r("ade3"),a=(r("d9e2"),r("99af"),r("4de4"),r("7db0"),r("a630"),r("caad"),r("a15b"),r("d81d"),r("14d9"),r("a434"),r("b0c0"),r("e9c4"),r("4ec9"),r("a9e3"),r("b64b"),r("d3b7"),r("00b4"),r("6062"),r("1e70"),r("79a4"),r("c1a1"),r("8b00"),r("a4e7"),r("1e5a"),r("72c3"),r("2532"),r("3ca3"),r("466d"),r("159b"),r("ddb0"),r("90fe")),l=r("e472"),p={computed:{availableProviders:function(){var t=this;return function(e){var r=JSON.parse(JSON.stringify(t.providers)),i=t.formmodel.MNCList[0].suppliers.map((function(t){return t.supplierId}));return r=r.filter((function(t){return e==t.supplierId||-1==i.indexOf(t.supplierId)?t:void 0})),r}},availableProviderss:function(){var t=this;return function(e){var r=JSON.parse(JSON.stringify(t.providers)),i=t.upform.mnc[0].suppliers.map((function(t){return t.supplierId}));return r=r.filter((function(t){return e==t.supplierId||-1==i.indexOf(t.supplierId)?t:void 0})),r}},formmodelSupportNetType:function(){var t=this,e=new Set;this.formmodel.MNCList.forEach((function(t){var r=t.suppliers.some((function(e){return[1,2].includes(e.status)&&t.operatorId}));r&&t.operatorId&&e.add(t.operatorId)}));var r=this.formmodel.supportNetType.map((function(t){return t.operatorId}));return Array.from(e).forEach((function(e){r.includes(e)||t.$set(t.formmodel.supportNetType,t.formmodel.supportNetType.length,{operatorId:e,supportNetType:[]})})),this.formmodel.supportNetType=this.formmodel.supportNetType.filter((function(t){return e.has(t.operatorId)})),this.formmodel.supportNetType},upformSupportNetType:function(){if(this.upform.supportNetType&&this.upform.supportNetType.length>0)return this.upform.supportNetType;var t=new Set;this.upform.mnc.forEach((function(e){var r=e.suppliers.some((function(t){return[1,2].includes(t.status)&&e.operatorId}));r&&e.operatorId&&t.add(e.operatorId)}));var e=new Map;return this.upform.supportNetTypeDTOS&&this.upform.supportNetTypeDTOS.length>0&&this.upform.supportNetTypeDTOS.forEach((function(t){try{var r=JSON.parse(t.supportNetType);e.set(t.operatorId,r)}catch(i){console.error("解析supportNetType失败:",i)}})),this.upform.supportNetType.forEach((function(t){t.supportNetType&&t.supportNetType.length>0&&e.set(t.operatorId,t.supportNetType)})),this.upform.supportNetType=Array.from(t).map((function(t){return{operatorId:t,supportNetType:e.get(t)||[]}})),this.upform.supportNetType}},data:function(){var t=this,e=function(t,e,r){var i=/^[0-9a-zA-Z\/\(\)\,\.\?\!\=\-\+\~\^\\\'\;\#\$\%\&\*\`\@\_\]\[\'\s]+$/;0==i.test(e)?r(new Error("不能中文输入！")):r()},r=function(e,r,i){var o=e.field.match(/MNCList\.(\d+)\.mnc/),n=parseInt(o[1],10),s=new Set;t.formmodel.MNCList.forEach((function(t,e){if(e!==n){var r="".concat(t.mnc,"-").concat(t.operatorId);s.add(r)}}));var a="".concat(r,"-").concat(t.formmodel.MNCList[n].operatorId);s.has(a)?i(new Error("MNC + 运营商 的组合必须唯一")):i()},i=function(e,r,i){var o=e.field.match(/MNCList\.(\d+)\.operatorId/),n=parseInt(o[1],10),s=new Set;t.formmodel.MNCList.forEach((function(t,e){if(e!==n){var r="".concat(t.mnc,"-").concat(t.operatorId);s.add(r)}}));var a="".concat(t.formmodel.MNCList[n].mnc,"-").concat(r);s.has(a)?i(new Error("MNC + 运营商 的组合必须唯一")):i()},o=function(e,r,i){var o=/^[a-zA-Z0-9]+$/.test(r);r?o||i(new Error("TADIG仅支持英文和数字")):i();var n=e.field.match(/MNCList\.(\d+)\.tadig/),s=parseInt(n[1],10),a=t.formmodel.MNCList.filter((function(t,e){return e!==s})),l=a.find((function(t){return t.tadig===r}));l?l.operatorId===t.formmodel.MNCList[s].operatorId?i():i(new Error("TADIG只能对应一个运营商,请修改TADIG")):i()},n=function(e,r,i){var o=e.field.match(/mnc\.(\d+)\.mnc/),n=parseInt(o[1],10),s=new Set;t.upform.mnc.forEach((function(t,e){if(e!==n){var r="".concat(t.mnc,"-").concat(t.operatorId);s.add(r)}}));var a="".concat(r,"-").concat(t.upform.mnc[n].operatorId);s.has(a)?i(new Error("MNC + 运营商 的组合必须唯一")):i()},a=function(e,r,i){var o=e.field.match(/mnc\.(\d+)\.operatorId/),n=parseInt(o[1],10),s=new Set;t.upform.mnc.forEach((function(t,e){if(e!==n){var r="".concat(t.mnc,"-").concat(t.operatorId);s.add(r)}}));var a="".concat(t.upform.mnc[n].mnc,"-").concat(r);s.has(a)?i(new Error("MNC + 运营商 的组合必须唯一")):i()},l=function(e,r,i){var o=/^[a-zA-Z0-9]+$/.test(r);r?o||i(new Error("TADIG仅支持英文和数字")):i();var n=e.field.match(/mnc\.(\d+)\.tadig/),s=parseInt(n[1],10),a=t.upform.mnc.filter((function(t,e){return e!==s})),l=a.find((function(t){return t.tadig===r}));l?l.operatorId===t.upform.mnc[s].operatorId?i():i(new Error("TADIG只能对应一个运营商,请修改TADIG")):i()};return{excludeProvides:[],pictureShowFlag:!1,pictureUrl:"",pictureflag:!1,hotcountryList:[{id:1,value:"否"},{id:2,value:"是"}],openList:[{id:"1",value:"是"},{id:"2",value:"否"}],operatorIdList:[],providers:[],addPreSupplier:null,upadatePreSupplier:null,supplierIdlx:"default",supplierIdlxs:"default",formmodel:{GTList:[{index:0,gtcode:""}],MNCList:[{id:0,index:0,mnc:"",operatorId:"",tadig:"",suppliers:[{supplierId:"default",status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3}]}],supplierId:"",openRoaming:"",countryCn:"",countryTw:"",countryEn:"",continentCn:"",continentTw:"",continentEn:"",mcc:"",gtcode:"",mnc:"",operatorName:"",hotcountry:"",supportNetType:[{operatorId:"",supportNetType:[]}]},upform:{gt:[{index:0,gtCode:""}],mnc:[{id:0,index:0,mnc:"",operatorId:"",tadig:"",suppliers:[{supplierId:"default",status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3}]}],supportNetType:[{operatorId:"",supportNetType:[]}],openRoaming:"",countryCn:"",countryTw:"",countryEn:"",continentCn:"",continentTw:"",continentEn:"",mcc:"",hotcountry:""},country:"",countryId:"",continent:"",form:{},file:null,uploadUrl:"",loading:!1,searchLoading:!1,addLoading:!1,updateLoading:!1,deleteLoading:!1,total:0,currentPage:1,addmodel:!1,upmodel:!1,index:0,model1:!1,model2:!1,columns12:[{title:"国家名称",key:"countryCn",align:"center",minWidth:100,tooltip:!0},{title:"所属大洲",key:"continentCn",align:"center",minWidth:100,tooltip:!0},{title:"MCC",key:"mcc",align:"center",minWidth:100,tooltip:!0},{title:"是否为热门国家",key:"hotcountry",align:"center",minWidth:120,tooltip:!0,render:function(t,e){var r=e.row,i=2==r.hotCountry?"是":"否";return t("label",i)}},{title:"运营商",slot:"Operators",align:"center",minWidth:120,render:function(t,e){var r=e.row,i=new Set;r.mnc.forEach((function(t){i.add(t.operatorName)}));var o=Array.from(i).join("、 ");return t("div",[t("Tooltip",{props:{placement:"bottom",transfer:!0,content:o,maxWidth:300},style:{width:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}},o)])}},{title:"操作",slot:"action",align:"center",minWidth:150}],talbedata:[],defaultList:[],imgName:"",visible:!1,uploadList:[],hotCountry:"",gtList:[],operatorList:[],rules:Object(s["a"])(Object(s["a"])(Object(s["a"])({openRoaming:[{required:!0,message:"请选择是否开通",trigger:"blur"}],countryCn:[{required:!0,message:"请输入简体名称",trigger:"blur"},{min:0,max:100,message:"输入100位以内字符",trigger:"blur"}],countryTw:[{required:!0,message:"请输入繁体名称",trigger:"blur"},{min:0,max:100,message:"输入100位以内字符",trigger:"blur"}],countryEn:[{required:!0,message:"请输入英文名称",trigger:"blur"},{min:0,max:100,message:"输入100位以内字符",trigger:"blur"},{required:!0,validator:e,trigger:"blur"}],continentCn:[{required:!0,message:"请输入简体名称",trigger:"blur"},{min:0,max:100,message:"输入100位以内字符",trigger:"blur"}],continentTw:[{required:!0,message:"请输入繁体名称",trigger:"blur"},{min:0,max:100,message:"输入100位以内字符",trigger:"blur"}],continentEn:[{required:!0,message:"请输入英文名称",trigger:"blur"},{min:0,max:100,message:"输入100位以内字符",trigger:"blur"},{required:!0,validator:e,trigger:"blur"}],mcc:[{required:!0,message:"请输入MCC",trigger:"blur"},{min:0,max:50,message:"输入50位以内的号码",trigger:"blur"}],gtcode:[{required:!0,message:"请输入...",trigger:"blur"},{min:0,max:25,message:"输入25位以内的字符",trigger:"blur"}],mnc:[{required:!0,message:"请输入MCC",trigger:"blur"},{min:0,max:25,message:"输入25位以内的字符",trigger:"blur"}],operatorName:[{required:!0,message:"请输入...",trigger:"blur"}],apn:[{required:!0,message:"请输入...",trigger:"blur"}],hotcountry:[{required:!0,message:"请选择是否为热门国家",trigger:"blur",pattern:/.+/}]},"mnc",[{required:!0,message:"请输入MNC码"},{pattern:/^[0-9]*$/,message:"请输入纯数字号码"},{validator:r}]),"operatorId",[{required:!0,message:"请输入运营商名称"},{validator:i}]),"tadigRules",[{validator:o}]),upformrules:Object(s["a"])(Object(s["a"])(Object(s["a"])({openRoaming:[{required:!0,message:"请选择是否开通",trigger:"blur"}],countryCn:[{required:!0,message:"请输入简体名称",trigger:"blur"},{min:0,max:100,message:"输入100位以内字符",trigger:"blur"}],countryTw:[{required:!0,message:"请输入繁体名称",trigger:"blur"},{min:0,max:100,message:"输入100位以内字符",trigger:"blur"}],countryEn:[{required:!0,message:"请输入英文名称",trigger:"blur"},{min:0,max:100,message:"输入100位以内字符",trigger:"blur"},{required:!0,validator:e,trigger:"blur"}],continentCn:[{required:!0,message:"请输入简体名称",trigger:"blur"},{min:0,max:100,message:"输入100位以内字符",trigger:"blur"}],continentTw:[{required:!0,message:"请输入繁体名称",trigger:"blur"},{min:0,max:100,message:"输入100位以内字符",trigger:"blur"}],continentEn:[{required:!0,message:"请输入英文名称",trigger:"blur"},{min:0,max:100,message:"输入100位以内字符",trigger:"blur"},{required:!0,validator:e,trigger:"blur"}],mcc:[{required:!0,message:"请输入MCC",trigger:"blur"},{min:0,max:50,message:"输入50位以内的号码",trigger:"blur"}],gtcode:[{required:!0,message:"请输入...",trigger:"blur"},{min:0,max:25,message:"输入25位以内的字符",trigger:"blur"}],mnc:[{required:!0,message:"请输入MCC",trigger:"blur"},{min:0,max:25,message:"输入25位以内的字符",trigger:"blur"}],operatorName:[{required:!0,message:"请输入...",trigger:"blur"}],apn:[{required:!0,message:"请输入...",trigger:"blur"}],hotcountry:[{required:!0,message:"请选择是否为热门国家"}],addgt:[{type:"array",required:!0,message:"请添加gt码",trigger:"blur,change"}]},"mnc",[{required:!0,message:"请输入MNC码"},{pattern:/^[0-9]*$/,message:"请输入纯数字号码"},{validator:n}]),"operatorId",[{required:!0,message:"请输入运营商名称"},{validator:a}]),"tadigRules",[{validator:l}]),id:0,mncStatusOptions:[{value:1,label:"主用",color:"#2d8cf0"},{value:2,label:"备用",color:"#19be6b"},{value:3,label:"未启用",color:"#ed4014"}],activeOperators:[]}},created:function(){this.updateSupportNetType()},mounted:function(){this.goPageFirst(1),this.getSupplier(),this.getOperators()},methods:{goPageFirst:function(t){var e=this;this.loading=!0;var r=this,i=t,o=10,n=this.country,s=this.continent;Object(a["e"])({pageNum:i,pageSize:o,country:n,continent:s}).then((function(i){"0000"==i.code&&(r.loading=!1,e.searchLoading=!1,e.page=t,e.total=i.data.count,e.talbedata=i.data.list)})).catch((function(t){console.error(t)})).finally((function(){e.loading=!1,e.searchLoading=!1}))},goPage:function(t){this.goPageFirst(t)},search:function(){this.searchLoading=!0,this.goPageFirst(1),this.currentPage=this.page},getGTcode:function(t){this.gtList=t.gt,this.model1=!0},viewOperators:function(t){this.operatorList=t.mnc,this.model2=!0},address:function(){this.formmodel.picture=null,this.addmodel=!0},getProviders:function(t){this.hotCountry=t},addsave:function(){var t=this;if(this.formmodel.picture)if(this.formmodel.GTList.length<1)this.$Message.warning("请添加至少一组gt码");else if(this.formmodel.MNCList.length<1)this.$Message.warning("请添加至少一组mnc");else if(this.addPreSupplier){var e=JSON.parse(JSON.stringify(this.formmodel.MNCList)).map((function(e,r){var i=t.formmodel.MNCList[0].suppliers.map((function(t){return t.supplierId})),o=i.map((function(t,r){var i;return{supplierId:t,isEnable:(null===(i=e.suppliers[r])||void 0===i?void 0:i.status)||3}}));return o=o.filter((function(t){return null!==t.supplierId})),{id:e.id,index:e.index,mnc:e.mnc,operatorId:e.operatorId,suppliers:o}})),r=e.some((function(e){return e.suppliers.some((function(e){return e.supplierId===t.addPreSupplier}))}));if(r){var i=!1,o=!1;e.forEach((function(e){e.suppliers.forEach((function(e){e.supplierId==t.addPreSupplier&&1==e.isEnable&&(i=!0),"default"===e.supplierId&&1==e.isEnable&&(o=!0)}))})),i?o?this.$refs.formmodel.validate((function(r){if(r){var i=t.formmodel.MNCList.map((function(t){return{id:t.id,index:t.index,mnc:t.mnc,operatorId:t.operatorId,tadig:t.tadig}})),o=new FormData;o.append("gt",JSON.stringify(t.formmodel.GTList)),o.append("mncSupplierMap",JSON.stringify(e)),o.append("preferredSupplier",t.addPreSupplier),o.append("openRoaming",t.formmodel.openRoaming),o.append("countryCn",t.formmodel.countryCn),o.append("countryTw",t.formmodel.countryTw),o.append("countryEn",t.formmodel.countryEn),o.append("continentCn",t.formmodel.continentCn),o.append("continentTw",t.formmodel.continentTw),o.append("continentEn",t.formmodel.continentEn),o.append("mcc",t.formmodel.mcc),o.append("file",t.formmodel.picture),o.append("hotCountry",t.hotCountry),o.append("opreTadigRelations",JSON.stringify(i));var n=t.formmodel.supportNetType.map((function(t){return{operatorId:t.operatorId,supportNetType:t.supportNetType||[]}}));o.append("supportNetType",JSON.stringify(n)),t.addLoading=!0,Object(a["a"])(o).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提醒",desc:"添加成功！"}),t.goPageFirst(1),t.currentPage=1,t.addmodel=!1})).catch((function(t){console.log(t)})).finally((function(){t.addLoading=!1,t.cancelModal()}))}})):this.$Message.warning("H卡列至少主用一个MNC"):this.$Message.warning("首选供应商至少主用一个MNC")}else this.$Message.warning("请重新选择首选供应商")}else this.$Message.warning("请勾选首选供应商");else this.$Message.warning("请选择需要上传的文件")},updatesave:function(){var t=this;if(this.upform.imagePath)if(this.upform.gt.length<1)this.$Message.warning("请添加至少一组gt码");else if(this.upform.mnc.length<1)this.$Message.warning("请添加至少一组mnc");else if(this.upadatePreSupplier){var e=this.upform.mnc.some((function(e){return e.suppliers.some((function(e){return e.supplierId===t.upadatePreSupplier&&!0===e.choose}))}));if(e){var r=JSON.parse(JSON.stringify(this.upform.mnc)).map((function(e,r){var i=t.upform.mnc[0].suppliers.map((function(t){return t.supplierId})),o=i.map((function(t,r){var i;return{supplierId:t,isEnable:(null===(i=e.suppliers[r])||void 0===i?void 0:i.status)||3}}));return o=o.filter((function(t){return null!==t.supplierId})),{id:e.id,index:e.index,mnc:e.mnc,operatorId:e.operatorId,suppliers:o}})),i=!1,o=!1;r.forEach((function(e){e.suppliers.forEach((function(e){e.supplierId===t.upadatePreSupplier&&1===e.isEnable&&(i=!0),"default"===e.supplierId&&1===e.isEnable&&(o=!0)}))})),i?o?this.$refs.upform.validate((function(e){if(e){var i=t.upform.mnc.map((function(t){return{id:t.id,index:t.index,mnc:t.mnc,operatorId:t.operatorId,tadig:t.tadig}})),o=new FormData;o.append("mncSupplierMap",JSON.stringify(r)),o.append("preferredSupplier",t.upadatePreSupplier),o.append("id",t.countryId),o.append("gt",JSON.stringify(t.upform.gt)),o.append("openRoaming",t.upform.openRoaming),o.append("countryCn",t.upform.countryCn),o.append("countryTw",t.upform.countryTw),o.append("countryEn",t.upform.countryEn),o.append("continentCn",t.upform.continentCn),o.append("continentTw",t.upform.continentTw),o.append("continentEn",t.upform.continentEn),o.append("mcc",t.upform.mcc),o.append("hotCountry",t.hotCountry),o.append("opreTadigRelations",JSON.stringify(i));var n=t.upform.supportNetType.map((function(t){return{operatorId:t.operatorId,supportNetType:t.supportNetType||[]}}));o.append("supportNetType",JSON.stringify(n)),t.updateLoading=!0,!0===t.pictureflag&&o.append("file",t.upform.imagePath),Object(a["g"])(o).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提醒",desc:"修改成功！"}),t.upmodel=!1,t.pictureflag=!1,t.goPageFirst(1),t.currentPage=1})).catch((function(t){console.log(t)})).finally((function(){t.updateLoading=!1,t.updatecancelModal()}))}})):this.$Message.warning("H卡列至少主用一个MNC"):this.$Message.warning("首选供应商至少主用一个MNC")}else this.$Message.warning("请重新选择首选供应商")}else this.$Message.warning("请勾选首选供应商");else this.$Message.warning("请选择需要上传的文件")},clearSupplierId:function(t,e){var r=this,i=this.formmodel.MNCList[t].suppliers[e].supplierId,o=this.upform.mnc[t].suppliers[e].supplierId;this.formmodel.MNCList[t].suppliers[e].supplierId=null,this.upform.mnc[t].suppliers[e].supplierId=null,this.formmodel.MNCList[t].suppliers[e].choose=!1,this.upform.mnc[t].suppliers[e].choose=!1,this.addPreSupplier===i&&(this.addPreSupplier=null,this.supplierIdlx=null,this.formmodel.MNCList.forEach((function(t){t.suppliers.forEach((function(t){r.$set(t,"choose",!1)}))})),this.updateSupportNetType()),this.upadatePreSupplier===o&&(this.upadatePreSupplier=null,this.supplierIdlxs=null,this.upform.mnc.forEach((function(t){t.suppliers.forEach((function(t){r.$set(t,"choose",!1)}))})),this.updateSupportNetTypeUpdate()),this.$nextTick((function(){null===r.addPreSupplier&&(r.supplierIdlx=null),null===r.upadatePreSupplier&&(r.supplierIdlxs=null)}))},cancelModal:function(){this.addmodel=!1,this.formmodel={GTList:[{index:0,gtcode:""}],MNCList:[{id:0,index:0,mnc:"",operatorId:"",tadig:"",suppliers:[{supplierId:"default",status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3}]}],supplierId:"",openRoaming:"",countryCn:"",countryTw:"",countryEn:"",continentCn:"",continentTw:"",continentEn:"",mcc:"",gtcode:"",mnc:"",operatorName:"",hotcountry:"",supportNetType:[{operatorId:"",supportNetType:[]}]},this.pictureUrl="",this.deleteLoading=!1,this.pictureflag=!1,this.hotCountry="",this.countryId="",this.supplierIdlx="",this.addPreSupplier="",this.index=0,this.id=0,this.$refs.formmodel.resetFields()},updatecancelModal:function(){this.upmodel=!1,this.$refs.upform.resetFields(),this.goPageFirst(1),this.currentPage=1,this.upform.picture=null,this.pictureUrl="",this.pictureflag=!1,this.upform.openRoaming="",this.upform.countryCn="",this.upform.countryTw="",this.upform.countryEn="",this.upform.continentCn="",this.upform.continentTw="",this.upform.continentEn="",this.upform.mcc="",this.hotCountry="",this.countryId="",this.supplierIdlxs="",this.upadatePreSupplier="",this.upform.supplierId="",this.upform.gt.forEach((function(t){t.index=0,t.gtcode=""})),this.upform.mnc.forEach((function(t){t.index=0,t.id=0,t.operatorId="",t.mnc="",t.suppliers.forEach((function(t){t.choose=!1,t.supplierId=""}))}))},addgt:function(){this.index++,this.formmodel.GTList.push({index:this.index,gtcode:""})},upformaddgt:function(){this.index++,this.upform.gt.push({index:this.index,gtCode:""})},upformremovegt:function(t){this.upform.gt.length>1?(this.upform.gt.splice(t,1),this.index--):this.$Message.warning("至少保留一个GT码")},removegt:function(t){this.formmodel.GTList.length>1?(this.formmodel.GTList.splice(t,1),this.index--):this.$Message.warning("至少保留一个GT码")},noRepeat:function(t){var e=this;this.formmodel.supplierId=t,this.formmodel.MNCList.forEach((function(t){t.suppliers.forEach((function(t){e.$set(t,"choose",!1)}))})),t?(this.addPreSupplier=t,this.formmodel.MNCList.forEach((function(r){r.suppliers.forEach((function(r,i){r.supplierId===t&&e.$set(r,"choose",!0)}))}))):this.addPreSupplier=null,this.updateSupportNetType()},noRepeats:function(t){var e=this;this.upform.supplierId=t,this.upform.mnc.forEach((function(t){t.suppliers.forEach((function(t){e.$set(t,"choose",!1)}))})),t?(this.upform.mnc.forEach((function(r){r.suppliers.forEach((function(r){r.supplierId===t&&e.$set(r,"choose",!0)}))})),this.upadatePreSupplier=t):this.upadatePreSupplier=null,this.$nextTick((function(){e.updateSupportNetTypeUpdate()}))},getSupplier:function(t){var e=this;this.supplierIdlx=t,this.supplierIdlxs=t,this.upadatePreSupplier=t,this.addPreSupplier=t;var r=1,i=-1;Object(l["d"])({pageNum:r,pageSize:i}).then((function(t){"0000"==t.code&&(e.providers=t.data)})).catch((function(t){console.error(t)}))},addmnc:function(){this.index++,this.id++;var t=[{supplierId:"default",status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3}];this.formmodel.MNCList.push({id:this.id,index:this.index,mnc:"",operatorId:"",suppliers:t}),this.updateSupportNetType()},upformaddmnc:function(){this.index++,this.id++;var t=[{supplierId:"default",status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3},{supplierId:null,status:3}];this.upform.mnc.push({id:this.id,index:this.index,mnc:"",operatorId:"",suppliers:t}),this.updateSupportNetTypeUpdate()},removemnc:function(t){this.formmodel.MNCList.length>1?(this.formmodel.MNCList.splice(t,1),this.index--,this.id--,this.updateSupportNetType()):this.$Message.warning("至少保留一个MNC")},upformremovemnc:function(t){this.upform.mnc.length>1?(this.upform.mnc.splice(t,1),this.index--,this.id--,this.updateSupportNetTypeUpdate()):this.$Message.warning("至少保留一个MNC")},updaterow:function(t){var e=this;this.$set(this,"upform",Object(n["a"])(Object(n["a"])({},t),{},{supportNetType:[],supportNetTypeDTOS:t.supportNetTypeDTOS})),this.supplierIdlxs=t.preferredSupplierId,this.upadatePreSupplier=t.preferredSupplierId,this.noRepeats(t.preferredSupplierId),this.hotCountry=""===parseInt(t.hotCountry)?1:parseInt(t.hotCountry),this.upform.supplierId=t.preferredSupplierId,this.countryId=t.id,this.pictureUrl=t.imagePath,this.upform.imagePath=t.imagePath,t.supportNetTypeDTOS&&t.supportNetTypeDTOS.length>0&&(this.upform.supportNetType=t.supportNetTypeDTOS.map((function(t){var e=[];try{e=Array.isArray(t.supportNetType)?t.supportNetType:JSON.parse(t.supportNetType)}catch(r){console.error("解析supportNetType失败:",r)}return{operatorId:t.operatorId,supportNetType:e}}))),(null==t.gt||t.gt.length<1)&&(this.upform.gt=[{index:this.index,gtCode:""}]),t.mnc.forEach((function(r,i){e.$set(e.upform.mnc,i,Object(n["a"])(Object(n["a"])({},r),{},{suppliers:[]}));var o=r.suppliers.find((function(t){return"default"===t.supplierId}));e.$set(e.upform.mnc[i].suppliers,0,{supplierId:"default",status:null!==o&&void 0!==o&&o.isEnable?Number(o.isEnable):3,choose:"default"===t.preferredSupplierId});var s=r.suppliers.filter((function(t){return"default"!==t.supplierId}));s.forEach((function(r,o){e.$set(e.upform.mnc[i].suppliers,o+1,{supplierId:r.supplierId,status:r.isEnable?Number(r.isEnable):3,choose:r.supplierId===t.preferredSupplierId})}));for(var a=10-e.upform.mnc[i].suppliers.length,l=0;l<a;l++)e.$set(e.upform.mnc[i].suppliers,e.upform.mnc[i].suppliers.length,{supplierId:null,status:3,choose:!1})})),this.index=this.upform.mnc.length,this.upmodel=!0},remove:function(t){var e=this;this.$Modal.confirm({title:"确认删除该项？",onOk:function(){var r=t.mcc;Object(a["b"])({mcc:r}).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作提醒",desc:"删除成功！"}),e.deletemodel=!1,e.goPageFirst(1),e.currentPage=1})).catch((function(t){console.log(t)})).finally((function(){e.loading=!1}))}})},handlePreview:function(t){this.formmodel.picture=t},handleUpload:function(t){var e=this;if(/^.+(\.jpeg|\.png|\.jpg)$/.test(t.name)){this.formmodel.picture=t,this.upform.imagePath=t;var r=new FileReader;r.readAsDataURL(t),r.onload=function(){var t=r.result;e.pictureUrl=t},this.formmodel.file=t,this.upform.imagePath=t,this.pictureflag=!0}else this.$Notice.warning({title:"文件格式不正确",desc:"文件 "+t.name+" 格式不正确，请上传jpeg,png,jpg格式文件。"});return!1},cancelSelected:function(){this.formmodel.picture=null,this.upform.imagePath=null,this.pictureUrl=""},getOperators:function(){var t=this;Object(a["d"])({}).then((function(e){"0000"===e.code&&(t.operatorIdList=e.data)})).catch((function(t){console.log(t)}))},validateMncStatus:function(){var t=this,e=!1,r=!1;return this.activeOperators=[],this.form.mncs.forEach((function(i){1===i.status&&(e=!0,t.activeOperators.push(i.operator_id)),1!==i.status&&2!==i.status||t.activeOperators.push(i.operator_id),"h"===i.card_type&&1===i.status&&(r=!0)})),e?!!r||(this.$Message.error("H卡中至少需要设置一个主用MNC"),!1):(this.$Message.error("至少需要设置一个主用MNC"),!1)},changeSupplierStatus:function(t,e,r){this.formmodel.MNCList[t].suppliers[e].status=r,this.updateSupportNetType()},updateSupportNetType:function(){for(var t,e=this,r=(null===(t=this.formmodel.MNCList[0])||void 0===t?void 0:t.suppliers)||[],i=function(){var t=e.formmodel.MNCList[o],i=r.map((function(e,r){var i;return{supplierId:e.supplierId,status:(null===(i=t.suppliers[r])||void 0===i?void 0:i.status)||3,choose:e.choose}}));e.$set(t,"suppliers",i)},o=1;o<this.formmodel.MNCList.length;o++)i();var n=new Set;this.formmodel.MNCList.forEach((function(t){if(t.operatorId){var r=t.suppliers.some((function(t){var r=[1,2].includes(t.status),i=t.supplierId===e.addPreSupplier;return r&&i}));r&&n.add(t.operatorId)}}));var s=new Map;this.formmodel.supportNetType.forEach((function(t){t.supportNetType&&t.supportNetType.length>0&&s.set(t.operatorId,t.supportNetType)}));var a=Array.from(n).map((function(t){return{operatorId:t,supportNetType:s.get(t)||[]}}));this.$set(this.formmodel,"supportNetType",a)},changeSupplierStatusUpdate:function(t,e,r){this.$set(this.upform.mnc[t].suppliers[e],"status",r),this.updateSupportNetTypeUpdate()},updateSupportNetTypeUpdate:function(){for(var t,e=this,r=(null===(t=this.upform.mnc[0])||void 0===t?void 0:t.suppliers.map((function(t){return t.supplierId})))||[],i=function(){var t=e.upform.mnc[o],i=r.map((function(e,r){var i,o;return{supplierId:e,status:(null===(i=t.suppliers[r])||void 0===i?void 0:i.status)||3,choose:(null===(o=t.suppliers[r])||void 0===o?void 0:o.choose)||!1}}));e.$set(t,"suppliers",i)},o=1;o<this.upform.mnc.length;o++)i();var n=new Set;this.upform.mnc.forEach((function(t){var r=t.suppliers.some((function(r){var i=[1,2].includes(r.status),o=r.supplierId===e.upadatePreSupplier;return i&&o&&t.operatorId}));r&&t.operatorId&&n.add(t.operatorId)}));var s=new Map;this.upform.supportNetTypeDTOS&&this.upform.supportNetTypeDTOS.length>0&&this.upform.supportNetTypeDTOS.forEach((function(t){try{var e=JSON.parse(t.supportNetType);s.set(t.operatorId,e)}catch(r){console.error("解析supportNetType失败:",r)}})),this.upform.supportNetType.forEach((function(t){t.supportNetType&&t.supportNetType.length>0&&s.set(t.operatorId,t.supportNetType)}));var a=Array.from(n).map((function(t){return{operatorId:t,supportNetType:s.get(t)||[]}}));this.$set(this.upform,"supportNetType",a)},handleNetworkTypeChange:function(t,e){var r=this;this.upform.supportNetType&&this.upform.supportNetType[e]&&(this.$set(this.upform.supportNetType[e],"supportNetType",t),this.$nextTick((function(){r.$refs.upform.validateField("supportNetType.".concat(e,".supportNetType"))})))},handleSupplierChange:function(t,e){var r=this;if(this.formmodel.MNCList[0].suppliers[e].choose){this.addPreSupplier=t;for(var i=1;i<this.formmodel.MNCList.length;i++)this.$set(this.formmodel.MNCList[i].suppliers[e],"supplierId",t)}this.$nextTick((function(){r.updateSupportNetType()}))},handleSupplierChangeUpdate:function(t,e){var r=this;if(this.upform.mnc[0].suppliers[e].choose){this.upadatePreSupplier=t;for(var i=1;i<this.upform.mnc.length;i++)this.$set(this.upform.mnc[i].suppliers[e],"supplierId",t)}this.$nextTick((function(){r.updateSupportNetTypeUpdate()}))}}},u=p,c=(r("d1b9"),r("2877")),d=Object(c["a"])(u,i,o,!1,null,"7fd6d568",null);e["default"]=d.exports},"6f48":function(t,e,r){"use strict";var i=r("6d61"),o=r("6566");i("Map",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),o)},"841c":function(t,e,r){"use strict";var i=r("c65b"),o=r("d784"),n=r("825a"),s=r("7234"),a=r("1d80"),l=r("129f"),p=r("577e"),u=r("dc4a"),c=r("14c3");o("search",(function(t,e,r){return[function(e){var r=a(this),o=s(e)?void 0:u(e,t);return o?i(o,e,r):new RegExp(e)[t](p(r))},function(t){var i=n(this),o=p(t),s=r(e,i,o);if(s.done)return s.value;var a=i.lastIndex;l(a,0)||(i.lastIndex=0);var u=c(i,o);return l(i.lastIndex,a)||(i.lastIndex=a),null===u?-1:u.index}]}))},"90fe":function(t,e,r){"use strict";r.d(e,"e",(function(){return n})),r.d(e,"f",(function(){return s})),r.d(e,"a",(function(){return a})),r.d(e,"g",(function(){return l})),r.d(e,"b",(function(){return p})),r.d(e,"d",(function(){return u})),r.d(e,"c",(function(){return c}));var i=r("66df"),o="/oms/api/v1",n=function(t){return i["a"].request({url:o+"/country/queryCounrty",params:t,method:"get"})},s=function(){return i["a"].request({url:o+"/country/queryCounrtyList",method:"get"})},a=function(t){return i["a"].request({url:o+"/country/addCounrty",data:t,method:"post",contentType:"multipart/form-data"})},l=function(t){return i["a"].request({url:o+"/country/updateCounrty",data:t,method:"post",contentType:"multipart/form-data"})},p=function(t){return i["a"].request({url:o+"/country/deleteCounrty",params:t,method:"delete"})},u=function(t){return i["a"].request({url:o+"/country/getOperators",params:t,method:"get"})},c=function(t){return i["a"].request({url:o+"/operator/a2zChannelOperator",params:t,method:"get"})}},d1b9:function(t,e,r){"use strict";r("0a0e")},e472:function(t,e,r){"use strict";r.d(e,"d",(function(){return s})),r.d(e,"a",(function(){return a})),r.d(e,"e",(function(){return l})),r.d(e,"c",(function(){return p})),r.d(e,"b",(function(){return u}));var i=r("66df"),o="/rms/api/v1",n="/pms",s=function(t){return i["a"].request({url:o+"/supplier/selectSupplier",params:t,method:"get"})},a=function(t){return i["a"].request({url:o+"/supplier/saveSupplier",data:t,method:"post"})},l=function(t){return i["a"].request({url:o+"/supplier/updateSupplier",data:t,method:"post"})},p=function(t){return i["a"].request({url:o+"/supplier/queryShorten",data:t,method:"get"})},u=function(t){return i["a"].request({url:n+"/pms-realname/getMccList",data:t,method:"get"})}}}]);