/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { StoredValueAccountStatus } from './storedValueAccountStatus';
import { StoredValueTransactionType } from './storedValueTransactionType';
import { TransactionIdentification } from './transactionIdentification';
export declare class StoredValueResult {
    'Currency': string;
    'EanUpc'?: string;
    'HostTransactionID'?: TransactionIdentification;
    'ItemAmount': number;
    'ProductCode': string;
    'StoredValueAccountStatus': StoredValueAccountStatus;
    'StoredValueTransactionType': StoredValueTransactionType;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
