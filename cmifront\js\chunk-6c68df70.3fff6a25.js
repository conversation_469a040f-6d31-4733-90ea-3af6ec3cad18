(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6c68df70"],{"00b4":function(e,t,a){"use strict";a("ac1f");var i=a("23e7"),n=a("c65b"),o=a("1626"),l=a("825a"),s=a("577e"),c=function(){var e=!1,t=/[ac]/;return t.exec=function(){return e=!0,/./.exec.apply(this,arguments)},!0===t.test("abc")&&e}(),r=/./.test;i({target:"RegExp",proto:!0,forced:!c},{test:function(e){var t=l(this),a=s(e),i=t.exec;if(!o(i))return n(r,t,a);var c=n(i,t,a);return null!==c&&(l(c),!0)}})},"03ce":function(e,t,a){"use strict";a("2206")},2206:function(e,t,a){},af20:function(e,t,a){"use strict";a.r(t);a("b0c0");var i=function(){var e=this,t=e._self._c;return t("div",[t("Card",[t("Button",{directives:[{name:"has",rawName:"v-has",value:"delayPackage",expression:"'delayPackage'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"success"},on:{click:function(t){return e.doDelay()}}},[e._v("未激活套餐延期")]),t("div",{staticStyle:{"margin-top":"20px"}},[t("Table",{attrs:{columns:e.columns,data:e.tableData,ellipsis:!0,loading:e.loading},scopedSlots:e._u([{key:"fileAction",fn:function(a){var i=a.row;a.index;return[t("a",{directives:[{name:"has",rawName:"v-has",value:"download",expression:"'download'"}],staticStyle:{"margin-right":"10px"},on:{click:function(t){return e.downLoadFile(i)}}},[e._v("下载文件")])]}},{key:"status",fn:function(t){var a=t.row;t.index;return[e._v("\n            "+e._s("0"===a.status?"待审核":"1"===a.status?"审核通过":"审核未通过")+"\n          ")]}},{key:"action",fn:function(a){var i=a.row;a.index;return["1"==i.status?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"10px"},attrs:{loading:i.checkPassLoading,size:"small",type:"info"},on:{click:function(t){return e.doPass(i,"2")}}},[e._v("通过")]):e._e(),"1"==i.status?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"10px"},attrs:{loading:i.checkUnPassLoading,size:"small",type:"warning"},on:{click:function(t){return e.doPass(i,"3")}}},[e._v("不通过")]):e._e()]}}])})],1),t("div",{staticClass:"table-botton",staticStyle:{"margin-top":"15px"}},[t("Page",{attrs:{total:e.total,current:e.page,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.page=t},"on-change":e.goPage}})],1)],1),t("Modal",{attrs:{title:"套餐延期","mask-closable":!1},on:{"on-cancel":e.cancelUpload},model:{value:e.delayModal,callback:function(t){e.delayModal=t},expression:"delayModal"}},[t("Upload",{attrs:{type:"drag",action:e.uploadUrl,"on-success":e.fileSuccess,"before-upload":e.handleBeforeUpload,"on-progress":e.fileUploading}},[t("div",{staticStyle:{padding:"20px 0"}},[t("Icon",{staticStyle:{color:"#3399ff"},attrs:{type:"ios-cloud-upload",size:"52"}}),t("p",[e._v("点击或拖拽文件上传")])],1)]),e.file?t("ul",{staticClass:"ivu-upload-list"},[t("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[t("span",[t("i",{staticClass:"ivu-icon ivu-icon-ios-stats"}),e._v(e._s(e.file.name))]),t("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:e.removeFile}})])]):e._e(),t("div",{staticStyle:{width:"100%",padding:"10px 0"}},[t("Button",{attrs:{type:"primary",loading:e.downloading,icon:"ios-download"},on:{click:e.downloadTempFile}},[e._v("下载模板文件")]),t("Alert",{staticStyle:{float:"right",padding:"8px 10px 8px 10px"},attrs:{type:"warning"}},[e._v(e._s(e.message))])],1),t("div",{staticStyle:{width:"100%","margin-top":"10px"}},[t("Form",{ref:"modalData",attrs:{model:e.modalData,rules:e.rules},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleUpload.apply(null,arguments)}}},[t("FormItem",{attrs:{label:"延期时间",prop:"delayTime"}},[t("DatePicker",{staticStyle:{width:"85%"},attrs:{options:e.options3,type:"date",editable:!1,"show-week-numbers":"",placeholder:"选择延期时间..."},on:{"on-change":e.getDelayTime},model:{value:e.modalData.delayTime,callback:function(t){e.$set(e.modalData,"delayTime",t)},expression:"modalData.delayTime"}})],1)],1)],1),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:e.cancelUpload}},[e._v("取消")]),t("Button",{attrs:{type:"primary",loading:e.uploading},on:{click:e.handleUpload}},[e._v("延期")])],1),t("a",{ref:"downloadLink",staticStyle:{display:"none"}})],1),t("Table",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"modelTable",attrs:{columns:e.modelColumns,data:e.modelData}})],1)},n=[],o=(a("d81d"),a("d3b7"),a("ac1f"),a("00b4"),a("66df")),l="/cms/packageDeferred",s=function(e,t){return o["a"].request({url:l+"/packageDelayTask/check/"+e+"/"+t,method:"put"})},c=function(e){return o["a"].request({url:l+"/queryPackageDelayTask",params:e,method:"get"})},r=function(e){return o["a"].request({url:l+"/packageDelayTask/add",data:e,method:"post",contentType:"multipart/form-data"})},d={data:function(){return{options3:{disabledDate:function(e){return e&&e.valueOf()<Date.now()+1}},delayModal:!1,tableData:[],columns:[{title:"延期文件",slot:"fileAction",align:"center"},{title:"延期日期",key:"delayDate",tooltip:!0,tooltipTheme:"light",tooltipMaxWidth:600,align:"center"},{title:"延期状态",slot:"status",tooltip:!0,tooltipTheme:"light",tooltipMaxWidth:600,align:"center",render:function(e,t){var a=t.row,i=1==a.status?"#19be6b":2==a.status?"#ff0000":3==a.status?"#27A1FF":4==a.status?"#ff9900":"#515a6e",n=1==a.status?"待审核":2==a.status?"通过":3==a.status?"不通过":4==a.status?"执行中":5==a.status?"延期失败":"其他";return e("label",{style:{color:i}},n)}},{title:"审核操作",slot:"action",align:"center"}],modalData:{delayTime:""},rules:{delayTime:[{required:!0,message:"请选择延期时间",trigger:"change",pattern:/.+/}]},infoData:[{ICCID:"453543",IMSI:"45323",packageName:"新年套餐",createTime:"2019-01-01 00:00:00",expirationTime:"2022-01-01 00:00:00",currency:"人民币",amount:"400"},{ICCID:"23432442",IMSI:"434234",packageName:"元旦套餐",createTime:"2019-01-01 00:00:00",expirationTime:"2022-01-01 00:00:00",currency:"人民币",amount:"400"}],page:1,total:0,loading:!1,infoMoadl:!1,file:null,uploadUrl:"",downloading:!1,delayTime:"",uploading:!1,message:"文件仅支持csv格式文件,大小不能超过5MB",modelData:[],modelColumns:[{title:"iccid号码",key:"iccid"},{title:"HIMSI",key:"himsi"},{title:"套餐ID",key:"packageId"},{title:"未激活套餐名称",key:"name"},{title:"购买时间",key:"buyTime"},{title:"过期时间（延期前的原过期时间）",key:"expireTime"},{title:"币种",key:"currency"},{title:"金额",key:"price"}]}},computed:{},methods:{downloadTempFile:function(){this.$refs.modelTable.exportCsv({filename:"套餐延期文件模板",columns:this.modelColumns,data:this.modelData})},getDelayTime:function(e,t){this.delayTime=e},doDelay:function(){this.file=null,this.delayTime="",this.delayModal=!0},goPageFirst:function(e){var t=this;this.page=e,this.loading=!0,c({pageNum:e,pageSize:10}).then((function(e){if(!e||"0000"!=e.code)throw e;t.tableData=e.data,t.total=e.count})).catch((function(e){console.log(e)})).finally((function(){t.loading=!1})),this.tableData.length&&this.tableData.map((function(e){return t.$set(e,"checkPassLoading",!1),t.$set(e,"checkUnPassLoading",!1),e}))},doPass:function(e,t){var a=this;this.$Modal.confirm({title:"确认审核？",onOk:function(){if("2"===t)e.checkPassLoading=!0;else{if("3"!==t)return;e.checkUnPassLoading=!0}s(e.taskId,t).then((function(e){if(!e||"0000"!=e.code)throw e;a.success("审批状态已更新"),a.goPageFirst(a.page)})).catch((function(e){console.log(e)})).finally((function(){a.loading=!1}))}})},removeFile:function(){this.file=""},handleBeforeUpload:function(e){return/^.+(\.csv)$/.test(e.name)?e.size>5242880?this.$Notice.warning({title:"文件大小超过限制",desc:"文件 "+e.name+"超过了最大限制范围5MB"}):this.file=e:this.$Notice.warning({title:"文件格式不正确",desc:"文件 "+e.name+" 格式不正确，请上传.csv。"}),!1},downLoadFile:function(e){var t=e.sourceFilePath,a=e.taskName+".csv";if("download"in document.createElement("a")){var i=this.$refs.downloadLink;i.download=a,i.href=t,i.click()}else navigator.msSaveBlob(content,a)},searchByCondition:function(){this.goPageFirst(1)},goPage:function(e){this.goPageFirst(e)},cancelUpload:function(){this.delayModal=!1,this.$refs["modalData"].resetFields()},fileUploading:function(e,t,a){this.message="文件上传中、待进度条消失后再操作"},fileSuccess:function(e,t,a){this.message="请先下载模板文件，并按格式填写后上传"},handleUpload:function(){var e=this;if(this.file)if(this.delayTime){this.uploading=!0;var t=new FormData;t.append("file",this.file),t.append("delayDate",this.delayTime),r(t).then((function(t){if("0000"!==t.code)throw t;e.$Notice.success({title:"操作成功"}),e.uploading=!1,e.cancelUpload(),e.goPageFirst(1)})).catch((function(e){console.log(e)})).finally((function(){e.uploading=!1}))}else this.$Message.warning("请选择延期时间");else this.$Message.warning("请选择需要上传的文件")},success:function(e){this.$Notice.success({title:"操作成功",desc:e})}},mounted:function(){this.goPageFirst(1)},watch:{}},u=d,p=(a("03ce"),a("2877")),f=Object(p["a"])(u,i,n,!1,null,"93d6b8ea",null);t["default"]=f.exports}}]);