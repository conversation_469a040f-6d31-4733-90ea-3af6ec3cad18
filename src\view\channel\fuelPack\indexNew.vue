<template>
	<!-- 流量包管理 -->
	<ElCard class="fuelpack-card">
		<!-- 搜索表单 -->
		<div class="search-form">
			<ElForm inline>
				<ElFormItem label="ICCID" label-width="80px">
					<ElInput 
						v-model="iccid" 
						placeholder="请输入ICCID"  
						clearable 
						style="width: 200px" 
					/>
				</ElFormItem>
				<ElFormItem>
					<ElButton 
						v-if="checkPermission(['search'])" 
						:disabled="cooperationMode === '3'"  
						type="primary" 
						:loading="searchLoading" 
						@click="search"
						:icon="Search"
					>
						搜索
					</ElButton>
				</ElFormItem>
			</ElForm>
		</div>
		
		<!-- 表格 -->
		<ElTable 
			:data="tableData" 
			style="width: 100%; margin-top: 40px;" 
			v-loading="loading"
			border
			class="fuelpack-table"
		>
			<ElTableColumn 
				prop="iccid"
				label="ICCID"
				min-width="150"
				align="center"
			/>
			<ElTableColumn 
				prop="packageName"
				label="套餐名称"
				min-width="150"
				align="center"
			/>
			<ElTableColumn 
				prop="flowLimitName"
				label="流量限制类型"
				min-width="120"
				align="center"
			/>
			<ElTableColumn 
				prop="status"
				label="状态"
				min-width="100"
				align="center"
			>
				<template #default="{ row }">
					<ElTag :type="getStatusType(row.status)">
						{{ getStatusText(row.status) }}
					</ElTag>
				</template>
			</ElTableColumn>
			<ElTableColumn 
				prop="remainingFlow"
				label="剩余流量"
				min-width="120"
				align="center"
			/>
			<ElTableColumn 
				prop="expireTime"
				label="到期时间"
				min-width="160"
				align="center"
			/>
			<ElTableColumn 
				label="操作"
				min-width="120"
				align="center"
				fixed="right"
			>
				<template #default="{ row }">
					<ElButton 
						v-if="checkPermission(['buy_fuelpack'])" 
						type="primary" 
						size="small"
						@click="buyFuelpack(row)"
					>
						购买流量包
					</ElButton>
				</template>
			</ElTableColumn>
		</ElTable>
		
		<!-- 分页 -->
		<div class="pagination-container">
			<ElPagination
				:current-page="currentPage"
				:page-size="pageSize"
				:total="total"
				:page-sizes="[10, 20, 50, 100]"
				layout="total, sizes, prev, pager, next, jumper"
				@current-change="goPage"
				@size-change="handleSizeChange"
			/>
		</div>
		
		<!-- 购买流量包弹窗 -->
		<ElDialog 
			v-model="buyFuelpackModal" 
			:close-on-click-modal="true"
			width="600px"
			class="fuelpack-dialog"
		>
			<template #header>
				<span>购买流量包</span>
			</template>
			
			<ElForm 
				ref="formRef" 
				:model="form" 
				:rules="formRules" 
				label-width="140px"
				class="fuelpack-form"
			>
				<ElFormItem label="ICCID:">
					<span>{{ form.iccid }}</span>
				</ElFormItem>
				<ElFormItem label="套餐名称:">
					<span>{{ form.packageName }}</span>
				</ElFormItem>
				<ElFormItem label="流量限制类型:">
					<span>{{ form.flowLimitName }}</span>
				</ElFormItem>
				<ElFormItem label="选择流量包:" prop="packageRefuelId">
					<ElSelect 
						v-model="form.packageRefuelId"  
						style="width: 200px;" 
						@change="chooseRefuelId"
						placeholder="请选择流量包"
					>
						<ElOption 
							v-for="item in fuelpackList" 
							:key="item.refuelId"
							:value="item.refuelId" 
							:label="item.nameCn"
						/>
					</ElSelect>
					<span style="margin-left: 20px;">
						价格: ¥{{ selectedAmount.toFixed(2) }}
					</span>
				</ElFormItem>
				<ElFormItem 
					v-if="form.flowLimitType === '2'"
					label="购买类型:" 
					prop="type"
				>
					<ElRadioGroup v-model="form.type">
						<ElRadio label="1">
							{{ getPeriodText('current') }}
						</ElRadio>
						<ElRadio label="2">
							{{ getPeriodText('next') }}
							<span v-if="form.type === '2'">
								(剩余天数: {{ form.daysRemaining }})
							</span>
						</ElRadio>
					</ElRadioGroup>
				</ElFormItem>
				<ElFormItem 
					v-if="form.flowLimitType === '1'"
					label="购买数量:" 
					prop="quantity"
				>
					<ElInput 
						v-model="form.quantity" 
						placeholder="请输入购买数量" 
						clearable 
						style="width: 180px" 
						@focus="getPrice"
					/>
				</ElFormItem>
			</ElForm>

			<template #footer>
				<div class="fuelpack-footer">
					<ElButton @click="cancelModal">
						取消
					</ElButton>
					<ElButton 
						type="primary" 
						:loading="purchaseLoading" 
						@click="confirmPurchase"
					>
						确认购买
					</ElButton>
				</div>
			</template>
		</ElDialog>
	</ElCard>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElCard, ElForm, ElFormItem, ElInput, ElButton, ElTable, ElTableColumn, ElPagination, ElDialog, ElSelect, ElOption, ElRadioGroup, ElRadio, ElTag } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

defineOptions({
  name: 'ChannelFuelPackManagement'
})

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const purchaseLoading = ref(false)
const buyFuelpackModal = ref(false)

const cooperationMode = ref('')
const iccid = ref('')
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const selectedAmount = ref(0)

const form = reactive({
  iccid: '',
  packageName: '',
  flowLimitName: '',
  flowLimitType: '',
  packageRefuelId: '',
  type: '1',
  quantity: '',
  periodUnit: '',
  daysRemaining: 0
})

const tableData = ref([])
const fuelpackList = ref([])

// 表单验证规则
const formRules = reactive({
  packageRefuelId: [
    { required: true, message: '请选择流量包', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择购买类型', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入购买数量', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的数量', trigger: 'blur' }
  ]
})

// 权限检查函数
const checkPermission = (permissions: string[]): boolean => {
  return true
}

// 状态类型映射
const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': 'success',  // 激活
    '2': 'warning',  // 待激活
    '3': 'danger',   // 停用
    '4': 'info'      // 其他
  }
  return statusMap[status] || 'info'
}

// 状态文本映射
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': '激活',
    '2': '待激活',
    '3': '停用',
    '4': '其他'
  }
  return statusMap[status] || '-'
}

// 获取周期文本
const getPeriodText = (type: 'current' | 'next'): string => {
  const periodUnit = form.periodUnit
  if (type === 'current') {
    if (periodUnit === '1' || periodUnit === '2') return '当日'
    if (periodUnit === '3') return '当月'
    return '当年'
  } else {
    if (periodUnit === '1' || periodUnit === '2') return '次日'
    if (periodUnit === '3') return '次月'
    return '次年'
  }
}

// 方法
const search = () => {
  if (!iccid.value.trim()) {
    ElMessage.warning('请输入ICCID')
    return
  }

  searchLoading.value = true
  currentPage.value = 1
  loadData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

const goPage = (page: number) => {
  currentPage.value = page
  loadData()
}

const buyFuelpack = (row: any) => {
  form.iccid = row.iccid
  form.packageName = row.packageName
  form.flowLimitName = row.flowLimitName
  form.flowLimitType = row.flowLimitType
  form.periodUnit = row.periodUnit
  form.daysRemaining = row.daysRemaining || 0

  // 重置表单
  form.packageRefuelId = ''
  form.type = '1'
  form.quantity = ''
  selectedAmount.value = 0

  // 加载流量包列表
  loadFuelpackList()
  buyFuelpackModal.value = true
}

const chooseRefuelId = (refuelId: string) => {
  const selectedFuelpack = fuelpackList.value.find(item => item.refuelId === refuelId)
  if (selectedFuelpack) {
    selectedAmount.value = selectedFuelpack.amount || 0
  }
}

const getPrice = () => {
  // 获取价格逻辑
  console.log('获取价格')
}

const cancelModal = () => {
  buyFuelpackModal.value = false
}

const confirmPurchase = async () => {
  try {
    purchaseLoading.value = true
    console.log('购买流量包:', form)

    // 模拟购买过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('购买成功')
    buyFuelpackModal.value = false
    loadData()
  } catch (error) {
    console.error('购买失败:', error)
    ElMessage.error('购买失败')
  } finally {
    purchaseLoading.value = false
  }
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    console.log('搜索流量包数据:', iccid.value)

    // 模拟数据
    tableData.value = [
      {
        iccid: '89860000000000000001',
        packageName: '国际流量套餐',
        flowLimitName: '日流量限制',
        flowLimitType: '2',
        status: '1',
        remainingFlow: '500MB',
        expireTime: '2024-12-31 23:59:59',
        periodUnit: '3',
        daysRemaining: 15
      }
    ]
    total.value = 1
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
    searchLoading.value = false
  }
}

// 加载流量包列表
const loadFuelpackList = async () => {
  try {
    console.log('加载流量包列表')

    // 模拟流量包数据
    fuelpackList.value = [
      {
        refuelId: 'FP001',
        nameCn: '1GB流量包',
        amount: 50.00
      },
      {
        refuelId: 'FP002',
        nameCn: '2GB流量包',
        amount: 90.00
      },
      {
        refuelId: 'FP003',
        nameCn: '5GB流量包',
        amount: 200.00
      }
    ]
  } catch (error) {
    console.error('加载流量包列表失败:', error)
    ElMessage.error('加载流量包列表失败')
  }
}

// 组件挂载时初始化
onMounted(() => {
  cooperationMode.value = sessionStorage.getItem('cooperationMode') || ''
})
</script>

<style scoped>
.fuelpack-card {
  margin: 20px;
}

.search-form {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 15px;
  margin-right: 20px;
}

.fuelpack-table {
  margin-top: 40px;
}

.pagination-container {
  margin-top: 40px;
  text-align: right;
}

.fuelpack-form {
  padding: 20px 0;
}

.fuelpack-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.fuelpack-footer {
  display: flex;
  justify-content: center;
  gap: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form :deep(.el-form-item) {
    margin-bottom: 15px;
    margin-right: 0;
  }

  .pagination-container {
    text-align: center;
  }

  .fuelpack-form {
    padding: 10px 0;
  }
}
</style>
