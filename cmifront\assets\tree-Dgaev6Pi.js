var d=Object.defineProperty,w=Object.defineProperties;var A=Object.getOwnPropertyDescriptors;var a=Object.getOwnPropertySymbols;var C=Object.prototype.hasOwnProperty,E=Object.prototype.propertyIsEnumerable;var u=(s,t,r)=>t in s?d(s,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):s[t]=r,l=(s,t)=>{for(var r in t||(t={}))C.call(t,r)&&u(s,r,t[r]);if(a)for(var r of a(t))E.call(t,r)&&u(s,r,t[r]);return s},p=(s,t)=>w(s,A(t));const F={id:"id",children:"children",pid:"pid"},o=s=>Object.assign({},F,s),v=(s,t={})=>{t=o(t);const{children:r}=t,e=[...s];for(let n=0;n<e.length;n++)e[n][r]&&e.splice(n+1,0,...e[n][r]);return e},y=(s,t,r={})=>{r=o(r);const e=[],n=[...s],c=new Set,{children:i}=r;for(;n.length;){const h=n[0];if(c.has(h))e.pop(),n.shift();else if(c.add(h),h[i]&&n.unshift(...h[i]),e.push(h),t(h))return e}return null},D=(s,t,r={})=>{r=o(r);const e=r.children;function n(c){return c.map(i=>l({},i)).filter(i=>(i[e]=i[e]&&n(i[e]),t(i)||i[e]&&i[e].length))}return n(s)},L=(s,t)=>s.map(r=>f(r,t)),f=(s,{children:t="children",conversion:r})=>{const e=Array.isArray(s[t])&&s[t].length>0,n=r(s)||{};return e?p(l({},n),{[t]:s[t].map(c=>f(c,{children:t,conversion:r}))}):l({},n)},T=(s,t,r={})=>{s.forEach(e=>{const n=t(e,r)||e;e.children&&T(e.children,t,n)})};export{y as a,v as b,T as e,D as f,L as t};
