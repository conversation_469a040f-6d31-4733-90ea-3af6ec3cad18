<style lang="less" scoped="scoped">
	@import './login.less';
</style>

<template>
	<div class="login">
		<!-- 切换中英文 -->
		<header class="login-header">
			<div class="left-box">
				<div class="cmlink">
					<img :src='cmlink_src' width="100%" height="100%"></img>
				</div>
				<div class="line"></div>
				<div v-if="this.$i18n.locale === 'zh-CN'" class="name">全球卡合作伙伴平台</div>
				<div v-else class="name">Global Data SIM Partner Portal</div>
			</div>
			<div class="right-box">
				<p @click="getLanguage(2)" id="en">English</p>
				<p class="line">|</p>
				<p @click="getLanguage(1)" id="zh">简体中文</p>
			</div>
		</header>
		<section class="login-section">
			<div class="trending-box">
				<div class="trending">
					<div class="global">
						<img :src="global_src" alt="news" width="78%" height="78%" />
					</div>
					<div class="parallelogram">
						<div class="text-box">
							<div class="text-box-child" ref="animateBox">
								<!-- -60因为文本做了padding -->
								<p :class="inforWidth > boxWidth - 60 ? 'marquee' : 'marquees'" ref="marqueeOld">{{trending}}</p>
							</div>
						</div>
						<div class="blueline"></div>
					</div>
				</div>
				<div class="onebox"></div>
				<div class="twobox"></div>
				<div class="threebox"></div>
				<div class="greenline"></div>
			</div>
		</section>
		<!-- 登录 -->
		<div v-if="noforget" class="login-con">
			<div class="card">
				<div v-if="this.$i18n.locale === 'en-US'" class="login-box">
					<Tabs style="padding: 20px 0px 0px 0px " value="name1" @on-click="clicdTabs" id="loginTabs">
						<TabPane label="Log In" name="name1">
							<div v-if="tabOne" class="form-con">
								<login-form @on-success-valid="handleSubmit" :refresh="refresh" :language="language"></login-form>
							</div>
						</TabPane>
					</Tabs>
					<div style="display: flex; justify-content: flex-end;margin-right: 12%;">
            <Button v-if="ssoSwitchConfig" type="text" shape="circle" @click="handleSSO">SSO</Button>
						<Button type="text" shape="circle" @click="goto">Forget Password</Button>
					</div>
				</div>
				<div v-else class="login-box">
					<Tabs style="padding: 20px 0px 0px 0px " value="name1" @on-click="clicdTabs" id="loginTabs">
						<TabPane label="登录" name="name1">
							<div v-if="tabOne" class="form-con">
								<login-form @on-success-valid="handleSubmit" :refresh="refresh" :language="language"></login-form>
							</div>
						</TabPane>
					</Tabs>
					<div style="display: flex; justify-content: flex-end; margin-right: 16%;">
            <Button v-if="ssoSwitchConfig" type="text" shape="circle" @click="handleSSO">SSO登录</Button>
						<Button type="text" shape="circle" @click="goto">忘记密码</Button>
					</div>
				</div>
			</div>
		</div>
    <!-- 验证码 -->
    <div v-if="isCood" class="login-cood">
    	<div class="card">
    		<div v-if="this.$i18n.locale === 'en-US'" class="relogin-box">
    			<Tabs style="padding: 20px 0px 0px 0px " value="name1" @on-click="clicdTabs" id="loginTabs">
    				<TabPane label="Verification code verification" name="name1">
    					<div v-if="tabOne" class="form-con">
    						<login-cood @on-success-valid="handleSubmitCood" :language="language" @parent-method="parentMethod"
                :refresh="refresh" :userInfo="userInfo">
    						</login-cood>
    					</div>
    				</TabPane>
    			</Tabs>
    		</div>
    		<div v-else class="relogin-box">
    			<Tabs style="padding: 20px 0px 0px 0px " value="name1" @on-click="clicdTabs" id="loginTabs">
    				<TabPane label="验证码校验" name="name1">
    					<div v-if="tabOne" class="form-con">
    						<login-cood @on-success-valid="handleSubmitCood" :language="language" @parent-method="parentMethod"
                :refresh="refresh" :userInfo="userInfo">
    						</login-cood>
    					</div>
    				</TabPane>
    			</Tabs>
    		</div>
    	</div>
    </div>
		<!-- 忘记密码 -->
		<div v-if="isforget" class="login-conPwd">
			<div class="card">
				<div v-if="this.$i18n.locale === 'en-US'" class="relogin-box">
					<Tabs style="padding: 20px 0px 0px 0px " value="name1" @on-click="clicdTabs" id="loginTabs">
						<TabPane label="Forgot Password" name="name1">
							<div v-if="tabOne" class="form-con">
								<pwd-reset @on-success-valid="handleSubmitPwd" @on-cancel-valid="cancel" :language="language"
									@on-send-code="doSendMsgCodePwd" @timeChange="timeChangePwd" :time="time" :refresh="refresh">
								</pwd-reset>
							</div>
						</TabPane>
					</Tabs>
				</div>
				<div v-else class="relogin-box">
					<Tabs style="padding: 20px 0px 0px 0px " value="name1" @on-click="clicdTabs" id="loginTabs">
						<TabPane label="忘记密码" name="name1">
							<div v-if="tabOne" class="form-con">
								<pwd-reset @on-success-valid="handleSubmitPwd" @on-cancel-valid="cancel" :language="language"
									@on-send-code="doSendMsgCodePwd" @timeChange="timeChangePwd" :time="time" :refresh="refresh">
								</pwd-reset>
							</div>
						</TabPane>
					</Tabs>
				</div>
			</div>
		</div>
  </div>
</template>

<script>
	import LoginForm from '_c/login-form'
	import LoginMsg from '_c/login-msg'
  import LoginCood from '_c/login-cood'
	import PwdReset from '_c/pwd-reset'
	import head_src from '../../assets/images/background.jpeg'
	import {
		mapActions
	} from 'vuex'
	import {
		getSmsMsg,
		resetPwdByPhone,
    getSSOSwitchConfig,
    loginOne,
	} from '@/api/system/login'
	import {
		searchcorpid,
    getChannelCooperationModeForLogin
	} from '@/api/channel'
	import {
		getAnnouncement
	} from '@/api/system/announcement'
  import {
    getConfigure
  } from '@/api/user'
	export default {
		// inject: ['reload'], //接受App那边提供的方法
		data() {
			return {
				time: 0,
				timePwd: 0,
				src: head_src,
				cmlink_src: require("@/assets/images/cmLink-header.png"),
				global_src: require("@/assets/images/news.png"),
				refresh: false,
				tabOne: true,
				tabTwo: false,
				reSetPwd: false,
				noforget: true,
				isforget: false,
        isCood: false,
        ssoSwitchConfig: false, //SSO开关配置
				title: '登录',
				corpId: '',
				language: '',
				trending: '',
				boxWidth: '',
				inforWidth: '',
        queryParams: "", //url
        userInfo: '',
			}
		},
		components: {
			LoginForm,
			LoginMsg,
			PwdReset,
      LoginCood
		},
		// beforeMount: function() {
		//   // this.reload() //在触发切换语言后调用，相当于直接调用app里写的reload方法
		// },
		methods: {
			...mapActions([
				'handleLogin',
				'handleLogOut',
        'handleSSOLogin'
				// 'getUserInfo'
			]),

			// 显示修改密码框
			reSetPwdModal: function() {
				this.reSetPwd = true
			},
			timeChange: function(e) {
				this.time = e
			},
			timeChangePwd: function(e) {
				this.timePwd = e
			},
			// 切换登录模式
			clicdTabs: function(e) {
				if (e === 'name1') {
					this.tabOne = true
				}
			},
			// 用户名+密码第一次登录 - 登录提交
			handleSubmit(e) {
				this.handleLogOut().then(() => {
          getConfigure(e).then((resp) => {
            if (resp.data.userDetails.needVerifyCode == "1") {
              this.handleLogin(e).then(res => {
              	if (res.code === '0000') {
                  this.$store.dispatch('changeAuthenStatus', false)
                  //密码过期或者首次登录强制修改密码
                  if (this.$store.state.user.isUpdatePassword === 1) {
                  	this.$router.push({
                  		name: "pwd_mngr"
                  	});
                  	return;
                  }
                  //如果是渠道商 判断合作模式，2种或2种以上合作模式，进入选择模式页面，一种模式，代入数据
                  searchcorpid({
                  	userName: this.$store.state.user.userName,
                  }).then(res => {
                  	if (res.code == '0000') {
                  		this.corpId = res.data
                  		sessionStorage.setItem("corpId",this.corpId)
                  		if (this.corpId) {
                  			getChannelCooperationModeForLogin({
                  				corpId: this.corpId
                  			}).then(res => {
                  				if (res.code == '0000') {
                  					var cooperationMode = res.data[0]
                  					sessionStorage.setItem("modeLength", res.data.length)
                  					if (res.data.length > 1) {
                  						this.$router.push({
                  							name: "channelCooperationMode",
                  							query: {
                  								modeList: res.data
                  							}
                  						});
                  					} else {
                  						sessionStorage.setItem("cooperationMode",cooperationMode)
                  						this.$router.push({
                  							name: this.$config.homeName,
                  						})
                  					}
                  				}
                  			}).catch((err) => {
                  				console.error(err)
                  			}).finally(() => {
                  			})
                  		} else {
                  			//非渠道商
                  			this.$router.push({
                  				name: this.$config.homeName
                  			})
                  		}
                  	}
                  }).catch((err) => {
                  	console.error(err)
                  }).finally(() => {})

                  if (res.pwdBeOverdue) {
                  	this.$Notice.warning({
                  		title: '密码过期提醒',
                  		name: 'rePwd',
                  		// desc: '',
                  		render: h => {
                  			return h('div', [
                  				'你的密码即将过期，请点击',
                  				h('a', {
                  					style: {
                  						marginRight: '10px',
                  						marginLeft: '10px',
                  						color: '#ff0000'
                  					},
                  					on: {
                  						click: () => {
                  							this.$router.push({
                  								name: 'pwd_mngr'
                  							})
                  							this.$Notice.close(
                  								'rePwd')
                  						}
                  					}
                  				}, '修改密码'),
                  				'前往设置',
                  				h('p')
                  			])
                  		},
                  		duration: 0
                  	})
                  	this.$router.push({
                  		name: this.$config.homeName
                  	})
                  }
              	} else {
              		throw res
              	}
              }, err => this.refresh = !this.refresh)
            } else {
              this.noforget = false
              this.isCood = true
              this.userInfo = e
            }
          })
				})
			},
      // 验证码 二次登录- 登录提交
      handleSubmitCood(e) {
        this.handleLogOut().then(() => {
          this.handleLogin(e).then(res => {
          	if (res.code === '0000') {
              this.$store.dispatch('changeAuthenStatus', false)
              //密码过期或者首次登录强制修改密码
              if (this.$store.state.user.isUpdatePassword === 1) {
              	this.$router.push({
              		name: "pwd_mngr"
              	});
              	return;
              }
              //如果是渠道商 判断合作模式，2种或2种以上合作模式，进入选择模式页面，一种模式，代入数据
              searchcorpid({
              	userName: this.$store.state.user.userName,
              }).then(res => {
              	if (res.code == '0000') {
              		this.corpId = res.data
              		sessionStorage.setItem("corpId",this.corpId)
              		if (this.corpId) {
              			getChannelCooperationModeForLogin({
              				corpId: this.corpId
              			}).then(res => {
              				if (res.code == '0000') {
              					var cooperationMode = res.data[0]
              					sessionStorage.setItem("modeLength", res.data.length)
              					if (res.data.length > 1) {
              						this.$router.push({
              							name: "channelCooperationMode",
              							query: {
              								modeList: res.data
              							}
              						});
              					} else {
              						sessionStorage.setItem("cooperationMode",cooperationMode)
              						this.$router.push({
              							name: this.$config.homeName,
              						})
              					}
              				}
              			}).catch((err) => {
              				console.error(err)
              			}).finally(() => {
              			})
              		} else {
              			//非渠道商
              			this.$router.push({
              				name: this.$config.homeName
              			})
              		}
              	}
              }).catch((err) => {
              	console.error(err)
              }).finally(() => {})

              if (res.pwdBeOverdue) {
              	this.$Notice.warning({
              		title: '密码过期提醒',
              		name: 'rePwd',
              		// desc: '',
              		render: h => {
              			return h('div', [
              				'你的密码即将过期，请点击',
              				h('a', {
              					style: {
              						marginRight: '10px',
              						marginLeft: '10px',
              						color: '#ff0000'
              					},
              					on: {
              						click: () => {
              							this.$router.push({
              								name: 'pwd_mngr'
              							})
              							this.$Notice.close(
              								'rePwd')
              						}
              					}
              				}, '修改密码'),
              				'前往设置',
              				h('p')
              			])
              		},
              		duration: 0
              	})
              	this.$router.push({
              		name: this.$config.homeName
              	})
              }
          	} else {
          		throw res
          	}
          }, err => this.refresh = !this.refresh)
        })
      },
      //SSO登录
      handleSSO() {
        const redirectUrl = this.$getRedirectUrl()
        const paramsUrl = this.getUrlWithoutParams()
        let url =  redirectUrl + paramsUrl;
        this.redirectOnce(url);
      },
      ssoLoigin(ticket) {
        let data = {
          ticket: ticket,
          "service": this.getUrlWithoutParams(),
        }
        this.handleLogOut().then(() => {
        	this.handleSSOLogin(data).then(res => {
        		if (res.code === '0000') {
              sessionStorage.setItem("corpId","")
        			this.$store.dispatch('changeAuthenStatus', false)
        			//密码过期或者首次登录强制修改密码
        			if (this.$store.state.user.isUpdatePassword === 1) {
        				this.$router.push({
        					name: "pwd_mngr"
        				});
        				return;
        			}
        			searchcorpid({
        				userName: this.$store.state.user.userName,
        			}).then(res => {
        				if (res.code == '0000') {
        					//超管
        					this.$router.push({
        						name: this.$config.homeName
        					})
        				}
        			}).catch((err) => {
        				console.error(err)
        			}).finally(() => {})

        			if (res.pwdBeOverdue) {
        				this.$Notice.warning({
        					title: '密码过期提醒',
        					name: 'rePwd',
        					// desc: '',
        					render: h => {
        						return h('div', [
        							'你的密码即将过期，请点击',
        							h('a', {
        								style: {
        									marginRight: '10px',
        									marginLeft: '10px',
        									color: '#ff0000'
        								},
        								on: {
        									click: () => {
        										this.$router.push({
        											name: 'pwd_mngr'
        										})
        										this.$Notice.close(
        											'rePwd')
        									}
        								}
        							}, '修改密码'),
        							'前往设置',
        							h('p')
        						])
        					},
        					duration: 0
        				})
        				this.$router.push({
        					name: this.$config.homeName
        				})
        			}
        		} else {
        			throw res
        		}
        	}).catch((err) => {
            this.$router.push({
            	name: "login"
            })
            this.refresh = !this.refresh
          }).finally(() => {
          })
        })
      },
      // 获取页面url携带的参数
      parseUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        let params = {};
        for (let [key, value] of urlParams.entries()) {
          params[key] = value.trim();
        }
        this.queryParams = params;
        if (this.queryParams.ticket) {
          this.ssoLoigin(this.queryParams.ticket);
        }
      },
      // 登录页url
      getUrlWithoutParams() {
        var url = window.location.href;
        var index = url.indexOf("?");
        if (index !== -1) {
          return url.substring(0, index);
        } else {
          return url;
        }
      },
      // 重定向只执行一次
      redirectOnce(url) {
        // 检查URL的哈希部分
        if (window.location.hash !== "#redirected") {
          // 如果未设置，则设置并重定向
          window.location.replace(`${url}#redirected`);
        }
      },
			cancel: function(e) {
				this.clicdTabs("name1")
				this.isforget = false
				this.noforget = true
			},
      parentMethod() {
        this.noforget = true
        this.isCood = false
      },
			// 发送短信验证码
			doSendMsgCode: function(e) {
				getSmsMsg(e).then(res => {
					if (res.code === '0000') {
						this.$Notice.success({
							title: '操作提醒',
							desc: '短信验证码已发送至你的手机，请注意查收！'
						})
						this.time = this.$config.sendsmsCodeTimeLimit
					} else {
						throw res
					}
				}).catch((err) => {
					this.time = 0
					this.refresh = !this.refresh
				}).finally(() => {})
			},
			doSendMsgCodePwd: function(e) {
				getSmsMsg(e).then(res => {
					if (res.code === '0000') {
						this.$Notice.success({
							title: this.$t("stock.Code"),
							desc: '验证码已发送至你的手机，请注意查收！'
						})
						this.timePwd = this.$config.sendsmsCodeTimeLimit
					} else {
						throw res
					}
				}).catch((err) => {
					this.timePwd = 0
					this.refresh = !this.refresh
				}).finally(() => {})
			},
			// 忘记密码模态框显示
			goto() {
				this.noforget = false
				this.isforget = true
			},
			// 忘记密码，密码重置
			handleSubmitPwd(e) {
				resetPwdByPhone(e).then(res => {
					if (res.code === '0000') {
						this.$Notice.success({
							title: '操作提醒',
							desc: '密码修改成功，即将跳转到登录页面'
						})
						var _this = this
						setTimeout(function() {
							// _this.reSetPwd = false
							_this.noforget = true
							_this.isforget = false
						}, 3000);
					} else {
						throw res
					}
				}).catch((err) => {
					this.refresh = !this.refresh
				}).finally(() => {})
			},
			error: function(desc) {
				this.$Notice.error({
					title: '操作提醒',
					desc: desc || '服务器内部异常，请稍候再试'
				})
			},
      //切换中英文
			getLanguage(lang){
				var element1 = document.getElementById("zh")
				var element2 = document.getElementById("en")
				if (lang === 1) {
					localStorage.setItem('local','zh-CN')
					this.$i18n.locale = 'zh-CN'
					this.language = this.$i18n.locale
					element1.style.color = "rgb(228,0,119)"
					element2.style.color = "#000"
				} else {
					localStorage.setItem('local','en-US')
					this.$i18n.locale = 'en-US'
					this.language = this.$i18n.locale
					element1.style.color = "#000"
					element2.style.color = "rgb(228,0,119)"
				}
				// if (this.isforget=true){
				// 	//刷新表单
				// 	this.$refs.forgetPwForm.resetFields()

				// } else {
				// 	//刷新登录表单
				// 	this.$refs.loginForm.resetFields()
				// }
			},
			//获取公告
			getAnnouncement() {
				getAnnouncement().then(res => {
					if (res.code === '0000') {
						this.trending = res.data.notice
						this.$nextTick(() => {
							// 盒子高度
							let el = this.$refs.animateBox
							this.boxWidth = el.offsetWidth
							//文本高度
							let el2 = this.$refs.marqueeOld
							this.inforWidth = el2.offsetWidth
							// 根据数据长度设置滚动速度 1s滚动一颗字
							this.$refs.marqueeOld.style.animationDuration = this.trending.length * 0.5 + "s";
						});
					} else {
						throw res
					}
				}).catch((err) => {
					this.refresh = !this.refresh
				}).finally(() => {})
			},
      //获取SSO开关配置
      getSSOSwitchConfig() {
      	getSSOSwitchConfig().then(res => {
      		if (res.code === '0000') {
      			this.ssoSwitchConfig = res.data
      		} else {
      			throw res
      		}
      	}).catch((err) => {
      		this.refresh = !this.refresh
      	}).finally(() => {})
      },
		},
		mounted() {
			this.getAnnouncement()
      this.getSSOSwitchConfig()
      this.parseUrlParams()
			let lang = this.$i18n.locale
			this.language = this.$i18n.locale
			var element1 = document.getElementById("zh")
			var element2 = document.getElementById("en")
			if (lang === 'en-US') {
				this.title = 'Log in'
				element1.style.color = "#000"
				element2.style.color = "rgb(228,0,119)"
			} else {
				element1.style.color = "rgb(228,0,119)"
				element2.style.color = "#000"
			}
		}
	}
</script>
