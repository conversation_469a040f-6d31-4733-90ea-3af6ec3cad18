<template>
  <!-- 地址管理 -->
  <ContentWrap>
    <el-card>
      <div class="search-container">
        <el-form :model="searchForm" inline>
          <el-form-item label="收件人：">
            <el-input
              v-model="searchForm.receiverName"
              placeholder="请输入收件人姓名"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="手机号：">
            <el-input
              v-model="searchForm.phone"
              placeholder="请输入手机号"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="省份：">
            <el-select
              v-model="searchForm.province"
              placeholder="请选择省份"
              clearable
              style="width: 150px"
            >
              <el-option value="北京" label="北京" />
              <el-option value="上海" label="上海" />
              <el-option value="广东" label="广东" />
              <el-option value="江苏" label="江苏" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="hasPermission('search')"
              type="primary"
              :loading="searchLoading"
              @click="handleSearch"
            >
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button v-if="hasPermission('add')" type="success" @click="handleAdd">
              <Icon icon="ep:plus" class="mr-5px" />
              新增地址
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <div style="margin-top: 20px">
        <el-table :data="tableData" v-loading="loading" border>
          <el-table-column prop="receiverName" label="收件人" min-width="100" />
          <el-table-column prop="phone" label="手机号" min-width="120" />
          <el-table-column prop="province" label="省份" min-width="80" />
          <el-table-column prop="city" label="城市" min-width="80" />
          <el-table-column prop="district" label="区县" min-width="80" />
          <el-table-column prop="detailAddress" label="详细地址" min-width="200" />
          <el-table-column prop="postalCode" label="邮编" min-width="80" />
          <el-table-column prop="isDefault" label="默认地址" min-width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.isDefault ? 'success' : 'info'">
                {{ scope.row.isDefault ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="160" />
          <el-table-column label="操作" min-width="200" align="center" fixed="right">
            <template #default="scope">
              <el-button
                v-if="hasPermission('edit')"
                type="primary"
                size="small"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                v-if="hasPermission('setDefault') && !scope.row.isDefault"
                type="warning"
                size="small"
                @click="handleSetDefault(scope.row)"
              >
                设为默认
              </el-button>
              <el-button
                v-if="hasPermission('delete')"
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="pagination.pageSizes"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { useAddressPermission } from '@/composables/useChannelPermission'
import { useConfirmOperation } from '@/composables/useChannelApi'
import type { AddressInfo, AddressSearchForm, PaginationConfig } from '@/types/channel'

// 组件选项
defineOptions({
  name: 'ChannelAddressManagement'
})

// 权限管理
const { hasPermission } = useAddressPermission()

// 操作确认
const { executeWithConfirm } = useConfirmOperation({
  successMessage: '操作成功',
  errorMessage: '操作失败'
})

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)

// 分页配置
const pagination = reactive<PaginationConfig>({
  currentPage: 1,
  pageSize: 20,
  total: 0,
  pageSizes: [10, 20, 50, 100]
})

// 搜索表单 - 使用类型定义
const searchForm = reactive<AddressSearchForm>({
  receiverName: '',
  phone: '',
  province: ''
})

// 表格数据 - 使用具体类型
const tableData = ref<AddressInfo[]>([])

// 方法
const handleSearch = () => {
  pagination.currentPage = 1
  getTableData()
}

const handlePageChange = (page: number) => {
  pagination.currentPage = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  getTableData()
}

const handleAdd = () => {
  ElMessage.info('新增地址功能开发中...')
}

const handleEdit = (row: AddressInfo) => {
  ElMessage.info(`编辑地址: ${row.receiverName}`)
}

const handleSetDefault = async (row: AddressInfo) => {
  const success = await executeWithConfirm(
    `确定要将 "${row.receiverName}" 的地址设为默认地址吗？`,
    async () => {
      // TODO: 实现设置默认地址API调用
      console.log('设置默认地址:', row.id)
    }
  )

  if (success) {
    getTableData()
  }
}

const handleDelete = async (row: AddressInfo) => {
  if (row.isDefault) {
    ElMessage.warning('默认地址不能删除')
    return
  }

  const success = await executeWithConfirm(
    `确定要删除 "${row.receiverName}" 的地址吗？`,
    async () => {
      // TODO: 实现删除地址API调用
      console.log('删除地址:', row.id)
    }
  )

  if (success) {
    getTableData()
  }
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        receiverName: '张三',
        phone: '13800138000',
        province: '北京',
        city: '北京市',
        district: '朝阳区',
        detailAddress: '建国门外大街1号',
        postalCode: '100000',
        isDefault: true,
        createTime: '2024-01-01 10:00:00'
      },
      {
        id: 2,
        receiverName: '李四',
        phone: '13900139000',
        province: '上海',
        city: '上海市',
        district: '浦东新区',
        detailAddress: '陆家嘴环路1000号',
        postalCode: '200000',
        isDefault: false,
        createTime: '2024-01-02 11:00:00'
      }
    ]
    pagination.total = 2
  } catch (error) {
    ElMessage.error('获取地址数据失败')
    console.error('获取地址数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.search-container {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}
</style>
