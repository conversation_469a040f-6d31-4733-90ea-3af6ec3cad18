"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrackFormatType = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var TrackFormatType;
(function (TrackFormatType) {
    TrackFormatType[TrackFormatType["Aamva"] = 'AAMVA'] = "Aamva";
    TrackFormatType[TrackFormatType["Cmc7"] = 'CMC-7'] = "Cmc7";
    TrackFormatType[TrackFormatType["E13B"] = 'E-13B'] = "E13B";
    TrackFormatType[TrackFormatType["Iso"] = 'ISO'] = "Iso";
    TrackFormatType[TrackFormatType["JisI"] = 'JIS-I'] = "JisI";
    TrackFormatType[TrackFormatType["JisIi"] = 'JIS-II'] = "JisIi";
})(TrackFormatType = exports.TrackFormatType || (exports.TrackFormatType = {}));
//# sourceMappingURL=trackFormatType.js.map