<template>
  <!-- 库存管理 -->
  <ContentWrap>
    <el-card>
      <div style="width: 100%; margin-top: 20px;">
        <div class="search-form">
          <el-form :model="searchForm" inline>
            <el-form-item label="订单号：">
              <el-input 
                v-model="searchForm.taskName" 
                placeholder="请输入订单号" 
                clearable
                style="width: 200px;"
              />
            </el-form-item>
            <el-form-item label="时间段：">
              <el-date-picker
                v-model="searchForm.timeSlot"
                type="daterange"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px;"
              />
            </el-form-item>
            <el-form-item>
              <el-button 
                v-if="hasPermission('search')"
                type="primary" 
                :loading="searchLoading"
                @click="handleSearch"
              >
                <Icon icon="ep:search" class="mr-5px" />
                搜索
              </el-button>
              <el-button 
                v-if="hasPermission('showiccid')"
                type="warning"
                @click="showICCID"
              >
                显示ICCID
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 表格 -->
        <div style="margin-top: 20px;">
          <el-table :data="tableData" v-loading="loading" border>
            <el-table-column prop="taskName" label="任务名称" min-width="150">
              <template #default="{ row }">
                <strong>{{ row.taskName }}</strong>
              </template>
            </el-table-column>
            <el-table-column prop="taskNum" label="任务编号" min-width="150">
              <template #default="{ row }">
                <strong>{{ row.taskNum }}</strong>
              </template>
            </el-table-column>
            <el-table-column prop="totalCount" label="总数量" min-width="100" align="center" />
            <el-table-column prop="successCount" label="成功数量" min-width="100" align="center" />
            <el-table-column prop="failCount" label="失败数量" min-width="100" align="center" />
            <el-table-column prop="status" label="状态" min-width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" min-width="160">
              <template #default="{ row }">
                <strong>{{ row.createTime }}</strong>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="120" align="center" fixed="right">
              <template #default="{ row }">
                <el-button 
                  v-if="hasPermission('view')"
                  type="warning" 
                  size="small"
                  @click="viewDetails(row)"
                >
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 分页 -->
        <div style="margin-top: 20px; text-align: right;">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { useRouter } from 'vue-router'

// 权限检查函数 - 临时返回true以确保页面正常显示
const hasPermission = (permission: string): boolean => {
  console.log(`🔍 [库存管理权限检查] ${permission}: 允许访问`)
  return true // 临时返回true，后续需要实现真实的权限检查逻辑
}

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const searchForm = reactive({
  taskName: '',
  timeSlot: [] as string[]
})

const tableData = ref<any[]>([])

// 方法
const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '1': 'success',
    '2': 'warning',
    '3': 'danger',
    '0': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '1': '已完成',
    '2': '进行中',
    '3': '失败',
    '0': '待处理'
  }
  return statusMap[status] || '未知'
}

const handleSearch = () => {
  currentPage.value = 1
  getTableData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getTableData()
}

const viewDetails = (row: any) => {
  // 跳转到库存详情页面
  router.push({
    path: '/newcmi/channel/stock/card-list',
    query: { 
      taskId: row.id,
      taskName: row.taskName 
    }
  })
}

const showICCID = () => {
  // 跳转到ICCID显示页面
  router.push({
    path: '/newcmi/channel/stock/show-iccid'
  })
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        taskName: '库存补充任务001',
        taskNum: 'TASK20240101001',
        totalCount: 1000,
        successCount: 950,
        failCount: 50,
        status: '1',
        createTime: '2024-01-01 10:00:00'
      },
      {
        id: 2,
        taskName: '库存补充任务002',
        taskNum: 'TASK20240101002',
        totalCount: 500,
        successCount: 300,
        failCount: 0,
        status: '2',
        createTime: '2024-01-01 11:00:00'
      }
    ]
    total.value = 2
  } catch (error) {
    ElMessage.error('获取库存数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.search-form {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-bottom: 0;
}
</style>
