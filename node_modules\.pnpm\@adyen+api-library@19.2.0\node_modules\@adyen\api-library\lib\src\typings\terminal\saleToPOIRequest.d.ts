/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { AbortRequest } from './abortRequest';
import { AdminRequest } from './adminRequest';
import { BalanceInquiryRequest } from './balanceInquiryRequest';
import { BatchRequest } from './batchRequest';
import { CardAcquisitionRequest } from './cardAcquisitionRequest';
import { CardReaderAPDURequest } from './cardReaderAPDURequest';
import { CardReaderInitRequest } from './cardReaderInitRequest';
import { CardReaderPowerOffRequest } from './cardReaderPowerOffRequest';
import { ContentInformation } from './contentInformation';
import { DiagnosisRequest } from './diagnosisRequest';
import { DisplayRequest } from './displayRequest';
import { EnableServiceRequest } from './enableServiceRequest';
import { EventNotification } from './eventNotification';
import { GetTotalsRequest } from './getTotalsRequest';
import { InputRequest } from './inputRequest';
import { InputUpdate } from './inputUpdate';
import { LoginRequest } from './loginRequest';
import { LogoutRequest } from './logoutRequest';
import { LoyaltyRequest } from './loyaltyRequest';
import { MessageHeader } from './messageHeader';
import { PINRequest } from './pINRequest';
import { PaymentRequest } from './paymentRequest';
import { PrintRequest } from './printRequest';
import { ReconciliationRequest } from './reconciliationRequest';
import { ReversalRequest } from './reversalRequest';
import { SoundRequest } from './soundRequest';
import { StoredValueRequest } from './storedValueRequest';
import { TransactionStatusRequest } from './transactionStatusRequest';
import { TransmitRequest } from './transmitRequest';
export declare class SaleToPOIRequest {
    'AbortRequest'?: AbortRequest;
    'AdminRequest'?: AdminRequest;
    'BalanceInquiryRequest'?: BalanceInquiryRequest;
    'BatchRequest'?: BatchRequest;
    'CardAcquisitionRequest'?: CardAcquisitionRequest;
    'CardReaderAPDURequest'?: CardReaderAPDURequest;
    'CardReaderInitRequest'?: CardReaderInitRequest;
    'CardReaderPowerOffRequest'?: CardReaderPowerOffRequest;
    'DiagnosisRequest'?: DiagnosisRequest;
    'DisplayRequest'?: DisplayRequest;
    'EnableServiceRequest'?: EnableServiceRequest;
    'EventNotification'?: EventNotification;
    'GetTotalsRequest'?: GetTotalsRequest;
    'InputRequest'?: InputRequest;
    'InputUpdate'?: InputUpdate;
    'LoginRequest'?: LoginRequest;
    'LogoutRequest'?: LogoutRequest;
    'LoyaltyRequest'?: LoyaltyRequest;
    'MessageHeader': MessageHeader;
    'PaymentRequest'?: PaymentRequest;
    'PINRequest'?: PINRequest;
    'PrintRequest'?: PrintRequest;
    'ReconciliationRequest'?: ReconciliationRequest;
    'ReversalRequest'?: ReversalRequest;
    'SecurityTrailer'?: ContentInformation;
    'SoundRequest'?: SoundRequest;
    'StoredValueRequest'?: StoredValueRequest;
    'TransactionStatusRequest'?: TransactionStatusRequest;
    'TransmitRequest'?: TransmitRequest;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
