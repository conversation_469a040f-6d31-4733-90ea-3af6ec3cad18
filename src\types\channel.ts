/**
 * Channel 模块统一类型定义
 * 为渠道自服务模块提供类型安全支持
 */

// ==================== 通用类型 ====================

/** 基础实体接口 */
export interface BaseEntity {
  id: string | number
  createTime?: string
  updateTime?: string
}

/** 分页配置 */
export interface PaginationConfig {
  currentPage: number
  pageSize: number
  total: number
  pageSizes: number[]
}

/** 搜索表单基础接口 */
export interface BaseSearchForm {
  [key: string]: string | number | null | string[] | undefined
}

/** 权限检查函数类型 */
export type PermissionChecker = (permission: string) => boolean

// ==================== 地址管理 ====================

/** 地址信息 */
export interface AddressInfo extends BaseEntity {
  receiverName: string
  phone: string
  province: string
  city: string
  district: string
  detailAddress: string
  postalCode: string
  isDefault: boolean
}

/** 地址搜索表单 */
export interface AddressSearchForm extends BaseSearchForm {
  receiverName: string
  phone: string
  province: string
}

// ==================== 套餐管理 ====================

/** 套餐信息 */
export interface PackageInfo extends BaseEntity {
  packageName: string
  packageType: number
  dataAmount: number // 流量，单位MB
  voiceMinutes: number // 通话分钟数
  smsCount: number // 短信条数
  monthlyFee: number // 月费
  validDays: number // 有效期天数
  status: number // 状态：1-上架，0-下架
}

/** 套餐搜索表单 */
export interface PackageSearchForm extends BaseSearchForm {
  packageName: string
  packageType: number | null
  status: number | null
}

// ==================== 订单管理 ====================

/** 订单信息 */
export interface OrderInfo extends BaseEntity {
  orderNo: string
  packageName: string
  iccid: string
  msisdn: string
  orderType: string // 订单类型：1-套餐订购，2-流量充值，3-套餐变更，7-其他
  amount: number
  status: string // 状态：0-待处理，1-已完成，2-处理中，3-失败
  attributableChannel: string
  isUsed: boolean
}

/** 订单搜索表单 */
export interface OrderSearchForm extends BaseSearchForm {
  packageName: string
  iccid: string
  timeSlot: string[]
  attributableChannel: string
}

/** 渠道信息 */
export interface ChannelInfo {
  corpId: string
  corpName: string
}

// ==================== 服务支持 ====================

/** 工单信息 */
export interface TicketInfo extends BaseEntity {
  ticketNo: string
  title: string
  issueType: number // 问题类型：1-技术问题，2-账单问题，3-服务问题，4-其他
  priority: number // 优先级：1-紧急，2-高，3-中，4-低
  status: number // 状态：1-待处理，2-处理中，3-已解决，4-已关闭
  assignee: string
}

/** 工单搜索表单 */
export interface TicketSearchForm extends BaseSearchForm {
  ticketNo: string
  issueType: number | null
  status: number | null
}

// ==================== 充值管理 ====================

/** 账户信息 */
export interface AccountInfo {
  cooperationMode: string // 合作模式
  channelType: string // 渠道类型
  currencyCode: string // 币种
  marketingAmount: number // 营销账户金额
  creditAmount: number // 信用账户金额
}

/** 账户余额表格数据 */
export interface AccountBalanceData {
  deposit: string
  marketingAmount: number
  creditAmount: number
}

// ==================== 组件 Props 类型 ====================

/** 搜索表单组件 Props */
export interface SearchFormProps {
  modelValue: BaseSearchForm
  loading?: boolean
  fields: SearchFieldConfig[]
}

/** 搜索字段配置 */
export interface SearchFieldConfig {
  key: string
  label: string
  type: 'input' | 'select' | 'date' | 'daterange'
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  style?: Record<string, any>
}

/** 数据表格组件 Props */
export interface DataTableProps<T = any> {
  data: T[]
  loading?: boolean
  columns: TableColumnConfig[]
  pagination?: PaginationConfig
  border?: boolean
}

/** 表格列配置 */
export interface TableColumnConfig {
  prop?: string
  label: string
  minWidth?: string | number
  align?: 'left' | 'center' | 'right'
  fixed?: 'left' | 'right'
  type?: 'selection' | 'index' | 'expand'
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
  slot?: string
}

// ==================== API 响应类型 ====================

/** API 响应基础结构 */
export interface ApiResponse<T = any> {
  code: string | number
  data: T
  message?: string
  success?: boolean
}

/** 分页响应数据 */
export interface PageResponse<T = any> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
}

// ==================== 枚举类型 ====================

/** 套餐类型枚举 */
export enum PackageType {
  BASIC = 1, // 基础套餐
  ADVANCED = 2, // 高级套餐
  ENTERPRISE = 3 // 企业套餐
}

/** 订单类型枚举 */
export enum OrderType {
  PACKAGE_SUBSCRIBE = '1', // 套餐订购
  TRAFFIC_RECHARGE = '2', // 流量充值
  PACKAGE_CHANGE = '3', // 套餐变更
  OTHER = '7' // 其他
}

/** 订单状态枚举 */
export enum OrderStatus {
  PENDING = '0', // 待处理
  COMPLETED = '1', // 已完成
  PROCESSING = '2', // 处理中
  FAILED = '3' // 失败
}

/** 工单问题类型枚举 */
export enum IssueType {
  TECHNICAL = 1, // 技术问题
  BILLING = 2, // 账单问题
  SERVICE = 3, // 服务问题
  OTHER = 4 // 其他
}

/** 工单优先级枚举 */
export enum Priority {
  URGENT = 1, // 紧急
  HIGH = 2, // 高
  MEDIUM = 3, // 中
  LOW = 4 // 低
}

/** 工单状态枚举 */
export enum TicketStatus {
  PENDING = 1, // 待处理
  PROCESSING = 2, // 处理中
  RESOLVED = 3, // 已解决
  CLOSED = 4 // 已关闭
}
