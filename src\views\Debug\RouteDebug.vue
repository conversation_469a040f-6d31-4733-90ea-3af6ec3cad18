<template>
  <div class="route-debug">
    <ContentWrap>
      <h1>路由调试页面</h1>
      
      <el-card class="mb-20px">
        <template #header>
          <h3>用户信息</h3>
        </template>
        <div>
          <p><strong>用户ID:</strong> {{ userStore.getUserId }}</p>
          <p><strong>用户名:</strong> {{ userStore.getUserName }}</p>
          <p><strong>角色ID:</strong> {{ userStore.getRoleId }}</p>
          <p><strong>是否已登录:</strong> {{ !!userStore.getUserInfo }}</p>
        </div>
      </el-card>

      <el-card class="mb-20px">
        <template #header>
          <h3>用户权限</h3>
        </template>
        <div>
          <p><strong>权限数量:</strong> {{ userPermissions.length }}</p>
          <p><strong>权限列表:</strong></p>
          <ul>
            <li v-for="permission in userPermissions" :key="permission">
              {{ permission }}
            </li>
          </ul>
        </div>
      </el-card>

      <el-card class="mb-20px">
        <template #header>
          <h3>动态路由</h3>
        </template>
        <div>
          <p><strong>路由数量:</strong> {{ roleRouters.length }}</p>
          <p><strong>路由列表:</strong></p>
          <div v-for="route in roleRouters" :key="route.path" class="route-item">
            <h4>{{ route.name }} ({{ route.path }})</h4>
            <p>组件: {{ route.component }}</p>
            <div v-if="route.children && route.children.length > 0" class="children">
              <p><strong>子路由:</strong></p>
              <ul>
                <li v-for="child in route.children" :key="child.path">
                  {{ child.name }} ({{ child.path }}) - {{ child.component }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="mb-20px">
        <template #header>
          <h3>当前路由信息</h3>
        </template>
        <div>
          <p><strong>当前路径:</strong> {{ $route.path }}</p>
          <p><strong>当前路由名:</strong> {{ $route.name }}</p>
          <p><strong>路由参数:</strong> {{ JSON.stringify($route.params) }}</p>
          <p><strong>查询参数:</strong> {{ JSON.stringify($route.query) }}</p>
        </div>
      </el-card>

      <el-card>
        <template #header>
          <h3>测试跳转</h3>
        </template>
        <div>
          <el-button @click="testJumpToHome" type="primary">跳转到首页</el-button>
          <el-button @click="testJumpToPersonal" type="success">跳转到个人中心</el-button>
          <el-button @click="refreshUserInfo" type="warning">刷新用户信息</el-button>
        </div>
      </el-card>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { ContentWrap } from '@/components/ContentWrap'
import { ElCard, ElButton } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

const userPermissions = computed(() => userStore.getPermissions || [])
const roleRouters = computed(() => userStore.getRoleRouters || [])

const testJumpToHome = () => {
  console.log('🔍 [调试页面] 尝试跳转到首页')
  router.push('/dashboard/analysis')
}

const testJumpToPersonal = () => {
  console.log('🔍 [调试页面] 尝试跳转到个人中心')
  router.push('/personal/personal-center')
}

const refreshUserInfo = () => {
  console.log('🔍 [调试页面] 刷新用户信息')
  console.log('用户信息:', userStore.getUserInfo)
  console.log('用户权限:', userStore.getPermissions)
  console.log('用户路由:', userStore.getRoleRouters)
}
</script>

<style lang="less" scoped>
.route-debug {
  .route-item {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 12px;
    
    h4 {
      margin: 0 0 8px 0;
      color: #409eff;
    }
    
    .children {
      margin-top: 12px;
      padding-left: 20px;
      border-left: 2px solid #e4e7ed;
    }
  }
}
</style>
