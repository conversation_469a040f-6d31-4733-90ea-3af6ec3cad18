<template>
  <!-- 押金/账户管理 -->
  <ContentWrap>
    <el-card style="z-index: auto;">
      <el-form ref="formRef" :model="formInline" inline style="margin: 50px 0; font-weight: bold;">
        <el-form-item label="合作模式" :label-width="120">
          <el-input v-model="formInline.cooperationMode" readonly style="width: 250px;" />
        </el-form-item>
        <el-form-item label="渠道模式" :label-width="100">
          <el-input v-model="formInline.channelType" readonly style="width: 250px;" />
        </el-form-item>
        <el-form-item label="币种" :label-width="80">
          <el-input v-model="formInline.currencyCode" readonly style="width: 250px;" />
        </el-form-item>
      </el-form>
      
      <el-table :data="tableData" border style="width: 100%;" v-loading="loading">
        <el-table-column prop="deposit" label="" min-width="200" align="center" />
        <el-table-column label="营销账户" min-width="200" align="center">
          <template #default="{ row }">
            <div class="cell-content">
              <div style="margin: 10px 0;">{{ row.marketingAmount }}</div>
              <el-button 
                v-if="hasPermission('marketingAccountDetails')"
                type="info" 
                size="small" 
                plain
                @click="handleDetailClick(row, 'marketing')" 
                style="margin: 0 0 10px 0;"
              >
                营销账户详情
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="信用账户" min-width="200" align="center">
          <template #default="{ row }">
            <div class="cell-content">
              <div style="margin: 10px 0;">{{ row.creditAmount }}</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div style="margin: 50px 0; font-weight: bold; display: flex; justify-content: center;">
        <el-button 
          v-if="hasPermission('Packagedetails')"
          :disabled="cooperationMode === '3'" 
          size="large" 
          type="success" 
          style="width: 150px; margin-right: 100px;"
          @click="details(1)"
        >
          可购套餐
        </el-button>
        <el-button 
          v-if="hasPermission('streamdetails')"
          :disabled="['2', '3'].includes(cooperationMode)" 
          size="large" 
          type="warning" 
          style="min-width: 150px; margin-right: 100px;"
          @click="details(2)"
        >
          流量详情
        </el-button>
        <el-button 
          v-if="hasPermission('recharge')"
          :disabled="cooperationMode === '3'" 
          size="large" 
          type="danger" 
          style="width: 150px;"
          @click="recharge"
        >
          充值
        </el-button>
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { useRouter } from 'vue-router'

// 权限检查函数 - 需要根据实际权限系统实现
const hasPermission = (permission: string): boolean => {
  // TODO: 实现权限检查逻辑
  return true
}

const router = useRouter()

// 响应式数据
const loading = ref(false)
const corpId = ref('')
const cooperationMode = ref('')

const formInline = reactive({
  cooperationMode: '',
  channelType: '',
  currencyCode: ''
})

const tableData = ref([
  {
    deposit: '账户余额',
    marketingAmount: 0,
    creditAmount: 0
  }
])

// 方法
const handleDetailClick = (row: any, type: string) => {
  if (type === 'marketing') {
    // 跳转到营销账户详情页面
    router.push({
      path: '/newcmi/channel/deposit/marketing-account',
      query: { corpId: corpId.value }
    })
  }
}

const details = (type: number) => {
  if (type === 1) {
    // 跳转到可购套餐页面
    router.push({
      path: '/newcmi/channel/deposit/meal-list',
      query: { corpId: corpId.value }
    })
  } else if (type === 2) {
    // 跳转到流量详情页面
    router.push({
      path: '/newcmi/channel/deposit/stream-list',
      query: { corpId: corpId.value }
    })
  }
}

const recharge = () => {
  // 跳转到充值页面
  router.push({
    path: '/newcmi/channel/deposit/offline-payment',
    query: { corpId: corpId.value }
  })
}

// 获取账户管理数据
const getAccountData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // const response = await getAccountManagement()
    // 模拟数据
    formInline.cooperationMode = '预付费'
    formInline.channelType = '直销'
    formInline.currencyCode = 'CNY'
    
    tableData.value = [
      {
        deposit: '账户余额',
        marketingAmount: 10000.00,
        creditAmount: 5000.00
      }
    ]
  } catch (error) {
    ElMessage.error('获取账户数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getAccountData()
})
</script>

<style scoped>
.cell-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
