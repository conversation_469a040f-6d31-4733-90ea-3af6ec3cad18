(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3da7f730"],{8038:function(t,e,n){},"803e":function(t,e,n){"use strict";n.r(e);var o=function(){var t=this,e=t._self._c;return e("Card",{staticStyle:{width:"100%",padiing:"16px"}},[e("Table",{ref:"selection",attrs:{columns:t.columns,data:t.tableData,ellipsis:!0,loading:t.tableLoading}}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,"page-size":t.pageSize,current:t.page,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.goPage}}),e("div",{staticStyle:{"text-align":"center","margin-top":"20px"}},[e("Button",{staticStyle:{"margin-right":"8px"},on:{click:t.back}},[t._v("返回")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],attrs:{type:"primary",icon:"ios-cloud-download-outline",loading:t.exportLoading},on:{click:t.exportbill}},[t._v("导出")])],1),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v("导出提示")]),e("FormItem",{attrs:{label:"你本次导出任务ID为:"}},[e("span",{staticStyle:{width:"100px"}},[t._v(t._s(t.taskId))])]),e("FormItem",{attrs:{label:"你本次导出的文件名为:"}},[e("span",[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v("请前往下载管理-下载列表查看及下载。")])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v("立即前往")])],1)])],1)},a=[],r=(n("14d9"),n("b64b"),n("f7fa")),i=function(){var t=this,e=t._self._c;return t.s_showByRow?e("table",{staticClass:"mailTable",style:t.styleObject},t._l(t.tableData,(function(n,o){return e("tr",[e("td",0===o?{staticStyle:{"background-color":"#0573C8",border:"1px solid #DCDEE2",height:"40px",width:"200px","padding-left":"10px","text-align":"center",color:"#333"}}:{staticStyle:{border:"1px solid #DCDEE2",height:"40px",width:"200px","text-align":"center","padding-left":"10px",color:"#333"}},[t._v(t._s(n.key))]),e("td",0===o?{staticStyle:{"background-color":"#0573C8",border:"1px solid #DCDEE2",height:"40px",width:"200px","text-align":"center","padding-left":"10px",color:"#333"}}:{staticStyle:{border:"1px solid #DCDEE2",height:"40px",width:"200px","text-align":"center","padding-left":"10px",color:"#333"}},[t._v(t._s(n.value))])])})),0):t._e()},l=[],u={data:function(){return{styleObject:{},s_showByRow:!0}},props:["tableData","tableStyle","showByRow"],computed:{rowCount:function(){return Math.ceil(this.tableData.length)}},created:function(){this.styleObject=this.tableStyle,void 0!==this.showByRow&&(this.s_showByRow=this.showByRow)}},s=u,c=(n("f8e2"),n("2877")),d=Object(c["a"])(s,i,l,!1,null,null,null),p=d.exports,h={components:{mailTable:p},data:function(){return{taskName:"",taskId:"",billInfo:[],exportModal:!1,tableData:[],total:0,pageSize:10,page:1,tableLoading:!1,exportLoading:!1,columns:[{title:"账单月份",key:"statTime",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"所属企业",key:"corpName",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"国家/地区",key:"countryCn",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"落地运营商",key:"operatorName",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"上行流量/G",key:"flowUpLinkTotal",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"下行流量/G",key:"flowDownLinkTotal",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"流量总量/G",key:"flowByteTotal",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"流量单价/G",key:"amount",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"计费币种",key:"currencyCodeName",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"账单金额",key:"totalPrice",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3}]}},methods:{loadByPage:function(t){var e=this;e.tableLoading=!0;var n=10,o=t;this.page=t,Object(r["n"])({amount:this.billInfo.amount,corpId:this.billInfo.corpId,userId:this.$store.state.user.userId,roleId:this.$store.state.user.roleId,currencyCode:this.billInfo.currencyCode,mcc:this.billInfo.mcc,month:this.billInfo.statTime,pageNum:o,pageSize:n}).then((function(t){if(!t||"0000"!=t.code)throw t;var n=t.data;e.tableData=n,e.total=t.count,e.tableLoading=!1})).catch((function(t){e.tableLoading=!1}))},goPage:function(t){this.page=t,this.loadByPage(t)},back:function(){this.$router.go(-1)},exportbill:function(){var t=this;Object(r["l"])({amount:this.billInfo.amount,corpId:this.billInfo.corpId,currencyCode:this.billInfo.currencyCode,mcc:this.billInfo.mcc,month:this.billInfo.statTime,userId:this.$store.state.user.userId,roleId:this.$store.state.user.roleId,pageNum:this.page,pageSize:10}).then((function(e){e&&"0000"==e.code&&(t.exportModal=!0,t.taskId=e.data.taskId,t.taskName=e.data.taskName),t.downloading=!1})).catch((function(e){return t.downloading=!1}))},cancelModal:function(){this.exportModal=!1},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModal=!1}},mounted:function(){var t=JSON.parse(decodeURIComponent(this.$route.query.billInfo));this.billInfo=t,this.loadByPage(1)}},m=h,f=Object(c["a"])(m,o,a,!1,null,null,null);e["default"]=f.exports},f7fa:function(t,e,n){"use strict";n.d(e,"p",(function(){return r})),n.d(e,"r",(function(){return i})),n.d(e,"a",(function(){return l})),n.d(e,"u",(function(){return u})),n.d(e,"d",(function(){return s})),n.d(e,"f",(function(){return c})),n.d(e,"o",(function(){return d})),n.d(e,"i",(function(){return p})),n.d(e,"b",(function(){return h})),n.d(e,"v",(function(){return m})),n.d(e,"e",(function(){return f})),n.d(e,"h",(function(){return g})),n.d(e,"g",(function(){return x})),n.d(e,"s",(function(){return y})),n.d(e,"l",(function(){return b})),n.d(e,"k",(function(){return w})),n.d(e,"t",(function(){return k})),n.d(e,"m",(function(){return I})),n.d(e,"n",(function(){return v})),n.d(e,"j",(function(){return q})),n.d(e,"w",(function(){return _})),n.d(e,"c",(function(){return M})),n.d(e,"q",(function(){return S}));var o=n("66df"),a="/cms/api/v1",r=function(t){return o["a"].request({url:a+"/terminal/pages",params:t,method:"get"})},i=function(t){return o["a"].request({url:a+"/terminal/settleRule/queryList",params:t,method:"get"})},l=function(t){return o["a"].request({url:a+"/terminal",data:t,method:"post"})},u=function(t,e){return o["a"].request({url:a+"/terminal/"+t,data:e,method:"put"})},s=function(t,e){return o["a"].request({url:a+"/terminal/audit/"+t,params:e,method:"put"})},c=function(t){return o["a"].request({url:a+"/terminal",data:t,method:"delete"})},d=function(t){return o["a"].request({url:a+"/terminal/details",params:t,method:"get"})},p=function(t){return o["a"].request({url:a+"/terminal/details/export",params:t,responseType:"blob",method:"get"})},h=function(t){return o["a"].request({url:a+"/terminal/settleRule/add",data:t,method:"post"})},m=function(t){return o["a"].request({url:a+"/terminal/settleRule/update",data:t,method:"put"})},f=function(t){return o["a"].request({url:"/pms/api/v1/cardPool/checkPackage",params:t,method:"get"})},g=function(t){return o["a"].request({url:a+"/terminal/settleRule/delete/"+t,method:"delete"})},x=function(t){return o["a"].request({url:a+"/terminal/settleRule/deleteBatch",data:t,method:"post"})},y=function(t){return o["a"].request({url:"/stat/cdr/flow/get/list",params:t,method:"get"})},b=function(t){return o["a"].request({url:"/stat/cdr/flow/export/details",params:t,method:"get"})},w=function(t){return o["a"].request({url:"/stat/cdr/flow/export/info",params:t,method:"get"})},k=function(t){return o["a"].request({url:"/stat/cdr/flow/get/info",params:t,method:"get"})},I=function(t){return o["a"].request({url:"/stat/cdr/flow/export/list",params:t,method:"get"})},v=function(t){return o["a"].request({url:"/stat/cdr/flow/get/details",params:t,method:"get"})},q=function(t){return o["a"].request({url:"/stat/cdr/flow/export/info/all",params:t,method:"get"})},_=function(t){return o["a"].request({url:a+"/terminal/plmnlist/update",data:t,method:"post",headers:{"Content-Type":"application/json;charset=UTF-8"}})},M=function(t){return o["a"].request({url:a+"/terminal/plmnlist/createByFile",data:t,method:"post",contentType:"multipart/form-data"})},S=function(t){return o["a"].request({url:a+"/terminal/plmnlist/get",params:t,method:"get"})}},f8e2:function(t,e,n){"use strict";n("8038")}}]);