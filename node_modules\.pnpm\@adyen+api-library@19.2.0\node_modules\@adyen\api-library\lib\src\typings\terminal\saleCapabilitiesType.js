"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SaleCapabilitiesType = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var SaleCapabilitiesType;
(function (SaleCapabilitiesType) {
    SaleCapabilitiesType[SaleCapabilitiesType["CashierDisplay"] = 'CashierDisplay'] = "CashierDisplay";
    SaleCapabilitiesType[SaleCapabilitiesType["CashierError"] = 'CashierError'] = "CashierError";
    SaleCapabilitiesType[SaleCapabilitiesType["CashierInput"] = 'CashierInput'] = "CashierInput";
    SaleCapabilitiesType[SaleCapabilitiesType["CashierStatus"] = 'CashierStatus'] = "CashierStatus";
    SaleCapabilitiesType[SaleCapabilitiesType["CustomerAssistance"] = 'CustomerAssistance'] = "CustomerAssistance";
    SaleCapabilitiesType[SaleCapabilitiesType["CustomerDisplay"] = 'CustomerDisplay'] = "CustomerDisplay";
    SaleCapabilitiesType[SaleCapabilitiesType["CustomerError"] = 'CustomerError'] = "CustomerError";
    SaleCapabilitiesType[SaleCapabilitiesType["CustomerInput"] = 'CustomerInput'] = "CustomerInput";
    SaleCapabilitiesType[SaleCapabilitiesType["EmvContactless"] = 'EMVContactless'] = "EmvContactless";
    SaleCapabilitiesType[SaleCapabilitiesType["Icc"] = 'ICC'] = "Icc";
    SaleCapabilitiesType[SaleCapabilitiesType["MagStripe"] = 'MagStripe'] = "MagStripe";
    SaleCapabilitiesType[SaleCapabilitiesType["PoiReplication"] = 'POIReplication'] = "PoiReplication";
    SaleCapabilitiesType[SaleCapabilitiesType["PrinterDocument"] = 'PrinterDocument'] = "PrinterDocument";
    SaleCapabilitiesType[SaleCapabilitiesType["PrinterReceipt"] = 'PrinterReceipt'] = "PrinterReceipt";
    SaleCapabilitiesType[SaleCapabilitiesType["PrinterVoucher"] = 'PrinterVoucher'] = "PrinterVoucher";
})(SaleCapabilitiesType = exports.SaleCapabilitiesType || (exports.SaleCapabilitiesType = {}));
//# sourceMappingURL=saleCapabilitiesType.js.map