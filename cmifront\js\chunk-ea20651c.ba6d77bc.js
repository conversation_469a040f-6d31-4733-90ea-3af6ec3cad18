(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ea20651c"],{"00b4":function(t,e,a){"use strict";a("ac1f");var r=a("23e7"),n=a("c65b"),i=a("1626"),o=a("825a"),s=a("577e"),c=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),l=/./.test;r({target:"RegExp",proto:!0,forced:!c},{test:function(t){var e=o(this),a=s(t),r=e.exec;if(!i(r))return n(l,e,a);var c=n(r,e,a);return null!==c&&(o(c),!0)}})},"602a":function(t,e,a){"use strict";a("8169")},7327:function(t,e,a){"use strict";a.r(e);a("b0c0"),a("498a");var r=function(){var t=this,e=t._self._c;return e("Card",[e("div",{staticClass:"search_head_i"},[e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("账期名称")]),e("Input",{staticClass:"seachSty",attrs:{maxlength:"50",clearable:"",placeholder:"请输入账期名称"},model:{value:t.name,callback:function(e){t.name="string"===typeof e?e.trim():e},expression:"name"}})],1),e("div",{staticClass:"search_box"},[e("span",{staticClass:"search_box_label"},[t._v("账期类型")]),e("Select",{staticClass:"seachSty",attrs:{placeholder:"请选择账期类型",clearable:""},model:{value:t.type,callback:function(e){t.type=e},expression:"type"}},[e("Option",{attrs:{value:"1"}},[t._v("标准账期")]),e("Option",{attrs:{value:"2"}},[t._v("自然月")]),e("Option",{attrs:{value:"3"}},[t._v("定制账期")])],1)],1),e("div",{staticStyle:{width:"110px",display:"flex","justify-content":"center","margin-bottom":"20px"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.searchOne()}}},[t._v("搜索")])],1),e("div",{staticStyle:{width:"110px",display:"flex","justify-content":"center","margin-bottom":"20px"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],attrs:{type:"info",icon:"md-add"},on:{click:t.addItem}},[t._v("新增")])],1)]),e("Table",{staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:t.columns,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(a){var r=a.row;a.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"delete",expression:"'delete'"}],attrs:{type:"error",ghost:""},on:{click:function(e){return t.delItem(r)}}},[t._v("删除")])]}}])}),e("div",{staticStyle:{"margin-top":"15px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Modal",{attrs:{title:"账期新建","mask-closable":!1,width:"700px"},on:{"on-cancel":t.cancelModal},model:{value:t.modal1,callback:function(e){t.modal1=e},expression:"modal1"}},[e("Form",{ref:"formObj",staticStyle:{padding:"10px"},attrs:{model:t.formObj,rules:t.ruleAddValidate,"label-width":100},nativeOn:{submit:function(t){t.preventDefault()}}},[e("Row",[e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:"账期名称",prop:"name"}},[e("Input",{staticClass:"addInputSty",attrs:{maxlength:"50",clearable:"",placeholder:"请输入账期名称"},model:{value:t.formObj.name,callback:function(e){t.$set(t.formObj,"name","string"===typeof e?e.trim():e)},expression:"formObj.name"}})],1)],1),e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:"账期类型",prop:"type"}},[e("Select",{staticClass:"addInputSty",attrs:{placeholder:"请选择账期类型"},model:{value:t.formObj.type,callback:function(e){t.$set(t.formObj,"type",e)},expression:"formObj.type"}},[e("Option",{attrs:{value:"1"}},[t._v("标准账期")]),e("Option",{attrs:{value:"2"}},[t._v("自然月")]),e("Option",{attrs:{value:"3"}},[t._v("定制账期")])],1)],1)],1)],1),"3"==t.formObj.type?e("Row",[e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:"出账周期",prop:"accountingPeriod",rules:"3"==t.formObj.type?t.ruleAddValidate.accountingPeriod:[{required:!1}]}},[e("Input",{staticClass:"addInputSty numSty",attrs:{type:"number",clearable:"",placeholder:"请输入出账周期"},scopedSlots:t._u([{key:"append",fn:function(){return[e("span",[t._v("月")])]},proxy:!0}],null,!1,*********),model:{value:t.formObj.accountingPeriod,callback:function(e){t.$set(t.formObj,"accountingPeriod","string"===typeof e?e.trim():e)},expression:"formObj.accountingPeriod"}})],1)],1),e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:"出账次数",prop:"paymentNum",rules:"3"==t.formObj.type?t.ruleAddValidate.paymentNum:[{required:!1}]}},[e("Input",{staticClass:"addInputSty numSty",attrs:{id:"accountingTimes",type:"number",clearable:"",placeholder:"请输入出账次数"},model:{value:t.formObj.paymentNum,callback:function(e){t.$set(t.formObj,"paymentNum",t._n(e))},expression:"formObj.paymentNum"}})],1)],1)],1):t._e(),"3"==t.formObj.type?e("Row",[e("Col",{attrs:{span:"12"}},[e("FormItem",{attrs:{label:"记账开始时间",prop:"startTime",rules:"3"==t.formObj.type?t.ruleAddValidate.startTime:[{required:!1}]}},[e("DatePicker",{staticStyle:{width:"200px"},attrs:{type:"date",options:t.options1,placeholder:"选择记账开始时间"},model:{value:t.formObj.startTime,callback:function(e){t.$set(t.formObj,"startTime",e)},expression:"formObj.startTime"}})],1)],1),e("Col",{attrs:{span:"12"}},t._l(t.timePickers,(function(a,r){return e("FormItem",{key:r,attrs:{label:"出账时间"+(r+1),prop:"'cycleStart' + index",rules:t.getRules(r)}},[e("DatePicker",{staticStyle:{width:"200px"},attrs:{type:"date",options:t.options1,disabled:r===t.timePickers.length-1||!t.formObj.startTime||!t.formObj.accountingPeriod,placeholder:"选择出账时间"},on:{"on-change":function(e){return t.changeTime(r,e)}},model:{value:t.timePickers[r],callback:function(e){t.$set(t.timePickers,r,e)},expression:"timePickers[index]"}})],1)})),1)],1):t._e()],1),e("div",{staticClass:"footer_wrap",attrs:{slot:"footer"},slot:"footer"},[e("Button",{attrs:{icon:"ios-arrow-back"},on:{click:t.cancelModal}},[t._v("返回")]),e("Button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",loading:t.submitLoading},on:{click:t.submit}},[t._v("确定")])],1)],1)],1)},n=[],i=(a("d9e2"),a("99af"),a("4de4"),a("a630"),a("d81d"),a("a9e3"),a("d3b7"),a("ac1f"),a("00b4"),a("25f0"),a("3ca3"),a("4d90"),a("b818")),o={data:function(){var t=this,e=function(e,a,r){var n=new Date(t.formObj.startTime);n.setHours(0,0,0,0);var i=t.timePickers.map((function(t){var e=new Date(t);return e.setHours(0,0,0,0),e})),o=t.timePickers[t.timePickers.length-1],s=t.timePickers[0];if(i.length>0&&(i[0].getTime()<n.getTime()||i[0].getTime()===n.getTime()))r(new Error("出账时间不能小于或等于记账开始时间"));else if("2"==t.formObj.paymentNum&&new Date(s)>new Date(o))r(new Error("出账时间不能超过最后一个出账时间"));else for(var c=1;c<t.timePickers.length;c++){if(c==t.timePickers.length-1&&r(),t.timePickers[c]&&t.timePickers[c-1]&&!(t.timePickers[c]>t.timePickers[c-1]))return void r(new Error("出账时间".concat(c+1,"不能小于或等于出账时间").concat(c)));if(o&&new Date(t.timePickers[c])>new Date(o))return void r(new Error("出账时间不能超过最后一个出账时间"))}};return{total:0,currentPage:1,page:0,name:"",type:"",loading:!1,searchloading:!1,data:[],columns:[{title:"账期名称",key:"name",minWidth:150,align:"center",tooltip:!0},{title:"账期类型",key:"type",minWidth:150,align:"center",tooltip:!0,render:function(t,e){var a=e.row,r="1"==a.type?"标准账期":"2"==a.type?"自然月":"3"==a.type?"定制账期":"";return t("label",r)}},{title:"出账周期（月）",key:"accountingPeriod",minWidth:120,align:"center",tooltip:!0},{title:"出账次数",key:"paymentNum",minWidth:100,align:"center",tooltip:!0},{title:"服务开始时间",key:"start",minWidth:160,align:"center",tooltip:!0,render:function(e,a){var r=a.row,n=r.servicesCycles,i="...";return 0===n.length?e("span",""):1===n.length?e("span",n[0].start):2===n.length?e("div",[e("div",n[0].start),e("div",n[1].start)]):e("div",[e("Tooltip",{props:{placement:"bottom",transfer:!0},style:{cursor:"pointer"}},[e("span",{style:{display:"block"}},n[0].start),e("span",{},n[1].start),e("div",{},i),e("ul",{slot:"content",style:{listStyleType:"none",whiteSpace:"normal",wordBreak:"break-all"}},t.data[a.index].servicesCycles.map((function(t){return e("li",t.start)})))])])}},{title:"服务结束时间",key:"end",minWidth:160,align:"center",tooltip:!0,render:function(e,a){var r=a.row,n=r.servicesCycles,i="...";return 0===n.length?e("span",""):1===n.length?e("span",n[0].end):2===n.length?e("div",[e("div",n[0].end),e("div",n[1].end)]):e("div",[e("Tooltip",{props:{placement:"bottom",transfer:!0},style:{cursor:"pointer"}},[e("span",{style:{display:"block"}},n[0].end),e("span",{},n[1].end),e("div",{},i),e("ul",{slot:"content",style:{listStyleType:"none",whiteSpace:"normal",wordBreak:"break-all"}},t.data[a.index].servicesCycles.map((function(t){return e("li",t.end)})))])])}},{title:"关联渠道商",key:"corpPeriods",minWidth:250,align:"center",tooltip:!0,render:function(e,a){var r=a.row,n=r.corpPeriods,i="...";return 0===n.length?e("span",""):1===n.length?e("span",n[0].corpName):2===n.length?e("div",[e("div",n[0].corpName),e("div",n[1].corpName)]):e("div",[e("Tooltip",{props:{placement:"bottom",transfer:!0},style:{cursor:"pointer"}},[e("span",{style:{display:"block"}},n[0].corpName),e("span",{},n[1].corpName),e("div",{},i),e("ul",{slot:"content",style:{listStyleType:"none",whiteSpace:"normal",wordBreak:"break-all"}},t.data[a.index].corpPeriods.map((function(t){return e("li",t.corpName)})))])])}},{title:"操作",slot:"action",minWidth:120,align:"center",fixed:"right"}],modal1:!1,submitLoading:!1,title:"",index:0,timePickers:[],options1:{disabledDate:function(t){return t&&t.getDate()>28}},formObj:{name:"",type:"",accountingPeriod:null,paymentNum:"",startTime:"",cycleStart:{}},ruleAddValidate:{name:[{required:!0,type:"string",message:"账期名称不能为空"}],type:[{required:!0,message:"账期类型不能为空"}],accountingPeriod:[{required:!0,message:"出账周期不能为空",trigger:"blur"},{validator:function(t,e,a){var r=/^[1-9]\d*$/;return r.test(e)},message:"请输入正整数"}],paymentNum:[{required:!0,type:"number",message:"出账次数不能为空",trigger:"change"},{validator:function(t,e,a){var r=/^[1-9]\d*$/;return r.test(e)},message:"请输入正整数"}],startTime:[{required:!0,type:"date",message:"记账开始时间不能为空",trigger:"change"}],cycleStart:[{validator:e}]}}},mounted:function(){this.goPageFirst(1)},watch:{"formObj.paymentNum":function(t){this.updateTimePickers(t),this.updateLastPaymentDate()},"formObj.startTime":function(t){t&&this.updateLastPaymentDate()},"formObj.accountingPeriod":function(t){t&&this.updateLastPaymentDate()}},methods:{goPageFirst:function(t){var e=this;this.loading=!0;var a=this;Object(i["c"])({current:t,size:10,name:this.name,type:this.type}).then((function(r){"0000"==r.code&&(a.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t,e.total=Number(r.count),e.data=r.data)})).catch((function(t){console.error(t)})).finally((function(){a.loading=!1,e.searchloading=!1}))},searchOne:function(){this.searchloading=!0,this.goPageFirst(1)},goPage:function(t){this.goPageFirst(t)},addItem:function(){this.modal1=!0},cancelModal:function(){this.modal1=!1,this.$refs["formObj"].resetFields()},delItem:function(t){var e=this;this.$Modal.confirm({title:"确认删除？",onOk:function(){Object(i["b"])({id:t.id}).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.goPageFirst(1)})).catch((function(t){}))}})},submit:function(){var t=this,e=this.timePickers.filter((function(t){return null!==t&&""!==t&&void 0!==t}));"3"==this.formObj.type&&e.length!==this.formObj.paymentNum?this.$Message["warning"]({background:!0,content:"请确保所有出账时间都已完整选择！"}):this.$refs["formObj"].validate((function(e){if(e){var a="3"==t.formObj.type?t.formatDateToYYYYMMDD(t.formObj.startTime):void 0;t.submitLoading=!0,t.timePickers=t.formatDatesToYYYYMMDD(t.timePickers),Object(i["a"])({name:t.formObj.name,type:t.formObj.type,startTime:"3"==t.formObj.type?a:void 0,paymentTimes:"3"==t.formObj.type?t.timePickers:void 0,paymentNum:"3"==t.formObj.type?t.formObj.paymentNum:void 0,accountingPeriod:"3"==t.formObj.type?t.formObj.accountingPeriod:void 0}).then((function(e){if(!e||"0000"!=e.code)throw t.submitLoading=!1,e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),setTimeout((function(){t.submitLoading=!1,t.cancelModal(),t.goPageFirst(1)}),1500)})).catch((function(e){t.submitLoading=!1}))}}))},getRules:function(t){return t===this.timePickers.length-1?[{required:!1,validator:function(){return!0}}]:"3"===this.formObj.type?this.ruleAddValidate.cycleStart:[{required:!1}]},updateLastPaymentDate:function(){var t=new Date(this.formObj.startTime),e=parseInt(this.formObj.accountingPeriod,10)*(this.formObj.accountingTimes||1),a=new Date(t),r=a.getDate();a.setMonth(a.getMonth()+e),a.getDate()!==r&&a.setDate(1);var n=this.formatDate(a);this.timePickers[this.timePickers.length-1]=n},updateTimePickers:function(t){this.timePickers=Array.from({length:t},(function(t,e){return null}))},formatDate:function(t){var e=t.getFullYear(),a=(t.getMonth()+1).toString().padStart(2,"0"),r=t.getDate().toString().padStart(2,"0");return"".concat(e,"-").concat(a,"-").concat(r)},changeTime:function(t,e){this.timePickers[t]=e,this.formObj.cycleStart[t]=e},formatDateToYYYYMMDD:function(t){var e=new Date(t);if(isNaN(e.getTime()))throw new Error("Invalid date string");var a=e.getFullYear(),r=String(e.getMonth()+1).padStart(2,"0"),n=String(e.getDate()).padStart(2,"0");return"".concat(a,"-").concat(r,"-").concat(n)},formatDatesToYYYYMMDD:function(t){return t.map((function(t){var e=new Date(t);if(isNaN(e.getTime()))return t;var a=e.getFullYear(),r=String(e.getMonth()+1).padStart(2,"0"),n=String(e.getDate()).padStart(2,"0");return"".concat(a,"-").concat(r,"-").concat(n)}))}}},s=o,c=(a("602a"),a("2877")),l=Object(c["a"])(s,r,n,!1,null,"38f1490a",null);e["default"]=l.exports},8169:function(t,e,a){},b818:function(t,e,a){"use strict";a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return o})),a.d(e,"b",(function(){return s}));var r=a("66df"),n="/charging/accountingPeriod",i=function(t){return r["a"].request({url:n+"/query",data:t,method:"post"})},o=function(t){return r["a"].request({url:n+"/add",data:t,method:"post"})},s=function(t){return r["a"].request({url:n+"/del",params:t,method:"post"})}}}]);