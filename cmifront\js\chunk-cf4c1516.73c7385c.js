(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cf4c1516"],{"129f":function(e,t,a){"use strict";e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!==e&&t!==t}},"1c31":function(e,t,a){"use strict";a.d(t,"t",(function(){return s})),a.d(t,"s",(function(){return i})),a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return l})),a.d(t,"c",(function(){return c})),a.d(t,"d",(function(){return u})),a.d(t,"e",(function(){return d})),a.d(t,"f",(function(){return p})),a.d(t,"g",(function(){return m})),a.d(t,"h",(function(){return f})),a.d(t,"i",(function(){return h})),a.d(t,"j",(function(){return g})),a.d(t,"k",(function(){return v})),a.d(t,"l",(function(){return y})),a.d(t,"x",(function(){return w})),a.d(t,"m",(function(){return k})),a.d(t,"n",(function(){return x})),a.d(t,"o",(function(){return b})),a.d(t,"p",(function(){return I})),a.d(t,"q",(function(){return C})),a.d(t,"v",(function(){return T})),a.d(t,"r",(function(){return _})),a.d(t,"w",(function(){return P})),a.d(t,"u",(function(){return q}));var n=a("66df"),r="/stat",s=function(e){return n["a"].request({url:"/cms/api/v1/packageCard/countReuseExport",data:e,responseType:"blob",method:"post"})},i=function(e){return n["a"].request({url:"/cms/api/v1/packageCard/countReuse",data:e,method:"post"})},o=function(e){return n["a"].request({url:r+"/activereport/detailDownload",params:e,responseType:"blob",method:"get"})},l=function(e){return n["a"].request({url:r+"/activereport/pageList",data:e,method:"post"})},c=function(e){return n["a"].request({url:r+"/cardReport",params:e,method:"get"})},u=function(e){return n["a"].request({url:r+"/cardReport/export",params:e,responseType:"blob",method:"get"})},d=function(e){return n["a"].request({url:r+"/offline/export",params:e,responseType:"blob",method:"get"})},p=function(e){return n["a"].request({url:r+"/offline/import",data:e,method:"post"})},m=function(e){return n["a"].request({url:r+"/offline/pageList",data:e,method:"post"})},f=function(e){return n["a"].request({url:r+"/operatorsettle/detailDownload",params:e,responseType:"blob",method:"get"})},h=function(e){return n["a"].request({url:r+"/operatorsettle/pageList",data:e,method:"post"})},g=function(e){return n["a"].request({url:r+"/postpaidsettle/detailDownload",params:e,responseType:"blob",method:"get"})},v=function(e){return n["a"].request({url:r+"/postpaidsettle/pageList",data:e,method:"post"})},y=function(e){return n["a"].request({url:r+"/rate",params:e,method:"get"})},w=function(e){return n["a"].request({url:r+"/rate",data:e,method:"post"})},k=function(e){return n["a"].request({url:r+"/rate/export",params:e,responseType:"blob",method:"get"})},x=function(e){return n["a"].request({url:r+"/report/package/analysis/export",params:e,responseType:"blob",method:"get"})},b=function(e){return n["a"].request({url:r+"/report/package/analysis/search",data:e,method:"post"})},I=function(e){return n["a"].request({url:r+"/terminalsettle/detailDownload",params:e,responseType:"blob",method:"get"})},C=function(e){return n["a"].request({url:r+"/terminalsettle/pageList",data:e,method:"post"})},T=function(e){return n["a"].request({url:"/charging/cost/supplierCostQuery",data:e,method:"post"})},_=function(e){return n["a"].request({url:"/charging/cost/supplierCostExport",data:e,responseType:"blob",method:"post"})},P=function(e){return n["a"].request({url:"/cms/esim/getEsimcardStats",params:e,method:"get"})},q=function(e){return n["a"].request({url:"/cms/esim/exportEsimcardStats",params:e,method:"get"})}},6367:function(e,t,a){},"70d2":function(e,t,a){"use strict";a.r(t);a("ac1f"),a("841c");var n=function(){var e=this,t=e._self._c;return t("Card",[t("div",{staticStyle:{display:"flex",width:"100%"}},[t("Form",{ref:"form",attrs:{"label-width":0,model:e.form,rules:e.rule,inline:""}},[t("FormItem",{attrs:{prop:"cropName"}},[t("Input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入销售主体",prop:"showTitle",clearable:""},model:{value:e.form.corpName,callback:function(t){e.$set(e.form,"corpName",t)},expression:"form.corpName"}})],1),t("FormItem",{attrs:{prop:"type"}},[t("Select",{staticStyle:{width:"200px","text-align":"left",margin:"0 10px"},attrs:{clearable:!0,placeholder:"请选择统计维度"},on:{"on-change":function(t){e.date="",e.resetField(["startTime","endTime"])}},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},e._l(e.cycleList,(function(a,n){return t("Option",{key:n,attrs:{value:a.id}},[e._v(e._s(a.value))])})),1)],1),t("FormItem",{attrs:{prop:"endTime"}},["2"!=e.form.type?t("FormItem",{attrs:{prop:"startTime"}},[t("DatePicker",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{format:"yyyyMMdd",editable:!1,type:"daterange",placeholder:"选择时间段",clearable:""},on:{"on-change":e.checkDatePicker},model:{value:e.date,callback:function(t){e.date=t},expression:"date"}})],1):e._e(),"2"==e.form.type?t("FormItem",{attrs:{prop:"startTime"}},[t("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择开始月份",editable:!1},on:{"on-change":function(t){return e.checkDatePicker(t,1)}}})],1):e._e(),"2"==e.form.type?t("FormItem",{attrs:{prop:"endTime"}},[t("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择结束月份",editable:!1},on:{"on-change":function(t){return e.checkDatePicker(t,2)}}})],1):e._e()],1),t("FormItem",[t("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",size:"large"},on:{click:function(t){return e.search()}}},[e._v("搜索")]),t("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],staticStyle:{"margin-left":"20px"},attrs:{type:"success",icon:"ios-cloud-download-outline",size:"large"},on:{click:function(t){return e.exportTable()}}},[e._v("导出")])],1)],1)],1),t("Table",{staticStyle:{"margin-top":"50px"},attrs:{columns:e.columns12,data:e.page.showList,ellipsis:!0,border:"",loading:e.loading}}),t("div",{staticStyle:{"margin-left":"38%","margin-top":"100px","margin-bottom":"10px"}},[t("Page",{attrs:{"show-total":"","show-elevator":"",total:e.page.allPieces,"page-size":e.page.pagePieces},on:{"on-change":e.slicePage}})],1),e.data1.length?t("Table",{staticStyle:{margin:"100px auto"},attrs:{columns:e.columns13,data:e.data1,ellipsis:!0,border:"",loading:e.loading}}):e._e()],1)},r=[],s=a("5530"),i=(a("caad"),a("14d9"),a("fb6a"),a("d3b7"),a("2532"),a("3ca3"),a("159b"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("1c31")),o=function(){var e=this,t=e._self._c;return t("div",[t("Row",{staticClass:"expand-row"},[t("Col",{attrs:{span:"5"}},[t("span",{staticClass:"expand-key"},[e._v("激活数： ")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.allcount))])]),t("Col",{attrs:{span:"5"}},[t("span",{staticClass:"expand-key"},[e._v("激活收入(单位:美元)：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.my))])]),t("Col",{attrs:{span:"5"}},[t("span",{staticClass:"expand-key"},[e._v("激活收入(单位:人民币)： ")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.rmb))])]),t("Col",{attrs:{span:"5"}},[t("span",{staticClass:"expand-key"},[e._v("激活收入(单位:港币)： ")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.gb))])]),t("Col",{attrs:{span:"4"}},[t("span",{staticClass:"expand-key"},[e._v("激活总收入(单位:港币)： ")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.allgb))])]),t("Col",{attrs:{span:"5"}},[t("span",{staticClass:"expand-key"},[e._v("过期数： ")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.cardStatus))])]),t("Col",{attrs:{span:"5"}},[t("span",{staticClass:"expand-key"},[e._v("过期收入(单位:美元)：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.cardType))])]),t("Col",{attrs:{span:"5"}},[t("span",{staticClass:"expand-key"},[e._v("过期收入(单位:人民币)： ")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.smsTemp))])]),t("Col",{attrs:{span:"5"}},[t("span",{staticClass:"expand-key"},[e._v("过期收入(单位:港币)： ")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.activeType))])]),t("Col",{attrs:{span:"4"}},[t("span",{staticClass:"expand-key"},[e._v("过期总收入(单位:港币)： ")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.overdueTime))])]),t("Col",{attrs:{span:"5"}},[t("span",{staticClass:"expand-key"},[e._v("退款收入（美元）负数：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.cardStatus))])]),t("Col",{attrs:{span:"5"}},[t("span",{staticClass:"expand-key"},[e._v("退款收入（人民币）负数：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.cardType))])]),t("Col",{attrs:{span:"5"}},[t("span",{staticClass:"expand-key"},[e._v("退款收入（港币）负数：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.smsTemp))])]),t("Col",{attrs:{span:"5"}},[t("span",{staticClass:"expand-key"},[e._v("退款总收入（港币)：")]),t("span",{staticClass:"expand-value"},[e._v(e._s(e.row.activeType))])])],1)],1)},l=[],c={props:{row:Object},methods:{coypVal:function(e){try{var t=e,a=document.createElement("input");a.value=t,document.body.appendChild(a),a.select(),document.execCommand("Copy"),a.className="oInput",a.style.display="none",this.$Message.success("复制成功")}catch(n){this.$Message.error("复制功能暂不可使用，请手动复制")}}}},u=c,d=(a("8d4d"),a("2877")),p=Object(d["a"])(u,o,l,!1,null,"64ba15f5",null),m=p.exports,f=a("b35e"),h={mixins:[f["a"]],components:{expandRow:m},data:function(){return{date:"",page:{pagePieces:10,allPieces:0,showList:[]},form:{cropName:"",endTime:"",startTime:"",type:""},rule:{startTime:[{required:!0,message:"请选择时间"}],endTime:[{required:!0,message:"请选择时间"}],type:[{required:!0,message:"请选择维度"}]},total:0,currentPage:1,loading:!1,cycleList:[{id:1,value:"日"},{id:2,value:"月"}],typeList:[{value:"1",label:"普通卡（实体卡）"},{value:"2",label:"Esim卡"},{value:"3",label:"贴片卡"}],sellList:[{value:"102",label:"API"},{value:"103",label:"官网（H5）"},{value:"104",label:"北京移动"},{value:"105",label:"批量售卖"},{value:"106",label:"推广活动"},{value:"110",label:"测试渠道"},{value:"111",label:"合作发卡"},{value:"112",label:"后付费发卡"},{value:"113",label:"WEB"},{value:"114",label:"流量池WEB"}],columns12:[{title:"套餐id",key:"packageId",align:"center",height:"",width:80},{title:"套餐名称",key:"packageName",align:"center",width:100},{title:"销售主体",key:"corpName",align:"center",width:150},{title:"时间",key:"statTime",align:"center",width:150},{title:"销售数",key:"salesVolume",align:"center",width:80},{title:"销售收入(单位:美元)",key:"salesUsdIncome",align:"center",width:150,render:function(e,t){return e("span",t.row.salesUsdIncome)}},{title:"销售收入(单位:人民币)",key:"salesCnyIncome",align:"center",width:150,render:function(e,t){return e("span",t.row.salesCnyIncome)}},{title:"销售收入(单位:港币)",key:"salesHkdIncome",align:"center",width:150,render:function(e,t){return e("span",t.row.salesHkdIncome)}},{title:"销售总收入(单位:港币)",key:"salesTotalIncome",align:"center",width:150,render:function(e,t){return e("span",t.row.salesTotalIncome)}},{title:"激活数",key:"activeVolume",align:"center",width:80},{title:"激活收入(单位:美元)",key:"activeUsdIncome",align:"center",width:150,render:function(e,t){return e("span",t.row.activeUsdIncome)}},{title:"激活收入(单位:人民币)",key:"activeCnyIncome",align:"center",width:150,render:function(e,t){return e("span",t.row.activeCnyIncome)}},{title:"激活收入(单位:港币)",key:"activeHkdIncome",align:"center",width:150,render:function(e,t){return e("span",t.row.activeHkdIncome)}},{title:"激活总收入(单位:港币)",key:"activeTotalIncome",align:"center",width:150,render:function(e,t){return e("span",t.row.activeTotalIncome)}},{title:"过期数",key:"expireVolume",align:"center",width:80},{title:"过期收入(单位:美元)",key:"expireUsdIncome",align:"center",width:150,render:function(e,t){return e("span",t.row.expireUsdIncome)}},{title:"过期收入(单位:人民币)",key:"expireCnyIncome",align:"center",width:150,render:function(e,t){return e("span",t.row.expireCnyIncome)}},{title:"过期收入(单位:港币)",key:"expireHkdIncome",align:"center",width:150,render:function(e,t){return e("span",t.row.expireHkdIncome)}},{title:"过期总收入(单位:港币)",key:"expireTotalIncome",align:"center",width:150,render:function(e,t){return e("span",t.row.expireTotalIncome)}},{title:"总收入（港币）",key:"totalSum",align:"center",width:150,render:function(e,t){return e("span",t.row.totalSum)}}],columns13:[{title:"套餐id",key:"packageId",align:"center",height:"",width:80,render:function(e,t){return e("span","合计")}},{title:"销售数",key:"sumSalesVolume",align:"center",width:80},{title:"销售收入(单位:美元)",key:"sumSalesUsdIncome",align:"center",width:100,render:function(e,t){return e("span",t.row.sumSalesUsdIncome)}},{title:"销售收入(单位:人民币)",key:"sumSalesCnyIncome",align:"center",width:100,render:function(e,t){return e("span",t.row.sumSalesCnyIncome)}},{title:"销售收入(单位:港币)",key:"sumSalesHkdIncome",align:"center",width:100,render:function(e,t){return e("span",t.row.sumSalesHkdIncome)}},{title:"销售总收入(单位:港币)",key:"sumSalesTotalIncome",align:"center",width:100,render:function(e,t){return e("span",t.row.sumSalesTotalIncome)}},{title:"激活数",key:"sumActiveVolume",align:"center",width:80},{title:"激活收入(单位:美元)",key:"sumActiveUsdIncome",align:"center",width:100,render:function(e,t){return e("span",t.row.sumActiveUsdIncome)}},{title:"激活收入(单位:人民币)",key:"sumActiveCnyIncome",align:"center",width:100,render:function(e,t){return e("span",t.row.sumActiveCnyIncome)}},{title:"激活收入(单位:港币)",key:"sumActiveHkdIncome",align:"center",width:100,render:function(e,t){return e("span",t.row.sumActiveHkdIncome)}},{title:"激活总收入(单位:港币)",key:"sumActiveTotalIncome",align:"center",width:100,render:function(e,t){return e("span",t.row.sumActiveTotalIncome)}},{title:"过期数",key:"sumExpireVolume",align:"center",width:80},{title:"过期收入(单位:美元)",key:"sumExpireUsdIncome",align:"center",width:100,render:function(e,t){return e("span",t.row.sumExpireUsdIncome)}},{title:"过期收入(单位:人民币)",key:"sumExpireCnyIncome",align:"center",width:100,render:function(e,t){return e("span",t.row.sumExpireCnyIncome)}},{title:"过期收入(单位:港币)",key:"sumExpireHkdIncome",align:"center",width:100,render:function(e,t){return e("span",t.row.sumExpireHkdIncome)}},{title:"过期总收入(单位:港币)",key:"sumExpireTotalIncome",align:"center",width:100,render:function(e,t){return e("span",t.row.sumExpireTotalIncome)}},{title:"总收入（港币）",key:"totalIncome",align:"center",width:100,render:function(e,t){return e("span",t.row.totalIncome)}}],data:[],data1:[],list:[],rules:{}}},created:function(){this.rule.startTime.push({validator:this.validateDate,trigger:"change"}),this.rule.endTime.push({validator:this.validateDate,trigger:"change"})},mounted:function(){},methods:{resetField:function(e){this.$refs["form"].fields.forEach((function(t){e.includes(t.prop)&&t.resetField()}))},checkDatePicker:function(e,t){Array.isArray(e)?(this.form.startTime=e[0],this.form.endTime=e[1]):1===t?this.form.startTime=e:this.form.endTime=e},slicePage:function(e){var t=e*this.page.pagePieces-this.page.pagePieces,a=e*this.page.pagePieces;this.page.showList=this.list.slice(t,a)},goPageFirst:function(e){var t=this;0===e&&(this.currentPage=1);var a=this,n=this.currentPage,r=10;this.$refs["form"].validate((function(e){e?(t.loading=!0,Object(i["o"])(Object(s["a"])({pageNum:n,pageSize:r},t.form)).then((function(e){"0000"==e.code&&(a.loading=!1,t.data1=[e.data.sumAnalyzeDataDTO],t.list=e.data.analyzeDayList||e.data.analyzeMonthList,t.page.allPieces=t.list.length,t.page.showList=t.list.slice(0,t.page.pagePieces))})).catch((function(e){"1000"===e.code&&(t.page={pagePieces:10,allPieces:0,showList:[]},t.data1=[])})).finally((function(){t.loading=!1}))):t.$Message.error("参数校验不通过")}))},goPage:function(e){this.goPageFirst(e)},search:function(){this.goPageFirst(0)},exportTable:function(){var e=this;this.$refs["form"].validate((function(t){t&&Object(i["n"])(Object(s["a"])({},e.form)).then((function(e){var t=e.data,a="套餐分析报表.csv";if("download"in document.createElement("a")){var n=document.createElement("a"),r=URL.createObjectURL(t);n.download=a,n.href=r,n.click(),URL.revokeObjectURL(r)}else navigator.msSaveBlob(t,a)})).catch((function(){return e.downloading=!1}))}))},details:function(e){this.$router.push({path:"/channel/detailsList"})}}},g=h,v=Object(d["a"])(g,n,r,!1,null,null,null);t["default"]=v.exports},"841c":function(e,t,a){"use strict";var n=a("c65b"),r=a("d784"),s=a("825a"),i=a("7234"),o=a("1d80"),l=a("129f"),c=a("577e"),u=a("dc4a"),d=a("14c3");r("search",(function(e,t,a){return[function(t){var a=o(this),r=i(t)?void 0:u(t,e);return r?n(r,t,a):new RegExp(t)[e](c(a))},function(e){var n=s(this),r=c(e),i=a(t,n,r);if(i.done)return i.value;var o=n.lastIndex;l(o,0)||(n.lastIndex=0);var u=d(n,r);return l(n.lastIndex,o)||(n.lastIndex=o),null===u?-1:u.index}]}))},"8d4d":function(e,t,a){"use strict";a("6367")},b35e:function(e,t,a){"use strict";a("d9e2");t["a"]={methods:{validateDate:function(e,t,a){var n=this.form.endDate||this.form.endTime,r=this.form.startDate||this.form.startTime;n&&r?"startDate"===e.field||"startTime"===e.field?this.$time(t,">",n)?a(new Error("开始时间不能大于结束时间")):a():this.$time(t,"<",n)?a(new Error("结束时间不能小于开始时间")):a():a()}}}}}]);