"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SaleItemRebate = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
class SaleItemRebate {
    static getAttributeTypeMap() {
        return SaleItemRebate.attributeTypeMap;
    }
}
exports.SaleItemRebate = SaleItemRebate;
SaleItemRebate.discriminator = undefined;
SaleItemRebate.attributeTypeMap = [
    {
        "name": "EanUpc",
        "baseName": "EanUpc",
        "type": "string"
    },
    {
        "name": "ItemAmount",
        "baseName": "ItemAmount",
        "type": "number"
    },
    {
        "name": "ItemID",
        "baseName": "ItemID",
        "type": "number"
    },
    {
        "name": "ProductCode",
        "baseName": "ProductCode",
        "type": "string"
    },
    {
        "name": "Quantity",
        "baseName": "Quantity",
        "type": "number"
    },
    {
        "name": "RebateLabel",
        "baseName": "RebateLabel",
        "type": "string"
    },
    {
        "name": "UnitOfMeasure",
        "baseName": "UnitOfMeasure",
        "type": "SaleItemRebate.UnitOfMeasureEnum"
    }
];
(function (SaleItemRebate) {
    let UnitOfMeasureEnum;
    (function (UnitOfMeasureEnum) {
        UnitOfMeasureEnum[UnitOfMeasureEnum["Case"] = 'Case'] = "Case";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Centilitre"] = 'Centilitre'] = "Centilitre";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Centimetre"] = 'Centimetre'] = "Centimetre";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Foot"] = 'Foot'] = "Foot";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Gram"] = 'Gram'] = "Gram";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Inch"] = 'Inch'] = "Inch";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Kilogram"] = 'Kilogram'] = "Kilogram";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Kilometre"] = 'Kilometre'] = "Kilometre";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Litre"] = 'Litre'] = "Litre";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Meter"] = 'Meter'] = "Meter";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Mile"] = 'Mile'] = "Mile";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Other"] = 'Other'] = "Other";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Ounce"] = 'Ounce'] = "Ounce";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Pint"] = 'Pint'] = "Pint";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Pound"] = 'Pound'] = "Pound";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Quart"] = 'Quart'] = "Quart";
        UnitOfMeasureEnum[UnitOfMeasureEnum["UkGallon"] = 'UKGallon'] = "UkGallon";
        UnitOfMeasureEnum[UnitOfMeasureEnum["UsGallon"] = 'USGallon'] = "UsGallon";
        UnitOfMeasureEnum[UnitOfMeasureEnum["Yard"] = 'Yard'] = "Yard";
    })(UnitOfMeasureEnum = SaleItemRebate.UnitOfMeasureEnum || (SaleItemRebate.UnitOfMeasureEnum = {}));
})(SaleItemRebate = exports.SaleItemRebate || (exports.SaleItemRebate = {}));
//# sourceMappingURL=saleItemRebate.js.map