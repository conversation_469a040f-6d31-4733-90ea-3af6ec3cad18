"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityTrailer = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
class SecurityTrailer {
    static getAttributeTypeMap() {
        return SecurityTrailer.attributeTypeMap;
    }
}
exports.SecurityTrailer = SecurityTrailer;
SecurityTrailer.discriminator = undefined;
SecurityTrailer.attributeTypeMap = [
    {
        "name": "AdyenCryptoVersion",
        "baseName": "AdyenCryptoVersion",
        "type": "number"
    },
    {
        "name": "Hmac",
        "baseName": "Hmac",
        "type": "any"
    },
    {
        "name": "KeyIdentifier",
        "baseName": "KeyIdentifier",
        "type": "string"
    },
    {
        "name": "KeyVersion",
        "baseName": "KeyVersion",
        "type": "number"
    },
    {
        "name": "Nonce",
        "baseName": "Nonce",
        "type": "any"
    }
];
//# sourceMappingURL=securityTrailer.js.map