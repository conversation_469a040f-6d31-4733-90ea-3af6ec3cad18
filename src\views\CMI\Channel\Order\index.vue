<template>
  <!-- 订单管理 -->
  <ContentWrap>
    <el-card>
      <div style="margin-top: 20px;">
        <div class="search-form">
          <el-form :model="searchForm" inline label-width="100px">
            <el-form-item label="套餐名称：">
              <el-input 
                v-model="searchForm.packageName" 
                placeholder="请输入套餐名称" 
                clearable
                style="width: 200px;"
              />
            </el-form-item>
            <el-form-item label="号码：">
              <el-input 
                v-model="searchForm.iccid" 
                placeholder="请选择号码" 
                clearable
                style="width: 200px;"
              />
            </el-form-item>
            <el-form-item label="时间段：">
              <el-date-picker
                v-model="searchForm.timeSlot"
                type="daterange"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px;"
              />
            </el-form-item>
            <el-form-item label="归属渠道：">
              <el-select 
                v-model="searchForm.attributableChannel" 
                placeholder="请选择归属渠道"
                clearable
                style="width: 200px;"
              >
                <el-option
                  v-for="item in channelList"
                  :key="item.corpId"
                  :label="item.corpName"
                  :value="item.corpId"
                />
              </el-select>
            </el-form-item>
          </el-form>
          
          <div class="search-buttons">
            <el-button 
              v-if="hasPermission('view')"
              type="success"
              @click="showBill"
            >
              月账单
            </el-button>
            <el-button 
              v-if="hasPermission('search')"
              type="primary" 
              :loading="searchLoading"
              @click="handleSearch"
            >
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button 
              v-if="hasPermission('export')"
              type="success" 
              :loading="exportLoading"
              @click="exportData"
            >
              <Icon icon="ep:download" class="mr-5px" />
              导出
            </el-button>
            <el-button 
              v-if="hasPermission('trafficDetails')"
              type="info"
              @click="getTrafficDetails"
            >
              <Icon icon="ep:view" class="mr-5px" />
              流量详情
            </el-button>
          </div>
        </div>
        
        <!-- 表格 -->
        <div style="margin-top: 20px;">
          <el-table :data="tableData" v-loading="loading" border>
            <el-table-column prop="orderNo" label="订单号" min-width="150" />
            <el-table-column prop="packageName" label="套餐名称" min-width="150" />
            <el-table-column prop="iccid" label="ICCID" min-width="180" />
            <el-table-column prop="msisdn" label="MSISDN" min-width="120" />
            <el-table-column prop="orderType" label="订单类型" min-width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getOrderTypeTag(row.orderType)">
                  {{ getOrderTypeText(row.orderType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="金额" min-width="100" align="right" />
            <el-table-column prop="status" label="状态" min-width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusTag(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" min-width="160" />
            <el-table-column prop="attributableChannel" label="归属渠道" min-width="120" />
            <el-table-column label="操作" min-width="120" align="center" fixed="right">
              <template #default="{ row }">
                <el-button 
                  v-if="!row.isUsed && row.orderType !== '7'"
                  type="danger" 
                  size="small"
                  @click="handleUnsubscribe(row)"
                >
                  退订
                </el-button>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 分页 -->
        <div style="margin-top: 20px; text-align: right;">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { useRouter } from 'vue-router'

// 权限检查函数
const hasPermission = (permission: string): boolean => {
  return true // TODO: 实现权限检查逻辑
}

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const exportLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const searchForm = reactive({
  packageName: '',
  iccid: '',
  timeSlot: [] as string[],
  attributableChannel: ''
})

const tableData = ref([])
const channelList = ref([])

// 方法
const getOrderTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    '1': 'primary',
    '2': 'success',
    '3': 'warning',
    '7': 'info'
  }
  return typeMap[type] || 'info'
}

const getOrderTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    '1': '套餐订购',
    '2': '流量充值',
    '3': '套餐变更',
    '7': '其他'
  }
  return typeMap[type] || '未知'
}

const getStatusTag = (status: string) => {
  const statusMap: Record<string, string> = {
    '1': 'success',
    '2': 'warning',
    '3': 'danger',
    '0': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '1': '已完成',
    '2': '处理中',
    '3': '失败',
    '0': '待处理'
  }
  return statusMap[status] || '未知'
}

const handleSearch = () => {
  currentPage.value = 1
  getTableData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getTableData()
}

const showBill = () => {
  // 跳转到月账单页面
  router.push({
    path: '/newcmi/channel/order/bill'
  })
}

const exportData = async () => {
  try {
    exportLoading.value = true
    // TODO: 实现导出功能
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const getTrafficDetails = () => {
  // 跳转到流量详情页面
  router.push({
    path: '/newcmi/channel/order/traffic-details'
  })
}

const handleUnsubscribe = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要退订订单 ${row.orderNo} 吗？`,
      '确认退订',
      {
        type: 'warning'
      }
    )
    
    // TODO: 实现退订API调用
    ElMessage.success('退订成功')
    getTableData()
  } catch (error) {
    // 用户取消操作
  }
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    tableData.value = [
      {
        orderNo: 'ORD20240101001',
        packageName: '基础套餐',
        iccid: '89860000000000000001',
        msisdn: '13800138000',
        orderType: '1',
        amount: 100.00,
        status: '1',
        createTime: '2024-01-01 10:00:00',
        attributableChannel: '渠道A',
        isUsed: false
      }
    ]
    total.value = 1
  } catch (error) {
    ElMessage.error('获取订单数据失败')
  } finally {
    loading.value = false
  }
}

// 获取渠道列表
const getChannelList = async () => {
  try {
    // TODO: 实现API调用
    channelList.value = [
      { corpId: '1', corpName: '渠道A' },
      { corpId: '2', corpName: '渠道B' }
    ]
  } catch (error) {
    ElMessage.error('获取渠道列表失败')
  }
}

// 生命周期
onMounted(() => {
  getTableData()
  getChannelList()
})
</script>

<style scoped>
.search-form {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.search-buttons {
  margin-top: 10px;
}

.search-buttons .el-button {
  margin-right: 10px;
}
</style>
