import{v as o,u as p,x as _,y as i,az as u}from"./vue-chunks-Co0GcRjL.js";const y=o({__name:"Redirect",setup(l){const{currentRoute:c,replace:a}=u(),{params:e,query:s}=p(c),{path:r,_redirect_type:n="path"}=e;Reflect.deleteProperty(e,"_redirect_type"),Reflect.deleteProperty(e,"path");const t=Array.isArray(r)?r.join("/"):r;return a(n==="name"?{name:t,query:s,params:e}:{path:t.startsWith("/")?t:"/"+t,query:s}),(m,d)=>(_(),i("div"))}});export{y as default};
