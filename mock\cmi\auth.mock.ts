import { SUCCESS_CODE } from '@/constants'

const timeout = 1000

// CMI 测试用户数据
const CMI_USERS = [
  {
    username: 'admin',
    password: 'admin123',
    userDetails: {
      id: '1',
      username: 'admin',
      roleId: 'admin',
      rePassword: 0, // 不需要更新密码
      pagePrivileges: [
        {
          access: 'home',
          url: '/dashboard',
          buttons: ['view']
        },
        {
          access: 'account_list',
          url: '/system/account',
          buttons: ['add', 'edit', 'delete', 'view']
        },
        {
          access: 'pwd_mngr',
          url: '/system/password',
          buttons: ['edit', 'view']
        },
        {
          access: 'pri_mngr',
          url: '/system/role',
          buttons: ['add', 'edit', 'delete', 'view']
        },
        {
          access: 'login_mngr',
          url: '/system/login-log',
          buttons: ['view', 'export']
        },
        {
          access: 'msisdn',
          url: '/resource/msisdn',
          buttons: ['add', 'edit', 'delete', 'view', 'import', 'export']
        },
        {
          access: 'iccid',
          url: '/resource/iccid',
          buttons: ['add', 'edit', 'delete', 'view', 'import', 'export']
        },
        {
          access: 'imsi',
          url: '/resource/imsi',
          buttons: ['add', 'edit', 'delete', 'view', 'import', 'export']
        },
        {
          access: 'supplyImsi',
          url: '/resource/supply-imsi',
          buttons: ['add', 'edit', 'delete', 'view']
        },
        {
          access: 'makeCard',
          url: '/product/make-card',
          buttons: ['add', 'edit', 'delete', 'view']
        },
        {
          access: 'masterCard',
          url: '/product/master-card',
          buttons: ['add', 'edit', 'delete', 'view']
        },
        {
          access: 'cardPool',
          url: '/product/card-pool',
          buttons: ['add', 'edit', 'delete', 'view']
        },
        {
          access: 'vimsi',
          url: '/product/vimsi',
          buttons: ['add', 'edit', 'delete', 'view']
        },
        {
          access: 'channelManage',
          url: '/customer/channel',
          buttons: ['add', 'edit', 'delete', 'view']
        },
        {
          access: 'cooperativeManage',
          url: '/customer/cooperative',
          buttons: ['add', 'edit', 'delete', 'view']
        }
      ],
      needVerifyCode: '0' // 不需要验证码
    },
    oauth2AccessToken: {
      access_token: 'mock-access-token-admin'
    }
  },
  {
    username: 'test',
    password: 'test123',
    userDetails: {
      id: '2',
      username: 'test',
      roleId: 'user',
      rePassword: 0,
      pagePrivileges: [
        {
          access: 'home',
          url: '/dashboard',
          buttons: ['view']
        },
        {
          access: 'account_list',
          url: '/system/account',
          buttons: ['view']
        },
        {
          access: 'msisdn',
          url: '/resource/msisdn',
          buttons: ['view']
        }
      ],
      needVerifyCode: '0'
    },
    oauth2AccessToken: {
      access_token: 'mock-access-token-test'
    }
  }
]

export default [
  // CMI 登录接口
  {
    url: '/auth/login',
    method: 'post',
    timeout,
    response: ({ body }) => {
      const { username, password } = body
      
      // 查找匹配的用户
      const user = CMI_USERS.find(u => u.username === username && u.password === password)
      
      if (user) {
        return {
          code: '0000',
          data: {
            userDetails: user.userDetails,
            oauth2AccessToken: user.oauth2AccessToken
          },
          msg: '登录成功'
        }
      } else {
        return {
          code: '1001',
          data: null,
          msg: '用户名或密码错误'
        }
      }
    }
  },
  
  // 获取验证码配置接口
  {
    url: '/auth/code/getIsOpen',
    method: 'post',
    timeout,
    response: ({ body }) => {
      const { username } = body
      
      // 查找用户
      const user = CMI_USERS.find(u => u.username === username)
      
      if (user) {
        return {
          code: '0000',
          data: {
            userDetails: {
              needVerifyCode: user.userDetails.needVerifyCode
            }
          },
          msg: '获取成功'
        }
      } else {
        return {
          code: '1001',
          data: null,
          msg: '用户不存在'
        }
      }
    }
  },
  
  // SSO 登录接口
  {
    url: '/aep/sso/ssoLoginGDS',
    method: 'post',
    timeout,
    response: () => {
      // 默认返回 admin 用户数据
      const adminUser = CMI_USERS[0]
      return {
        code: '0000',
        data: {
          userDetails: adminUser.userDetails,
          oauth2AccessToken: adminUser.oauth2AccessToken
        },
        msg: 'SSO登录成功'
      }
    }
  },
  
  // 登出接口
  {
    url: '/auth/logout',
    method: 'delete',
    timeout,
    response: () => {
      return {
        code: '0000',
        data: null,
        msg: '登出成功'
      }
    }
  },
  
  // 获取验证码接口
  {
    url: '/rcs/api/v1/passport/captcha',
    method: 'get',
    timeout,
    response: () => {
      return {
        code: '0000',
        data: {
          captchaId: 'mock-captcha-id',
          captchaImage: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        },
        msg: '获取验证码成功'
      }
    }
  }
]
