"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TotalFilter = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
class TotalFilter {
    static getAttributeTypeMap() {
        return TotalFilter.attributeTypeMap;
    }
}
exports.TotalFilter = TotalFilter;
TotalFilter.discriminator = undefined;
TotalFilter.attributeTypeMap = [
    {
        "name": "OperatorID",
        "baseName": "OperatorID",
        "type": "string"
    },
    {
        "name": "POIID",
        "baseName": "POIID",
        "type": "string"
    },
    {
        "name": "SaleID",
        "baseName": "SaleID",
        "type": "string"
    },
    {
        "name": "ShiftNumber",
        "baseName": "ShiftNumber",
        "type": "string"
    },
    {
        "name": "TotalsGroupID",
        "baseName": "TotalsGroupID",
        "type": "string"
    }
];
//# sourceMappingURL=totalFilter.js.map