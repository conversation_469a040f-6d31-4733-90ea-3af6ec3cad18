<template>
  <div class="search-container">
    <el-form :model="modelValue" inline>
      <el-form-item
        v-for="field in fields"
        :key="field.key"
        :label="field.label"
      >
        <!-- 输入框 -->
        <el-input
          v-if="field.type === 'input'"
          v-model="modelValue[field.key]"
          :placeholder="field.placeholder"
          clearable
          :style="field.style"
        />
        
        <!-- 选择器 -->
        <el-select
          v-else-if="field.type === 'select'"
          v-model="modelValue[field.key]"
          :placeholder="field.placeholder"
          clearable
          :style="field.style"
        >
          <el-option
            v-for="option in field.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        
        <!-- 日期选择器 -->
        <el-date-picker
          v-else-if="field.type === 'date'"
          v-model="modelValue[field.key]"
          type="date"
          :placeholder="field.placeholder"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :style="field.style"
        />
        
        <!-- 日期范围选择器 -->
        <el-date-picker
          v-else-if="field.type === 'daterange'"
          v-model="modelValue[field.key]"
          type="daterange"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :style="field.style"
        />
      </el-form-item>
      
      <!-- 操作按钮 -->
      <el-form-item>
        <slot name="buttons">
          <el-button
            type="primary"
            :loading="loading"
            @click="handleSearch"
          >
            <Icon icon="ep:search" class="mr-5px" />
            搜索
          </el-button>
        </slot>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@/components/Icon'
import type { SearchFieldConfig, BaseSearchForm } from '@/types/channel'

// 组件选项
defineOptions({
  name: 'ChannelSearchForm'
})

// Props 定义
interface Props {
  modelValue: BaseSearchForm
  fields: SearchFieldConfig[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: BaseSearchForm]
  search: [params: BaseSearchForm]
}>()

// 方法
const handleSearch = () => {
  emit('search', props.modelValue)
}
</script>

<style scoped>
.search-container {
  background-color: var(--el-bg-color-page);
  padding: var(--el-space-lg);
  border-radius: var(--el-border-radius-base);
  margin-bottom: var(--el-space-lg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-container {
    padding: var(--el-space-md);
  }
  
  :deep(.el-form--inline .el-form-item) {
    display: block;
    margin-bottom: var(--el-space-md);
  }
}
</style>
