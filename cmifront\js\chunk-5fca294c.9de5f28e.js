(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5fca294c"],{"08c9":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("Card",{staticStyle:{width:"100%",padiing:"16px"}},[e("Form",{ref:"searchForm",attrs:{model:t.searchObj,inline:""}},[e("FormItem",[e("Input",{staticStyle:{width:"200px"},attrs:{placeholder:"请输入任务名称",clearable:""},model:{value:t.searchObj.taskName,callback:function(e){t.$set(t.searchObj,"taskName",e)},expression:"searchObj.taskName"}})],1),e("FormItem",[e("Select",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择任务状态",clearable:!0},model:{value:t.searchObj.status,callback:function(e){t.$set(t.searchObj,"status",e)},expression:"searchObj.status"}},[e("Option",{attrs:{value:0}},[t._v("等待发送")]),e("Option",{attrs:{value:1}},[t._v("发送中")]),e("Option",{attrs:{value:2}},[t._v("发送成功")]),e("Option",{attrs:{value:3}},[t._v("取消")])],1)],1),e("FormItem",[e("DatePicker",{staticClass:"inputSty",attrs:{type:"datetime",format:"yyyy/MM/dd HH:mm:ss",placeholder:"请选择发送时间",clearable:!0},model:{value:t.searchObj.startTime,callback:function(e){t.$set(t.searchObj,"startTime",e)},expression:"searchObj.startTime"}})],1),e("FormItem",[e("Button",{staticStyle:{margin:"0 2px"},attrs:{type:"primary",loading:t.loading1},on:{click:t.searchSMS}},[e("Icon",{directives:[{name:"show",rawName:"v-show",value:!t.loading1,expression:"!loading1"}],attrs:{type:"ios-search"}}),t._v(" 搜索\n        ")],1),e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{margin:"0 2px"},attrs:{type:"info"},on:{click:t.SMSAdd}},[e("Icon",{attrs:{type:"md-add"}}),t._v(" 新增\n        ")],1)],1)],1),e("div",[e("Table",{ref:"selection",attrs:{columns:t.columns,data:t.tableData,ellipsis:!0,loading:t.tableLoading},scopedSlots:t._u([{key:"action",fn:function(a){var i=a.row;a.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.SMSInfo(i)}}},[t._v("详情")]),0===i.status?e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small"},on:{click:function(e){return t.SMSEdit(i)}}},[t._v("编辑")]):t._e(),0===i.status?e("Button",{directives:[{name:"has",rawName:"v-has",value:"cancel",expression:"'cancel'"}],attrs:{type:"error",size:"small"},on:{click:function(e){return t.SMSCancel(i)}}},[t._v("取消")]):t._e()]}}])}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,"page-size":t.pageSize,current:t.page,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.loadByPage}})],1),e("Modal",{attrs:{title:t.SMSTitle,"footer-hide":!0,"mask-closable":!1,width:"500px"},on:{"on-cancel":function(e){return t.reset("editObj")}},model:{value:t.SMSEditFlag,callback:function(e){t.SMSEditFlag=e},expression:"SMSEditFlag"}},[e("div",{staticStyle:{padding:"0 16px"}},[e("Button",{staticStyle:{"margin-bottom":"10px"},attrs:{type:"success",size:"small"},on:{click:t.dowloadTemplate}},[t._v("下载模板")]),e("Form",{ref:"editObj",attrs:{model:t.editObj,"label-width":100,rules:t.ruleEditValidate}},[e("FormItem",{attrs:{label:"任务名称",prop:"taskName"}},[e("Input",{attrs:{clearable:!0,placeholder:"请输入任务名称"},model:{value:t.editObj.taskName,callback:function(e){t.$set(t.editObj,"taskName",e)},expression:"editObj.taskName"}})],1),e("FormItem",{attrs:{label:"发送时间",prop:"startTime"}},[e("DatePicker",{staticStyle:{width:"100%"},attrs:{type:"datetime",format:"yyyy/MM/dd HH:mm:ss",placeholder:"请选择发送时间",clearable:!0},model:{value:t.editObj.startTime,callback:function(e){t.$set(t.editObj,"startTime",e)},expression:"editObj.startTime"}})],1),e("FormItem",{attrs:{label:"发送内容",prop:"taskContent"}},[e("Input",{attrs:{clearable:!0,placeholder:"请输入短信内容,最多支持600个字符",type:"textarea",rows:5},model:{value:t.editObj.taskContent,callback:function(e){t.$set(t.editObj,"taskContent",e)},expression:"editObj.taskContent"}})],1),e("FormItem",{attrs:{label:"号码录入方式",prop:"type"}},[e("Select",{attrs:{disabled:"Update"==t.operationType,placeholder:"选择号码录入方式",clearable:!0},model:{value:t.editObj.type,callback:function(e){t.$set(t.editObj,"type",e)},expression:"editObj.type"}},[e("Option",{attrs:{value:0}},[t._v("手动输入")]),e("Option",{attrs:{value:1}},[t._v("文件上传")])],1)],1),0==t.editObj.type?e("div",[e("FormItem",{attrs:{label:"接收号码",prop:"phones"}},[e("Input",{attrs:{clearable:!0,placeholder:"请输入接收号码，号码之间使用 , 分隔；例如：161**********1,161**********2",type:"textarea",rows:5},model:{value:t.editObj.phones,callback:function(e){t.$set(t.editObj,"phones",e)},expression:"editObj.phones"}})],1)],1):t._e(),1==t.editObj.type?e("div",[e("FormItem",{attrs:{label:"接收号码",prop:"phoneFile"}},[e("Upload",{attrs:{action:("development"===t.NODE_ENV?"/cmiweb/":"/api/")+"sms/task/upload",headers:t.headers,accept:".csv",format:["csv"],"max-size":5120,"on-success":t.uploadSuccess,"on-error":t.uploadFails,"on-exceeded-size":t.uploadExceededSize,"on-format-error":t.uploadFormatError}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"import",expression:"'import'"}],attrs:{type:"dashed",long:"",icon:"md-add"}},[t._v("号码导入")])],1)],1)],1):t._e()],1),e("div",{staticStyle:{"text-align":"center"}},["Add"==t.operationType?e("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],attrs:{type:"primary",loading:t.addLoading},on:{click:t.submit}},[t._v("提交")]):t._e(),"Update"==t.operationType?e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],attrs:{type:"primary",loading:t.addLoading},on:{click:t.submit}},[t._v("提交")]):t._e(),e("Button",{staticStyle:{"margin-left":"8px"},on:{click:function(e){return t.reset("editObj")}}},[t._v("重置")])],1)],1)]),e("Table",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"modelTable",attrs:{columns:t.modelColumns,data:t.modelData}})],1)},s=[],r=a("5530"),n=(a("d9e2"),a("a15b"),a("14d9"),a("a434"),a("e9c4"),a("b64b"),a("d3b7"),a("159b"),a("33cc")),o=a("5a0c"),l=a.n(o),c={components:{},data:function(){var t=this,e=function(e,a,i){0===t.editObj.type&&""===a?i(new Error("接收号码不能为空")):i()},a=function(e,a,i){1===t.editObj.type&&""===a?i(new Error("上传文件不能为空")):i()};return{templateDownloadUrl:"",loading1:!1,addLoading:!1,NODE_ENV:"",headers:{},searchObj:{taskName:"",startTime:"",status:""},SMSEditFlag:!1,SMSTitle:"营销短信新增",editObj:{id:"",taskName:"",startTime:"",taskContent:"",type:"0",phoneFile:"",phones:""},ruleEditValidate:{taskName:[{required:!0,message:"任务名称不能为空",trigger:"blur"}],startTime:[{required:!0,type:"date",message:"发送时间不能为空",trigger:"blur"}],taskContent:[{required:!0,message:"发送内容不能为空",trigger:"blur"},{min:0,max:600,message:"最多支持600个字符"}],type:[{required:!0,type:"number",message:"请选择号码录入方式",trigger:"select"}],phoneFile:[{validator:a}],phones:[{validator:e}]},operationType:"Add",tableData:[],tableLoading:!1,total:0,pageSize:10,page:1,columns:[{type:"selection",minWidth:60,maxWidth:60,align:"center"},{title:"任务名称",key:"taskName",align:"center",minWidth:150,tooltip:!0},{title:"发送状态",key:"status",align:"center",minWidth:150,tooltip:!0,render:function(t,e){var a=e.row,i=["等待发送","发送中","发送成功","取消"],s=i[a.status];return t("label",s)}},{title:"发送开始时间",key:"startTime",align:"center",minWidth:150,tooltip:!0},{title:"发送完成时间",key:"endTime",align:"center",minWidth:150,tooltip:!0},{title:"操作",slot:"action",minWidth:200,maxWidth:220,align:"center"}],modelData:[{number:"********"}],modelColumns:[{title:"号码",key:"number"}]}},created:function(){},methods:{dowloadTemplate:function(){this.$refs.modelTable.exportCsv({filename:"号码模板",columns:this.modelColumns,data:this.modelData})},init:function(){var t=this.$store.state.user.token;t&&(this.headers={Authorization:"bearer ".concat(t),userName:encodeURIComponent(this.$store.state.user.userName,"utf-8")}),this.NODE_ENV="production",this.loadByPage(0)},formatValue:function(t){if(1===t){var e=JSON.parse(JSON.stringify(this.editObj));return Array.isArray(e.phones)||(e.phones=e.phones.split(",")),e.startTime=l()(e.startTime).format("YYYY-MM-DD HH:mm:ss"),e}},submit:function(){var t=this,e={Add:n["a"],Update:n["g"]};console.log(this.editObj),this.$refs["editObj"].validate((function(a){console.log(a),a&&(t.addLoading=!0,e[t.operationType](t.formatValue(1)).then((function(e){"0000"===e.code&&(t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.SMSEditFlag=!1,t.reset("editObj"),t.init())})).finally((function(){t.addLoading=!1})))}))},reset:function(t){var e=this,a={type:this.editObj.type};this.$refs[t].resetFields(),this.$nextTick((function(){e.$set(e.editObj,"type",a.type)}))},loadByPage:function(t){var e=this;0===t&&(this.page=1),this.searchObj.startTime&&(this.searchObj.startTime=l()(this.searchObj.startTime).format("YYYY-MM-DD HH:mm:ss")),this.tableLoading=!0,Object(n["e"])(Object(r["a"])({current:t,size:"10"},this.searchObj)).then((function(t){"0000"===t.code&&(e.tableData=t.paging.data,e.total=t.paging.total)})).finally((function(){e.tableLoading=!1,e.loading1=!1}))},searchSMS:function(){this.loading1=!0,this.loadByPage(0)},SMSAdd:function(){this.operationType="Add",this.SMSTitle="营销短信新增",this.editObj={id:"",taskName:"",startTime:"",taskContent:"",type:0,phoneFile:"",phones:""},this.SMSEditFlag=!0},SMSEdit:function(t){var e=this;this.operationType="Update",this.SMSTitle="营销短信编辑",Object(n["c"])(t.id).then((function(t){if("0000"===t.code){var a={};Object.keys(e.editObj).forEach((function(e){a[e]="phones"===e?t.data[e]?t.data[e].join(","):"":t.data[e]})),e.editObj=a}console.log(e.editObj),e.SMSEditFlag=!0}))},SMSInfo:function(t){this.$router.push({name:"marketingInfo",query:{id:t.id,searchList:encodeURIComponent(JSON.stringify(this.searchObj))}})},SMSCancel:function(t){var e=this,a=t.endTime;if(a)return this.$Notice.error({title:"操作提示",desc:"操作失败,该任务已完成"}),!1;this.$Modal.confirm({title:"确认取消？",onOk:function(){Object(n["b"])(t.id).then((function(t){"0000"===t.code&&e.$Notice.success({title:"操作提示",desc:"操作成功"}),e.init()}))}})},uploadSuccess:function(t,e,a){"0000"===t.code&&1===this.editObj.type?this.editObj.phoneFile=t.data:(a.splice(-1,1),this.$Notice.error({title:"操作提示",desc:t.msg}))},uploadFails:function(t,e,a){a.splice(-1,1),this.$Notice.error({title:"操作提示",desc:t.msg})},uploadExceededSize:function(){this.$Notice.error({title:"操作提示",desc:"单个文件最大为5M,该文件超出指定大小"})},uploadFormatError:function(){this.$Notice.error({title:"操作提示",desc:"只允许上传csv文件，格式不正确"})}},mounted:function(){var t=null===JSON.parse(localStorage.getItem("searchList"))?"":JSON.parse(localStorage.getItem("searchList"));t&&(this.searchObj.taskName=void 0===t.taskName?"":t.taskName,this.searchObj.status=void 0===t.status?"":t.status,this.searchObj.startTime=void 0===t.startTime?"":t.startTime),this.init(),localStorage.removeItem("searchList")}},d=c,u=(a("ee4b"),a("2877")),m=Object(u["a"])(d,i,s,!1,null,null,null);e["default"]=m.exports},"33cc":function(t,e,a){"use strict";a.d(e,"e",(function(){return r})),a.d(e,"c",(function(){return n})),a.d(e,"a",(function(){return o})),a.d(e,"g",(function(){return l})),a.d(e,"b",(function(){return c})),a.d(e,"d",(function(){return d})),a.d(e,"f",(function(){return u}));a("99af");var i=a("66df"),s="/sms",r=function(t){return i["a"].request({url:s+"/task/pageList",data:t,method:"POST"})},n=function(t){return i["a"].request({url:s+"/task/".concat(t),method:"GET"})},o=function(t){return i["a"].request({url:s+"/task",data:t,method:"POST"})},l=function(t){return i["a"].request({url:s+"/task",data:t,method:"PUT"})},c=function(t){return i["a"].request({url:s+"/task/".concat(t),method:"put"})},d=function(t){return i["a"].request({url:s+"/task/download/".concat(t.taskId,"?status=").concat(t.status),method:"post",responseType:"blob"})},u=function(t){return i["a"].request({url:s+"/task/sendResult/".concat(t.taskId,"?status=").concat(t.status),method:"post"})}},"756d":function(t,e,a){},ee4b:function(t,e,a){"use strict";a("756d")}}]);