(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-72c49067"],{"00b4":function(t,e,a){"use strict";a("ac1f");var i=a("23e7"),n=a("c65b"),s=a("1626"),o=a("825a"),l=a("577e"),r=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),c=/./.test;i({target:"RegExp",proto:!0,forced:!r},{test:function(t){var e=o(this),a=l(t),i=e.exec;if(!s(i))return n(c,e,a);var r=n(i,e,a);return null!==r&&(o(r),!0)}})},"129f":function(t,e,a){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},"841c":function(t,e,a){"use strict";var i=a("c65b"),n=a("d784"),s=a("825a"),o=a("7234"),l=a("1d80"),r=a("129f"),c=a("577e"),d=a("dc4a"),u=a("14c3");n("search",(function(t,e,a){return[function(e){var a=l(this),n=o(e)?void 0:d(e,t);return n?i(n,e,a):new RegExp(e)[t](c(a))},function(t){var i=s(this),n=c(t),o=a(e,i,n);if(o.done)return o.value;var l=i.lastIndex;r(l,0)||(i.lastIndex=0);var d=u(i,n);return r(i.lastIndex,l)||(i.lastIndex=l),null===d?-1:d.index}]}))},b47b:function(t,e,a){},c153:function(t,e,a){"use strict";a("b47b")},ee7a:function(t,e,a){"use strict";a.r(e);a("b0c0"),a("ac1f"),a("841c"),a("498a");var i=function(){var t=this,e=t._self._c;return e("Card",[e("div",{staticStyle:{width:"100%"}},[e("div",{staticStyle:{display:"flex","justify-content":"flex-start","align-items":"center","flex-wrap":"wrap"}},[e("span",{staticStyle:{margin:"5px","font-weight":"bold"}},[t._v("ICCID")]),t._v("  \n\t\t\t"),e("Input",{staticStyle:{width:"200px",margin:"5px"},attrs:{placeholder:t.$t("support.ICCIDenter"),prop:"showTitle",clearable:""},model:{value:t.iccid,callback:function(e){t.iccid="string"===typeof e?e.trim():e},expression:"iccid"}}),t._v("      \n\t\t\t"),e("span",{staticStyle:{margin:"5px","font-weight":"bold"}},[t._v(t._s(t.$t("stock.attributableChannel")))]),t._v("  \n\t\t\t"),e("Select",{staticStyle:{width:"200px","text-align":"left",margin:"0 10px"},attrs:{filterable:"",clearable:""},model:{value:t.attributableChannel,callback:function(e){t.attributableChannel=e},expression:"attributableChannel"}},t._l(t.attributableChannelList,(function(a){return e("Option",{key:a.corpId,attrs:{value:a.corpId}},[t._v(t._s(a.corpName))])})),1),t._v("  \n\t\t\t"),e("span",{staticStyle:{margin:"5px","font-weight":"bold"}},[t._v(t._s(t.$t("stock.availablePackages")))]),t._v("  \n\t\t\t"),e("Select",{staticStyle:{width:"200px","text-align":"left",margin:"5px 10px"},attrs:{filterable:"",clearable:""},model:{value:t.isHasPackage,callback:function(e){t.isHasPackage=e},expression:"isHasPackage"}},[e("Option",{attrs:{value:1}},[t._v(t._s(t.$t("order.yes")))]),e("Option",{attrs:{value:2}},[t._v(t._s(t.$t("order.no")))])],1),t._v("  \n\t\t\t"),e("Button",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],staticStyle:{"margin-left":"20px"},attrs:{disabled:"3"==t.cooperationMode,type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.search()}}},[t._v(t._s(t.$t("stock.search")))]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"export",expression:"'export'"}],staticStyle:{margin:"0 2px","margin-left":"20px"},attrs:{disabled:"3"==t.cooperationMode,icon:"ios-cloud-download-outline",type:"success",loading:t.downloading},on:{click:t.exportFile}},[t._v("\n\t\t\t\t"+t._s(t.$t("stock.exporttb"))+"\n\t\t\t")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"transfer",expression:"'transfer'"}],staticStyle:{margin:"0 2px","margin-left":"20px"},attrs:{disabled:"2"==t.cooperationMode||"3"==t.cooperationMode,icon:"ios-brush-outline",type:"info"},on:{click:t.transfer}},[t._v("\n\t\t\t\t"+t._s(t.$t("stock.transfer"))+"\n\t\t\t")])],1),e("Table",{staticStyle:{width:"100%","margin-top":"40px"},attrs:{columns:t.columns12,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"action",fn:function(a){var i=a.row;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"view",expression:"'view'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"warning",size:"small"},on:{click:function(e){return t.details(i)}}},[t._v(t._s(t.$t("stock.details")))])]}}])}),e("div",{staticStyle:{"margin-top":"100px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1),e("Modal",{attrs:{title:t.Titlemessage,width:"620px"},on:{"on-ok":t.ok,"on-cancel":t.cancelModal},model:{value:t.modal5,callback:function(e){t.modal5=e},expression:"modal5"}},[e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",[t._v("MSISDN:")]),t._v("  \n\t\t\t\t"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.more.msisdn))]),t._v("  \n\t\t\t")]),e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v("ICCID:")]),t._v("  \n\t\t\t\t"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.more.iccid))]),t._v("  "),e("br")]),e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v("IMSI:")]),t._v("  \n\t\t\t\t"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.more.imsi))]),t._v("  "),e("br")]),e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.$t("stock.Code")))]),t._v("  \n\t\t\t\t"),e("span",[t._v(t._s(t.more.pin2))]),t._v("  "),e("br")]),e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",[t._v("PUK:")]),t._v("  \n\t\t\t\t"),e("span",[t._v(t._s(t.more.puk1))]),t._v("  "),e("br")]),e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",[t._v("Output File:")]),t._v("  "),e("br")]),e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",[t._v("ADM:")]),t._v("  \n\t\t\t\t"),e("span",[t._v(t._s(t.more.fileNameAdm))]),t._v("  "),e("br")]),e("div",{staticClass:"search_head",staticStyle:{"font-weight":"bold"}},[e("span",[t._v("SDB:")]),t._v("  \n\t\t\t\t"),e("span",[t._v(t._s(t.more.fileNameSdb))]),t._v("  "),e("br")])]),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v(t._s(t.$t("exportMS")))]),e("FormItem",{attrs:{label:t.$t("exportID")}},[e("span",{staticStyle:{width:"100px"}},[t._v(t._s(t.taskId))])]),e("FormItem",{attrs:{label:t.$t("exportFlie")}},[e("span",[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v(t._s(t.$t("downloadResult")))])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v(t._s(t.$t("common.cancel")))]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v(t._s(t.$t("Goto")))])],1)]),e("Modal",{attrs:{title:t.$t("stock.transfer"),"footer-hide":!0,"mask-closable":!1,width:"500px"},on:{"on-cancel":t.cancelModal},model:{value:t.subChannelModal,callback:function(e){t.subChannelModal=e},expression:"subChannelModal"}},[e("div",{staticStyle:{padding:"0 10px"}},[e("Form",{ref:"editObj",staticStyle:{display:"flex","flex-wrap":"wrap"},attrs:{model:t.editObj,"label-width":80,rules:t.ruleEditValidate}},[e("FormItem",{attrs:{label:t.$t("stock.subChannel"),prop:"subChannel"}},[e("Select",{staticStyle:{width:"200px"},attrs:{filterable:""},model:{value:t.editObj.subChannel,callback:function(e){t.$set(t.editObj,"subChannel",e)},expression:"editObj.subChannel"}},t._l(t.attributableChannelList2,(function(a){return e("Option",{key:a.corpId,attrs:{value:a.corpId+"-"+a.parentCorpId}},[t._v(t._s(a.corpName)+"\n\t\t\t\t\t\t\t")])})),1)],1),e("FormItem",{attrs:{prop:"file"}},[e("div",{staticStyle:{display:"flex"}},[e("Upload",{ref:"upload",attrs:{action:t.uploadUrl,"on-success":t.fileSuccess,"on-error":t.handleError,"before-upload":t.handleBeforeUpload,"on-progress":t.fileUploading},model:{value:t.editObj.file,callback:function(e){t.$set(t.editObj,"file",e)},expression:"editObj.file"}},[e("Button",{attrs:{icon:"ios-cloud-upload-outline"}},[t._v(t._s(t.$t("flow.UploadICCID")))])],1),e("div",{staticStyle:{width:"250px","margin-left":"30px"}},[e("Button",{attrs:{type:"info",icon:"ios-download"},on:{click:t.downloadFile}},[t._v(t._s(t.$t("support.downloadfile")))])],1)],1),t.file?e("ul",{staticClass:"ivu-upload-list"},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("Icon",{attrs:{type:"ios-folder"}}),t._v(t._s(t.file.name)+"\n\t\t\t\t\t\t\t\t")],1),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeFile}})])]):t._e()])],1),e("div",{staticStyle:{"text-align":"center","margin-top":"20px"}},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"submit",expression:"'submit'"}],staticStyle:{"margin-right":"20px"},attrs:{type:"primary",loading:t.submitLoading},on:{click:function(e){return t.submit()}}},[t._v(t._s(t.$t("support.submit")))]),e("Button",{staticStyle:{"margin-left":"8px"},on:{click:t.cancelModal}},[t._v(t._s(t.$t("support.back")))])],1)],1)]),e("Table",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],ref:"modelTable",attrs:{columns:t.SubmodelColumns,data:t.SubmodelData}})],1)])},n=[],s=a("3835"),o=(a("d9e2"),a("14d9"),a("d3b7"),a("00b4"),a("6dfa")),l={data:function(){var t=this,e=function(e,a,i){t.uploadList&&0===t.uploadList.length?i(new Error(t.$t("support.pleaseUploadFile"))):i()};return{cooperationMode:"",iccid:"",attributableChannel:"",isHasPackage:"",modal5:!1,time_slot:"",searchBeginTime:"",searchEndTime:"",Titlemessage:this.$t("stock.details"),total:0,page:0,currentPage:1,loading:!1,searchloading:!1,detailsloading:!1,downloading:!1,submitLoading:!1,exportModal:!1,subChannelModal:!1,taskId:"",taskName:"",form:{},attributableChannelList:[],attributableChannelList2:[],legth:"",columns12:[{title:"IMSI",key:"imsi",align:"center",minWidth:120},{title:"MSISDN",key:"msisdn",align:"center",minWidth:120},{title:"ICCID",key:"iccid",minWidth:120,align:"center"},{title:this.$t("stock.usedstate"),key:"cardStatus",align:"center",minWidth:120,render:function(e,a){var i=a.row,n="1"==i.cardStatus?t.$t("order.Normal"):"2"==i.cardStatus?t.$t("order.Terminated"):"3"==i.cardStatus?t.$t("order.Suspend"):"";return e("label",n)}},{title:this.$t("stock.cardtype"),key:"cardForm",align:"center",minWidth:120,render:function(e,a){var i=a.row,n="1"===i.cardForm?t.$t("stock.PhysicalSIM"):"2"===i.cardForm?t.$t("stock.eSIM"):"3"===i.cardForm?t.$t("stock.TSIM"):"4"===i.cardForm?t.$t("support.imsi"):"";return e("label",n)}},{title:this.$t("stock.Storagetime"),key:"createTime",align:"center",minWidth:120},{title:this.$t("stock.attributableChannel"),key:"corpName",align:"center",minWidth:120},{title:this.$t("stock.action"),slot:"action",align:"center",minWidth:120}],data:[],corpId:"",more:{msisdn:"123",imsi:"1234",iccid:"12345",verCode:"234",pin:"12",puk:"23",adm:"test1.adm",sdb:"test2.adm"},rules:{},uploadUrl:"",file:null,editObj:{subChannel:"",file:""},ruleEditValidate:{subChannel:[{required:!0,type:"string",message:this.$t("stock.subChannelProviderEmpty")}],file:[{required:!0,validator:e,trigger:"change"}]},uploadList:[],SubmodelData:[{ICCID:"********"}],SubmodelColumns:[{title:"ICCID",key:"ICCID"}]}},mounted:function(){this.cooperationMode=sessionStorage.getItem("cooperationMode"),"3"!=this.cooperationMode&&(this.getAttributableChannelList(),this.goPageFirst(1))},methods:{goPageFirst:function(t){var e=this;this.loading=!0;var a=this,i=t,n=10,s=this.iccid;Object(o["F"])({userName:this.$store.state.user.userName}).then((function(l){if("0000"==l.code){var r=l.data,c=e.attributableChannel,d=e.cooperationMode,u=e.isHasPackage;Object(o["k"])({pageNumber:i,pageSize:n,corpId:r,iccid:s,cooperationMode:d,chooseCorpId:c,isHasPackage:u}).then((function(i){"0000"==i.code&&(a.loading=!1,e.searchloading=!1,e.page=t,e.currentPage=t,e.total=i.data.total,e.data=i.data.record)})).catch((function(t){console.error(t)})).finally((function(){e.loading=!1,e.searchloading=!1}))}})).catch((function(t){})).finally((function(){}))},details:function(t){var e=this;this.detailsloading=!0,Object(o["j"])({imsi:t.imsi}).then((function(t){"0000"==t.code&&(e.modal5=!0,e.more=t.data,e.detailsloading=!1)})).catch((function(t){console.error(t)})).finally((function(){e.detailsloading=!1}))},search:function(){this.goPageFirst(1),this.searchloading=!0},goPage:function(t){this.goPageFirst(t)},exportFile:function(){var t=this;this.downloading=!0,Object(o["G"])({corpId:this.corpId,userId:this.corpId,cooperationMode:this.cooperationMode,chooseCorpId:this.attributableChannel}).then((function(e){t.exportModal=!0,t.taskId=e.data.taskId,t.taskName=e.data.taskName,t.downloading=!1})).catch((function(){return t.downloading=!1}))},transfer:function(){this.subChannelModal=!0,this.getAttributableChannelList2()},fileSuccess:function(t,e,a){this.message=this.$t("support.downTemplateFilelAndUpload")},handleError:function(t,e){var a=this;setTimeout((function(){a.uploading=!1,a.$Notice.warning({title:this.$t("common.Error"),desc:this.$t("support.uploadFailed")})}),3e3)},handleBeforeUpload:function(t,e){return/^.+(\.csv)$/.test(t.name)?(this.file=t,this.uploadList=e):this.$Notice.warning({title:this.$t("buymeal.fileformat"),desc:this.$t("support.files")+t.name+this.$t("buymeal.incorrect")}),!1},fileUploading:function(t,e,a){this.message=this.$t("support.fileUploadedAndProgressDisappears")},removeFile:function(){this.file=""},downloadFile:function(){this.$refs.modelTable.exportCsv({filename:this.$t("showiccid_mngr"),columns:this.SubmodelColumns,data:this.SubmodelData})},submit:function(){var t=this;this.$refs["editObj"].validate((function(e){if(e){t.submitLoading=!0;var a=new FormData;a.append("file",t.file),a.append("parentCorpId",t.editObj.subChannel.split("-")[1]),a.append("corpId",t.editObj.subChannel.split("-")[0]),Object(o["L"])(a).then((function(e){"0000"===e.code&&(t.$Notice.success({title:t.$t("address.Operationreminder"),desc:t.$t("common.Successful")}),t.submitLoading=!1,t.file=null,t.subChannelModal=!1,t.goPageFirst(1),t.uploadList=[],t.$refs["editObj"].resetFields())}))}}))},hanldeDateClear:function(){this.searchBeginTime="",this.searchEndTime=""},handleDateChange:function(t){var e=this.time_slot[0]||"",a=this.time_slot[1]||"";if(""!=e&&""!=a){var i=Object(s["a"])(t,2);this.searchBeginTime=i[0],this.searchEndTime=i[1]}},ok:function(){},cancelModal:function(){this.modal5=!1,this.exportModal=!1,this.subChannelModal=!1,this.$refs["editObj"].resetFields(),this.file=""},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName),corpId:encodeURIComponent(this.corpId)}}),this.exportModal=!1},getAttributableChannelList:function(){var t=this;Object(o["F"])({userName:this.$store.state.user.userName}).then((function(e){t.corpId=e.data,Object(o["q"])({corpId:t.corpId,selfContain:!0}).then((function(e){if("0000"===e.code){if(1==t.cooperationMode)t.attributableChannelList=e.data;else{var a=[{corpName:e.data[0].corpName,corpId:e.data[0].corpId}];t.attributableChannelList=a}1==t.attributableChannelList.length&&(t.attributableChannel=t.attributableChannelList[0].corpId)}})).catch((function(t){console.log(t)}))}))},getAttributableChannelList2:function(){var t=this;Object(o["F"])({userName:this.$store.state.user.userName}).then((function(e){t.corpId=e.data,Object(o["q"])({corpId:t.corpId,selfContain:!1}).then((function(e){"0000"===e.code&&(t.attributableChannelList2=e.data)})).catch((function(t){console.log(t)}))}))}}},r=l,c=(a("c153"),a("2877")),d=Object(c["a"])(r,i,n,!1,null,null,null);e["default"]=d.exports}}]);