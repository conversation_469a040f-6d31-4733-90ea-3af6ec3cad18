(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-855242c8"],{"00b4":function(t,e,n){"use strict";n("ac1f");var a=n("23e7"),i=n("c65b"),r=n("1626"),o=n("825a"),s=n("577e"),c=function(){var t=!1,e=/[ac]/;return e.exec=function(){return t=!0,/./.exec.apply(this,arguments)},!0===e.test("abc")&&t}(),l=/./.test;a({target:"RegExp",proto:!0,forced:!c},{test:function(t){var e=o(this),n=s(t),a=e.exec;if(!r(a))return i(l,e,n);var c=i(a,e,n);return null!==c&&(o(c),!0)}})},"0ae5":function(t,e,n){"use strict";n("d9e2"),n("d3b7"),n("25f0");e["a"]={methods:{validateDate:function(t,e,n){var a=this.form.endDate||this.form.endTime||this.searchEndTime.toString(),i=this.form.startDate||this.form.startTime||this.searchBeginTime.toString();a&&i?"startDate"===t.field||"startTime"===t.field||"beginMonth"===t.field?this.$time(i,">",a)?n(new Error("开始时间不能大于结束时间")):n():a<i?n(new Error("结束时间不能小于开始时间")):n():n()}}}},"129f":function(t,e,n){"use strict";t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!==t&&e!==e}},1537:function(t,e,n){},"1b61":function(t,e,n){},"28ae":function(t,e,n){"use strict";n("1537")},"2a6f":function(t,e,n){"use strict";n("1b61")},"63e1":function(t,e,n){"use strict";var a=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"_img_view"},[e("img",{staticClass:"img",attrs:{src:t.heard_src,width:"100%",height:"100%"}})]),e("div",{staticStyle:{width:"100%",margin:"20px 0"}},[e("div",{staticStyle:{display:"flex","flex-wrap":"wrap","align-items":"flex-start","justify-content":"space-between"}},[e("div",{staticStyle:{width:"50%"}},[e("div",{staticClass:"color_front",staticStyle:{width:"250px"}},[t._v(t._s(t.AccountNo))]),e("div",{staticStyle:{width:"250px"}},[t._v(t._s(t.address))])]),e("div",{staticStyle:{width:"50%",display:"flex","flex-wrap":"wrap","align-items":"center","justify-content":"space-between"}},[e("p",{staticStyle:{width:"100%",display:"flex","justify-content":"flex-start"}},[e("span",{staticStyle:{width:"25%"}},[t._v("Amount Due\n              :")]),t._v(" "+t._s(t.AmountDue))]),e("p",{staticStyle:{width:"100%",display:"flex","justify-content":"flex-start"}},[e("span",{staticStyle:{width:"25%"}},[t._v("Invoice No.\n              :")]),t._v(t._s(t.InvoiceNo))]),e("p",{staticStyle:{width:"100%",display:"flex","justify-content":"flex-start"}},[e("span",{staticStyle:{width:"25%"}},[t._v("Account No.\n              :")]),t._v(t._s(t.AccountNo))]),e("p",{staticStyle:{width:"100%",display:"flex","justify-content":"flex-start"}},[e("span",{staticStyle:{width:"25%"}},[t._v("Invoice Date\n              :")]),t._v(t._s(t.InvoiceDate))])]),e("div",{staticStyle:{width:"100%",padding:"30px","text-align":"center","font-size":"25px",color:"#000000"}},[t._v("\n          "+t._s(t.FileTitle)+"\n        ")]),e("div",{staticStyle:{width:"100%"}},[3==t.invoiceForm.length?e("Form",{ref:"invoiceForm",attrs:{model:t.invoiceForm,rules:t.rule}},[e("Table",{attrs:{border:"",columns:t.columns,data:t.data},scopedSlots:t._u([{key:"description",fn:function(n){n.row;var a=n.index;return[e("FormItem",{attrs:{prop:"invoiceNameDesc"}},[e("Input",{staticStyle:{width:"200px","margin-top":"15px"},attrs:{placeholder:"请输入description",clearable:!0},model:{value:t.invoiceForm[a].invoiceNameDesc,callback:function(e){t.$set(t.invoiceForm[a],"invoiceNameDesc",e)},expression:"invoiceForm[index].invoiceNameDesc"}})],1)]}},{key:"billingPeriod",fn:function(n){n.row;var a=n.index;return[e("FormItem",{attrs:{prop:"billingPeriod"}},[e("Input",{staticStyle:{width:"200px","margin-top":"15px"},attrs:{placeholder:"请输入billingPeriod",clearable:!0},model:{value:t.invoiceForm[a].billingPeriod,callback:function(e){t.$set(t.invoiceForm[a],"billingPeriod",e)},expression:"invoiceForm[index].billingPeriod"}})],1)]}}],null,!1,1589144393)})],1):t._e()],1),e("div",{staticClass:"description_box"},[e("span",{staticClass:"textual_box"},[t._v("Amount before Tax:")]),e("span",{staticClass:"currencyCode_box"},[t._v(t._s(t.currencyCode))]),e("span",{staticClass:"currencyCode_box"},[t._v(t._s(t.TotalAmount))])]),e("div",{staticClass:"description_box"},[e("span",{staticClass:"textual_box"},[t._v("TAX:")]),e("span",{staticClass:"currencyCode_box"},[t._v(t._s(t.currencyCode))]),e("span",{staticClass:"currencyCode_box"},[t._v(t._s(t.Tax))])]),e("div",{staticClass:"description_box"},[e("span",{staticClass:"textual_box"},[t._v("Total Amount Due:")]),e("span",{staticClass:"currencyCode_box"},[t._v(t._s(t.currencyCode))]),e("span",{staticClass:"currencyCode_box"},[t._v(t._s(t.TotalAmount))])]),e("Input",{staticClass:"input-call",staticStyle:{"margin-top":"50px"},attrs:{type:"textarea",autosize:!0,placeholder:"发票说明"},model:{value:t.InvoiceDesc,callback:function(e){t.InvoiceDesc=e},expression:"InvoiceDesc"}})],1)])])},i=[],r=(n("a15b"),n("14d9"),n("d3b7"),n("159b"),n("f121"),{data:function(){return{heard_src:n("7129"),rule:{}}},props:{AccountNo:{type:String,default:""},address:{type:String,default:""},AmountDue:{type:String,default:""},InvoiceNo:{type:String,default:""},InvoiceDate:{type:String,default:""},FileTitle:{type:String,default:"INVOICE"},AmountTax:{type:String,default:""},Tax:{type:String,default:""},TotalAmount:{type:String,default:""},currencyCode:{type:String,default:""},InvoiceDesc:{type:String,default:""},columns:{type:Array,default:function(){return[]}},data:{type:Array,default:function(){return[]}},invoiceForm:{type:Array,default:function(){return[]}}},methods:{validateInvoiceForm:function(){var t=[];return this.invoiceForm.forEach((function(e,n){e.invoiceNameDesc||t.push("第 ".concat(n+1," 行的 description 是必填项")),e.billingPeriod||t.push("第 ".concat(n+1," 行的 billingPeriod 是必填项"))})),!(t.length>0)||(this.$Message["warning"]({content:t.join("; "),background:!0,duration:5}),!1)}},mounted:function(){},watch:{InvoiceDesc:{handler:function(t,e){this.$emit("InvoiceDesc",t)},deep:!0}}}),o=r,s=(n("28ae"),n("2877")),c=Object(s["a"])(o,a,i,!1,null,null,null);e["a"]=c.exports},"705b":function(t,e,n){"use strict";n.r(e);n("ac1f"),n("841c");var a=function(){var t=this,e=t._self._c;return e("Card",[e("Form",{ref:"form",attrs:{"label-width":80,model:t.form,inline:""}},[e("FormItem",{attrs:{label:"渠道商名称"}},[e("Select",{staticStyle:{width:"260px"},attrs:{multiple:""},model:{value:t.form.corpName,callback:function(e){t.$set(t.form,"corpName",e)},expression:"form.corpName"}},t._l(t.corpNameList,(function(n){return e("Option",{key:n.corpId,attrs:{value:n.corpId}},[t._v(t._s(n.corpName))])})),1)],1),e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.search()}}},[t._v("搜索")]),t._v("  \n    "),e("Button",{directives:[{name:"has",rawName:"v-has",value:"historyBill",expression:"'historyBill'"}],attrs:{type:"warning"},on:{click:function(e){return t.GotoHistroy()}}},[t._v("历史账单")]),t._v("  \n  ")],1),e("Table",{staticStyle:{width:"100%","margin-top":"50px"},attrs:{columns:t.columns12,data:t.talbedata,loading:t.loading}}),e("div",{staticStyle:{"margin-left":"38%","margin-top":"100px","margin-bottom":"160px"}},[e("Page",{attrs:{total:t.total,current:t.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.currentPage=e},"on-change":t.goPage}})],1)],1)},i=[],r=(n("d9e2"),n("14d9"),n("b680"),n("d3b7"),n("00b4"),n("5319"),n("159b"),n("0ae5")),o=n("d4fb"),s=n("63e1"),c=(n("c70b"),{mixins:[r["a"]],components:{invoiceTemplate:s["a"]},data:function(){var t=function(t,e,n){var a=e;"-"===e.substr(0,1)&&(a=e.substr(1,e.length));var i=/^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;!a||i.test(a)?n():n(new Error(t.message))};return{corpNameList:[],corpNameListCorpId:[],columns12:[{title:"渠道商名称",key:"corpName",align:"center"},{title:"关联销售账号",key:"account",align:"center"},{title:"合作模式",key:"cooperationMode",align:"center",render:function(t,e){var n=e.row,a="1"==n.cooperationMode?"代销":"2"==n.cooperationMode?"a2z":"3"==n.cooperationMode?"资源合作":"";return t("label",a)}},{title:"币种",key:"currencyCode",align:"center",render:function(t,e){var n=e.row,a="156"==n.currencyCode?"CNY":"840"==n.currencyCode?"USD":"344"==n.currencyCode?"HKD":"";return t("label",a)}},{title:"承诺销售金额",key:"contractSellAmount",align:"center"},{title:"已完成承诺金额",key:"completedAmount",align:"center"},{title:"本期账单",key:"currentPeriodBill",align:"center"},{title:"欠费金额",key:"arrears",align:"center"}],form:{corpName:[]},searchloading:!1,data:[],talbedata:[],row:{},currentPage:1,total:0,pageSize:10,page:1,spanData:[],loading:!1,rule:{accountAdjustment:[{required:!0,message:"请输入调账金额",trigger:"blur"},{validator:t,message:"最高支持8位整数和2位小数的正负数"}],flowAdjustment:[{required:!0,message:"请输入流量收入",trigger:"blur"},{validator:t,message:"最高支持8位整数和2位小数的正负数"}],imsiAdjustment:[{required:!0,message:"请输入IMSI费收入",trigger:"blur"},{validator:t,message:"最高支持8位整数和2位小数的正负数"}]},searchBeginTime:"",searchEndTime:"",type:"",cooperationMode:""}},created:function(){this.getCoprList()},mounted:function(){},methods:{getCoprList:function(){var t=this;Object(o["b"])({userName:this.$store.state.user.userName}).then((function(e){"0000"==e.code&&(t.corpNameList=e.data,e.data.forEach((function(e){t.corpNameListCorpId.push(e.corpId)})))}))},GotoHistroy:function(){this.$router.push({path:"/channelSellHistory"})},search:function(){this.searchloading=!0,this.goPageFirst(1),this.currentPage=1},goPage:function(t){this.goPageFirst(t)},goPageFirst:function(t){var e=this;this.loading=!0;var n=this,a=t,i=10;Object(o["c"])({corpId:this.form.corpName.length?this.form.corpName:this.corpNameListCorpId,pageNum:a,pageSize:i,userId:this.$store.state.user.userId}).then((function(a){"0000"==a.code&&(n.loading=!1,e.searchloading=!1,e.page=t,e.total=a.count,e.talbedata=a.data)})).catch((function(t){console.error(t)})).finally((function(){console.log(111),e.loading=!1,e.searchloading=!1}))},test:function(t){var e=/(\d{1,3})(?=(\d{3})+(?:$|\.))/g;return(t+"").replace(e,"$1,")},formatNumber:function(t){var e=(t/100).toFixed(2),n=new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2,useGrouping:!0}).format(parseFloat(e));return n},getSpanData:function(t){var e=this,n=0;e.spanData=[],t.forEach((function(a,i){0===i?(e.spanData.push(1),n=0):t[i].salesChannel===t[i-1].salesChannel&&t[i].statTime===t[i-1].statTime?(e.spanData[n]+=1,e.spanData.push(0)):(e.spanData.push(1),n=i)}))}}}),l=c,u=(n("2a6f"),n("2877")),d=Object(u["a"])(l,a,i,!1,null,null,null);e["default"]=d.exports},7129:function(t,e,n){t.exports=n.p+"img/china_mobail.1f46cf27.png"},"841c":function(t,e,n){"use strict";var a=n("c65b"),i=n("d784"),r=n("825a"),o=n("7234"),s=n("1d80"),c=n("129f"),l=n("577e"),u=n("dc4a"),d=n("14c3");i("search",(function(t,e,n){return[function(e){var n=s(this),i=o(e)?void 0:u(e,t);return i?a(i,e,n):new RegExp(e)[t](l(n))},function(t){var a=r(this),i=l(t),o=n(e,a,i);if(o.done)return o.value;var s=a.lastIndex;c(s,0)||(a.lastIndex=0);var u=d(a,i);return c(a.lastIndex,s)||(a.lastIndex=s),null===u?-1:u.index}]}))},b680:function(t,e,n){"use strict";var a=n("23e7"),i=n("e330"),r=n("5926"),o=n("408a"),s=n("1148"),c=n("d039"),l=RangeError,u=String,d=Math.floor,p=i(s),f=i("".slice),m=i(1..toFixed),h=function(t,e,n){return 0===e?n:e%2===1?h(t,e-1,n*t):h(t*t,e/2,n)},g=function(t){var e=0,n=t;while(n>=4096)e+=12,n/=4096;while(n>=2)e+=1,n/=2;return e},v=function(t,e,n){var a=-1,i=n;while(++a<6)i+=e*t[a],t[a]=i%1e7,i=d(i/1e7)},y=function(t,e){var n=6,a=0;while(--n>=0)a+=t[n],t[n]=d(a/e),a=a%e*1e7},b=function(t){var e=6,n="";while(--e>=0)if(""!==n||0===e||0!==t[e]){var a=u(t[e]);n=""===n?a:n+p("0",7-a.length)+a}return n},x=c((function(){return"0.000"!==m(8e-5,3)||"1"!==m(.9,0)||"1.25"!==m(1.255,2)||"1000000000000000128"!==m(0xde0b6b3a7640080,0)}))||!c((function(){m({})}));a({target:"Number",proto:!0,forced:x},{toFixed:function(t){var e,n,a,i,s=o(this),c=r(t),d=[0,0,0,0,0,0],m="",x="0";if(c<0||c>20)throw new l("Incorrect fraction digits");if(s!==s)return"NaN";if(s<=-1e21||s>=1e21)return u(s);if(s<0&&(m="-",s=-s),s>1e-21)if(e=g(s*h(2,69,1))-69,n=e<0?s*h(2,-e,1):s/h(2,e,1),n*=4503599627370496,e=52-e,e>0){v(d,0,n),a=c;while(a>=7)v(d,1e7,0),a-=7;v(d,h(10,a,1),0),a=e-1;while(a>=23)y(d,1<<23),a-=23;y(d,1<<a),v(d,1,1),y(d,2),x=b(d)}else v(d,0,n),v(d,1<<-e,0),x=b(d)+p("0",c);return c>0?(i=x.length,x=m+(i<=c?"0."+p("0",c-i)+x:f(x,0,i-c)+"."+f(x,i-c))):x=m+x,x}})},d4fb:function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"d",(function(){return o})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){return c}));var a=n("66df"),i="/cms",r=function(t){return a["a"].request({url:i+"/channel/getChannelByEmail",params:t,method:"get"})},o=function(t){return a["a"].request({url:i+"/IBoss/getPage",data:t,method:"post"})},s=function(t){return a["a"].request({url:i+"/channel/getChannelSellData",data:t,method:"post"})},c=function(t){return a["a"].request({url:i+"/IBoss/downLoad",params:t,method:"get",responseType:"blob"})}}}]);