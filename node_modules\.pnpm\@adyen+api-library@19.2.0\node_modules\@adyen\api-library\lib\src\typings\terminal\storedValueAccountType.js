"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.StoredValueAccountType = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var StoredValueAccountType;
(function (StoredValueAccountType) {
    StoredValueAccountType[StoredValueAccountType["GiftCard"] = 'GiftCard'] = "GiftCard";
    StoredValueAccountType[StoredValueAccountType["Other"] = 'Other'] = "Other";
    StoredValueAccountType[StoredValueAccountType["PhoneCard"] = 'PhoneCard'] = "PhoneCard";
})(StoredValueAccountType = exports.StoredValueAccountType || (exports.StoredValueAccountType = {}));
//# sourceMappingURL=storedValueAccountType.js.map