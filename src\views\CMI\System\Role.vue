<template>
  <div class="role-management">
    <ContentWrap>
      <div class="page-header">
        <h2>角色管理</h2>
        <p>管理系统角色和权限配置</p>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <el-button type="primary" @click="handleAdd">
          <Icon icon="vi-ep:plus" class="mr-5px" />
          新增角色
        </el-button>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <el-table v-loading="loading" :data="tableData" stripe border>
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="roleName" label="角色名称" min-width="120" />
          <el-table-column prop="roleCode" label="角色编码" min-width="120" />
          <el-table-column prop="description" label="角色描述" min-width="200" />
          <el-table-column prop="userCount" label="用户数量" width="100" />
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.status === '1' ? 'success' : 'danger'">
                {{ row.status === '1' ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="160" />
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button type="warning" size="small" @click="handlePermission(row)">
                权限配置
              </el-button>
              <el-button
                :type="row.status === '1' ? 'warning' : 'success'"
                size="small"
                @click="handleToggleStatus(row)"
              >
                {{ row.status === '1' ? '禁用' : '启用' }}
              </el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </ContentWrap>

    <!-- 角色编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="formData.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色编码" prop="roleCode">
          <el-input v-model="formData.roleCode" placeholder="请输入角色编码" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 权限配置对话框 -->
    <el-dialog v-model="permissionDialogVisible" title="权限配置" width="800px">
      <el-tree
        ref="permissionTreeRef"
        :data="permissionTreeData"
        :props="{ children: 'children', label: 'title' }"
        show-checkbox
        node-key="id"
        :default-checked-keys="checkedPermissions"
      />
      <template #footer>
        <el-button @click="permissionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSavePermission" :loading="permissionLoading">
          保存权限
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

// 表格数据
const loading = ref(false)
const tableData = ref([
  {
    id: 1,
    roleName: '超级管理员',
    roleCode: 'admin',
    description: '系统超级管理员，拥有所有权限',
    userCount: 1,
    status: '1',
    createTime: '2024-01-01 10:00:00'
  },
  {
    id: 2,
    roleName: '管理员',
    roleCode: 'manager',
    description: '系统管理员，拥有大部分管理权限',
    userCount: 3,
    status: '1',
    createTime: '2024-01-02 10:00:00'
  }
])

// 对话框
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const submitLoading = ref(false)

// 权限配置对话框
const permissionDialogVisible = ref(false)
const permissionLoading = ref(false)
const checkedPermissions = ref<string[]>([])
const currentRoleId = ref<number>()

// 表单数据
const formData = reactive({
  id: '',
  roleName: '',
  roleCode: '',
  description: '',
  status: '1'
})

// 表单验证规则
const formRules = {
  roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  roleCode: [{ required: true, message: '请输入角色编码', trigger: 'blur' }]
}

// 权限树数据
const permissionTreeData = ref([
  {
    id: 'system',
    title: '系统管理',
    children: [
      { id: 'account_list', title: '账户管理' },
      { id: 'pwd_mngr', title: '密码管理' },
      { id: 'pri_mngr', title: '角色管理' },
      { id: 'login_mngr', title: '登录日志' }
    ]
  },
  {
    id: 'resource',
    title: '资源管理',
    children: [
      { id: 'msisdn', title: 'MSISDN管理' },
      { id: 'iccid', title: 'ICCID管理' },
      { id: 'imsi', title: 'IMSI管理' },
      { id: 'supplyImsi', title: '消息上报IMSI管理' }
    ]
  },
  {
    id: 'product',
    title: '产品管理',
    children: [
      { id: 'makeCard', title: '制卡管理' },
      { id: 'masterCard', title: '主卡管理' },
      { id: 'cardPool', title: '卡池管理' },
      { id: 'vimsi', title: 'VIMSI管理' }
    ]
  },
  {
    id: 'customer',
    title: '客户管理',
    children: [
      { id: 'channelManage', title: '渠道商管理' },
      { id: 'cooperativeManage', title: '合作商管理' }
    ]
  }
])

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增角色'
  isEdit.value = false
  dialogVisible.value = true
  resetFormData()
}

// 编辑
const handleEdit = (row: any) => {
  dialogTitle.value = '编辑角色'
  isEdit.value = true
  dialogVisible.value = true
  Object.assign(formData, row)
}

// 删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除该角色吗？', '提示', {
      type: 'warning'
    })
    ElMessage.success('删除成功')
    loadTableData()
  } catch (error) {
    // 用户取消删除
  }
}

// 切换状态
const handleToggleStatus = async (row: any) => {
  const action = row.status === '1' ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}该角色吗？`, '提示', {
      type: 'warning'
    })
    row.status = row.status === '1' ? '0' : '1'
    ElMessage.success(`${action}成功`)
  } catch (error) {
    // 用户取消操作
  }
}

// 权限配置
const handlePermission = (row: any) => {
  currentRoleId.value = row.id
  permissionDialogVisible.value = true
  // 加载角色权限
  loadRolePermissions(row.id)
}

// 提交表单
const handleSubmit = async () => {
  submitLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
    dialogVisible.value = false
    loadTableData()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 保存权限
const handleSavePermission = async () => {
  permissionLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('权限保存成功')
    permissionDialogVisible.value = false
  } catch (error) {
    ElMessage.error('权限保存失败')
  } finally {
    permissionLoading.value = false
  }
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    id: '',
    roleName: '',
    roleCode: '',
    description: '',
    status: '1'
  })
}

// 加载角色权限
const loadRolePermissions = (roleId: number) => {
  // 模拟加载角色权限
  if (roleId === 1) {
    checkedPermissions.value = ['account_list', 'pwd_mngr', 'pri_mngr', 'login_mngr']
  } else {
    checkedPermissions.value = ['account_list']
  }
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadTableData()
})
</script>

<style scoped>
.role-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.page-header p {
  color: #606266;
  margin: 0;
}

.action-section {
  margin-bottom: 20px;
}

.table-section {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
