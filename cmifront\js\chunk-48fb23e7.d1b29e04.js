(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-48fb23e7"],{"07ac":function(t,e,i){"use strict";var a=i("23e7"),n=i("6f53").values;a({target:"Object",stat:!0},{values:function(t){return n(t)}})},2315:function(t,e,i){"use strict";i.d(e,"h",(function(){return o})),i.d(e,"e",(function(){return s})),i.d(e,"f",(function(){return r})),i.d(e,"a",(function(){return c})),i.d(e,"i",(function(){return l})),i.d(e,"b",(function(){return d})),i.d(e,"g",(function(){return u})),i.d(e,"d",(function(){return p})),i.d(e,"c",(function(){return m}));var a=i("66df"),n="/stat",o=function(t){return a["a"].request({url:n+"/channelincome/month/getChannelBill",data:t,method:"post"})},s=function(t){return a["a"].request({url:"/charging/atzCharging/getChargingByChannel",params:t,method:"get"})},r=function(t){return a["a"].request({url:"pms/imsiAmount/getChannelImsiAmount",params:t,method:"post"})},c=function(t){return a["a"].request({url:"/cms/IBoss/payBill",data:t,method:"POST",contentType:"multipart/form-data"})},l=function(t){return a["a"].request({url:"/cms/IBoss/cancelPayBill/",params:t,method:"get"})},d=function(t){return a["a"].request({url:"/cms/channel/channelExport",data:t,method:"post"})},u=function(t){return a["a"].request({url:"cms/channel/deposit/record",data:t,method:"post"})},p=function(t){return a["a"].request({url:"cms/channel/deposit/recordExport",data:t,method:"post",responseType:"blob"})},m=function(t){return a["a"].request({url:n+"/channelincome/month/exportChannelBill",data:t,method:"post",responseType:"blob"})}},"3f7e":function(t,e,i){"use strict";var a=i("b5db"),n=a.match(/firefox\/(\d+)/i);t.exports=!!n&&+n[1]},"466d":function(t,e,i){"use strict";var a=i("c65b"),n=i("d784"),o=i("825a"),s=i("7234"),r=i("50c4"),c=i("577e"),l=i("1d80"),d=i("dc4a"),u=i("8aa5"),p=i("14c3");n("match",(function(t,e,i){return[function(e){var i=l(this),n=s(e)?void 0:d(e,t);return n?a(n,e,i):new RegExp(e)[t](c(i))},function(t){var a=o(this),n=c(t),s=i(e,a,n);if(s.done)return s.value;if(!a.global)return p(a,n);var l=a.unicode;a.lastIndex=0;var d,m=[],h=0;while(null!==(d=p(a,n))){var g=c(d[0]);m[h]=g,""===g&&(a.lastIndex=u(n,r(a.lastIndex),l)),h++}return 0===h?null:m}]}))},"4e82":function(t,e,i){"use strict";var a=i("23e7"),n=i("e330"),o=i("59ed"),s=i("7b0b"),r=i("07fa"),c=i("083a"),l=i("577e"),d=i("d039"),u=i("addb"),p=i("a640"),m=i("3f7e"),h=i("99f4"),g=i("1212"),f=i("ea83"),v=[],y=n(v.sort),b=n(v.push),I=d((function(){v.sort(void 0)})),x=d((function(){v.sort(null)})),k=p("sort"),S=!d((function(){if(g)return g<70;if(!(m&&m>3)){if(h)return!0;if(f)return f<603;var t,e,i,a,n="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:i=3;break;case 68:case 71:i=4;break;default:i=2}for(a=0;a<47;a++)v.push({k:e+a,v:i})}for(v.sort((function(t,e){return e.v-t.v})),a=0;a<v.length;a++)e=v[a].k.charAt(0),n.charAt(n.length-1)!==e&&(n+=e);return"DGBEFHACIJK"!==n}})),w=I||!x||!k||!S,T=function(t){return function(e,i){return void 0===i?-1:void 0===e?1:void 0!==t?+t(e,i)||0:l(e)>l(i)?1:-1}};a({target:"Array",proto:!0,forced:w},{sort:function(t){void 0!==t&&o(t);var e=s(this);if(S)return void 0===t?y(e):y(e,t);var i,a,n=[],l=r(e);for(a=0;a<l;a++)a in e&&b(n,e[a]);u(n,T(t)),i=r(n),a=0;while(a<i)e[a]=n[a++];while(a<l)c(e,a++);return e}})},"6f53":function(t,e,i){"use strict";var a=i("83ab"),n=i("d039"),o=i("e330"),s=i("e163"),r=i("df75"),c=i("fc6a"),l=i("d1e7").f,d=o(l),u=o([].push),p=a&&n((function(){var t=Object.create(null);return t[2]=2,!d(t,2)})),m=function(t){return function(e){var i,n=c(e),o=r(n),l=p&&null===s(n),m=o.length,h=0,g=[];while(m>h)i=o[h++],a&&!(l?i in n:d(n,i))||u(g,t?[i,n[i]]:n[i]);return g}};t.exports={entries:m(!0),values:m(!1)}},"99f4":function(t,e,i){"use strict";var a=i("b5db");t.exports=/MSIE|Trident/.test(a)},ba27:function(t,e,i){"use strict";i.d(e,"i",(function(){return o})),i.d(e,"d",(function(){return s})),i.d(e,"c",(function(){return r})),i.d(e,"h",(function(){return c})),i.d(e,"e",(function(){return l})),i.d(e,"f",(function(){return d})),i.d(e,"k",(function(){return u})),i.d(e,"g",(function(){return p})),i.d(e,"a",(function(){return m})),i.d(e,"j",(function(){return h})),i.d(e,"b",(function(){return g}));var a=i("66df"),n=(i("1157"),"/order/blankCardOrder"),o=function(t){return a["a"].request({url:n+"/getOrder",params:t,method:"get"})},s=function(t){return a["a"].request({url:n+"/comfirmOrder/"+t.id,data:t,method:"put"})},r=function(t){return a["a"].request({url:n+"/cancelOrder/".concat(t),method:"put"})},c=function(t){return a["a"].request({url:n+"/download",params:t,method:"get",responseType:"blob"})},l=function(t){return a["a"].request({url:n+"/generateInvoice",data:t,method:"post"})},d=function(t){return a["a"].request({url:n+"/regenerateInvoice",data:t,method:"post"})},u=function(t){return a["a"].request({url:n+"/deliver",data:t,method:"put",contentType:"multipart/form-data"})},p=function(t){return a["a"].request({url:n+"/getInvoiceInfo",params:t,method:"get"})},m=function(t){return a["a"].request({url:"cms/channel/getInfo4Order",params:t,method:"get"})},h=function(t){return a["a"].request({url:n+"/rollback",params:t,method:"put"})},g=function(t){return a["a"].request({url:n+"/export",data:t,method:"post",responseType:"blob"})}},d511:function(t,e,i){},e28c:function(t,e,i){"use strict";i.r(e);i("caad"),i("b0c0"),i("ac1f"),i("841c");var a=function(){var t=this,e=t._self._c;return e("Card",[e("Form",{ref:"form",attrs:{"label-width":90,model:t.form,rules:t.ruleInline,inline:""}},[e("FormItem",{attrs:{label:"收入类型:",prop:"incomeType"}},[e("Select",{attrs:{filterable:"",placeholder:"下拉选择收入类型",clearable:""},on:{"on-change":t.changeType},model:{value:t.form.incomeType,callback:function(e){t.$set(t.form,"incomeType",e)},expression:"form.incomeType"}},t._l(t.typeList,(function(i,a){return e("Option",{key:a,attrs:{value:i.value}},[t._v(t._s(i.label))])})),1)],1),"5"!=t.form.incomeType&&"6"!=t.form.incomeType?e("FormItem",{key:"beginMonth",attrs:{label:"开始月份:",prop:"beginMonth"}},[e("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择开始月份",editable:!0},on:{"on-change":t.handleChangeBeginMonth},model:{value:t.form.beginMonth,callback:function(e){t.$set(t.form,"beginMonth",e)},expression:"form.beginMonth"}})],1):t._e(),"5"!=t.form.incomeType&&"6"!=t.form.incomeType?e("FormItem",{key:"endMonth",attrs:{label:"结束月份:",prop:"endMonth"}},[e("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择结束月份",editable:!0},on:{"on-change":t.handleChangeEndMonth},model:{value:t.form.endMonth,callback:function(e){t.$set(t.form,"endMonth",e)},expression:"form.endMonth"}})],1):t._e(),"5"!=t.form.incomeType&&"6"!=t.form.incomeType&&"4"!=t.form.incomeType&&t.form.incomeType?e("FormItem",{attrs:{label:"1"==t.form.incomeType?"选择客户:":"公司名称:"}},[e("Select",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"1"==t.form.incomeType?"下拉选择客户":"下拉选择公司",filterable:""},model:{value:t.form.corpId,callback:function(e){t.$set(t.form,"corpId",e)},expression:"form.corpId"}},t._l(t.corpLists,(function(i,a){return e("Option",{key:a,attrs:{value:i.corpId}},[t._v(t._s("1"==t.form.incomeType?i.corpName:i.companyName))])})),1)],1):t._e(),"4"==t.form.incomeType&&t.form.incomeType?e("FormItem",{attrs:{label:"公司名称:"}},[e("Select",{staticStyle:{width:"200px"},attrs:{clearable:"",placeholder:"下拉选择公司名称",filterable:""},model:{value:t.form.customer,callback:function(e){t.$set(t.form,"customer",e)},expression:"form.customer"}},t._l(t.customerList,(function(i){return e("Option",{key:i.corpId,attrs:{value:i.corpId}},[t._v(t._s(i.companyName))])})),1)],1):t._e(),"5"==t.form.incomeType?e("FormItem",{attrs:{label:"资源供应商:"}},[e("Select",{attrs:{filterable:"",placeholder:"请选择资源供应商",clearable:""},model:{value:t.form.supplierShortenName,callback:function(e){t.$set(t.form,"supplierShortenName",e)},expression:"form.supplierShortenName"}},t._l(t.supplierShortenList,(function(i,a){return e("Option",{key:a,attrs:{value:a}},[t._v(t._s(a))])})),1)],1):t._e(),"5"==t.form.incomeType?e("FormItem",{attrs:{label:"供应商编码:"}},[e("Input",{attrs:{placeholder:"请输入供应商编码",clearable:!0},model:{value:t.form.supplierShortenCode,callback:function(e){t.$set(t.form,"supplierShortenCode",e)},expression:"form.supplierShortenCode"}})],1):t._e(),"5"==t.form.incomeType||"6"==t.form.incomeType?e("FormItem",{key:"statDate",attrs:{label:"结算周期:",prop:"statDate"}},[e("DatePicker",{attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择结算周期",editable:!0},on:{"on-change":t.handleChangeStatDate},model:{value:t.form.statDate,callback:function(e){t.$set(t.form,"statDate",e)},expression:"form.statDate"}})],1):t._e(),"5"==t.form.incomeType?e("FormItem",{attrs:{label:"同步Rap状态:"}},[e("Select",{attrs:{filterable:"",placeholder:"下拉选择同步Rap状态",clearable:""},model:{value:t.form.syncRap,callback:function(e){t.$set(t.form,"syncRap",e)},expression:"form.syncRap"}},[e("Option",{attrs:{value:"1"}},[t._v("未上传")]),e("Option",{attrs:{value:"2"}},[t._v("待上传")]),e("Option",{attrs:{value:"3"}},[t._v("已上传")]),e("Option",{attrs:{value:"4"}},[t._v("上传失败")])],1)],1):t._e(),"5"==t.form.incomeType?e("FormItem",{attrs:{label:"审批状态:"}},[e("Select",{attrs:{filterable:"",placeholder:"下拉选择审批状态",clearable:""},model:{value:t.form.approvalStatus,callback:function(e){t.$set(t.form,"approvalStatus",e)},expression:"form.approvalStatus"}},[e("Option",{attrs:{value:"1"}},[t._v("未审批")]),e("Option",{attrs:{value:"2"}},[t._v("审批完成")])],1)],1):t._e(),t._v("  \n      "),e("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",icon:"md-search",loading:t.searchloading},on:{click:function(e){return t.search("form")}}},[t._v("搜索")]),"4"==t.form.incomeType?e("Button",{directives:[{name:"has",rawName:"v-has",value:"exportWhite",expression:"'exportWhite'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"error",icon:"md-arrow-down",loading:t.downloading},on:{click:function(e){return t.exportWhite("form")}}},[t._v("导出")]):t._e(),"5"==t.form.incomeType?e("Button",{directives:[{name:"has",rawName:"v-has",value:"exportSummary",expression:"'exportSummary'"}],staticStyle:{"margin-right":"10px"},attrs:{ghost:"",type:"info",icon:"md-arrow-down",loading:t.summaryLoading},on:{click:function(e){return t.exportCost("form","1")}}},[t._v("汇总表导出")]):t._e(),"5"==t.form.incomeType?e("Button",{directives:[{name:"has",rawName:"v-has",value:"exportDeatil",expression:"'exportDeatil'"}],staticStyle:{"margin-right":"10px"},attrs:{ghost:"",type:"success",icon:"md-arrow-down",loading:t.detailLoading},on:{click:function(e){return t.exportCost("form","2")}}},[t._v("明细表导出")]):t._e(),"5"==t.form.incomeType?e("Button",{directives:[{name:"has",rawName:"v-has",value:"approval",expression:"'approval'"}],staticStyle:{"margin-right":"10px"},attrs:{ghost:"",type:"primary",icon:"md-checkmark"},on:{click:t.financialApproval}},[t._v("运营审批确认")]):t._e(),"5"==t.form.incomeType?e("Button",{directives:[{name:"has",rawName:"v-has",value:"addCost",expression:"'addCost'"}],staticStyle:{"margin-right":"10px"},attrs:{ghost:"",type:"warning",icon:"md-add"},on:{click:function(e){return t.addCostItem("1")}}},[t._v("新增成本")]):t._e(),e("Button",{directives:[{name:"has",rawName:"v-has",value:"batchDownloadInvoice",expression:"'batchDownloadInvoice'"},{name:"show",rawName:"v-show",value:!0===t.isSearch&&4===t.form.incomeType,expression:"isSearch === true && form.incomeType === 4"}],staticStyle:{"margin-right":"10px"},attrs:{type:"primary",icon:"md-download",loading:t.batchDownloadLoading},on:{click:t.batchDownloadBlankCardInvoice}},[t._v("Invoice批量下载")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"rechargeExport",expression:"'rechargeExport'"},{name:"show",rawName:"v-show",value:"6"==t.form.incomeType,expression:"form.incomeType == '6'"}],attrs:{type:"error",icon:"md-arrow-down",loading:t.downloading},on:{click:function(e){return t.exportRechargeFreeFeeConsumption("form")}}},[t._v("导出")])],1),!0===t.isSearch&&1===t.form.incomeType?e("div",{directives:[{name:"has",rawName:"v-has",value:"online_search",expression:"'online_search'"}]},[e("h3",[t._v("线上收入")]),e("Table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{"no-data-text":"",border:"","highlight-row":"",columns:t.columns,data:t.data,loading:t.loading,"span-method":t.handleSpan},scopedSlots:t._u([{key:"action",fn:function(i){var a=i.row;i.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"online_sum_export",expression:"'online_sum_export'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"info",ghost:"",size:"small"},on:{click:function(e){return t.exportOnline(a,"sum")}}},[t._v("线上收入账单汇总")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"online_detail_export",expression:"'online_detail_export'"}],attrs:{type:"success",ghost:"",size:"small"},on:{click:function(e){return t.exportOnline(a,"detail")}}},[t._v("线上收入报表明细")])]}}],null,!1,3167096867)}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,"page-size":t.pageSize,current:t.page,"show-sizer":"","show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.loadByPage,"on-page-size-change":t.loadByPageSize}})],1):t._e(),!0===t.isSearch&&2===t.form.incomeType?e("div",{directives:[{name:"has",rawName:"v-has",value:"corp_search",expression:"'corp_search'"}]},[e("div",{staticStyle:{display:"flex","align-items":"center"}},[e("h3",{staticStyle:{"margin-right":"10px"}},[t._v("渠道商收入")]),e("div",[e("Button",{directives:[{name:"has",rawName:"v-has",value:"corps_detail_export",expression:"'corps_detail_export'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"info",ghost:"",icon:"ios-download"},on:{click:function(e){return t.exportDetail(null,"month")}}},[t._v("Distribution Sales Revenue Report")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"corps_sum_export",expression:"'corps_sum_export'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"success",ghost:"",icon:"ios-download"},on:{click:function(e){return t.exportSum(null,"month")}}},[t._v("Settlement")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"batch_invoice_export",expression:"'batch_invoice_export'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"warning",ghost:"",icon:"ios-download"},on:{click:t.batchExportInvoice}},[t._v("批量下载Invoice")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"account_debit",expression:"'account_debit'"}],staticStyle:{"margin-right":"10px"},attrs:{type:"error",ghost:"",icon:"ios-list-box"},on:{click:t.rapidPaymentOut}},[t._v("立即出账")])],1)]),e("Table",{ref:"selection",staticStyle:{width:"100%","margin-top":"15px"},attrs:{"max-height":"565","no-data-text":"","highlight-row":"",border:"",columns:t.columnsOP,data:t.data,loading:t.loading},on:{"on-selection-change":t.handleRowChangeInvoice,"on-select-cancel":t.cancelItem,"on-select-all-cancel":t.cancelInvoiceAll},scopedSlots:t._u([{key:"download",fn:function(i){var a=i.row;i.index;return[e("div",{staticStyle:{"padding-top":"5px"}},[e("Poptip",{attrs:{trigger:"hover",placement:"bottom-end",width:"330",transfer:!0}},[e("Button",{attrs:{type:"primary",size:"small"}},[t._v("下载")]),e("div",{staticStyle:{display:"flex","flex-wrap":"wrap",gap:"8px"},attrs:{slot:"content"},slot:"content"},[e("Button",{directives:[{name:"has",rawName:"v-has",value:"corp_detail_export",expression:"'corp_detail_export'"}],staticClass:"special-button",attrs:{type:"info",ghost:"",size:"small",disabled:["2","3","5"].includes(a.accountingType),title:"Distribution Sale Revenue"},on:{click:function(e){return t.exportDetail(a,"month")}}},[t._v("Distribution Sale Revenue")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"imsi_detail_export",expression:"'imsi_detail_export'"}],staticClass:"special-button",attrs:{type:"warning",ghost:"",size:"small",title:"IMSI Detail"},on:{click:function(e){return t.exportIMSIdeatil(a)}}},[t._v("IMSI Detail")]),a.invoicePath?e("Button",{directives:[{name:"has",rawName:"v-has",value:"invoice_export",expression:"'invoice_export'"}],staticClass:"special-button",attrs:{type:"success",ghost:"",size:"small",title:"Invoice"},on:{click:function(e){return t.exportInvoice(a)}}},[t._v("Invoice")]):e("Button",{directives:[{name:"has",rawName:"v-has",value:"invoice_export",expression:"'invoice_export'"}],staticClass:"special-button",attrs:{disabled:"",type:"success",ghost:"",size:"small",title:"Invoice"},on:{click:function(e){return t.exportInvoice(a)}}},[t._v("Invoice")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"a2z_detail_export",expression:"'a2z_detail_export'"}],staticClass:"special-button",attrs:{type:"error",ghost:"",size:"small",disabled:!["2","3","4","5","6","7"].includes(a.accountingType),title:"A~Z Detail"},on:{click:function(e){return t.exportA2Zdeatil(a)}}},[t._v("A~Z Detail")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"a2z_summary_export",expression:"'a2z_summary_export'"}],staticClass:"special-button",attrs:{type:"error",ghost:"",size:"small",disabled:!["2","3","4","5","6","7"].includes(a.accountingType),title:"A~Z Summary"},on:{click:function(e){return t.exportA2ZSummary(a)}}},[t._v("A~Z Summary")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"corp_sum_export",expression:"'corp_sum_export'"}],staticClass:"special-button",attrs:{type:"warning",ghost:"",size:"small",title:"CMLink Global Data SIM Sales Revenue Report"},on:{click:function(e){return t.exportSum(a,"month")}}},[t._v("\n\t\t\t\t\t\t\t\t\tCMLink Global Data SIM Sales Revenue Report\n\t\t\t\t\t\t\t\t")])],1)],1)],1)]}},{key:"action",fn:function(i){var a=i.row;i.index;return[e("div",{staticStyle:{"padding-top":"5px"}},[e("Poptip",{attrs:{trigger:"hover",placement:"bottom-end",width:"230",transfer:!0}},[e("Button",{attrs:{type:"primary",size:"small"}},[t._v("编辑")]),e("div",{staticStyle:{display:"flex","flex-wrap":"wrap",gap:"8px"},attrs:{slot:"content"},slot:"content"},["1"!==a.isUpdate&&"2"!==a.authStatus&&"3"!==a.authStatus&&"4"!==a.authStatus||"1"==a.isAdjustment?e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticClass:"special-button",attrs:{ghost:"",type:"primary",disabled:"",size:"small"},on:{click:function(e){return t.update(a)}}},[t._v("点击修改")]):e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticClass:"special-button",attrs:{ghost:"",type:"primary",size:"small"},on:{click:function(e){return t.update(a)}}},[t._v("点击修改")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"inputImsi",expression:"'inputImsi'"}],staticClass:"special-button",attrs:{disabled:"3"==a.authStatus||a.invoicePath||"1"==a.accountingType,type:"info",ghost:"",size:"small"},on:{click:function(e){return t.inputImsi(a)}}},[t._v("录入IMSI费")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"get_invoice",expression:"'get_invoice'"}],staticClass:"special-button",attrs:{type:"success",ghost:"",size:"small"},on:{click:function(e){return t.showInvoiceView(a,"detail")}}},[t._v("生成Invoice")])],1)],1)],1)]}},{key:"approveAction",fn:function(i){var a=i.row;i.index;return["3"===a.authStatus?e("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticClass:"special-button",staticStyle:{"margin-top":"5px",width:"65px"},attrs:{type:"success",ghost:"",size:"small"},on:{click:function(e){return t.channelapprove(1,a)}}},[t._v("通过")]):t._e(),"3"===a.authStatus?e("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticClass:"special-button",staticStyle:{width:"65px"},attrs:{type:"error",ghost:"",size:"small"},on:{click:function(e){return t.channelapprove(2,a)}}},[t._v("不通过")]):t._e()]}}],null,!1,4219277813)}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,"page-size":t.pageSize,current:t.page,"show-sizer":"","show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.loadByPage,"on-page-size-change":t.loadByPageSize}})],1):t._e(),!0===t.isSearch&&3===t.form.incomeType?e("div",{directives:[{name:"has",rawName:"v-has",value:"other_corp_search",expression:"'other_corp_search'"}]},[e("h3",[t._v("其他客户收入")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"export_corps",expression:"'export_corps'"}],staticStyle:{"margin-right":"10px","margin-top":"10px"},attrs:{type:"info",loading:t.downloading,icon:"ios-download"},on:{click:function(e){return t.downloadFile(0)}}},[t._v("导出其他客户收入汇总表(暂估)")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"export_reality",expression:"'export_reality'"}],staticStyle:{"margin-right":"10px","margin-top":"10px"},attrs:{type:"success",loading:t.downloading,icon:"ios-download"},on:{click:function(e){return t.downloadFile(1)}}},[t._v("导出其他客户收入汇总表(真实)")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"create_real_bill",expression:"'create_real_bill'"}],staticStyle:{"margin-right":"10px","margin-top":"10px"},attrs:{type:"warning",loading:t.downloading},on:{click:function(e){return t.createReal()}}},[t._v("生成真实账单")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"history_really_bill",expression:"'history_really_bill'"}],staticStyle:{"margin-right":"10px","margin-top":"10px"},attrs:{type:"primary",loading:t.downloading},on:{click:function(e){return t.createHistory()}}},[t._v("历史真实账单")]),e("Table",{ref:"selection",staticStyle:{width:"100%","margin-top":"20px"},attrs:{columns:t.columnsOT,data:t.data,ellipsis:!0,loading:t.loading},on:{"on-selection-change":t.handleRowChange,"on-select":t.selectPackage,"on-select-cancel":t.cancelPackage,"on-select-all":t.selectPackage,"on-select-all-cancel":t.cancelPackageAll},scopedSlots:t._u([{key:"download",fn:function(i){var a=i.row;i.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"other_mealsum_export",expression:"'other_mealsum_export'"}],staticStyle:{"margin-right":"5px","margin-top":"5px"},attrs:{type:"primary",ghost:""},on:{click:function(e){return t.exportCommon(a,"Package")}}},[t._v("按套餐结算汇总报表")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"other_flowsum_export",expression:"'other_flowsum_export'"}],staticStyle:{"margin-right":"5px","margin-top":"5px"},attrs:{type:"success",ghost:""},on:{click:function(e){return t.exportCommon(a,"flowSettle")}}},[t._v("按流量结算汇总报表")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"other_mealdetail_export",expression:"'other_mealdetail_export'"}],staticStyle:{"margin-right":"5px","margin-top":"5px"},attrs:{type:"warning",ghost:""},on:{click:function(e){return t.exportCommon(a,"PackageUsed")}}},[t._v("按套餐使用明细报表")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"other_flowdetail_export",expression:"'other_flowdetail_export'"}],staticStyle:{"margin-right":"5px","margin-top":"5px"},attrs:{type:"info",ghost:""},on:{click:function(e){return t.exportCommon(a,"flowUsed")}}},[t._v("按流量使用明细报表")])]}},{key:"update",fn:function(i){var a=i.row;i.index;return[null!=a.invoiceNo&&"IN"===a.invoiceNo.substr(0,2)?e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"5px"},attrs:{disabled:"",type:"primary",size:"small"},on:{click:function(e){return t.updateOT(a)}}},[t._v("点击修改")]):e("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.updateOT(a)}}},[t._v("点击修改")])]}},{key:"approveAction",fn:function(i){var a=i.row;i.index;return["3"===a.authStatus?e("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",ghost:"",size:"small"},on:{click:function(e){return t.otherapprove(1,a)}}},[t._v("通过")]):t._e(),e("div",{staticStyle:{height:"6px"}}),"3"===a.authStatus?e("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],attrs:{type:"error",ghost:"",size:"small"},on:{click:function(e){return t.otherapprove(2,a)}}},[t._v("不通过")]):t._e()]}}],null,!1,3456756061)}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,"page-size":t.pageSize,current:t.page,"show-sizer":"","show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.loadByPage,"on-page-size-change":t.loadByPageSize}})],1):t._e(),!0===t.isSearch&&6===t.form.incomeType?e("div",{directives:[{name:"has",rawName:"v-has",value:"online_search",expression:"'online_search'"}]},[e("h3",[t._v("充值赠费消耗汇总表报表")]),e("Table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{"no-data-text":"",border:"","highlight-row":"",columns:t.RechargeFreeFeeConsumption,data:t.data,loading:t.loading}}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,"page-size":t.pageSize,current:t.page,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.loadByPage}})],1):t._e(),!0===t.isSearch&&4===t.form.incomeType?e("div",{directives:[{name:"has",rawName:"v-has",value:"online_search",expression:"'online_search'"}]},[e("h3",[t._v("白卡订单收入")]),e("Table",{ref:"whiteCardTable",staticStyle:{width:"100%","margin-top":"20px"},attrs:{"no-data-text":"",border:"","highlight-row":"",columns:t.whiteCradColumns,data:t.data,loading:t.loading},on:{"on-selection-change":t.handleSelectionChange,"on-select-cancel":t.handleSelectionCancel,"on-select-all":t.handleSelectAll},scopedSlots:t._u([{key:"action",fn:function(i){var a=i.row;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"singleDownloadInvoice",expression:"'singleDownloadInvoice'"}],attrs:{type:"primary",size:"small",ghost:"",loading:t.downloadingMap[a.orderId],disabled:t.downloadingMap[a.orderId]},on:{click:function(e){return t.downloadInvoice(a)}}},[t._v("\n\t\t\t\t\t\tInvoice下载\n\t\t\t\t\t")])]}}],null,!1,2879295371)}),e("div",{staticStyle:{margin:"10px 0"}},[e("span",[t._v("已选择 "+t._s(t.selectedBlankCardRows.length)+" 条记录")])]),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,"page-size":t.pageSize,current:t.page,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.loadByPage}})],1):t._e(),!0===t.isSearch&&5===t.form.incomeType?e("div",{directives:[{name:"has",rawName:"v-has",value:"online_search",expression:"'online_search'"}]},[e("h3",[t._v("成本报表收入")]),e("Table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{"no-data-text":"",border:"","highlight-row":"",columns:t.costPriceColumns,data:t.data,loading:t.loading},on:{"on-selection-change":t.handleRowChangeInvoice,"on-select-cancel":t.cancelItem,"on-select-all-cancel":t.cancelInvoiceAll},scopedSlots:t._u([{key:"action",fn:function(i){var a=i.row;i.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"updateCost",expression:"'updateCost'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"info",ghost:"",size:"small",disabled:"1"!=a.approvalStatus},on:{click:function(e){return t.updateCostItem(a,"2")}}},[t._v("修改")])]}}],null,!1,2890378838)}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,"page-size":t.pageSize,current:t.page,"show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.loadByPage,"on-page-size-change":t.loadByPageSize}})],1):t._e(),e("Modal",{attrs:{title:"编辑","mask-closable":!0},on:{"on-cancel":t.cancel1},model:{value:t.updateModal,callback:function(e){t.updateModal=e},expression:"updateModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{ref:"corpListOP",staticStyle:{"align-items":"center","justify-content":"center"},attrs:{model:t.corpListOP,"label-position":"left",rules:t.rule,"label-width":150}},[e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"客户名称:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.corpListOP.corpName))])]),["0","1","4","6","7"].includes(t.cooperationMode)?e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"代销收入:",prop:"accountAdjustment"}},[e("Input",{staticStyle:{width:"190px","margin-right":"10px"},attrs:{placeholder:"请输入代销收入",clearable:!0},model:{value:t.corpListOP.accountAdjustment,callback:function(e){t.$set(t.corpListOP,"accountAdjustment",e)},expression:"corpListOP.accountAdjustment"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("元")])])],1):t._e(),["0","1"].includes(t.cooperationMode)?t._e():e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"流量收入:",prop:"flowAdjustment"}},[e("Input",{staticStyle:{width:"190px","margin-right":"10px"},attrs:{placeholder:"请输入流量收入",clearable:!0},model:{value:t.corpListOP.flowAdjustment,callback:function(e){t.$set(t.corpListOP,"flowAdjustment",e)},expression:"corpListOP.flowAdjustment"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("元")])])],1),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"IMSI费收入:",prop:"imsiAdjustment"}},[e("Input",{staticStyle:{width:"190px","margin-right":"10px"},attrs:{placeholder:"请输入IMSI费收入",clearable:!0},model:{value:t.corpListOP.imsiAdjustment,callback:function(e){t.$set(t.corpListOP,"imsiAdjustment",e)},expression:"corpListOP.imsiAdjustment"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("元")])])],1)],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancel1}},[t._v("返回")]),e("Button",{attrs:{type:"primary",loading:t.updateLoading},on:{click:t.confirm}},[t._v("确定")])],1)]),e("Modal",{attrs:{title:"选择合作模式出账","mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.selectModeModal,callback:function(e){t.selectModeModal=e},expression:"selectModeModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{ref:"accountingList",staticStyle:{"align-items":"center","justify-content":"center"},attrs:{model:t.accountingList,"label-position":"left",rules:t.ruleMode,"label-width":100}},[e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"选择出账:",prop:"accountingType"}},[e("Select",{staticStyle:{width:"200px"},attrs:{placeholder:"请选择出账",clearable:""},model:{value:t.accountingList.accountingType,callback:function(e){t.$set(t.accountingList,"accountingType",e)},expression:"accountingList.accountingType"}},[e("Option",{attrs:{value:"1"}},[t._v("代销")]),e("Option",{attrs:{value:"2"}},[t._v("流量")]),e("Option",{attrs:{value:"3"}},[t._v("合并出账")])],1)],1)],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("返回")]),e("Button",{attrs:{type:"primary",loading:t.accountingLoading},on:{click:t.accountingConfirm}},[t._v("确定")])],1)]),e("Modal",{attrs:{title:"编辑","mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.updateOTModal,callback:function(e){t.updateOTModal=e},expression:"updateOTModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{ref:"corpList",staticStyle:{"align-items":"center","justify-content":"center"},attrs:{model:t.corpList,"label-position":"left",rules:t.ruleOT,"label-width":150}},[e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"客户名称:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.corpList.corpName))])]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"结算月份:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.corpList.statTime))])]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"服务开始时间:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.corpList.svcStartTime))])]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"服务结束时间:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.corpList.svcEndTime))])]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"套餐收入:",prop:"packageIncome"}},[e("Input",{staticStyle:{width:"230px","margin-right":"10px"},attrs:{placeholder:"请输入套餐收入",clearable:!0},model:{value:t.corpList.packageIncome,callback:function(e){t.$set(t.corpList,"packageIncome",e)},expression:"corpList.packageIncome"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("元")])])],1),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"用量收入:",prop:"useIncome"}},[e("Input",{staticStyle:{width:"230px","margin-right":"10px"},attrs:{placeholder:"请输入用量收入",clearable:!0},model:{value:t.corpList.useIncome,callback:function(e){t.$set(t.corpList,"useIncome",e)},expression:"corpList.useIncome"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("元")])])],1)],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("返回")]),e("Button",{attrs:{type:"primary",loading:t.OTloading},on:{click:t.OTconfirm}},[t._v("确定")])],1)]),e("Modal",{attrs:{title:"生成真实账单","mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.CerateModal,callback:function(e){t.CerateModal=e},expression:"CerateModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{ref:"cerateList",staticStyle:{"align-items":"center","justify-content":"center"},attrs:{model:t.cerateList,"label-position":"left",rules:t.ruleCerate,"label-width":150}},[e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"客户名称:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.cerateList.corpName))])]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"结算月份:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.cerateList.statTime))])]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"服务开始时间:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.cerateList.svcStartTime))])]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"服务结束时间:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.cerateList.svcEndTime))])]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"套餐收入:",prop:"packageIncome"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.cerateList.packageIncome))])]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"用量收入:",prop:"useIncome"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.cerateList.useIncome))])]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"总收入:",prop:"useIncome"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.cerateList.totalIncome))])]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"税费:",prop:"taxation"}},[e("Input",{staticStyle:{width:"190px","margin-right":"10px"},attrs:{placeholder:"请输入税费",clearable:!0},model:{value:t.cerateList.taxation,callback:function(e){t.$set(t.cerateList,"taxation",e)},expression:"cerateList.taxation"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("元")])])],1),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"上传税费文件:"}},[e("Upload",{attrs:{action:t.uploadUrl,"on-success":t.fileSuccess,"on-error":t.handleError,"before-upload":t.handleBeforeUpload,"on-progress":t.fileUploading}},[e("Button",{attrs:{icon:"ios-cloud-upload-outline"}},[t._v("点击上传")])],1),t.file?e("ul",{staticClass:"ivu-upload-list"},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("Icon",{attrs:{type:"ios-folder"}}),t._v(t._s(t.file.name))],1),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeFile}})])]):t._e()],1),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"上传客户账单:"}},[e("Upload",{attrs:{action:t.uploadUrl,"on-success":t.CustomerfileSuccess,"on-error":t.CustomerhandleError,"before-upload":t.CustomerhandleBeforeUpload,"on-progress":t.CustomerfileUploading}},[e("Button",{attrs:{icon:"ios-cloud-upload-outline"}},[t._v("点击上传")])],1),t.Customerfile?e("ul",{staticClass:"ivu-upload-list"},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("Icon",{attrs:{type:"ios-folder"}}),t._v(t._s(t.Customerfile.name))],1),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeCustomerfile}})])]):t._e()],1)],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("返回")]),e("Button",{attrs:{type:"primary",loading:t.Cerateloading},on:{click:t.Cerateconfirm}},[t._v("确定")])],1)]),e("Modal",{attrs:{title:"生成发票预览",width:"800px",styles:{top:"10px"}},on:{"on-ok":t.createInvoice,"on-cancel":t.cancelInvoice},model:{value:t.invoice_model,callback:function(e){t.invoice_model=e},expression:"invoice_model"}},[e("Card",{attrs:{width:"750px"}},[e("invoiceTemplate",{ref:"dataForm",attrs:{AccountNo:t.invoiceInfo.AccountNo,address:t.invoiceInfo.address,AmountDue:t.invoiceInfo.AmountDue,InvoiceNo:t.invoiceInfo.InvoiceNo,InvoiceDate:t.invoiceInfo.InvoiceDate,FileTitle:t.invoiceInfo.FileTitle,InvoiceDesc:t.invoiceInfo.InvoiceDesc,AmountTax:t.invoiceInfo.AmountTax,Tax:t.invoiceInfo.Tax,TotalAmount:t.invoiceInfo.TotalAmount,columns:t.invoiceColumns,data:t.invoiceInfo.data,invoiceForm:t.invoiceForm,currencyCode:t.invoiceInfo.currencyCode},on:{"update:invoiceForm":function(e){t.invoiceForm=e},"update:invoice-form":function(e){t.invoiceForm=e},InvoiceDesc:t.getdesc}})],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelInvoice}},[t._v("取消")]),e("Button",{attrs:{type:"primary",loading:t.Invoiceloading},on:{click:t.createInvoice}},[t._v("生成Invoice")])],1)],1),e("Modal",{attrs:{title:t.titleName,"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.costModal,callback:function(e){t.costModal=e},expression:"costModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{ref:"corpListCost",staticStyle:{"align-items":"center","justify-content":"center"},attrs:{model:t.corpListCost,"label-position":"left",rules:t.ruleCost,"label-width":150}},["1"==t.costType?e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"结算周期:",prop:"statDate"}},[e("DatePicker",{staticStyle:{width:"200px"},attrs:{format:"yyyyMM",type:"month",placement:"bottom-start",placeholder:"请选择结算周期",editable:!0},on:{"on-change":t.handleStatDate},model:{value:t.corpListCost.statDate,callback:function(e){t.$set(t.corpListCost,"statDate",e)},expression:"corpListCost.statDate"}})],1):t._e(),"1"==t.costType?e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"资源供应商:",prop:"supplierShortenName"}},[e("Select",{attrs:{filterable:"",placeholder:"请选择资源供应商",clearable:""},on:{"on-change":function(e){return t.changeCode(e)}},model:{value:t.corpListCost.supplierShortenName,callback:function(e){t.$set(t.corpListCost,"supplierShortenName",e)},expression:"corpListCost.supplierShortenName"}},t._l(t.supplierShortenList,(function(i,a){return e("Option",{key:a,attrs:{value:a}},[t._v(t._s(a))])})),1)],1):t._e(),"1"==t.costType?e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"供应商编码:",prop:"supplierShortenCode"}},[e("Input",{staticStyle:{width:"200px"},attrs:{disabled:"",placeholder:"请输入供应商编码",clearable:!0},model:{value:t.corpListCost.supplierShortenCode,callback:function(e){t.$set(t.corpListCost,"supplierShortenCode",e)},expression:"corpListCost.supplierShortenCode"}})],1):t._e(),"1"==t.costType?e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"币种:",prop:"currency"}},[e("Select",{staticStyle:{width:"200px"},attrs:{filterable:"",clearable:"",placeholder:"请选择资源供应商"},model:{value:t.corpListCost.currency,callback:function(e){t.$set(t.corpListCost,"currency",e)},expression:"corpListCost.currency"}},[e("Option",{attrs:{value:"CNY"}},[t._v("人民币")]),e("Option",{attrs:{value:"HKD"}},[t._v("港币")]),e("Option",{attrs:{value:"USD"}},[t._v("美元")]),e("Option",{attrs:{value:"EUR"}},[t._v("欧元")])],1)],1):t._e(),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"成本预估:",prop:"totalAmount"}},[e("Input",{attrs:{placeholder:"请输入成本预估",clearable:!0},model:{value:t.corpListCost.totalAmount,callback:function(e){t.$set(t.corpListCost,"totalAmount",e)},expression:"corpListCost.totalAmount"}})],1),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"成本类型:",prop:"type"}},[e("Input",{attrs:{placeholder:"请输入成本类型",clearable:!0},model:{value:t.corpListCost.type,callback:function(e){t.$set(t.corpListCost,"type",e)},expression:"corpListCost.type"}})],1)],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("返回")]),e("Button",{attrs:{type:"primary",loading:t.costLoading},on:{click:t.costConfirm}},[t._v("确定")])],1)]),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.exportcancelModal},model:{value:t.exportModalr,callback:function(e){t.exportModalr=e},expression:"exportModalr"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex","flex-wrap":"wrap"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v("导出提示")]),e("FormItem",{attrs:{label:"你本次导出任务ID为:"}},[e("span",[t._v(t._s(t.taskId))])]),e("FormItem",{attrs:{label:"你本次导出的文件名为:"}},[e("span",{staticClass:"task-name"},[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v("请前往下载管理-下载列表查看及下载。")])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.exportcancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.Gotor}},[t._v("立即前往")])],1)]),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{width:"500px","align-items":"center","justify-content":"center","margin-bottom":"30px"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"20px"}},[t._v("导出提示")]),e("FormItem",{attrs:{label:"你本次导出任务ID为:"}},[e("ul",{staticStyle:{"margin-bottom":"15px"}},t._l(t.taskIds,(function(i,a){return e("li",{key:t.taskIds.i,attrs:{id:"space"}},[t._v("\n\t\t\t\t\t\t\t\t"+t._s(i)+"\n\t\t\t\t\t\t\t")])})),0),t.remind?e("div",[e("span",[t._v("……")])]):t._e()]),e("FormItem",{attrs:{label:"你本次导出的文件名为:"}},[e("ul",{staticStyle:{"margin-bottom":"15px"}},t._l(t.taskNames,(function(i,a){return e("li",{key:t.taskNames.i,staticClass:"task-name",attrs:{id:"space"}},[t._v("\n\t\t\t\t\t\t\t\t"+t._s(i)+"\n\t\t\t\t\t\t\t")])})),0),t.remind?e("div",[e("span",[t._v("……")])]):t._e()]),e("span",{staticStyle:{"text-align":"left"}},[t._v("请前往"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v("下载管理-下载列表")]),t._v("查看及下载。")])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v("立即前往")])],1)]),e("Modal",{attrs:{title:t.modelTitle,"mask-closable":!1,width:"900px"},on:{"on-cancel":t.cancelInvoiceFile},model:{value:t.invoiceModal,callback:function(e){t.invoiceModal=e},expression:"invoiceModal"}},[e("Table",{ref:"selection",attrs:{columns:t.invoiceFileColumns,data:t.invoiceFileTableData,ellipsis:!0}}),e("h3",{staticStyle:{margin:"30px 0","text-decoration":"underline","text-decoration-color":"red","text-decoration-style":"solid"}},[t._v(t._s(t.invoiceTitle))]),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelInvoiceFile}},[t._v("返回")]),e("Button",{attrs:{type:"primary",loading:t.besureSubmitLoading},on:{click:t.besureSubmit}},[t._v("确定")])],1)],1),e("Modal",{attrs:{title:"录入IMSI费","mask-closable":!0,width:"600"},on:{"on-cancel":t.cancel1},model:{value:t.imsiModal,callback:function(e){t.imsiModal=e},expression:"imsiModal"}},[e("Form",{ref:"imsiOP",staticStyle:{"align-items":"center","justify-content":"center"},attrs:{model:t.imsiOP,"label-position":"left",rules:t.imsiRule,"label-width":150}},[e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"IMSI费金额:",prop:"amount"}},[e("Input",{attrs:{placeholder:"请输入imsi费金额",clearable:""},model:{value:t.imsiOP.amount,callback:function(e){t.$set(t.imsiOP,"amount",e)},expression:"imsiOP.amount"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("元")])])],1),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"IMSI费明细:",prop:"desc"}},[e("Input",{attrs:{maxlength:"500",clearable:"",type:"textarea",autosize:{minRows:3,maxRows:10},placeholder:"请输入imsi费明细"},model:{value:t.imsiOP.desc,callback:function(e){t.$set(t.imsiOP,"desc",e)},expression:"imsiOP.desc"}})],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancel1}},[t._v("返回")]),e("Button",{attrs:{type:"primary",loading:t.imsiLoading},on:{click:t.imsiConfirm}},[t._v("确定")])],1)],1),e("a",{ref:"downloadLink",staticStyle:{display:"none"}})],1)},n=[],o=i("c7eb"),s=i("1da1"),r=i("2909"),c=i("5530"),l=i("53ca"),d=(i("d9e2"),i("99af"),i("4de4"),i("d81d"),i("14d9"),i("fb6a"),i("4e82"),i("a434"),i("e9c4"),i("a9e3"),i("b680"),i("b64b"),i("d3b7"),i("07ac"),i("00b4"),i("25f0"),i("6062"),i("1e70"),i("79a4"),i("c1a1"),i("8b00"),i("a4e7"),i("1e5a"),i("72c3"),i("3ca3"),i("466d"),i("5319"),i("159b"),i("ddb0"),i("2b3d"),i("bf19"),i("9861"),i("88a7"),i("271a"),i("5494"),i("951d")),u=function(){var t=this,e=t._self._c;return e("div",[e("h3",[t._v("线上收入")]),e("Table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{"no-data-text":"",border:"","highlight-row":"",columns:t.columns,data:t.data,loading:t.loading,"span-method":t.handleSpan},scopedSlots:t._u([{key:"action",fn:function(i){var a=i.row;i.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"online_sum_export",expression:"'online_sum_export'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"info",ghost:"",size:"small"},on:{click:function(e){return t.exportCommon(a,"sum")}}},[t._v("线上收入账单汇总")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"online_detail_export",expression:"'online_detail_export'"}],attrs:{type:"success",ghost:"",size:"small"},on:{click:function(e){return t.exportCommon(a,"detail")}}},[t._v("线上收入报表明细")])]}}])}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,"page-size":t.pageSize,current:t.page,"show-sizer":"","show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.loadByPage,"on-page-size-change":t.loadByPageSize}}),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center","margin-bottom":"30px"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"20px"}},[t._v("导出提示")]),e("FormItem",{staticStyle:{"margin-bottom":"15px"},attrs:{label:"你本次导出任务ID为:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.taskId))])]),e("FormItem",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"你本次导出的文件名为:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left"}},[t._v("请前往"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v("下载管理-下载列表")]),t._v("查看及下载。")])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v("立即前往")])],1)])],1)},p=[],m=i("1b26"),h=i("c70b"),g={props:{form:{incomeType:"",beginMonth:"",endMonth:"",corp:""},searchBeginTime:"",searchEndTime:""},data:function(){return{total:0,pageSize:10,page:1,searchObj:{},columns:[{title:"销售渠道",key:"salesChannel",align:"center",render:function(t,e){var i={102:"API",103:"官网（H5）",104:"北京移动",105:"批量售卖",106:"推广活动",110:"测试渠道",111:"合作发卡",112:"后付费发卡",113:"WEB",114:"流量池WEB"};return t("span",i[e.row.salesChannel])},tooltip:!0,minWidth:200,tooltipMaxWidth:2e3},{title:"结算月份",key:"statTime",align:"center",tooltip:!0,minWidth:200,tooltipMaxWidth:2e3},{title:"套餐数量",key:"packageNum",align:"center",tooltip:!0,minWidth:200,tooltipMaxWidth:2e3},{title:"套餐收入金额",key:"amount",align:"center",tooltip:!0,minWidth:200,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(h.divide(h.bignumber(i.amount),100).toFixed(2)).toString();return t("label",a)}},{title:"币种",key:"currency",align:"center",minWidth:200,render:function(t,e){var i=e.row,a="156"==i.currency?"CNY":"840"==i.currency?"USD":"344"==i.currency?"HKD":"";return t("label",a)}},{title:"文件下载",slot:"action",minWidth:200,align:"center"}],data:[],spanData:[],loading:!1,downLoad:!1,taskName:"",taskId:"",exportModal:!1}},created:function(){},mounted:function(){this.searchObj=this.form,this.getTableData()},methods:{getTableData:function(){var t=this,e=this;Object(m["d"])({beginMonth:this.searchBeginTime,endMonth:this.searchEndTime,salesChannel:this.searchObj.corpId,pageNum:this.page,pageSize:this.pageSize}).then((function(i){"0000"===i.code&&(t.total=i.count,e.data=i.data,t.getSpanData(e.data))}))},exportCommon:function(t,e){var i=this;"sum"===e&&Object(m["c"])({month:t.statTime,salesChannel:t.salesChannel}).then((function(t){"0000"===t.code&&(""!=t.data.taskId&&""!=t.data.taskName?(i.exportModal=!0,i.taskId=t.data.taskId,i.taskName=t.data.taskName):i.$Message.error("获取下载任务失败"))})),"detail"===e&&Object(m["b"])({month:t.statTime,salesChannel:t.salesChannel}).then((function(t){"0000"===t.code&&(""!=t.data.taskId&&""!=t.data.taskName?(i.exportModal=!0,i.taskId=t.data.taskId,i.taskName=t.data.taskName):i.$Message.error("获取下载任务失败"))}))},search:function(){this.getTableData()},loadByPage:function(t){this.page=t,this.getTableData(t,this.pageSize)},loadByPageSize:function(t){this.pageSize=t,this.getTableData(this.page,t)},cancelModal:function(){this.exportModal=!1},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModal=!1},getSpanData:function(t){var e=this,i=0;e.spanData=[],t.forEach((function(a,n){0===n?(e.spanData.push(1),e.pos=0):t[n].salesChannel===t[n-1].salesChannel&&t[n].statTime===t[n-1].statTime?(e.spanData[i]+=1,e.spanData.push(0)):(e.spanData.push(1),e.pos=1)}))},handleSpan:function(t){t.row,t.column;var e=t.rowIndex,i=t.columnIndex;if([0,1,5].includes(i)){var a=this.spanData[e],n=a>0?1:0;return{rowspan:a,colspan:n}}}}},f=g,v=i("2877"),y=Object(v["a"])(f,u,p,!1,null,null,null),b=y.exports,I=function(){var t=this,e=t._self._c;return e("div",[e("h3",[t._v("渠道商收入")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"corp_detail_export",expression:"'corp_detail_export'"}],staticStyle:{"margin-right":"10px","margin-top":"10px"},attrs:{type:"success",ghost:"",icon:"ios-download"},on:{click:function(e){return t.exportDetail(null,"month")}}},[t._v("Sales Revenue Report")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"corp_sum_export",expression:"'corp_sum_export'"}],staticStyle:{"margin-right":"10px","margin-top":"10px"},attrs:{type:"warning",ghost:"",icon:"ios-download"},on:{click:function(e){return t.exportSum(null,"month")}}},[t._v("Settlement")]),e("Table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{"no-data-text":"","highlight-row":"",border:"",columns:t.columns,data:t.data,loading:t.loading},scopedSlots:t._u([{key:"download",fn:function(i){var a=i.row;i.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"corp_detail_export",expression:"'corp_detail_export'"}],staticStyle:{margin:"10px 0px 10px -35px"},attrs:{type:"info",ghost:"",size:"small"},on:{click:function(e){return t.exportDetail(a,"month")}}},[t._v("Sale Revenue")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"invoice_export",expression:"'invoice_export'"}],staticStyle:{"margin-left":"10px"},attrs:{type:"success",ghost:"",size:"small"},on:{click:function(e){return t.exportInvoice(a)}}},[t._v("Invoice")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"corp_sum_export",expression:"'corp_sum_export'"}],staticStyle:{"margin-bottom":"10px"},attrs:{type:"warning",ghost:"",size:"small"},on:{click:function(e){return t.exportSum(a,"month")}}},[t._v("CMLink Global Data SIM Sales Revenue Report")])]}},{key:"action",fn:function(i){var a=i.row;i.index;return["1"===a.isUpdate?e("Button",{directives:[{name:"has",rawName:"v-has",value:"online_sum_export",expression:"'online_sum_export'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.update(a)}}},[t._v("点击修改")]):e("Button",{directives:[{name:"has",rawName:"v-has",value:"online_sum_export",expression:"'online_sum_export'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",disabled:"",size:"small"},on:{click:function(e){return t.update(a)}}},[t._v("点击修改")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"get_invoice",expression:"'get_invoice'"}],staticStyle:{width:"80px"},attrs:{type:"success",ghost:"",size:"small"},on:{click:function(e){return t.showInvoiceView(a,"detail")}}},[t._v("生成Invoice")])]}}])}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,"page-size":t.pageSize,current:t.page,"show-sizer":"","show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.loadByPage,"on-page-size-change":t.loadByPageSize}}),e("Modal",{attrs:{title:"编辑","mask-closable":!0},on:{"on-cancel":t.cancel1},model:{value:t.updateModal,callback:function(e){t.updateModal=e},expression:"updateModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{ref:"corpList",staticStyle:{"align-items":"center","justify-content":"center"},attrs:{model:t.corpList,"label-position":"left",rules:t.rule,"label-width":150}},[e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"客户名称:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.corpList.corpName))])]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"调账金额:",prop:"accountAdjustment"}},[e("Input",{staticStyle:{width:"190px","margin-right":"10px"},attrs:{placeholder:"请输入调账金额",clearable:!0},model:{value:t.corpList.accountAdjustment,callback:function(e){t.$set(t.corpList,"accountAdjustment",e)},expression:"corpList.accountAdjustment"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("元")])])],1),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"服务开始时间:",prop:"svcStartTime"}},[e("DatePicker",{attrs:{format:"yyyyMMdd",type:"date",placeholder:"选择时间"},on:{"on-change":t.startTimeDateChange},model:{value:t.corpList.svcStartTime,callback:function(e){t.$set(t.corpList,"svcStartTime",e)},expression:"corpList.svcStartTime"}})],1),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"服务结束时间:",prop:"svcEndTime"}},[e("DatePicker",{attrs:{format:"yyyyMMdd",type:"date",placeholder:"选择时间"},on:{"on-change":t.endTimeDateChange},model:{value:t.corpList.svcEndTime,callback:function(e){t.$set(t.corpList,"svcEndTime",e)},expression:"corpList.svcEndTime"}})],1)],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancel1}},[t._v("返回")]),e("Button",{attrs:{type:"primary"},on:{click:t.confirm}},[t._v("确定")])],1)]),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v("导出提示")]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"你本次导出任务ID为:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.taskId))])]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"你本次导出的文件名为:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left","margin-bottom":"10px"}},[t._v("请前往"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v("下载管理-下载列表")]),t._v("查看及下载。")])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v("立即前往")])],1)]),e("Modal",{attrs:{title:"生成发票预览",width:"800px",styles:{top:"10px"}},on:{"on-ok":t.createInvoice,"on-cancel":t.cancelInvoice},model:{value:t.invoice_model,callback:function(e){t.invoice_model=e},expression:"invoice_model"}},[e("Card",{attrs:{width:"750px"}},[e("invoiceTemplate",{attrs:{AccountNo:t.invoiceInfo.AccountNo,address:t.invoiceInfo.address,AmountDue:t.invoiceInfo.AmountDue,InvoiceNo:t.invoiceInfo.InvoiceNo,InvoiceDate:t.invoiceInfo.InvoiceDate,FileTitle:t.invoiceInfo.FileTitle,InvoiceDesc:t.invoiceInfo.InvoiceDesc,AmountTax:t.invoiceInfo.AmountTax,Tax:t.invoiceInfo.Tax,TotalAmount:t.invoiceInfo.TotalAmount,columns:t.invoiceColumns,data:t.invoiceInfo.data},on:{InvoiceDesc:t.getdesc}})],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelInvoice}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.createInvoice}},[t._v("生成Invoice")])],1)],1)],1)},x=[],k=i("0240"),S=i("63e1"),w=i("c70b"),T={components:{invoiceTemplate:S["a"]},props:{form:{incomeType:"",beginMonth:"",endMonth:"",corp:"",data:"",count:""},searchBeginTime:"",searchEndTime:""},data:function(){return{total:0,pageSize:10,page:1,searchObj:{},corpList:{},exportModal:!1,updateModal:!1,startTime:"",endTime:"",taskId:"",taskName:"",columns:[{title:"渠道商名称",key:"corpName",align:"center",minWidth:130,tooltip:!0,tooltipMaxWidth:2e3,fixed:"left"},{title:"客户EBS编码",key:"ebscode",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"Invoice no.",key:"invoiceNo",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"币种",key:"currency",align:"center",minWidth:100,render:function(t,e){var i=e.row,a="156"==i.currency?"CNY":"840"==i.currency?"USD":"344"==i.currency?"HKD":"";return t("label",a)}},{title:"直接收入总额",key:"directIncome",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(w.divide(w.bignumber(i.directIncome),100).toFixed(2)).toString();return t("label",a)}},{title:"调账金额",key:"accountAdjustment",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(w.divide(w.bignumber(i.accountAdjustment),100).toFixed(2)).toString();return t("label",a)}},{title:"间接收入总额",key:"indirectIncome",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(w.divide(w.bignumber(i.indirectIncome),100).toFixed(2)).toString();return t("label",a)}},{title:"总销售额",key:"saleIncome",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(w.divide(w.bignumber(i.saleIncome),100).toFixed(2)).toString();return t("label",a)}},{title:"直接收入酬金",key:"dirRemuneration",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(w.divide(w.bignumber(i.dirRemuneration),100).toFixed(2)).toString();return t("label",a)}},{title:"间接收入酬金",key:"indRemuneration",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(w.divide(w.bignumber(i.indRemuneration),100).toFixed(4)).toString();return t("label",a)}},{title:"酬金总额",key:"totleRemuneration",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(w.divide(w.bignumber(i.totleRemuneration),100).toFixed(2)).toString();return t("label",a)}},{title:"实际收入金额",key:"realIncome",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(w.divide(w.bignumber(i.realIncome),100).toFixed(2)).toString();return t("label",a)}},{title:"服务开始时间",key:"svcStartTime",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"服务结束时间",key:"svcEndTime",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"文件下载",slot:"download",minWidth:300,align:"center",fixed:"right"},{title:"编辑",slot:"action",minWidth:200,align:"center",fixed:"right"}],desc:"",data:[],loading:!1,addLoading:!1,modal1:!1,rule:{accountAdjustment:[{required:!0,message:"请输入调账金额",trigger:"blur"}],svcStartTime:[{type:"date",required:!0,message:"请选择服务开始时间",trigger:"change"}],svcEndTime:[{type:"date",required:!0,message:"请选择服务结束时间",trigger:"change"}]},updateLoading:!1,id:null,invoice_model:!1,invoiceInfo:{AccountNo:"北京博新創億科技股份有限公司",address:"北京市海淀区首都體育館南路6號3幢557室",AmountDue:"CNY 1,360.00",InvoiceNo:"IN-************-GDS",InvoiceDate:"30-Mar-2021",FileTitle:"INVOICE",InvoiceDesc:null,data:[{description:"GDS-Sales Settlement-Mar2021",billingPeriod:"25-Feb-2021 to 24-Mar-2021",qty:"1",unitPrice:"1,360.00",amount:"1,360.00"},{description:"Amount before Tax",billingPeriod:null,qty:null,unitPrice:"CNY",amount:"1,360.00"},{description:"TAX",billingPeriod:null,qty:null,unitPrice:"CNY",amount:"1,360.00"},{description:"Total Amount Due",billingPeriod:null,qty:null,unitPrice:"CNY",amount:"1,360.00"}]},invoiceColumns:[{title:"Description",align:"center",width:220,key:"description"},{title:"Billing Period",align:"center",width:220,key:"billingPeriod"},{title:"Qty",align:"center",width:60,key:"qty"},{title:"Unit Price",align:"center",width:115,key:"unitPrice"},{title:"amount",align:"center",width:116,key:"amount"}]}},created:function(){},mounted:function(){this.searchObj=this.form,this.getTableData()},methods:{getTableData:function(){var t=this;Object(k["k"])({beginMonth:this.searchBeginTime,endMonth:this.searchEndTime,corpId:this.searchObj.corpId,corpName:this.searchObj.corpName,pageNum:this.page,pageSize:this.pageSize}).then((function(e){"0000"===e.code&&(t.data=e.data,t.total=e.count)}))},search:function(){this.getTableData()},loadByPage:function(t){this.page=t,this.getTableData(t,this.pageSize)},loadByPageSize:function(t){this.pageSize=t,this.getTableData(this.page,t)},exportDetail:function(t,e){var i=this,a=null,n=null;null!=t&&(a=t.corpId,n=t.corpName),Object(k["a"])({beginMonth:this.searchBeginTime,endMonth:this.searchEndTime,corpId:a,corpName:n}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0,i.taskId=t.data.taskId,i.taskName=t.data.taskName})).catch((function(t){console.log(t)}))},exportSum:function(t,e){var i=this,a=null,n=null;null!=t&&(a=t.corpId,n=t.corpName),Object(k["b"])({beginMonth:this.searchBeginTime,endMonth:this.searchEndTime,corpId:a,corpName:n}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0,i.taskId=t.data.taskId,i.taskName=t.data.taskName})).catch((function(t){console.log(t)}))},exportInvoice:function(t){function e(e,i){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(t,e){var i=this;exportInvoice({corpName:t.corpName,invoicePath:t.invoicePath,month:t.statTime}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0,i.taskId=t.data.taskId,i.taskName=t.data.taskName})).catch((function(t){console.log(t)}))})),update:function(t){this.updateModal=!0,this.corpList=Object.assign({},t),this.corpList.accountAdjustment=parseFloat(w.divide(w.bignumber(t.accountAdjustment),100).toFixed(2)).toString()},cancel1:function(){this.updateModal=!1,this.updateLoading=!1},cancelModal:function(){this.exportModal=!1},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModal=!1},endTimeDateChange:function(t){this.endTime=t},startTimeDateChange:function(t){this.startTime=t},confirm:function(){var t=this;console.log(1111),this.$refs["corpList"].validate((function(e){e&&t.$Modal.info({title:"只能修改一次，是否确认提交修改？",onOk:function(){Object(k["m"])({accountAdjustment:w.multiply(w.bignumber(t.corpList.accountAdjustment),100).toString(),id:t.corpList.id,svcEndTime:""===t.endTime?t.corpList.svcEndTime:t.endTime,svcStartTime:""===t.startTime?t.corpList.svcStartTime:t.startTime}).then((function(e){"0000"===e.code&&(t.updateModal=!1,t.getTableData())}))}})}))},createInvoice:function(){var t=this;this.desc?Object(k["e"])({address:this.invoiceInfo.address,id:this.id,invoiceDesc:this.desc,type:2}).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"发票生成成功"}),t.invoice_model=!1,t.loadByPage(t.page)})).catch((function(t){console.log(t)})):this.$Message.error("发票说明不能为空")},createInvoiceNo:function(t,e){var i=this;Object(k["f"])(t.id,t.corpId,2).then((function(a){if(!a||"0000"!=a.code)throw a;i.address=a.data.address,i.InvoiceNo=2===e?a.data.invoiceNo:t.invoiceNo;var n="156"==t.currency?"CNY":"840"==t.currency?"USD":"344"==t.currency?"HKD":"";i.invoiceInfo={AccountNo:t.corpName,address:i.address,AmountDue:n+" "+(t.packageIncome+t.useIncome),InvoiceNo:i.InvoiceNo,InvoiceDate:a.data.invoiceDate,FileTitle:"INVOICE",InvoiceDesc:t.invoiceDesc,AmountTax:n+" "+(t.packageIncome+t.useIncome),Tax:n+" "+t.taxation,TotalAmount:n+" "+(t.packageIncome+t.useIncome),data:[{description:a.data.invoiceNameDesc,billingPeriod:a.data.billingPeriod,qty:"1",unitPrice:t.packageIncome+t.useIncome,amount:t.packageIncome+t.useIncome}]},i.invoice_model=!0})).catch((function(t){console.log(t)}))},cancelInvoice:function(){this.id=null,this.invoice_model=!1,this.invoiceInfo=[]},showInvoiceView:function(t){this.id=t.id,null==t.invoiceNo||null!=t.invoiceNo&&"IN"!=t.invoiceNo.substr(0,2)?this.createInvoiceNo(t,2):this.createInvoiceNo(t,1)},getdesc:function(t){this.desc=t}}},N=T,_=Object(v["a"])(N,I,x,!1,null,null,null),M=_.exports,C=function(){var t=this,e=t._self._c;return e("div",[e("h3",[t._v("其他客户收入")]),e("Button",{staticStyle:{"margin-right":"10px","margin-top":"10px"},attrs:{type:"success",loading:t.downloading,icon:"ios-download"},on:{click:function(e){return t.downloadFile()}}},[t._v("导出其他客户收入汇总表")]),e("Table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{columns:t.columns,data:t.data,ellipsis:!0,loading:t.loading},scopedSlots:t._u([{key:"download",fn:function(i){var a=i.row;i.index;return[e("Button",{directives:[{name:"has",rawName:"v-has",value:"online_sum_export",expression:"'online_sum_export'"}],staticStyle:{"margin-right":"5px","margin-top":"5px"},attrs:{type:"primary",ghost:""},on:{click:function(e){return t.exportCommon(a,"Package")}}},[t._v("按套餐结算汇总报表")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"online_detail_export",expression:"'online_detail_export'"}],staticStyle:{"margin-right":"5px","margin-top":"5px"},attrs:{type:"success",ghost:""},on:{click:function(e){return t.exportCommon(a,"flowSettle")}}},[t._v("按流量结算汇总报表")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"online_detail_export",expression:"'online_detail_export'"}],staticStyle:{"margin-right":"5px","margin-top":"5px"},attrs:{type:"warning",ghost:""},on:{click:function(e){return t.exportCommon(a,"PackageUsed")}}},[t._v("按套餐使用明细报表")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"online_detail_export",expression:"'online_detail_export'"}],staticStyle:{"margin-right":"5px","margin-top":"5px"},attrs:{type:"info",ghost:""},on:{click:function(e){return t.exportCommon(a,"flowUsed")}}},[t._v("按流量使用明细报表")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"online_detail_export",expression:"'online_detail_export'"}],staticStyle:{"margin-right":"5px","margin-top":"5px"},attrs:{type:"error",ghost:""},on:{click:function(e){return t.exportCommon(a,"Invoice")}}},[t._v("Invoice")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"online_detail_export",expression:"'online_detail_export'"}],staticStyle:{"margin-right":"5px","margin-top":"5px"},attrs:{type:"success",ghost:""},on:{click:function(e){return t.exportCommon(a,"taxation")}}},[t._v("税费文件")])]}},{key:"update",fn:function(i){var a=i.row;i.index;return["1"===a.isUpdate?e("Button",{directives:[{name:"has",rawName:"v-has",value:"online_sum_export",expression:"'online_sum_export'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(e){return t.update(a)}}},[t._v("点击修改")]):e("Button",{directives:[{name:"has",rawName:"v-has",value:"online_sum_export",expression:"'online_sum_export'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",disabled:"",size:"small"},on:{click:function(e){return t.update(a)}}},[t._v("点击修改")]),e("Button",{directives:[{name:"has",rawName:"v-has",value:"online_detail_export",expression:"'online_detail_export'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small"},on:{click:function(e){return t.showInvoiceView(a)}}},[t._v("生成Invoice")])]}}])}),e("Page",{staticStyle:{margin:"15px 0"},attrs:{total:t.total,"page-size":t.pageSize,current:t.page,"show-sizer":"","show-total":"","show-elevator":""},on:{"update:current":function(e){t.page=e},"on-change":t.loadByPage,"on-page-size-change":t.loadByPageSize}}),e("Modal",{attrs:{title:"编辑","mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.updateModal,callback:function(e){t.updateModal=e},expression:"updateModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{ref:"corpList",staticStyle:{"align-items":"center","justify-content":"center"},attrs:{model:t.corpList,"label-position":"left",rules:t.rule,"label-width":150}},[e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"客户名称:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.corpList.corpName))])]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"服务开始时间:",prop:"svcStartTime"}},[e("DatePicker",{attrs:{format:"yyyyMMdd",type:"date",placeholder:"选择时间"},on:{"on-change":t.startTimeDateChange},model:{value:t.corpList.svcStartTime,callback:function(e){t.$set(t.corpList,"svcStartTime",e)},expression:"corpList.svcStartTime"}})],1),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"服务结束时间:",prop:"svcEndTime"}},[e("DatePicker",{attrs:{format:"yyyyMMdd",type:"date",placeholder:"选择时间"},on:{"on-change":t.endTimeDateChange},model:{value:t.corpList.svcEndTime,callback:function(e){t.$set(t.corpList,"svcEndTime",e)},expression:"corpList.svcEndTime"}})],1),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"套餐收入:",prop:"packageIncome"}},[e("Input",{staticStyle:{width:"190px","margin-right":"10px"},attrs:{placeholder:"请输入套餐收入",clearable:!0},model:{value:t.corpList.packageIncome,callback:function(e){t.$set(t.corpList,"packageIncome",e)},expression:"corpList.packageIncome"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("元")])])],1),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"用量收入:",prop:"useIncome"}},[e("Input",{staticStyle:{width:"190px","margin-right":"10px"},attrs:{placeholder:"请输入用量收入",clearable:!0},model:{value:t.corpList.useIncome,callback:function(e){t.$set(t.corpList,"useIncome",e)},expression:"corpList.useIncome"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("元")])])],1),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"税费:",prop:"taxation"}},[e("Input",{staticStyle:{width:"190px","margin-right":"10px"},attrs:{placeholder:"请输入税费",clearable:!0},model:{value:t.corpList.taxation,callback:function(e){t.$set(t.corpList,"taxation",e)},expression:"corpList.taxation"}},[e("span",{attrs:{slot:"append"},slot:"append"},[t._v("元")])])],1),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"上传税费文件:"}},[e("Upload",{attrs:{action:t.uploadUrl,"on-success":t.fileSuccess,"on-error":t.handleError,"before-upload":t.handleBeforeUpload,"on-progress":t.fileUploading}},[e("Button",{attrs:{icon:"ios-cloud-upload-outline"}},[t._v("点击上传")])],1),t.file?e("ul",{staticClass:"ivu-upload-list",staticStyle:{width:"500px"}},[e("li",{staticClass:"ivu-upload-list-file ivu-upload-list-file-finish"},[e("span",[e("Icon",{attrs:{type:"ios-folder"}}),t._v(t._s(t.file.name))],1),e("i",{staticClass:"ivu-icon ivu-icon-ios-close ivu-upload-list-remove",on:{click:t.removeFile}})])]):t._e()],1)],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("返回")]),e("Button",{attrs:{type:"primary"},on:{click:t.confirm}},[t._v("确定")])],1)]),e("Modal",{attrs:{"mask-closable":!0},on:{"on-cancel":t.cancelModal},model:{value:t.exportModal,callback:function(e){t.exportModal=e},expression:"exportModal"}},[e("div",{staticStyle:{"align-items":"center","justify-content":"center",display:"flex"}},[e("Form",{staticStyle:{"align-items":"center","justify-content":"center"},attrs:{"label-position":"left","label-width":150}},[e("h1",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[t._v("导出提示")]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"你本次导出任务ID为:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.taskId))])]),e("FormItem",{staticStyle:{"font-size":"large"},attrs:{label:"你本次导出的文件名为:"}},[e("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(t.taskName))])]),e("span",{staticStyle:{"text-align":"left","margin-bottom":"10px"}},[t._v("请前往"),e("span",{staticStyle:{"font-weight":"bold"}},[t._v("下载管理-下载列表")]),t._v("查看及下载。")])],1)],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelModal}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.Goto}},[t._v("立即前往")])],1)]),e("Modal",{attrs:{title:"生成发票预览",width:"800px",styles:{top:"10px"}},on:{"on-ok":t.createInvoice,"on-cancel":t.cancelInvoice},model:{value:t.invoice_model,callback:function(e){t.invoice_model=e},expression:"invoice_model"}},[e("Card",{attrs:{width:"750px"}},[e("invoiceTemplate",{attrs:{AccountNo:t.invoiceInfo.AccountNo,address:t.invoiceInfo.address,AmountDue:t.invoiceInfo.AmountDue,InvoiceNo:t.invoiceInfo.InvoiceNo,InvoiceDate:t.invoiceInfo.InvoiceDate,FileTitle:t.invoiceInfo.FileTitle,InvoiceDesc:t.invoiceInfo.InvoiceDesc,AmountTax:t.invoiceInfo.AmountTax,Tax:t.invoiceInfo.Tax,TotalAmount:t.invoiceInfo.TotalAmount,columns:t.invoiceColumns,data:t.invoiceInfo.data},on:{InvoiceDesc:t.getdesc}})],1),e("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[e("Button",{on:{click:t.cancelInvoice}},[t._v("取消")]),e("Button",{attrs:{type:"primary"},on:{click:t.createInvoice}},[t._v("生成Invoice")])],1)],1)],1)},D=[],L=i("944d"),O=i("c70b"),j={components:{invoiceTemplate:S["a"]},props:{form:{incomeType:"",beginMonth:"",endMonth:"",corp:""},searchBeginTime:"",searchEndTime:""},data:function(){return{file:null,uploadUrl:"",total:0,pageSize:10,page:1,searchObj:{},corpList:{},downloading:!1,startTime:"",endTime:"",id:null,invoice_model:!1,invoiceInfo:{AccountNo:"北京博新創億科技股份有限公司",address:"北京市海淀区首都體育館南路6號3幢557室",AmountDue:"CNY 1,360.00",InvoiceNo:"IN-************-GDS",InvoiceDate:"30-Mar-2021",FileTitle:"INVOICE",InvoiceDesc:null,AmountTax:"",Tax:"",TotalAmount:"",data:[{description:"GDS-Sales Settlement-Mar2021",billingPeriod:"25-Feb-2021 to 24-Mar-2021",qty:"1",unitPrice:"1,360.00",amount:"1,360.00"},{description:"Amount before Tax",billingPeriod:null,qty:null,unitPrice:"CNY",amount:"1,360.00"},{description:"TAX",billingPeriod:null,qty:null,unitPrice:"CNY",amount:"1,360.00"},{description:"Total Amount Due",billingPeriod:null,qty:null,unitPrice:"CNY",amount:"1,360.00"}]},invoiceColumns:[{title:"Description",align:"center",width:220,key:"description"},{title:"Billing Period",align:"center",width:220,key:"billingPeriod"},{title:"Qty",align:"center",width:60,key:"qty"},{title:"Unit Price",align:"center",width:115,key:"unitPrice"},{title:"amount",align:"center",width:116,key:"amount"}],columns:[{title:"客户名称",key:"corpName",align:"center",minWidth:200,fixed:"left"},{title:"结算月份",key:"statTime",align:"center",minWidth:200},{title:"客户EBS编码",key:"ebscode",align:"center",minWidth:200},{title:"Invoice no.",key:"invoiceNo",align:"center",minWidth:200},{title:"币种",key:"currency",align:"center",minWidth:200,render:function(t,e){var i=e.row,a="156"==i.currency?"CNY":"840"==i.currency?"USD":"344"==i.currency?"HKD":"";return t("label",a)}},{title:"套餐收入",key:"packageIncome",align:"center",minWidth:200,render:function(t,e){var i=e.row,a=parseFloat(O.divide(O.bignumber(i.packageIncome),100).toFixed(2)).toString();return t("label",a)}},{title:"用量收入",key:"useIncome",align:"center",minWidth:200,render:function(t,e){var i=e.row,a=parseFloat(O.divide(O.bignumber(i.useIncome),100).toFixed(2)).toString();return t("label",a)}},{title:"总收入",key:"totalIncome",align:"center",minWidth:200,render:function(t,e){var i=e.row,a=parseFloat(O.divide(O.bignumber(i.totalIncome),100).toFixed(2)).toString();return t("label",a)}},{title:"税费",key:"taxation",align:"center",minWidth:200,render:function(t,e){var i=e.row,a=parseFloat(O.divide(O.bignumber(i.taxation),100).toFixed(2)).toString();return t("label",a)}},{title:"服务开始时间",key:"svcStartTime",align:"center",minWidth:200},{title:"服务结束时间",key:"svcEndTime",align:"center",minWidth:200},{title:"文件下载",slot:"download",minWidth:350,align:"center",fixed:"right"},{title:"编辑",slot:"update",minWidth:200,align:"center",fixed:"right"}],data:[],loading:!1,downLoad:!1,taskName:"测试",taskId:"test",exportModal:!1,updateModal:!1,InvoiceNo:"",address:"",desc:"",rule:{packageIncome:[{required:!0,message:"请输入套餐收入",trigger:"blur"}],useIncome:[{required:!0,message:"请输入用量收入",trigger:"blur"}],taxation:[{required:!0,message:"请输入税费",trigger:"blur"}],svcStartTime:[{type:"date",required:!0,message:"请选择服务开始时间",trigger:"blur"}],svcEndTime:[{type:"date",required:!0,message:"请选择服务结束时间",trigger:"blur"}]}}},created:function(){},mounted:function(){this.searchObj=this.form,this.getTableData()},methods:{getTableData:function(){var t=this;Object(L["a"])({beginMonth:this.searchBeginTime,endMonth:this.searchEndTime,corpId:this.searchObj.corpId,corpName:this.searchObj.corpName,pageNum:this.page,pageSize:this.pageSize,type:[3,4,7,8]}).then((function(e){"0000"===e.code&&(t.data=e.data,t.total=e.count)}))},search:function(){this.getTableData()},loadByPage:function(t){this.page=t,this.getTableData(t,this.pageSize)},loadByPageSize:function(t){this.pageSize=t,this.getTableData(this.page,t)},cancelModal:function(){this.exportModal=!1,this.updateModal=!1,this.$refs.corpList.resetFields()},confirm:function(){var t=this;this.$refs["corpList"].validate((function(e){e&&t.$Modal.info({title:"只能修改一次，是否确认提交修改？",onOk:function(){var e=new FormData;e.append("id",t.corpList.id),e.append("packageIncome ",O.multiply(O.bignumber(t.corpList.packageIncome),100).toString()),e.append("svcEndTime ",t.endTime),e.append("svcStartTime ",t.startTime),e.append("taxation",O.multiply(O.bignumber(t.corpList.taxation),100).toString()),e.append("useIncome",O.multiply(O.bignumber(t.corpList.useIncome),100).toString()),t.file&&e.append("multipartFile",t.file),Object(L["b"])(e).then((function(e){"0000"===e.code&&(t.updateModal=!1,t.getTableData())}))}})}))},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModal=!1},downloadFile:function(){var t=this;Object(L["n"])({beginMonth:this.searchBeginTime,endMonth:this.searchEndTime,corpId:this.searchObj.corpId,corpName:this.searchObj.corpName,userId:this.$store.state.user.userId}).then((function(e){if(!e||"0000"!=e.code)throw e;t.exportModal=!0,t.taskId=e.data.taskId,t.taskName=e.data.taskName})).catch((function(t){console.log(t)}))},exportCommon:function(t,e){var i=this;"Package"===e?Object(L["l"])({corpId:t.corpId,corpName:t.corpName,month:t.statTime,userId:this.$store.state.user.userId}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0,i.taskId=t.data.taskId,i.taskName=t.data.taskName})).catch((function(t){console.log(t)})):"flowSettle"===e?Object(L["p"])({corpId:t.corpId,corpName:t.corpName,month:t.statTime,userId:this.$store.state.user.userId}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0,i.taskId=t.data.taskId,i.taskName=t.data.taskName})).catch((function(t){console.log(t)})):"PackageUsed"===e?Object(L["m"])({corpId:t.corpId,corpName:t.corpName,month:t.statTime,userId:this.$store.state.user.userId}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0,i.taskId=t.data.taskId,i.taskName=t.data.taskName})).catch((function(t){console.log(t)})):"flowUsed"===e?Object(L["q"])({corpId:t.corpId,corpName:t.corpName,month:t.statTime,userId:this.$store.state.user.userId}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0,i.taskId=t.data.taskId,i.taskName=t.data.taskName})).catch((function(t){console.log(t)})):"Invoice"===e?exportInvoice({corpName:t.corpName,invoicePath:t.invoicePath,month:t.statTime}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0,i.taskId=t.data.taskId,i.taskName=t.data.taskName})).catch((function(t){console.log(t)})):"taxation"===e&&Object(L["o"])({corpName:t.corpName,taxationPath:t.invoicePath,month:t.statTime,userId:this.$store.state.user.userId}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0,i.taskId=t.data.taskId,i.taskName=t.data.taskName})).catch((function(t){console.log(t)}))},createInvoice:function(){var t=this;this.desc?Object(k["e"])({address:this.invoiceInfo.address,id:this.id,invoiceDesc:this.desc,type:2}).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"发票生成成功"}),t.invoice_model=!1,t.loadByPage(t.page)})).catch((function(t){console.log(t)})):this.$Message.error("发票说明不能为空")},createInvoiceNo:function(t,e){var i=this;Object(k["f"])(t.id,t.corpId,2).then((function(a){if(!a||"0000"!=a.code)throw a;i.address=a.data.address,i.InvoiceNo=2===e?a.data.invoiceNo:t.invoiceNo;var n="156"==t.currency?"CNY":"840"==t.currency?"USD":"344"==t.currency?"HKD":"";i.invoiceInfo={AccountNo:t.corpName,address:i.address,AmountDue:n+" "+(t.packageIncome+t.useIncome),InvoiceNo:i.InvoiceNo,InvoiceDate:a.data.invoiceDate,FileTitle:"INVOICE",InvoiceDesc:t.invoiceDesc,AmountTax:n+" "+(t.packageIncome+t.useIncome),Tax:n+" "+t.taxation,TotalAmount:n+" "+(t.packageIncome+t.useIncome),data:[{description:a.data.invoiceNameDesc,billingPeriod:a.data.billingPeriod,qty:"1",unitPrice:t.packageIncome+t.useIncome,amount:t.packageIncome+t.useIncome}]},i.invoice_model=!0})).catch((function(t){console.log(t)}))},cancelInvoice:function(){this.id=null,this.invoice_model=!1,this.invoiceInfo=[]},showInvoiceView:function(t){this.id=t.id,null==t.invoiceNo||null!=t.invoiceNo&&"IN"!=t.invoiceNo.substr(0,2)?this.createInvoiceNo(t,2):this.createInvoiceNo(t,1)},getdesc:function(t){this.desc=t},update:function(t){this.updateModal=!0,this.corpList=Object.assign({},t),this.corpList.taxation=parseFloat(O.divide(O.bignumber(t.taxation),100).toFixed(2)).toString(),this.corpList.useIncome=parseFloat(O.divide(O.bignumber(t.useIncome),100).toFixed(2)).toString(),this.corpList.packageIncome=parseFloat(O.divide(O.bignumber(t.packageIncome),100).toFixed(2)).toString(),this.endTime=this.corpList.svcEndTime,this.startTime=this.corpList.svcStartTime},endTimeDateChange:function(t){this.endTime=t},startTimeDateChange:function(t){this.startTime=t},fileSuccess:function(t,e,i){this.message="请先下载模板文件，并按格式填写后上传"},handleError:function(t,e){var i=this;setTimeout((function(){i.uploading=!1,i.$Notice.warning({title:"错误提示",desc:"上传失败！"})}),3e3)},handleBeforeUpload:function(t){return/^.+(\.pdf)$/.test(t.name)?this.file=t:this.$Notice.warning({title:"文件格式不正确",desc:"文件 "+t.name+" 格式不正确，请上传pdf格式文件。"}),!1},fileUploading:function(t,e,i){this.message="文件上传中、待进度条消失后再操作"},removeFile:function(){this.file=""}}},A=j,F=Object(v["a"])(A,C,D,!1,null,null,null),B=F.exports,P=i("0ae5"),z=i("ba27"),$=(i("2315"),i("e472")),E=i("66df"),W="/stat",R=function(t){return E["a"].request({url:W+"/statSupplierCost/query",data:t,method:"post"})},U=function(t){return E["a"].request({url:W+"/statSupplierCost/exportSummary",data:t,method:"POST",responseType:"blob"})},q=function(t){return E["a"].request({url:W+"/statSupplierCost/exportDetail",data:t,method:"POST",responseType:"blob"})},H=function(t){return E["a"].request({url:W+"/statSupplierCost/add",data:t,method:"post"})},Y=function(t){return E["a"].request({url:W+"/statSupplierCost/edit",data:t,method:"post"})},G=function(t){return E["a"].request({url:W+"/statSupplierCost/operationConfirm",data:t,method:"post"})},K="/stat",V=function(t){return E["a"].request({url:K+"/blankOrder/getOrder",params:t,method:"get"})},J=function(t){return E["a"].request({url:"/cms/channel/getChannelList",params:t,method:"get"})},Z=function(t){return E["a"].request({url:K+"/blankOrder/pageList",params:t,method:"get",responseType:"blob"})},Q=(i("6dfa"),i("c70b")),X={mixins:[P["a"]],components:{Online:b,Corp:M,Other:B,invoiceTemplate:S["a"]},data:function(){var t=function(t,e,i){var a=/^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;!e||a.test(e)?i():i(new Error(t.message))},e=function(t,e,i){var a=e;"-"===e.substr(0,1)&&(a=e.substr(1,e.length));var n=/^(([0-9]\d{0,7})|0)(\.\d{0,2})?$/;!a||n.test(a)?i():i(new Error(t.message))};return{isSearch:!0,showUpOnline:!1,form:{incomeType:"",beginMonth:"",endMonth:"",corpId:"",searchBeginTime:"",searchEndTime:"",data:"",count:"",supplierShortenName:"",supplierShortenCode:"",statDate:"",syncRap:"",approvalStatus:"",customer:""},invoiceForm:[{invoiceNameDesc:"",billingPeriod:""}],data:[],searchObj:{},row:{},total:0,pageSize:10,page:1,file:null,Customerfile:null,uploadUrl:"",downloading:!1,exportModal:!1,exportModalr:!1,updateModal:!1,updateOTModal:!1,CerateModal:!1,selectModeModal:!1,costModal:!1,invoiceModal:!1,imsiModal:!1,searchloading:!1,Cerateloading:!1,Invoiceloading:!1,OTloading:!1,updateLoading:!1,accountingLoading:!1,summaryLoading:!1,detailLoading:!1,costLoading:!1,besureSubmitLoading:!1,imsiLoading:!1,taskName:"",taskId:"",titleName:"",spanData:[],loading:!1,corpList:{},corpLists:[],corpListOP:{id:"",corpName:"",accountAdjustment:"",flowAdjustment:"",imsiAdjustment:""},cerateList:{},corpListCost:{statDate:"",supplierName:"",currency:"",totalAmount:"",type:""},supplierShortenList:[],customerList:[],costType:"",startTime:"",endTime:"",InvoiceNo:"",address:"",desc:"",default:"Payment Instruction \nPlease remit payment to beneficiary China Mobile International Limited by telegraph transfer\nAccount Name: China Mobile International Limited\nName of Bank: The Hongkong & Shanghai Banking Corporation Limited\nBank Address: 1 Queen's Road, Central, Hong Kong\nAccount Number: 848-021796-838\nSWIFT Code: HSBCHKHHHKH\n*Please quote our invoice number(s) with your payment instructions to the bank upon remittance.\n*Please email remittance <NAME_EMAIL> for update of your account.\nThis computer generated document requires no signature.",selection:[],selectionIds:[],selectionTypes:[],selectionList:[],imsiOP:{id:"",amount:"",desc:""},accountingList:{accountingType:""},remind:!1,taskIds:[],taskNames:[],costIdList:[],columns:[{title:"订购渠道",key:"salesChannel",align:"center",render:function(t,e){var i={102:"API",103:"官网（H5）",104:"北京移动",105:"批量售卖",106:"推广活动",110:"测试渠道",111:"合作发卡",112:"后付费发卡",113:"WEB",114:"流量池WEB"};return t("span",i[e.row.salesChannel])},tooltip:!0,minWidth:200,tooltipMaxWidth:2e3},{title:"结算月份",key:"statTime",align:"center",tooltip:!0,minWidth:200,tooltipMaxWidth:2e3},{title:"套餐数量",key:"packageNum",align:"center",tooltip:!0,minWidth:200,tooltipMaxWidth:2e3},{title:"加油包数量",key:"refuelNum",align:"center",tooltip:!0,minWidth:200,tooltipMaxWidth:2e3},{title:"套餐收入金额",key:"amount",align:"center",tooltip:!0,minWidth:200,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.amount),100).toFixed(2)).toString();return t("label",a)}},{title:"加油包收入金额",key:"amountRefuel",align:"center",tooltip:!0,minWidth:200,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.amountRefuel),100).toFixed(2)).toString();return t("label",a)}},{title:"总收入金额",key:"amountTotal",align:"center",tooltip:!0,minWidth:200,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.amountTotal),100).toFixed(2)).toString();return t("label",a)}},{title:"币种",key:"currency",align:"center",minWidth:200,render:function(t,e){var i=e.row,a="156"==i.currency?"CNY":"840"==i.currency?"USD":"344"==i.currency?"HKD":"";return t("label",a)}},{title:"文件下载",slot:"action",minWidth:270,fixed:"right",align:"center"}],columnsOP:[{type:"selection",width:60,align:"center",fixed:"left"},{title:"公司名称",key:"companyName",align:"center",width:150,tooltip:!0,tooltipMaxWidth:2e3,fixed:"left"},{title:"客户EBS编码",key:"ebscode",align:"center",width:120,tooltip:!0,tooltipMaxWidth:2e3},{title:"Invoice no.",key:"invoiceNo",align:"center",minWidth:135,tooltip:!0,tooltipMaxWidth:2e3},{title:"币种",key:"currency",align:"center",width:70,tooltip:!0,render:function(t,e){var i=e.row,a="156"==i.currency?"CNY":"840"==i.currency?"USD":"344"==i.currency?"HKD":"";return t("label",a)}},{title:"直接收入总额",key:"directIncome",align:"center",width:110,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.directIncome),100).toFixed(2)).toString();return t("label",a)}},{title:"调账金额",key:"accountAdjustment",align:"center",width:110,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.accountAdjustment),100).toFixed(2)).toString();return t("label",a)}},{title:"间接收入总额",key:"indirectIncome",align:"center",width:110,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.indirectIncome),100).toFixed(2)).toString();return t("label",a)}},{title:"流量收入总额",key:"flowTotalAmount",align:"center",width:110,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.flowTotalAmount),100).toFixed(2)).toString();return t("label",a)}},{title:"流量调账金额",key:"flowAdjustAmount",align:"center",width:110,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.flowAdjustAmount),100).toFixed(2)).toString();return t("label",a)}},{title:"IMSI费收入总额",key:"imsiTotalAmount",align:"center",width:130,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.imsiTotalAmount),100).toFixed(2)).toString();return t("label",a)}},{title:"IMSI费调账金额",key:"imsiAdjustAmount",align:"center",width:130,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.imsiAdjustAmount),100).toFixed(2)).toString();return t("label",a)}},{title:"总销售额",key:"saleIncome",align:"center",width:110,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.saleIncome),100).toFixed(2)).toString();return t("label",a)}},{title:"Top Up Bonus",key:"sellRebateUsedAmount",align:"center",width:120,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.sellRebateUsedAmount),100).toFixed(2)).toString();return t("label",a)}},{title:"实际收入金额",key:"realIncome",align:"center",width:110,tooltip:!0,tooltipMaxWidth:2e3,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.realIncome),100).toFixed(2)).toString();return t("label",a)}},{title:"服务开始时间",key:"svcStartTime",align:"center",width:110,tooltip:!0,tooltipMaxWidth:2e3},{title:"服务结束时间",key:"svcEndTime",align:"center",width:120,tooltip:!0,tooltipMaxWidth:2e3},{title:"上传RAP状态",key:"uploadStatus",align:"center",minWidth:120,tooltip:!0,fixed:"right",render:function(t,e){var i=e.row,a="1"===i.uploadStatus?"未上传":"2"===i.uploadStatus?"已上传":"";return t("label",a)}},{title:"审批状态",key:"authStatus",align:"center",minWidth:85,tooltip:!0,tooltipMaxWidth:2e3,fixed:"right",render:function(t,e){var i=e.row,a="1"===i.authStatus?"通过":"2"===i.authStatus?"不通过":"3"===i.authStatus?"待审核":(i.authStatus,"");return t("label",a)}},{title:"审批操作",slot:"approveAction",minWidth:100,align:"center",fixed:"right"},{title:"文件下载",slot:"download",width:100,align:"center",fixed:"right"},{title:"编辑",slot:"action",align:"center",width:100,fixed:"right"}],columnsOT:[{type:"selection",width:60,align:"center"},{title:"客户名称",key:"corpName",align:"center",minWidth:200},{title:"结算月份",key:"statTime",align:"center",minWidth:200},{title:"客户EBS编码",key:"ebscode",align:"center",minWidth:200},{title:"Invoice no.",key:"invoiceNo",align:"center",minWidth:200},{title:"币种",key:"currency",align:"center",minWidth:200,render:function(t,e){var i=e.row,a="156"==i.currency?"CNY":"840"==i.currency?"USD":"344"==i.currency?"HKD":"";return t("label",a)}},{title:"套餐收入",key:"packageIncome",align:"center",minWidth:200,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.packageIncome),100).toFixed(2)).toString();return t("label",a)}},{title:"用量收入",key:"useIncome",align:"center",minWidth:200,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.useIncome),100).toFixed(2)).toString();return t("label",a)}},{title:"总收入",key:"totalIncome",align:"center",minWidth:200,render:function(t,e){var i=e.row,a=parseFloat(Q.divide(Q.bignumber(i.totalIncome),100).toFixed(2)).toString();return t("label",a)}},{title:"服务开始时间",key:"svcStartTime",align:"center",minWidth:200},{title:"服务结束时间",key:"svcEndTime",align:"center",minWidth:200},{title:"文件下载",slot:"download",minWidth:340,align:"center",fixed:"right"},{title:"编辑",slot:"update",minWidth:90,align:"center",fixed:"right"},{title:"审批状态",key:"authStatus",align:"center",minWidth:90,tooltip:!0,tooltipMaxWidth:2e3,fixed:"right",render:function(t,e){var i=e.row,a="1"===i.authStatus?"通过":"2"===i.authStatus?"不通过":"3"===i.authStatus?"待审核":(i.authStatus,"");return t("label",a)}},{title:"审批操作",slot:"approveAction",minWidth:90,align:"center",fixed:"right"}],RechargeFreeFeeConsumption:[{title:"结算月份",key:"monthOfSettlement",align:"center",minWidth:130,tooltip:!0,fixed:"left"},{title:"币种",key:"currencyCode",align:"center",minWidth:100,render:function(t,e){var i=e.row,a="156"==i.currencyCode?"CNY":"840"==i.currencyCode?"USD":"344"==i.currencyCode?"HKD":"";return t("label",a)}},{title:"Top Up Bonus",key:"topUpBonus",align:"center",minWidth:150,tooltip:!0},{title:"Top Up Bonus(HKD)",key:"topUpBonusHkd",align:"center",minWidth:150,tooltip:!0}],whiteCradColumns:[{type:"selection",width:60,align:"center",fixed:"left"},{title:"客户公司名称",key:"corpName",align:"center",minWidth:130,tooltip:!0,fixed:"left"},{title:"EBSCode",key:"ebsCode",align:"center",minWidth:150,tooltip:!0},{title:"Invoice no.",key:"invoiceNo",align:"center",minWidth:150,tooltip:!0},{title:"白卡数量",key:"cardNum",align:"center",minWidth:150,tooltip:!0},{title:"白卡单价",key:"listedPrice",align:"center",minWidth:150,tooltip:!0},{title:"币种",key:"currencyCode",align:"center",minWidth:100,render:function(t,e){var i=e.row,a="156"==i.currencyCode?"CNY":"840"==i.currencyCode?"USD":"344"==i.currencyCode?"HKD":"";return t("label",a)}},{title:"总金额",key:"amount",align:"center",minWidth:150,tooltip:!0},{title:"订单日期",key:"orderDate",align:"center",minWidth:150,tooltip:!0},{title:"结算月份",key:"period",align:"center",minWidth:150,tooltip:!0},{title:"交付月份",key:"period",align:"center",minWidth:150,tooltip:!0},{title:"操作",key:"action",width:150,align:"center",slot:"action"}],costPriceColumns:[{type:"selection",width:60,align:"center",fixed:"left"},{title:"结算周期",key:"statDate",align:"center",minWidth:130,tooltip:!0,tooltipMaxWidth:2e3},{title:"资源供应商",key:"supplierShortenName",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"供应商编码",key:"supplierShortenCode",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"币种",key:"currency",align:"center",minWidth:100,render:function(t,e){var i=e.row,a="";switch(i.currency){case"CNY":a="人民币";break;case"HKD":a="港币";break;case"USD":a="美元";break;case"EUR":a="欧元";break;default:a=""}return t("label",a)}},{title:"成本预估",key:"totalAmount",align:"center",minWidth:150,tooltip:!0},{title:"成本类型",key:"type",align:"center",minWidth:150,tooltip:!0},{title:"审批状态",key:"approvalStatus",align:"center",minWidth:150,tooltip:!0,render:function(t,e){var i=e.row,a="";switch(i.approvalStatus){case"1":a="未审批";break;case"2":a="审批完成";break;default:a=""}return t("label",a)}},{title:"同步Rap状态",key:"syncRap",align:"center",minWidth:150,tooltip:!0,render:function(t,e){var i=e.row,a="";switch(i.syncRap){case"1":a="未上传";break;case"2":a="待上传";break;case"3":a="已上传";break;case"4":a="上传失败";break;default:a=""}return t("label",a)}},{title:"操作",slot:"action",align:"center",minWidth:150,tooltip:!0,fixed:"right"}],ruleInline:{incomeType:[{type:"number",required:!0,message:"请选择收入类型",trigger:"change"}],beginMonth:[{type:"date",required:!0,message:"请选择开始月份",trigger:"change"}],endMonth:[{type:"date",required:!0,message:"请选择结束月份",trigger:"blur"}],statDate:[{type:"date",required:!0,message:"请选择结算周期",trigger:"blur"}]},rule:{accountAdjustment:[{required:!0,message:"请输入调账金额",trigger:"blur"},{validator:e,message:"最高支持8位整数和2位小数的正负数"}],flowAdjustment:[{required:!0,message:"请输入流量收入",trigger:"blur"},{validator:e,message:"最高支持8位整数和2位小数的正负数"}],imsiAdjustment:[{required:!0,message:"请输入IMSI费收入",trigger:"blur"},{validator:e,message:"最高支持8位整数和2位小数的正负数"}]},ruleMode:{accountingType:[{required:!0,message:"请选择出账",trigger:"blur"}]},ruleCerate:{taxation:[{message:"请输入税费",trigger:"blur"},{validator:t,message:"最高支持8位整数和2位小数的正数或零"}]},ruleOT:{packageIncome:[{required:!0,message:"请输入套餐收入",trigger:"blur"},{validator:t,message:"最高支持8位整数和2位小数的正数或零"}],useIncome:[{required:!0,message:"请输入用量收入",trigger:"blur"},{validator:t,message:"最高支持8位整数和2位小数的正数或零"}],taxation:[{required:!0,message:"请输入税费",trigger:"blur"}],svcStartTime:[{type:"date",required:!0,message:"请选择服务开始时间",trigger:"blur"}],svcEndTime:[{type:"date",required:!0,message:"请选择服务结束时间",trigger:"blur"}]},ruleCost:{statDate:[{type:"date",required:!0,message:"请选择结算周期"}],supplierShortenName:[{required:!0,message:"请选择资源供应商"}],supplierShortenCode:[{required:!0,message:"请输入供应商编码"}],currency:[{required:!0,message:"请选择币种"}],totalAmount:[{required:!0,message:"请输入成本预估"},{validator:function(t,e,i){if(!e||""===e)return i();var a=/^(?!0(\.0+)?$)([1-9]\d{0,7}(\.\d{1,2})?|\d{1,8}(\.\d{1,2})?)$/;return a.test(e)?i():i(new Error("最高支持8位整数和2位小数的正数"))},trigger:"blur",message:"最高支持8位整数和2位小数的正数"}],type:[{required:!0,message:"请输入成本类型"}]},imsiRule:{amount:[{required:!0,message:"请输入imsi费金额"},{validator:t,message:"最高支持8位整数和2位小数的正数或零"}],desc:[{required:!0,message:"请输入imsi费描述"}]},typeList:[{value:1,label:"线上收入"},{value:2,label:"渠道商收入"},{value:3,label:"其他客户收入"},{value:4,label:"白卡订单收入"},{value:5,label:"成本报表收入"},{value:6,label:"充值赠费消耗汇总表报表"}],onlineCorpList:[{corpId:103,corpName:"官网H5"}],searchBeginTime:"",searchEndTime:"",searchStatDate:"",addStatDate:"",type:"",cooperationMode:"",invoiceTitle:"",modelTitle:"",modelType:"",id:null,invoiceNoCopy:"",invoice_model:!1,invoiceType:"",billType:"",rowData:{},invoiceInfo:{AccountNo:"北京博新創億科技股份有限公司",address:"北京市海淀区首都體育館南路6號3幢557室",AmountDue:"CNY 1,360.00",InvoiceNo:"IN-************-GDS",InvoiceDate:"30-Mar-2021",FileTitle:"INVOICE",InvoiceDesc:null,data:[{description:"GDS-Sales Settlement-Mar2021",billingPeriod:"25-Feb-2021 to 24-Mar-2021",qty:"1",unitPrice:"1,360.00",amount:"1,360.00"},{description:"Amount before Tax",billingPeriod:null,qty:null,unitPrice:"CNY",amount:"1,360.00"},{description:"TAX",billingPeriod:null,qty:null,unitPrice:"CNY",amount:"1,360.00"},{description:"Total Amount Due",billingPeriod:null,qty:null,unitPrice:"CNY",amount:"1,360.00"}]},invoiceColumns:[{title:"* Description",align:"center",width:220,slot:"description",renderHeader:function(t,e){return t("div",[t("span",{style:{color:"red"}},"*"),t("span"," description")])}},{title:"* Billing Period",align:"center",width:220,slot:"billingPeriod",renderHeader:function(t,e){return t("div",[t("span",{style:{color:"red"}},"*"),t("span"," Billing Period")])}},{title:"Qty",align:"center",width:60,key:"qty"},{title:"Unit Price",align:"center",width:115,key:"unitPrice"},{title:"amount",align:"center",width:116,key:"amount"}],invoiceFileColumns:[{title:"公司名称",key:"companyName",align:"center",minWidth:130,tooltip:!0,tooltipMaxWidth:2e3},{title:"客户EBS编码",key:"ebscode",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3},{title:"Invoice no.",key:"invoiceNo",align:"center",minWidth:150,tooltip:!0,tooltipMaxWidth:2e3}],invoiceFileTableData:[],batchIds:[],dataToSend:{},selectedBlankCardRows:[],selectedBlankCardIds:new Set,hasBlankCardOrder:!1,downloadingMap:{},batchDownloadLoading:!1}},created:function(){this.ruleInline.beginMonth.push({validator:this.validateDate,trigger:"change"}),this.ruleInline.endMonth.push({validator:this.validateDate,trigger:"change"})},mounted:function(){var t=null===JSON.parse(localStorage.getItem("formList"))?"":JSON.parse(localStorage.getItem("formList"));null!=t&&(this.form.incomeType=void 0===t.incomeType?"":Number(t.incomeType),this.form.corpId=void 0===t.corpId?"":t.corpId,this.searchObj.corpId=void 0===t.corpId?"":t.corpId,this.form.beginMonth=void 0===t.searchBeginTime?"":t.searchBeginTime,this.form.endMonth=void 0===t.searchEndTime?"":t.searchEndTime,this.searchBeginTime=void 0===t.searchBeginTime?"":t.searchBeginTime,this.searchEndTime=void 0===t.searchEndTime?"":t.searchEndTime,this.form.incomeType&&(this.changeType(this.form.incomeType),this.getTableData(1)),localStorage.removeItem("formList")),this.searchObj=this.form},methods:{getTableData:function(t){var e=this;this.page=t;var i=this;1===this.type&&Object(m["d"])({beginMonth:this.searchBeginTime,endMonth:this.searchEndTime,salesChannel:this.searchObj.corpId,pageNum:this.page,pageSize:this.pageSize}).then((function(t){"0000"===t.code&&(e.total=t.count,i.data=t.data,e.searchloading=!1,e.getSpanData(i.data))})),2===this.type&&Object(k["k"])({beginMonth:this.searchBeginTime,endMonth:this.searchEndTime,corpId:this.searchObj.corpId,corpName:this.searchObj.corpName,pageNum:this.page,pageSize:this.pageSize}).then((function(t){if("0000"===t.code){var i=t.data,a=[];i.map((function(t,e){a.push(t)})),e.selectionList.forEach((function(i){t.data.forEach((function(t){t.id==i.id&&e.$set(t,"_checked",!0)}))})),e.data=a,e.total=Number(t.count),e.searchloading=!1}})),3===this.type&&Object(L["a"])({beginMonth:this.searchBeginTime,endMonth:this.searchEndTime,corpId:this.searchObj.corpId,corpName:this.searchObj.corpName,pageNum:this.page,pageSize:this.pageSize,type:[3,4,7,8]}).then((function(t){"0000"===t.code&&(e.selectionTypes.forEach((function(i){t.data.forEach((function(t){t.id==i.id&&e.$set(t,"_checked",!0)}))})),e.data=t.data,e.total=t.count,e.searchloading=!1)})),4===this.type&&V({pageSize:this.pageSize,pageNum:this.page,corpId:this.form.customer,beginDate:this.searchBeginTime,endDate:this.searchEndTime}).then((function(t){"0000"===t.code&&(e.data=t.data,e.selectedBlankCardRows.forEach((function(t){e.data.forEach((function(i){i.id===t.id&&e.$set(i,"_checked",!0)}))})),e.total=Number(t.count))})),5===this.type&&R({supplierShortenName:this.form.supplierShortenName,supplierShortenCode:this.form.supplierShortenCode,statDate:this.searchStatDate,approvalStatus:this.form.approvalStatus,syncRap:this.form.syncRap,current:this.page,size:this.pageSize}).then((function(t){if("0000"===t.code){var i=t.data,a=[];i.map((function(t,e){a.push(t)})),e.selectionList.forEach((function(i){t.data.forEach((function(t){t.id==i.id&&e.$set(t,"_checked",!0)}))})),e.data=a,e.data.forEach((function(t){"1"!=t.approvalStatus&&e.$set(t,"_disabled",!0)})),e.total=Number(t.count)}})),6===this.type&&Object(L["v"])({pageSize:this.pageSize,pageNum:this.page,month:this.searchStatDate}).then((function(t){"0000"===t.code&&(e.data=t.data,e.total=Number(t.count))})),this.searchloading=!1},search:function(t){var e=this;this.$refs[t].validate((function(t){t&&(e.isSearch=!0,e.searchloading=!0,e.selectionTypes=[],e.getTableData(1))}))},selectPackage:function(t,e){},cancelPackage:function(t,e){var i=this;this.selectionTypes.forEach((function(t,a){t.id===e.id&&i.selectionTypes.splice(a,1)}))},cancelPackageAll:function(t,e){this.selectionTypes=[]},exportOnline:function(t,e){var i=this;"sum"===e&&Object(m["c"])({month:t.statTime,salesChannel:t.salesChannel,userId:this.$store.state.user.userId}).then((function(t){if("0000"===t.code)if(""!=t.data.taskId&&""!=t.data.taskName){i.exportModal=!0;var e=i;Object.values(t.data).length>0&&Object.values(t.data).forEach((function(t){if(e.taskIds.push(t.taskId),e.taskNames.push(t.taskName),e.taskIds.length>3||e.taskNames.length>3){var i=e.taskIds.slice(0,3),a=e.taskNames.slice(0,3);e.taskIds=i,e.taskNames=a,e.remind=!0}}))}else i.$Message.error("获取下载任务失败")})),"detail"===e&&Object(m["b"])({month:t.statTime,salesChannel:t.salesChannel,userId:this.$store.state.user.userId}).then((function(t){if("0000"!==t.code)throw t;if(""!=t.data.taskId&&""!=t.data.taskName){i.exportModal=!0;var e=i;Object.values(t.data).length>0&&Object.values(t.data).forEach((function(t){if(e.taskIds.push(t.taskId),e.taskNames.push(t.taskName),e.taskIds.length>3||e.taskNames.length>3){var i=e.taskIds.slice(0,3),a=e.taskNames.slice(0,3);e.taskIds=i,e.taskNames=a,e.remind=!0}}))}else i.$Message.error("获取下载任务失败")}))},loadByPage:function(t){this.page=t,this.getTableData(t,this.pageSize)},loadByPageSize:function(t){this.pageSize=t,this.getTableData(this.page,t)},exportcancelModal:function(){this.exportModalr=!1,this.file=""},cancelModal:function(){this.exportModal=!1,this.updateOTModal=!1,this.CerateModal=!1,this.$refs.corpList.resetFields(),this.$refs.cerateList.resetFields(),this.Customerfile="",this.file="",this.taskIds=[],this.taskNames=[],this.remind=!1,this.selectModeModal=!1,this.accountingList.accountingType="",this.$refs.corpListCost.resetFields(),this.corpListCost={},this.costModal=!1,this.costIdList=[]},Gotor:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportcancelModal(),this.exportModalr=!1},Goto:function(){this.$router.push({path:"/taskList",query:{taskId:encodeURIComponent(this.taskId),fileName:encodeURIComponent(this.taskName)}}),this.exportModal=!1},exportDetail:function(t,e){var i=this,a=null,n=null,o=null,s=null,r=null,c=null;null!=t&&(a=t.corpId,n=t.corpName,o=t.currency,s=t.statTime,r=t.id,c=t.accountingType),Object(k["a"])({id:r,beginMonth:this.searchBeginTime,endMonth:this.searchEndTime,corpId:a,corpName:n,currencyCode:o,userId:this.$store.state.user.userId,statTime:s,billType:c}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0;var e=i;Object.values(t.data).length>0&&Object.values(t.data).forEach((function(t){if(e.taskIds.push(t.taskId),e.taskNames.push(t.taskName),e.taskIds.length>3||e.taskNames.length>3){var i=e.taskIds.slice(0,3),a=e.taskNames.slice(0,3);e.taskIds=i,e.taskNames=a,e.remind=!0}}))})).catch((function(t){console.log(t)}))},exportSum:function(t,e){var i=this,a=null,n=null,o=null,s=null,r=null;null!=t&&(a=t.corpId,n=t.corpName,o=t.statTime,s=t.id,r=t.accountingType),Object(k["b"])({id:s,beginMonth:this.searchBeginTime,endMonth:this.searchEndTime,corpId:a,corpName:n,userId:this.$store.state.user.userId,statTime:o,billType:r}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0;var e=i;Object.values(t.data).length>0&&Object.values(t.data).forEach((function(t){if(e.taskIds.push(t.taskId),e.taskNames.push(t.taskName),e.taskIds.length>3||e.taskNames.length>3){var i=e.taskIds.slice(0,3),a=e.taskNames.slice(0,3);e.taskIds=i,e.taskNames=a,e.remind=!0}}))})).catch((function(t){console.log(t)}))},exportInvoice:function(t,e){var i=this;Object(k["j"])({corpName:t.corpName,invoicePath:t.invoicePath,month:t.statTime,corpId:this.$store.state.user.userId,billType:t.accountingType,incomeId:t.id}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModalr=!0,i.taskId=t.data.taskId,i.taskName=t.data.taskName})).catch((function(t){console.log(t)}))},exportA2Zdeatil:function(t){var e=this;Object(k["h"])({id:t.id,corpId:t.corpId,beginDate:t.svcStartTime,endDate:t.svcEndTime,billType:t.accountingType,userId:this.$store.state.user.userId,type:"2"==t.accountingType?"1":"3"==t.accountingType?"2":"4"==t.accountingType?1:"5"==t.accountingType?"3":"6"==t.accountingType?"2":"7"==t.accountingType?"3":""}).then((function(t){if(!t||"0000"!=t.code)throw t;e.exportModal=!0;var i=e;Object.values(t.data).length>0&&Object.values(t.data).forEach((function(t){if(i.taskIds.push(t.taskId),i.taskNames.push(t.taskName),i.taskIds.length>3||i.taskNames.length>3){var e=i.taskIds.slice(0,3),a=i.taskNames.slice(0,3);i.taskIds=e,i.taskNames=a,i.remind=!0}}))})).catch((function(t){console.log(t)}))},exportA2ZSummary:function(t){var e=this;Object(k["g"])({id:t.id,corpId:t.corpId,beginDate:t.svcStartTime,endDate:t.svcEndTime,billType:t.accountingType,userId:this.$store.state.user.userId,type:"2"==t.accountingType?"1":"3"==t.accountingType?"2":"4"==t.accountingType?1:"5"==t.accountingType?"3":"6"==t.accountingType?"2":"7"==t.accountingType?"3":""}).then((function(t){if(!t||"0000"!=t.code)throw t;e.exportModalr=!0,e.taskId=t.data.taskId,e.taskName=t.data.taskName})).catch((function(t){console.log(t)}))},exportIMSIdeatil:function(t){var e=this;Object(k["i"])({id:t.id,corpId:t.corpId,cooperationMode:t.accountingType,dateStart:t.svcStartTime,dateEnd:t.svcEndTime,billType:t.accountingType,userId:this.$store.state.user.userId}).then((function(t){if(!t||"0000"!=t.code)throw t;e.exportModalr=!0,e.taskId=t.data.taskId,e.taskName=t.data.taskName})).catch((function(t){console.log(t)}))},exportRechargeFreeFeeConsumption:function(t){var e=this;this.$refs[t].validate((function(t){t&&(e.downloading=!0,Object(L["h"])({month:e.searchStatDate}).then((function(t){var i=t.data,a=decodeURI(t.headers["content-disposition"].match(/=(.*)$/)[1]);if(console.log(a),"download"in document.createElement("a")){var n=e.$refs.downloadLink,o=URL.createObjectURL(i);n.download=a,n.href=o,n.click(),URL.revokeObjectURL(o)}else navigator.msSaveBlob(i,a)})).catch((function(t){console.log(t)})).finally((function(){e.downloading=!1})))}))},exportWhite:function(t){var e=this;this.$refs[t].validate((function(t){t&&(e.downloading=!0,Z({corpId:e.form.customer,beginDate:e.searchBeginTime,endDate:e.searchEndTime}).then((function(t){var i=t.data,a=decodeURI(t.headers["content-disposition"].match(/=(.*)$/)[1]);if("download"in document.createElement("a")){var n=e.$refs.downloadLink,o=URL.createObjectURL(i);n.download=a,n.href=o,n.click(),URL.revokeObjectURL(o)}else navigator.msSaveBlob(i,a)})).catch((function(t){console.log(t)})).finally((function(){e.downloading=!1})))}))},exportCost:function(t,e){var i=this;this.$refs[t].validate((function(t){if(t){"1"==e?i.summaryLoading=!0:i.detailLoading=!0;var a="1"==e?U:q;a({beginDate:i.searchBeginTime,endDate:i.searchEndTime,corpName:i.searchObj.corpName,pageNum:i.page,pageSize:i.pageSize,supplierShortenName:i.form.supplierShortenName,supplierShortenCode:i.form.supplierShortenCode,statDate:i.searchStatDate,syncRap:i.form.syncRap,approvalStatus:i.form.approvalStatus}).then((function(t){var e=t.data,a=decodeURI(t.headers["content-disposition"].match(/=(.*)$/)[1]);if("download"in document.createElement("a")){var n=i.$refs.downloadLink,o=URL.createObjectURL(e);n.download=a,n.href=o,n.click(),URL.revokeObjectURL(o)}else navigator.msSaveBlob(e,a)})).catch().finally((function(){i.summaryLoading=!1,i.detailLoading=!1}))}}))},update:function(t){this.updateModal=!0,this.cooperationMode=t.accountingType,this.corpListOP.id=t.id,this.corpListOP.corpName=t.corpName,this.corpListOP.accountAdjustment=parseFloat(Q.divide(Q.bignumber(t.accountAdjustment),100).toFixed(2)).toString(),this.corpListOP.flowAdjustment=parseFloat(Q.divide(Q.bignumber(t.flowAdjustAmount),100).toFixed(2)).toString(),this.corpListOP.imsiAdjustment=parseFloat(Q.divide(Q.bignumber(t.imsiAdjustAmount),100).toFixed(2)).toString()},updateOT:function(t){this.updateOTModal=!0,this.corpList=Object.assign({},t),this.corpList.taxation=parseFloat(Q.divide(Q.bignumber(t.taxation),100).toFixed(2)).toString(),this.corpList.useIncome=parseFloat(Q.divide(Q.bignumber(t.useIncome),100).toFixed(2)).toString(),this.corpList.packageIncome=parseFloat(Q.divide(Q.bignumber(t.packageIncome),100).toFixed(2)).toString(),this.endTime=this.corpList.svcEndTime,this.startTime=this.corpList.svcStartTime},channelapprove:function(t,e){var i=this;this.$Modal.confirm({title:1===t?"确认执行审核通过？":"确认执行审核不通过？",onOk:function(){Object(k["d"])({id:e.id,status:t}).then((function(t){"0000"===t.code&&(i.getTableData(1),i.cancel1())}))}})},otherapprove:function(t,e){var i=this;this.$Modal.confirm({title:1===t?"确认执行审核通过？":"确认执行审核不通过？",onOk:function(){Object(L["w"])({id:e.id,status:t}).then((function(t){"0000"===t.code&&(i.getTableData(1),i.cancel1())}))}})},cancel1:function(){this.updateModal=!1,this.updateLoading=!1,this.$refs.corpListOP.resetFields(),this.imsiModal=!1,this.$refs.imsiOP.resetFields()},confirm:function(){var t=this;this.$refs["corpListOP"].validate((function(e){e&&(t.updateLoading=!0,Object(k["m"])({accountAdjustment:Q.multiply(Q.bignumber(t.corpListOP.accountAdjustment),100).toString(),flowAdjustment:Q.multiply(Q.bignumber(t.corpListOP.flowAdjustment),100).toString(),imsiAdjustment:Q.multiply(Q.bignumber(t.corpListOP.imsiAdjustment),100).toString(),id:t.corpListOP.id}).then((function(e){"0000"===e.code&&(t.updateModal=!1,t.getTableData(1),t.cancel1())})).catch((function(e){t.updateLoading=!1})))}))},OTconfirm:function(){var t=this;this.$refs["corpList"].validate((function(e){e&&t.$Modal.confirm({title:"真实账单生成后修改不会修改真实账单,是否确认提交修改?",width:550,onOk:function(){t.OTloading=!0;var e=new FormData;e.append("id",t.corpList.id),e.append("packageIncome ",Q.multiply(Q.bignumber(t.corpList.packageIncome),100).toString()),e.append("svcEndTime ",t.endTime),e.append("svcStartTime ",t.startTime),e.append("useIncome",Q.multiply(Q.bignumber(t.corpList.useIncome),100).toString()),Object(L["b"])(e).then((function(e){"0000"===e.code&&(t.updateOTModal=!1,t.getTableData(1),t.OTloading=!1)})),t.selectionTypes=[]}})}))},accountingConfirm:function(){var t=this;this.$refs["accountingList"].validate((function(e){if(e){var i=t.accountingList.accountingType,a="1"==t.accountingList.accountingType?"代销":"2"==t.accountingList.accountingType?"流量":"合并出账";t.$Modal.confirm({title:"确认选择 "+a+" 进行出账",width:550,onOk:function(){t.accountingLoading=!0,null==t.invoiceNoCopy||null!=t.invoiceNoCopy&&"IN"!=t.invoiceNoCopy.substr(0,2)?t.createInvoiceNo(t.row,2,i,t.row.accountingType):t.createInvoiceNo(t.row,1,i,t.row.accountingType)}})}}))},Cerateconfirm:function(){var t=this;this.Cerateloading=!0;var e=new FormData;e.append("idList",this.cerateList.idList),e.append("corpId ",this.cerateList.corpId),e.append("corpName ",this.cerateList.corpName),e.append("currency ",this.cerateList.currency),e.append("statTime ",this.cerateList.statTime),e.append("svcEndTime ",this.cerateList.svcEndTime),e.append("svcStartTime ",this.cerateList.svcStartTime),e.append("packageIncome ",Q.multiply(Q.bignumber(this.cerateList.packageIncome),100).toString()),e.append("useIncome",Q.multiply(Q.bignumber(this.cerateList.useIncome),100).toString()),e.append("totalIncome",Q.multiply(Q.bignumber(this.cerateList.totalIncome),100).toString()),this.file&&e.append("taxationFile",this.file),this.Customerfile&&e.append("billPathFile",this.Customerfile);var i=!0;if(this.cerateList.taxation){var a=/^(([0-9]\d*)|0)(\.\d{0,2})?$/;a.test(this.cerateList.taxation)||this.$refs["cerateList"].validate((function(t){i=!!t})),i&&e.append("taxation",Q.multiply(Q.bignumber(this.cerateList.taxation),100).toString())}else e.append("taxation",0);i&&Object(L["r"])(e).then((function(e){"0000"===e.code&&(t.CerateModal=!1,t.Cerateloading=!1,t.$Notice.success({title:"提示",desc:"成功生成真实账单！"}),t.selectionTypes=[],t.getTableData(1),t.cancelModal())})).catch((function(e){t.cancelModal(),t.Cerateloading=!1}))},downloadFile:function(t){var e=this;Object(L["n"])({beginMonth:this.searchBeginTime,endMonth:this.searchEndTime,corpId:this.searchObj.corpId,userId:this.$store.state.user.userId,corpName:this.searchObj.corpName,type:[3,4,7,8],exportType:t}).then((function(t){if(!t||"0000"!=t.code)throw t;e.exportModal=!0;var i=e;Object.values(t.data).length>0&&Object.values(t.data).forEach((function(t){if(i.taskIds.push(t.taskId),i.taskNames.push(t.taskName),i.taskIds.length>3||i.taskNames.length>3){var e=i.taskIds.slice(0,3),a=i.taskNames.slice(0,3);i.taskIds=e,i.taskNames=a,i.remind=!0}}))})).catch((function(t){console.log(t)}))},changeCode:function(t){this.corpListCost.supplierShortenCode=this.supplierShortenList[t]},addCostItem:function(t){this.costType=t,this.titleName="新增成本",this.costModal=!0},updateCostItem:function(t,e){this.costType=e,this.corpListCost=JSON.parse(JSON.stringify(t)),this.titleName="修改成本",this.costModal=!0},costConfirm:function(){var t=this,e={statDate:this.addStatDate,supplierShortenName:this.corpListCost.supplierShortenName,supplierShortenCode:this.corpListCost.supplierShortenCode,currency:this.corpListCost.currency,totalAmount:this.corpListCost.totalAmount,type:this.corpListCost.type},i={id:this.corpListCost.id,totalAmount:this.corpListCost.totalAmount,type:this.corpListCost.type},a="1"==this.costType?H:Y,n="1"==this.costType?e:i;this.$refs["corpListCost"].validate((function(e){e&&(t.costLoading=!0,a(n).then((function(e){"0000"===e.code&&(t.$Notice.success({title:"操作提示",desc:"操作成功！"}),t.updateModal=!1,t.getTableData(1),t.cancelModal())})).catch((function(t){})).finally((function(){t.costLoading=!1})))}))},financialApproval:function(){var t=this,e=this.selection.length;e<1?this.$Message.warning("请至少选择一条记录"):(this.selectionList.forEach((function(e,i){t.costIdList.push(e.id)})),this.$Modal.confirm({title:"确定执行审核确认？",onOk:function(){G({statDate:t.searchStatDate,dataIds:t.costIdList}).then((function(e){"0000"===e.code&&(t.$Notice.success({title:"操作提示",desc:"操作成功！"}),t.costIdList=[],t.selectionList=[],t.getTableData(1))})).catch((function(t){})).finally((function(){}))}}))},handleRowChange:function(t){var e=this;this.selection=t,this.selectionIds=[],t.map((function(t,i){var a=!0;e.selectionTypes.map((function(e,i){t.id===e.id&&(a=!1)})),a&&e.selectionTypes.push(t)}))},handleRowChangeInvoice:function(t){var e=this;this.selection=t,t.map((function(t,i){var a=!0;e.selectionList.map((function(e,i){t.id===e.id&&(a=!1)})),a&&e.selectionList.push(t)}))},cancelItem:function(t,e){var i=this;this.selectionList.forEach((function(t,a){t.id===e.id&&i.selectionList.splice(a,1)}))},cancelInvoiceAll:function(t,e){this.selectionList=[]},cancelInvoiceFile:function(){this.modelType="",this.batchIds=[],this.dataToSend={},this.invoiceModal=!1},batchExportInvoice:function(){var t=this;this.modelTitle="缺少Invoice的数据项",this.modelType="1";var e=this.selection.length;if(e<1)this.$Message.warning("请至少选择一条记录");else{var i=[];this.selectionList.forEach((function(e,a){e.invoicePath&&null!=e.invoicePath&&""!==e.invoicePath&&void 0!==e.invoicePath?t.batchIds.push(e.id):i.push(e)})),0==this.batchIds.length?this.$Message.warning({background:!0,content:"所有勾选项均无invoice文件，请重新勾选！"}):i.length>0&&this.batchIds.length>0?(this.invoiceFileTableData=i,this.invoiceTitle="您勾选的数据项中，以上数据没有对应的invoice文件。是否继续下载那些有invoice文件的数据项？",this.invoiceModal=!0):this.batchIds.length>0&&0==i.length&&this.$Modal.confirm({title:"确认执行批量下载Invoice？",onOk:function(){t.besureSubmit()}})}},rapidPaymentOut:function(){var t=this;this.modelTitle="非定制账期选中项",this.modelType="2";var e=this.selection.length;if(e<1)this.$Message.warning("请至少选择一条记录");else{var i=[],a=[],n=[];this.selectionList.forEach((function(e){if(e.isCustomize)if(e.invoicePath&&null!=e.invoicePath&&""!==e.invoicePath&&void 0!==e.invoicePath)i.push(e.id);else{var o={channelExportDTO:{beginMonth:t.searchBeginTime,billType:e.accountingType,cooperationMode:e.accountingType,corpId:e.corpId,corpName:e.corpName,currencyCode:e.currency,dateEnd:e.svcEndTime,dateStart:e.svcStartTime,endMonth:t.searchEndTime,id:e.id,statTime:e.statTime,type:"2"===e.accountingType?"1":"3"===e.accountingType?"2":"4"===e.accountingType?"1":"5"===e.accountingType?"3":"6"===e.accountingType?"2":"7"===e.accountingType?"3":"",userId:t.$store.state.user.userId},corpId:e.corpId,id:e.id,invoiceDesc:e.invoiceDesc?e.invoiceDesc:t.default,type:e.type,invoiceType:e.accountingType?e.accountingType:void 0};a.push(o)}else n.push(e)})),this.dataToSend={createInvoiceVos:a,inComeIds:i},this.isDataToSendEmpty(this.dataToSend)?this.$Message.warning({background:!0,content:"勾选项均非定制账期，请重新勾选！"}):!this.isDataToSendEmpty(this.dataToSend)&&n.length>0?(this.invoiceFileTableData=n,this.invoiceTitle="您勾选的数据项中，以上数据不是定制账期。是否继续处理其余定制账期的数据项？",this.invoiceModal=!0):this.isDataToSendEmpty(this.dataToSend)||0!=n.length||this.$Modal.confirm({title:"确认执行立即出账？",onOk:function(){t.besureSubmit()}})}},besureSubmit:function(){var t=this;this.besureSubmitLoading=!0,"1"==this.modelType?Object(k["c"])({ids:this.batchIds,userId:this.$store.state.user.userId}).then((function(e){if("0000"!==e.code)throw e;t.exportModalr=!0,t.taskId=e.data.taskId,t.taskName=e.data.taskName,t.selection=[],t.selectionList=[],t.cancelInvoiceFile(),t.getTableData(1)})).catch((function(t){})).finally((function(){t.cancelInvoiceFile(),t.besureSubmitLoading=!1})):"2"==this.modelType&&Object(k["l"])(this.dataToSend).then((function(e){if("0000"!==e.code)throw e;t.$Notice.success({title:"操作成功",desc:"操作成功"}),t.selection=[],t.selectionList=[],t.cancelInvoiceFile(),t.getTableData(1)})).catch((function(t){})).finally((function(){t.cancelInvoiceFile(),t.besureSubmitLoading=!1}))},createReal:function(){var t=this.selectionTypes.length;if(t<1)this.$Message.warning("请至少选择一个客户");else{var e=new Date,i={year:e.getFullYear(),month:e.getMonth()+1,date:e.getDate()};this.selectionTypes.sort((function(t,e){return e.statTime.localeCompare(t.statTime)}));var a=null,n=null,o=0,s=0,r=this.selectionTypes[0].corpName,c=0,l=[],d=null,u=this.selectionTypes[0].statTime,p=!1,m=!1;this.selectionTypes.map((function(t,e){a=t.svcEndTime,n=t.svcStartTime,o+=t.packageIncome,s+=t.useIncome,l.push(t.id),t.corpName!=r&&(c+=1),r=t.corpName,Number(u)-Number(t.statTime)>1&&(p=!0),null!=t.invoiceNo&&"IN"===t.invoiceNo.substr(0,2)&&(m=!0),u=t.statTime})),c>0?this.$Message.warning("请选择同一个客户"):p?this.$Message.warning("请选择连续月份的客户"):m?this.$Message.warning("已经生成真实账单的数据不能被勾选"):(d=i.month<10?"0"+i.month:i.month,this.cerateList={idList:l,corpId:this.selectionTypes[0].corpId,corpName:this.selectionTypes[0].corpName,currency:this.selectionTypes[0].currency,statTime:i.year+""+d,svcStartTime:t<1?this.selectionTypes[0].svcStartTime:n,svcEndTime:t<1?a:this.selectionTypes[0].svcEndTime,packageIncome:parseFloat(Q.divide(Q.bignumber(o),100).toFixed(2)).toString(),useIncome:parseFloat(Q.divide(Q.bignumber(s),100).toFixed(2)).toString(),totalIncome:parseFloat(Q.divide(Q.bignumber(o+s),100).toFixed(2)).toString()},this.CerateModal=!0)}},createHistory:function(){this.form.searchBeginTime=this.searchBeginTime,this.form.searchEndTime=this.searchEndTime,this.$router.push({path:"/history",query:{formList:encodeURIComponent(JSON.stringify(this.form))}})},exportCommon:function(t,e){var i=this;"Package"===e?Object(L["l"])({corpId:t.corpId,corpName:t.corpName,month:t.statTime,userId:this.$store.state.user.userId}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0;var e=i;Object.values(t.data).length>0&&Object.values(t.data).forEach((function(t){if(e.taskIds.push(t.taskId),e.taskNames.push(t.taskName),e.taskIds.length>3||e.taskNames.length>3){var i=e.taskIds.slice(0,3),a=e.taskNames.slice(0,3);e.taskIds=i,e.taskNames=a,e.remind=!0}}))})).catch((function(t){console.log(t)})):"flowSettle"===e?Object(L["p"])({corpId:t.corpId,corpName:t.corpName,month:t.statTime,userId:this.$store.state.user.userId}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0;var e=i;Object.values(t.data).length>0&&Object.values(t.data).forEach((function(t){if(e.taskIds.push(t.taskId),e.taskNames.push(t.taskName),e.taskIds.length>3||e.taskNames.length>3){var i=e.taskIds.slice(0,3),a=e.taskNames.slice(0,3);e.taskIds=i,e.taskNames=a,e.remind=!0}}))})).catch((function(t){console.log(t)})):"PackageUsed"===e?Object(L["m"])({corpId:t.corpId,corpName:t.corpName,month:t.statTime,userId:this.$store.state.user.userId}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0;var e=i;Object.values(t.data).length>0&&Object.values(t.data).forEach((function(t){if(e.taskIds.push(t.taskId),e.taskNames.push(t.taskName),e.taskIds.length>3||e.taskNames.length>3){var i=e.taskIds.slice(0,3),a=e.taskNames.slice(0,3);e.taskIds=i,e.taskNames=a,e.remind=!0}}))})).catch((function(t){console.log(t)})):"flowUsed"===e?Object(L["q"])({corpId:t.corpId,corpName:t.corpName,month:t.statTime,userId:this.$store.state.user.userId}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0;var e=i;Object.values(t.data).length>0&&Object.values(t.data).forEach((function(t){if(e.taskIds.push(t.taskId),e.taskNames.push(t.taskName),e.taskIds.length>3||e.taskNames.length>3){var i=e.taskIds.slice(0,3),a=e.taskNames.slice(0,3);e.taskIds=i,e.taskNames=a,e.remind=!0}}))})).catch((function(t){console.log(t)})):"Invoice"===e?Object(k["j"])({corpName:t.corpName,invoicePath:t.invoicePath,month:t.statTime}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0;var e=i;Object.values(t.data).length>0&&Object.values(t.data).forEach((function(t){if(e.taskIds.push(t.taskId),e.taskNames.push(t.taskName),e.taskIds.length>3||e.taskNames.length>3){var i=e.taskIds.slice(0,3),a=e.taskNames.slice(0,3);e.taskIds=i,e.taskNames=a,e.remind=!0}}))})).catch((function(t){console.log(t)})):"taxation"===e&&Object(L["o"])({corpName:t.corpName,taxationPath:t.taxationPath,month:t.statTime,userId:this.$store.state.user.userId}).then((function(t){if(!t||"0000"!=t.code)throw t;i.exportModal=!0;var e=i;Object.values(t.data).length>0&&Object.values(t.data).forEach((function(t){if(e.taskIds.push(t.taskId),e.taskNames.push(t.taskName),e.taskIds.length>3||e.taskNames.length>3){var i=e.taskIds.slice(0,3),a=e.taskNames.slice(0,3);e.taskIds=i,e.taskNames=a,e.remind=!0}}))})).catch((function(t){console.log(t)}))},fileSuccess:function(t,e,i){this.message="请先下载模板文件，并按格式填写后上传"},handleError:function(t,e){var i=this;setTimeout((function(){i.uploading=!1,i.$Notice.warning({title:"错误提示",desc:"上传失败！"})}),3e3)},handleBeforeUpload:function(t){return this.file=t,!1},fileUploading:function(t,e,i){this.message="文件上传中、待进度条消失后再操作"},CustomerfileSuccess:function(t,e,i){this.message="请先下载模板文件，并按格式填写后上传"},CustomerhandleError:function(t,e){var i=this;setTimeout((function(){i.uploading=!1,i.$Notice.warning({title:"错误提示",desc:"上传失败！"})}),3e3)},CustomerhandleBeforeUpload:function(t){return this.Customerfile=t,!1},CustomerfileUploading:function(t,e,i){this.message="文件上传中、待进度条消失后再操作"},removeFile:function(){this.file=""},removeCustomerfile:function(){this.Customerfile=""},createInvoice:function(){var t,e=this,i=this.$refs.dataForm.validateInvoiceForm();this.desc?i&&(t=this.invoiceType?"4"==this.rowData.accountingType?"1"==this.invoiceType?"1":"2"==this.invoiceType?"2":this.rowData.accountingType:"6"==this.rowData.accountingType?"1"==this.invoiceType?"1":"2"==this.invoiceType?"3":this.rowData.accountingType:"7"==this.rowData.accountingType?"1"==this.invoiceType?"1":"2"==this.invoiceType?"5":this.rowData.accountingType:"1"==this.invoiceType?"1":"2"==this.invoiceType?"2":this.rowData.accountingType:this.rowData.accountingType,this.invoiceInfo.InvoiceDesc=this.desc,this.Invoiceloading=!0,Object(k["e"])({companyName:this.invoiceInfo.AccountNo,address:this.invoiceInfo.address,id:this.id,invoiceDesc:this.desc,invoiceNameDesc:this.invoiceForm[0].invoiceNameDesc,billingPeriod:this.invoiceForm[0].billingPeriod,usageInvoiceNameDesc:this.invoiceForm[1].invoiceNameDesc,usageBillingPeriod:this.invoiceForm[1].billingPeriod,imsiInvoiceNameDesc:this.invoiceForm[2].invoiceNameDesc,imsiBillingPeriod:this.invoiceForm[2].billingPeriod,type:2,invoiceType:this.invoiceType?this.invoiceType:void 0,billType:this.billType,channelExportDTO:{corpId:this.rowData.corpId,beginMonth:this.searchBeginTime,endMonth:this.searchEndTime,cooperationMode:this.rowData.accountingType,corpName:this.rowData.corpName,currencyCode:this.rowData.currency,billType:t,id:this.rowData.id,statTime:this.rowData.statTime,dateStart:this.rowData.svcStartTime,dateEnd:this.rowData.svcEndTime,userId:this.$store.state.user.userId,type:"2"==this.rowData.accountingType?"1":"3"==this.rowData.accountingType?"2":"4"==this.rowData.accountingType?1:"5"==this.rowData.accountingType?"3":"6"==this.rowData.accountingType?"2":"7"==this.rowData.accountingType?"3":""}}).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作提示",desc:"发票生成成功"}),e.invoice_model=!1,e.Invoiceloading=!1,e.loadByPage(e.page)})).catch((function(t){e.Invoiceloading=!1,console.log(t)}))):this.$Message.error("发票说明不能为空")},test:function(t){var e=/(\d{1,3})(?=(\d{3})+(?:$|\.))/g;return(t+"").replace(e,"$1,")},formatNumber:function(t){var e=(t/100).toFixed(2),i=new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2,useGrouping:!0}).format(parseFloat(e));return i},createInvoiceNo:function(t,e,i,a){var n=this;this.invoiceType=i,this.billType=a,Object(k["f"])(t.id,t.corpId,2).then((function(a){if(!a||"0000"!=a.code)throw n.accountingLoading=!1,a;n.address=a.data.address,n.InvoiceNo=2===e?a.data.invoiceNo:t.invoiceNo;var o,s,r,c,l="156"==t.currency?"CNY":"840"==t.currency?"USD":"344"==t.currency?"HKD":"",d=(n.test(parseFloat(Q.divide(Q.bignumber(t.saleIncomeOrigin),100).toFixed(2)).toString()),n.test(parseFloat(Q.divide(Q.bignumber(t.realIncomeOrigin),100).toFixed(2)).toString())),u=n.formatNumber(t.taxation);n.test(parseFloat(Q.divide(Q.bignumber(t.realIncomeOrigin-t.taxation),100).toFixed(2)).toString());"4"==t.accountingType||"6"==t.accountingType||"7"==t.accountingType?"1"==i?"3"==t.authStatus?(o=n.formatNumber(t.directIncome+t.indirectIncome+t.arrears-t.totleRemuneration-t.sellRebateUsedAmount),s=n.formatNumber(0),r=n.formatNumber(t.sellImsiAmount),c=n.formatNumber(t.directIncome+t.indirectIncome+t.arrears-t.totleRemuneration+t.sellImsiAmount-t.sellRebateUsedAmount)):(o=n.formatNumber(t.directIncome+t.accountAdjustment+t.indirectIncome+t.arrears-t.totleRemuneration-t.sellRebateUsedAmount),s=n.formatNumber(0),r=n.formatNumber(t.sellImsiAmount+t.sellImsiAdjustAmount),c=n.formatNumber(t.directIncome+t.accountAdjustment+t.indirectIncome+t.arrears-t.totleRemuneration+t.sellImsiAmount+t.sellImsiAdjustAmount-t.sellRebateUsedAmount)):"2"==i?"3"==t.authStatus?(o=n.formatNumber(0),s=n.formatNumber(t.flowTotalAmount),r=n.formatNumber(t.a2zImsiAmount+t.resourceImsiAmount),c=n.formatNumber(t.flowTotalAmount+t.a2zImsiAmount+t.resourceImsiAmount)):(o=n.formatNumber(0),s=n.formatNumber(t.flowTotalAmount+t.flowAdjustAmount),r=n.formatNumber(t.a2zImsiAmount+t.resourceImsiAmount+t.a2zImsiAdjustAmount+t.resourceImsiAdjustAmount),c=n.formatNumber(t.flowTotalAmount+t.flowAdjustAmount+t.a2zImsiAmount+t.resourceImsiAmount+t.a2zImsiAdjustAmount+t.resourceImsiAdjustAmount)):"3"==t.authStatus?(o=n.formatNumber(t.directIncome+t.indirectIncome+t.arrears-t.totleRemuneration-t.sellRebateUsedAmount),s=n.formatNumber(t.flowTotalAmount),r=n.formatNumber(t.imsiTotalAmount),c=n.formatNumber(t.directIncome+t.indirectIncome+t.arrears-t.totleRemuneration+t.flowTotalAmount+t.imsiTotalAmount)):(o=n.formatNumber(t.directIncome+t.accountAdjustment+t.indirectIncome+t.arrears-t.totleRemuneration-t.sellRebateUsedAmount),s=n.formatNumber(t.flowTotalAmount+t.flowAdjustAmount),r=n.formatNumber(t.imsiTotalAmount+t.imsiAdjustAmount),c=n.formatNumber(t.directIncome+t.accountAdjustment+t.indirectIncome+t.arrears-t.totleRemuneration+t.flowTotalAmount+t.flowAdjustAmount+t.imsiTotalAmount+t.imsiAdjustAmount-t.sellRebateUsedAmount)):"3"==t.authStatus?(o=n.formatNumber(t.directIncome+t.indirectIncome+t.arrears-t.totleRemuneration-t.sellRebateUsedAmount),s=n.formatNumber(t.flowTotalAmount),r=n.formatNumber(t.imsiTotalAmount),c=n.formatNumber(t.directIncome+t.indirectIncome+t.arrears-t.totleRemuneration+t.flowTotalAmount+t.imsiTotalAmount-t.sellRebateUsedAmount)):(o=n.formatNumber(t.directIncome+t.accountAdjustment+t.indirectIncome+t.arrears-t.totleRemuneration-t.sellRebateUsedAmount),s=n.formatNumber(t.flowTotalAmount+t.flowAdjustAmount),r=n.formatNumber(t.imsiTotalAmount+t.imsiAdjustAmount),c=n.formatNumber(t.directIncome+t.accountAdjustment+t.indirectIncome+t.arrears-t.totleRemuneration+t.flowTotalAmount+t.flowAdjustAmount+t.imsiTotalAmount+t.imsiAdjustAmount-t.sellRebateUsedAmount));var p=a.data.invoiceNameDesc.replace("%s","Distribution"),m=a.data.invoiceNameDesc.replace("%s","Usage"),h=a.data.invoiceNameDesc.replace("%s","IMSI"),g=a.data.billingPeriod,f=a.data.billingPeriod,v=a.data.billingPeriod;n.invoiceInfo={AccountNo:a.data.companyName,address:n.address,AmountDue:l+" "+c,InvoiceNo:n.InvoiceNo,InvoiceDate:a.data.invoiceDate,FileTitle:"INVOICE",InvoiceDesc:void 0===t.invoiceDesc?n.default:t.invoiceDesc,AmountTax:l+" "+d,Tax:u,TotalAmount:c,currencyCode:l,data:[{description:p,billingPeriod:g,qty:"1",unitPrice:o,amount:o},{description:m,billingPeriod:f,qty:"1",unitPrice:s,amount:s},{description:h,billingPeriod:v,qty:"1",unitPrice:r,amount:r}]},n.invoiceForm=[{invoiceNameDesc:p,billingPeriod:g},{invoiceNameDesc:m,billingPeriod:f},{invoiceNameDesc:h,billingPeriod:v}],n.selectModeModal=!1,n.accountingList.accountingType="",n.accountingLoading=!1,n.invoice_model=!0})).catch((function(t){console.log(t)}))},cancelInvoice:function(){this.id=null,this.invoice_model=!1,this.invoiceInfo=[],this.$refs.dataForm.$refs.invoiceForm.resetFields()},showInvoiceView:function(t){this.rowData=t,this.id=t.id,this.invoiceNoCopy=t.invoiceNo,"4"==t.accountingType||"6"==t.accountingType||"7"==t.accountingType?(this.selectModeModal=!0,this.row=JSON.parse(JSON.stringify(t))):null==t.invoiceNo||null!=t.invoiceNo&&"IN"!=t.invoiceNo.substr(0,2)?this.createInvoiceNo(t,2,null,t.accountingType):this.createInvoiceNo(t,1,null,t.accountingType)},getdesc:function(t){this.desc=t},getSpanData:function(t){var e=this,i=0;e.spanData=[],t.forEach((function(a,n){0===n?(e.spanData.push(1),i=0):t[n].salesChannel===t[n-1].salesChannel&&t[n].statTime===t[n-1].statTime?(e.spanData[i]+=1,e.spanData.push(0)):(e.spanData.push(1),i=n)}))},handleSpan:function(t){t.row,t.column;var e=t.rowIndex,i=t.columnIndex;if([0,1,8].includes(i)){var a=this.spanData[e],n=a>0?1:0;return{rowspan:a,colspan:n}}},handleChangeBeginMonth:function(t){this.searchBeginTime=t},handleChangeEndMonth:function(t){this.searchEndTime=t},handleChangeStatDate:function(t){this.searchStatDate=t},handleStatDate:function(t){this.addStatDate=t},getSupplierShortenCode:function(t,e){var i=this;Object($["c"])().then((function(t){if(!t||"0000"!=t.code)throw t;i.supplierShortenList=t.data})).catch((function(t){console.log(t)})).finally((function(){}))},changeType:function(t){var e=this;this.total=0,this.type=t,1===t&&(this.corpLists=this.onlineCorpList,this.isSearch=!0,this.data=[]),2===t&&(Object(d["e"])({type:1}).then((function(t){if(!t||"0000"!=t.code)throw t;var i=t.data.filter((function(t){return null!==t.companyName&&void 0!==t.companyName&&""!==t.companyName}));e.corpLists=i})).catch((function(t){})).finally((function(){})),this.$refs["form"].validate((function(t){t&&(e.isSearch=!0,e.data=[])}))),3===t&&(Object(d["e"])({types:[3,4,7,8]}).then((function(t){if(!t||"0000"!=t.code)throw t;e.corpLists=t.data})).catch((function(t){})).finally((function(){})),this.$refs["form"].validate((function(t){t&&(e.isSearch=!0,e.data=[])}))),4===t&&(J().then((function(t){if(!t||"0000"!=t.code)throw t;e.customerList=t.data})).catch((function(t){console.log(t)})).finally((function(){})),this.$refs["form"].validate((function(t){t&&(e.isSearch=!0,e.data=[])}))),5===t&&(this.getSupplierShortenCode(),this.$refs["form"].validate((function(t){t&&(e.isSearch=!0,e.data=[])}))),6===t&&this.$refs["form"].validate((function(t){t&&(e.isSearch=!0,e.data=[])}))},isArrayOrEmptyObject:function(t){return Array.isArray(t)&&0===t.length||"object"===Object(l["a"])(t)&&!Array.isArray(t)&&0===Object.keys(t).length},isDataToSendEmpty:function(t){return this.isArrayOrEmptyObject(t.createInvoiceVos)&&this.isArrayOrEmptyObject(t.inComeIds)},inputImsi:function(t){this.imsiOP.id=t.id,this.imsiModal=!0},imsiConfirm:function(){var t=this;this.$refs["imsiOP"].validate((function(e){if(e){t.imsiLoading=!0;var i=Object(c["a"])(Object(c["a"])({},t.imsiOP),{},{amount:Math.round(100*t.imsiOP.amount)});Object(k["n"])(i).then((function(e){"0000"===e.code&&(t.$Notice.success({title:"操作提示",desc:"操作成功！"}),t.imsiModal=!1,t.getTableData(1),t.cancel1())})).catch((function(t){})).finally((function(){t.imsiLoading=!1}))}}))},handleSelectionChange:function(t){var e=new Set(this.data.map((function(t){return t.id}))),i=this.selectedBlankCardRows.filter((function(t){return!e.has(t.id)}));this.selectedBlankCardRows=[].concat(Object(r["a"])(i),Object(r["a"])(t)),this.selectedBlankCardIds=new Set(this.selectedBlankCardRows.map((function(t){return t.id})))},handleSelectionCancel:function(t,e){this.selectedBlankCardRows=this.selectedBlankCardRows.filter((function(t){return t.id!==e.id})),this.selectedBlankCardIds.delete(e.id)},handleSelectAll:function(t){var e=new Set(this.data.map((function(t){return t.id}))),i=this.selectedBlankCardRows.filter((function(t){return!e.has(t.id)}));0===t.length?this.selectedBlankCardRows=i:this.selectedBlankCardRows=[].concat(Object(r["a"])(i),Object(r["a"])(t)),this.selectedBlankCardIds=new Set(this.selectedBlankCardRows.map((function(t){return t.id})))},batchDownloadBlankCardInvoice:function(){var t=this;if(0!==this.selectedBlankCardRows.length){var e=this,i=this.selectedBlankCardRows.map((function(t){return t.orderId}));this.$Modal.confirm({title:"确认批量下载Invoice？",content:"是否确认下载所选记录的Invoice？",onOk:function(){t.batchDownloadLoading=!0;var a=t.$store.state.user.userId;Object(L["d"])(i,a).then((function(i){if(t.batchDownloadLoading=!1,"0000"==i.code){t.exportModal=!0;var a=t;if(i.data&&i.data.taskId&&i.data.taskName&&(a.taskIds.push(i.data.taskId),a.taskNames.push(i.data.taskName),a.taskIds.length>3||a.taskNames.length>3)){var n=a.taskIds.slice(0,3),o=a.taskNames.slice(0,3);a.taskIds=n,a.taskNames=o,a.remind=!0}}else e.$Message.error(i.msg)})).catch((function(t){e.batchDownloadLoading=!1,e.$Message.error("下载失败")}))}})}else this.$Message.warning("请至少选择一条记录")},downloadInvoice:function(t){var e=this;return Object(s["a"])(Object(o["a"])().mark((function i(){return Object(o["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!e.downloadingMap[t.orderId]){i.next=2;break}return i.abrupt("return");case 2:return e.$set(e.downloadingMap,t.orderId,!0),i.prev=3,i.next=6,Object(z["h"])({id:t.orderId,type:"1"}).then((function(t){var e=t.data,i=decodeURIComponent(escape(t.headers["content-disposition"].match(/=(.*)$/)[1]));if("download"in document.createElement("a")){var a=document.createElement("a"),n=URL.createObjectURL(e);a.download=i,a.href=n,a.click(),URL.revokeObjectURL(n)}else navigator.msSaveBlob(e,i)})).catch((function(t){e.$Message.error("下载失败")}));case 6:return i.prev=6,e.$set(e.downloadingMap,t.orderId,!1),i.finish(6);case 9:case"end":return i.stop()}}),i,null,[[3,,6,9]])})))()}}},tt=X,et=(i("fdc5"),Object(v["a"])(tt,a,n,!1,null,"5d813a73",null));e["default"]=et.exports},e472:function(t,e,i){"use strict";i.d(e,"d",(function(){return s})),i.d(e,"a",(function(){return r})),i.d(e,"e",(function(){return c})),i.d(e,"c",(function(){return l})),i.d(e,"b",(function(){return d}));var a=i("66df"),n="/rms/api/v1",o="/pms",s=function(t){return a["a"].request({url:n+"/supplier/selectSupplier",params:t,method:"get"})},r=function(t){return a["a"].request({url:n+"/supplier/saveSupplier",data:t,method:"post"})},c=function(t){return a["a"].request({url:n+"/supplier/updateSupplier",data:t,method:"post"})},l=function(t){return a["a"].request({url:n+"/supplier/queryShorten",data:t,method:"get"})},d=function(t){return a["a"].request({url:o+"/pms-realname/getMccList",data:t,method:"get"})}},ea83:function(t,e,i){"use strict";var a=i("b5db"),n=a.match(/AppleWebKit\/(\d+)\./);t.exports=!!n&&+n[1]},fdc5:function(t,e,i){"use strict";i("d511")}}]);