"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SaleSoftware = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
class SaleSoftware {
    static getAttributeTypeMap() {
        return SaleSoftware.attributeTypeMap;
    }
}
exports.SaleSoftware = SaleSoftware;
SaleSoftware.discriminator = undefined;
SaleSoftware.attributeTypeMap = [
    {
        "name": "ApplicationName",
        "baseName": "ApplicationName",
        "type": "string"
    },
    {
        "name": "CertificationCode",
        "baseName": "CertificationCode",
        "type": "string"
    },
    {
        "name": "ManufacturerID",
        "baseName": "ManufacturerID",
        "type": "string"
    },
    {
        "name": "SoftwareVersion",
        "baseName": "SoftwareVersion",
        "type": "string"
    }
];
//# sourceMappingURL=saleSoftware.js.map