"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.VersionType = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var VersionType;
(function (VersionType) {
    VersionType[VersionType["V0"] = 'V0'] = "V0";
    VersionType[VersionType["V1"] = 'V1'] = "V1";
    VersionType[VersionType["V2"] = 'V2'] = "V2";
    VersionType[VersionType["V3"] = 'V3'] = "V3";
    VersionType[VersionType["V4"] = 'V4'] = "V4";
    VersionType[VersionType["V5"] = 'V5'] = "V5";
})(VersionType = exports.VersionType || (exports.VersionType = {}));
//# sourceMappingURL=versionType.js.map