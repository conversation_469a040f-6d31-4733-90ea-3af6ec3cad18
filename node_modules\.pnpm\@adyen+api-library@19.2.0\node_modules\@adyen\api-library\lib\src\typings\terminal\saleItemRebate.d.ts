/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
export declare class SaleItemRebate {
    'EanUpc'?: string;
    'ItemAmount'?: number;
    'ItemID': number;
    'ProductCode': string;
    'Quantity'?: number;
    'RebateLabel'?: string;
    'UnitOfMeasure'?: SaleItemRebate.UnitOfMeasureEnum;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace SaleItemRebate {
    enum UnitOfMeasureEnum {
        Case,
        Centilitre,
        Centimetre,
        Foot,
        Gram,
        Inch,
        Kilogram,
        Kilometre,
        Litre,
        Meter,
        <PERSON>,
        Other,
        <PERSON><PERSON><PERSON>,
        <PERSON>nt,
        <PERSON>,
        Quart,
        UkGallon,
        UsGallon,
        Yard
    }
}
