/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { MessageReference } from './messageReference';
export declare class TransactionStatusRequest {
    'DocumentQualifier'?: Array<TransactionStatusRequest.DocumentQualifierEnum>;
    'MessageReference'?: MessageReference;
    'ReceiptReprintFlag'?: boolean;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace TransactionStatusRequest {
    enum DocumentQualifierEnum {
        CashierReceipt,
        CustomerReceipt,
        Document,
        Journal,
        SaleReceipt,
        Voucher
    }
}
