<template>
  <!-- 库存管理 -->
  <ContentWrap>
    <!-- 加载状态 -->
    <div v-if="!isComponentMounted" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 主要内容 -->
    <el-card v-else>
      <div style="width: 100%; margin-top: 20px">
        <div class="search-form">
          <el-form :model="searchForm" inline>
            <el-form-item label="订单号：">
              <el-input
                v-model="searchForm.taskName"
                placeholder="请输入订单号"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="时间段：">
              <el-date-picker
                v-model="searchForm.timeSlot"
                type="daterange"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                v-if="hasPermission('search')"
                type="primary"
                :loading="searchLoading"
                @click="handleSearch"
              >
                <Icon icon="ep:search" class="mr-5px" />
                搜索
              </el-button>
              <el-button v-if="hasPermission('showiccid')" type="warning" @click="showICCID">
                显示ICCID
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格 -->
        <div style="margin-top: 20px" v-if="isComponentMounted">
          <el-table
            :data="tableData"
            v-loading="loading"
            border
            empty-text="暂无数据"
            :default-sort="{ prop: 'createTime', order: 'descending' }"
          >
            <el-table-column prop="taskName" label="任务名称" min-width="150">
              <template #default="{ row }">
                <strong>{{ row?.taskName || '-' }}</strong>
              </template>
            </el-table-column>
            <el-table-column prop="taskNum" label="任务编号" min-width="150">
              <template #default="{ row }">
                <strong>{{ row?.taskNum || '-' }}</strong>
              </template>
            </el-table-column>
            <el-table-column prop="totalCount" label="总数量" min-width="100" align="center">
              <template #default="{ row }">
                {{ row?.totalCount || 0 }}
              </template>
            </el-table-column>
            <el-table-column prop="successCount" label="成功数量" min-width="100" align="center">
              <template #default="{ row }">
                {{ row?.successCount || 0 }}
              </template>
            </el-table-column>
            <el-table-column prop="failCount" label="失败数量" min-width="100" align="center">
              <template #default="{ row }">
                {{ row?.failCount || 0 }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" min-width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row?.status || '0')" v-if="row">
                  {{ getStatusText(row?.status || '0') }}
                </el-tag>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" min-width="160">
              <template #default="{ row }">
                <strong>{{ row?.createTime || '-' }}</strong>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="120" align="center" fixed="right">
              <template #default="{ row }">
                <el-button
                  v-if="hasPermission('view') && row && row.id"
                  type="warning"
                  size="small"
                  @click="viewDetails(row)"
                >
                  详情
                </el-button>
                <span v-else-if="!row">-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div style="margin-top: 20px; text-align: right">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { useRouter } from 'vue-router'

// 权限检查函数 - 临时返回true以确保页面正常显示
const hasPermission = (permission: string): boolean => {
  console.log(`🔍 [库存管理权限检查] ${permission}: 允许访问`)
  return true // 临时返回true，后续需要实现真实的权限检查逻辑
}

const router = useRouter()

// 组件状态管理
const isComponentMounted = ref(false)
const isComponentDestroyed = ref(false)

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const searchForm = reactive({
  taskName: '',
  timeSlot: [] as string[]
})

const tableData = ref<any[]>([])

// 方法
const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '1': 'success',
    '2': 'warning',
    '3': 'danger',
    '0': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '1': '已完成',
    '2': '进行中',
    '3': '失败',
    '0': '待处理'
  }
  return statusMap[status] || '未知'
}

const handleSearch = () => {
  if (isComponentDestroyed.value) {
    console.warn('🚫 [库存管理] 组件已销毁，取消搜索操作')
    return
  }

  console.log('🔍 [库存管理] 执行搜索操作')
  currentPage.value = 1
  getTableData()
}

const handlePageChange = (page: number) => {
  if (isComponentDestroyed.value) {
    console.warn('🚫 [库存管理] 组件已销毁，取消分页操作')
    return
  }

  console.log('📄 [库存管理] 切换到第', page, '页')
  currentPage.value = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  if (isComponentDestroyed.value) {
    console.warn('🚫 [库存管理] 组件已销毁，取消页面大小变更')
    return
  }

  console.log('📏 [库存管理] 页面大小变更为', size)
  pageSize.value = size
  currentPage.value = 1
  getTableData()
}

const viewDetails = (row: any) => {
  // 防御性检查
  if (isComponentDestroyed.value) {
    console.warn('🚫 [库存管理] 组件已销毁，取消路由跳转')
    return
  }

  if (!row || !row.id) {
    ElMessage.warning('无效的任务数据')
    return
  }

  try {
    console.log('🔗 [库存管理] 跳转到库存详情页面:', row.taskName)
    // 跳转到库存详情页面
    router.push({
      path: '/newcmi/channel/stock/card-list',
      query: {
        taskId: row.id,
        taskName: row.taskName
      }
    })
  } catch (error) {
    console.error('❌ [库存管理] 路由跳转失败:', error)
    ElMessage.error('页面跳转失败')
  }
}

const showICCID = () => {
  // 防御性检查
  if (isComponentDestroyed.value) {
    console.warn('🚫 [库存管理] 组件已销毁，取消路由跳转')
    return
  }

  try {
    console.log('🔗 [库存管理] 跳转到ICCID显示页面')
    // 跳转到ICCID显示页面
    router.push({
      path: '/newcmi/channel/stock/show-iccid'
    })
  } catch (error) {
    console.error('❌ [库存管理] 路由跳转失败:', error)
    ElMessage.error('页面跳转失败')
  }
}

// 获取表格数据
const getTableData = async () => {
  // 防御性检查：确保组件未被销毁
  if (isComponentDestroyed.value) {
    console.warn('🚫 [库存管理] 组件已销毁，取消数据加载')
    return
  }

  try {
    loading.value = true
    console.log('📊 [库存管理] 开始加载表格数据')

    // 使用 nextTick 确保DOM已更新
    await nextTick()

    // 再次检查组件状态
    if (isComponentDestroyed.value) {
      console.warn('🚫 [库存管理] 组件在数据加载过程中被销毁')
      return
    }

    // TODO: 实现API调用
    // 模拟异步数据加载
    await new Promise((resolve) => setTimeout(resolve, 100))

    // 最终检查组件状态
    if (isComponentDestroyed.value) {
      console.warn('🚫 [库存管理] 组件在数据返回前被销毁')
      return
    }

    // 模拟数据
    const mockData = [
      {
        id: 1,
        taskName: '库存补充任务001',
        taskNum: 'TASK20240101001',
        totalCount: 1000,
        successCount: 950,
        failCount: 50,
        status: '1',
        createTime: '2024-01-01 10:00:00'
      },
      {
        id: 2,
        taskName: '库存补充任务002',
        taskNum: 'TASK20240101002',
        totalCount: 500,
        successCount: 300,
        failCount: 0,
        status: '2',
        createTime: '2024-01-01 11:00:00'
      }
    ]

    // 安全地更新数据
    if (!isComponentDestroyed.value) {
      tableData.value = mockData
      total.value = mockData.length
      console.log('✅ [库存管理] 表格数据加载完成')
    }
  } catch (error) {
    console.error('❌ [库存管理] 数据加载失败:', error)
    if (!isComponentDestroyed.value) {
      ElMessage.error('获取库存数据失败')
    }
  } finally {
    if (!isComponentDestroyed.value) {
      loading.value = false
    }
  }
}

// 生命周期
onMounted(async () => {
  console.log('🎉 [库存管理] 组件开始挂载')

  try {
    // 标记组件已挂载
    isComponentMounted.value = true
    isComponentDestroyed.value = false

    // 等待DOM完全渲染
    await nextTick()

    console.log('✅ [库存管理] 组件挂载完成，开始加载数据')

    // 延迟一小段时间确保所有DOM元素都已准备好
    setTimeout(() => {
      if (!isComponentDestroyed.value) {
        getTableData()
      }
    }, 50)
  } catch (error) {
    console.error('❌ [库存管理] 组件挂载失败:', error)
  }
})

onUnmounted(() => {
  console.log('🔄 [库存管理] 组件开始卸载')

  // 标记组件已销毁
  isComponentDestroyed.value = true
  isComponentMounted.value = false

  // 清理加载状态
  loading.value = false
  searchLoading.value = false

  // 清理数据
  tableData.value = []

  console.log('✅ [库存管理] 组件卸载完成')
})
</script>

<style scoped>
.loading-container {
  padding: 20px;
}

.search-form {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 确保表格在加载时不会闪烁 */
:deep(.el-table) {
  min-height: 200px;
}
</style>
