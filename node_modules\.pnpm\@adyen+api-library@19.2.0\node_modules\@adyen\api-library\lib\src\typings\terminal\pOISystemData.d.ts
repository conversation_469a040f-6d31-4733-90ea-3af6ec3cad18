/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { POISoftware } from './pOISoftware';
import { POIStatus } from './pOIStatus';
import { POITerminalData } from './pOITerminalData';
export declare class POISystemData {
    'DateTime': {
        [key: string]: any;
    };
    'POISoftware': POISoftware;
    'POIStatus'?: POIStatus;
    'POITerminalData'?: POITerminalData;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
