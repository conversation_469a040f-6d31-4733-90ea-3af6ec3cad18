<template>
  <div class="cmi-home">
    <div class="home-header">
      <h1>CMI 管理系统首页</h1>
      <p>欢迎使用 CMI 全球卡合作伙伴平台</p>
    </div>

    <div class="dashboard-grid">
      <!-- 统计卡片 -->
      <div class="stats-cards">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon">
                  <Icon icon="vi-ep:user" size="40" color="#409EFF" />
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ userStats.total }}</div>
                  <div class="stat-label">总用户数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon">
                  <Icon icon="vi-ep:phone" size="40" color="#67C23A" />
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ cardStats.active }}</div>
                  <div class="stat-label">活跃卡片</div>
                </div>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon">
                  <Icon icon="vi-ep:money" size="40" color="#E6A23C" />
                </div>
                <div class="stat-info">
                  <div class="stat-number">¥{{ revenueStats.total }}</div>
                  <div class="stat-label">总收入</div>
                </div>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon">
                  <Icon icon="vi-ep:warning" size="40" color="#F56C6C" />
                </div>
                <div class="stat-info">
                  <div class="stat-number">{{ alertStats.count }}</div>
                  <div class="stat-label">待处理告警</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 图表区域 -->
      <div class="charts-section">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>用户增长趋势</span>
                </div>
              </template>
              <div class="chart-container" ref="userChartRef"></div>
            </el-card>
          </el-col>
          
          <el-col :span="12">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>收入统计</span>
                </div>
              </template>
              <div class="chart-container" ref="revenueChartRef"></div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 快捷操作 -->
      <div class="quick-actions">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>快捷操作</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="4" v-for="action in quickActions" :key="action.name">
              <div class="action-item" @click="handleQuickAction(action)">
                <div class="action-icon">
                  <Icon :icon="action.icon" size="32" :color="action.color" />
                </div>
                <div class="action-name">{{ action.name }}</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 最近活动 -->
      <div class="recent-activities">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
              <el-button type="text" @click="viewAllActivities">查看全部</el-button>
            </div>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="activity in recentActivities"
              :key="activity.id"
              :timestamp="activity.timestamp"
              :color="activity.color"
            >
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-description">{{ activity.description }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElCard, ElRow, ElCol, ElButton, ElTimeline, ElTimelineItem } from 'element-plus'
import { Icon } from '@/components/Icon'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'

const { push } = useRouter()

// 统计数据
const userStats = reactive({
  total: 12580
})

const cardStats = reactive({
  active: 8960
})

const revenueStats = reactive({
  total: '2,580,000'
})

const alertStats = reactive({
  count: 15
})

// 快捷操作
const quickActions = ref([
  {
    name: '用户管理',
    icon: 'vi-ep:user',
    color: '#409EFF',
    path: '/system/account'
  },
  {
    name: '卡片管理',
    icon: 'vi-ep:phone',
    color: '#67C23A',
    path: '/resource/iccid'
  },
  {
    name: '订单管理',
    icon: 'vi-ep:shopping-cart',
    color: '#E6A23C',
    path: '/order/list'
  },
  {
    name: '财务报表',
    icon: 'vi-ep:money',
    color: '#F56C6C',
    path: '/finance/report'
  },
  {
    name: '系统设置',
    icon: 'vi-ep:setting',
    color: '#909399',
    path: '/system/config'
  },
  {
    name: '日志查看',
    icon: 'vi-ep:document',
    color: '#606266',
    path: '/system/login-log'
  }
])

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '新用户注册',
    description: '用户 <EMAIL> 完成注册',
    timestamp: '2024-01-15 14:30',
    color: '#67C23A'
  },
  {
    id: 2,
    title: '订单支付',
    description: '订单 #12345 支付成功，金额 ¥1,200',
    timestamp: '2024-01-15 13:45',
    color: '#409EFF'
  },
  {
    id: 3,
    title: '系统告警',
    description: '服务器 CPU 使用率超过 80%',
    timestamp: '2024-01-15 12:20',
    color: '#F56C6C'
  },
  {
    id: 4,
    title: '数据备份',
    description: '每日数据备份任务执行完成',
    timestamp: '2024-01-15 02:00',
    color: '#909399'
  }
])

// 图表引用
const userChartRef = ref<HTMLElement>()
const revenueChartRef = ref<HTMLElement>()

// 快捷操作处理
const handleQuickAction = (action: any) => {
  push(action.path)
}

// 查看全部活动
const viewAllActivities = () => {
  push('/system/activity-log')
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 用户增长趋势图
    if (userChartRef.value) {
      const userChart = echarts.init(userChartRef.value)
      const userOption = {
        title: {
          text: '用户增长',
          left: 'center',
          textStyle: { fontSize: 14 }
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: [1200, 1800, 2400, 3200, 4100, 5000],
          type: 'line',
          smooth: true,
          itemStyle: { color: '#409EFF' }
        }]
      }
      userChart.setOption(userOption)
    }

    // 收入统计图
    if (revenueChartRef.value) {
      const revenueChart = echarts.init(revenueChartRef.value)
      const revenueOption = {
        title: {
          text: '月度收入',
          left: 'center',
          textStyle: { fontSize: 14 }
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: [320000, 450000, 380000, 520000, 680000, 750000],
          type: 'bar',
          itemStyle: { color: '#67C23A' }
        }]
      }
      revenueChart.setOption(revenueOption)
    }
  })
}

onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.cmi-home {
  padding: 20px;
}

.home-header {
  text-align: center;
  margin-bottom: 30px;
}

.home-header h1 {
  color: #303133;
  margin-bottom: 10px;
}

.home-header p {
  color: #606266;
  font-size: 16px;
}

.dashboard-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  margin-right: 20px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.chart-container {
  height: 300px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.3s;
}

.action-item:hover {
  background-color: #f5f7fa;
}

.action-icon {
  margin-bottom: 10px;
}

.action-name {
  font-size: 14px;
  color: #606266;
}

.activity-content {
  margin-left: 10px;
}

.activity-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.activity-description {
  color: #606266;
  font-size: 14px;
}
</style>
