<template>
  <div class="cmi-dashboard">
    <ContentWrap>
      <!-- 页面标题 -->
      <div class="dashboard-header">
        <h1>CMI 业务运营仪表板</h1>
        <p>渠道客户业务运营数据概览 - 实时监控您的业务状况</p>
        <div class="last-update">
          最后更新时间: {{ lastUpdateTime }}
          <el-button type="text" size="small" @click="refreshData" :loading="refreshing">
            <Icon icon="vi-ep:refresh" class="mr-5px" />
            刷新数据
          </el-button>
        </div>
      </div>

      <!-- 账户信息面板 -->
      <div class="account-panel">
        <h2 class="panel-title">
          <Icon icon="vi-ep:wallet" class="mr-10px" />
          账户信息
        </h2>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
            <div class="info-card balance-card">
              <div class="card-header">
                <span class="card-title">账户余额</span>
                <el-tag :type="balanceStatus.type" size="small">{{ balanceStatus.text }}</el-tag>
              </div>
              <div class="card-value">
                <span class="currency">¥</span>
                <span class="amount">{{ formatCurrency(accountInfo.balance) }}</span>
              </div>
              <div class="card-trend">
                <div class="trend-chart" ref="balanceTrendRef"></div>
                <span class="trend-text">7天趋势</span>
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
            <div class="info-card credit-card">
              <div class="card-header">
                <span class="card-title">信用额度使用</span>
                <span class="usage-ratio">{{ creditUsageRatio }}%</span>
              </div>
              <div class="credit-progress">
                <el-progress
                  :percentage="creditUsageRatio"
                  :color="getCreditProgressColor(creditUsageRatio)"
                  :stroke-width="12"
                />
              </div>
              <div class="credit-details">
                <span>已用: ¥{{ formatCurrency(accountInfo.usedCredit) }}</span>
                <span>总额: ¥{{ formatCurrency(accountInfo.totalCredit) }}</span>
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
            <div class="info-card alert-card">
              <div class="card-header">
                <span class="card-title">信用预警</span>
                <el-badge :value="creditAlerts.length" :hidden="creditAlerts.length === 0">
                  <Icon icon="vi-ep:warning" size="20" />
                </el-badge>
              </div>
              <div class="alert-list">
                <div v-if="creditAlerts.length === 0" class="no-alerts">
                  <Icon icon="vi-ep:check" color="#67C23A" size="24" />
                  <span>暂无预警</span>
                </div>
                <div v-else v-for="alert in creditAlerts" :key="alert.id" class="alert-item">
                  <Icon :icon="alert.icon" :color="alert.color" size="16" />
                  <span>{{ alert.message }}</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 订单信息面板 -->
      <div class="order-panel">
        <h2 class="panel-title">
          <Icon icon="vi-ep:shopping-cart" class="mr-10px" />
          订单信息
        </h2>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
            <div class="chart-card">
              <div class="chart-header">
                <h3>近12个月订单统计</h3>
                <div class="chart-controls">
                  <el-radio-group v-model="orderChartType" size="small">
                    <el-radio-button label="count">订单数量</el-radio-button>
                    <el-radio-button label="amount">订单金额</el-radio-button>
                  </el-radio-group>
                </div>
              </div>
              <div class="chart-content">
                <div ref="orderTrendChartRef" style="width: 100%; height: 350px;"></div>
              </div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
            <div class="chart-card">
              <div class="chart-header">
                <h3>订单类型分布</h3>
              </div>
              <div class="chart-content">
                <div ref="orderTypeChartRef" style="width: 100%; height: 350px;"></div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 最新订单状态 -->
        <div class="recent-orders">
          <div class="section-header">
            <h3>最新订单状态</h3>
            <el-button type="text" size="small" @click="viewAllOrders">查看全部订单</el-button>
          </div>
          <el-table :data="recentOrders" style="width: 100%" size="small">
            <el-table-column prop="orderNo" label="订单号" width="180" />
            <el-table-column prop="orderType" label="订单类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getOrderTypeTag(row.orderType)" size="small">
                  {{ row.orderType }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="amount" label="订单金额" width="120">
              <template #default="{ row }">
                ¥{{ formatCurrency(row.amount) }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getOrderStatusTag(row.status)" size="small">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="160" />
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button type="text" size="small" @click="viewOrderDetail(row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- SIM卡信息面板 -->
      <div class="sim-panel">
        <h2 class="panel-title">
          <Icon icon="vi-ep:phone" class="mr-10px" />
          SIM卡信息
        </h2>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
            <div class="info-card sim-total-card">
              <div class="card-header">
                <span class="card-title">SIM卡总数</span>
              </div>
              <div class="card-value">
                <span class="amount">{{ formatNumber(simInfo.totalCards) }}</span>
              </div>
              <div class="card-subtitle">实时更新</div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
            <div class="info-card activation-card">
              <div class="card-header">
                <span class="card-title">激活率</span>
              </div>
              <div class="activation-gauge">
                <div ref="activationGaugeRef" style="width: 100%; height: 120px;"></div>
              </div>
              <div class="card-subtitle">目标: ≥90%</div>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <div class="chart-card">
              <div class="chart-header">
                <h3>流量使用TOP3国家</h3>
              </div>
              <div class="chart-content">
                <div ref="trafficHeatMapRef" style="width: 100%; height: 200px;"></div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 套餐分布 -->
        <div class="package-distribution">
          <div class="section-header">
            <h3>套餐分布统计</h3>
            <span class="update-info">每日更新</span>
          </div>
          <div ref="packageDistributionRef" style="width: 100%; height: 300px;"></div>
        </div>
      </div>

      <!-- 公告面板 -->
      <div class="announcement-panel">
        <h2 class="panel-title">
          <Icon icon="vi-ep:bell" class="mr-10px" />
          系统公告
        </h2>
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
            <div class="announcement-carousel">
              <div class="carousel-header">
                <h3>最新公告</h3>
                <span class="announcement-count">共 {{ announcements.length }} 条</span>
              </div>
              <el-carousel height="200px" direction="vertical" :autoplay="true" :interval="5000">
                <el-carousel-item v-for="announcement in announcements" :key="announcement.id">
                  <div class="announcement-item">
                    <div class="announcement-title">{{ announcement.title }}</div>
                    <div class="announcement-content">{{ announcement.content }}</div>
                    <div class="announcement-time">{{ announcement.publishTime }}</div>
                  </div>
                </el-carousel-item>
              </el-carousel>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
            <div class="announcement-search">
              <div class="search-header">
                <h3>历史公告查询</h3>
              </div>
              <div class="search-form">
                <el-input
                  v-model="announcementSearch.keyword"
                  placeholder="输入关键词搜索"
                  clearable
                  class="search-input"
                />
                <el-date-picker
                  v-model="announcementSearch.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  size="small"
                  class="date-picker"
                />
                <el-button type="primary" @click="searchAnnouncements" class="search-btn">
                  搜索
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { ElMessage, ElRadioGroup, ElRadioButton, ElProgress, ElBadge, ElTable, ElTableColumn, ElTag, ElCarousel, ElCarouselItem, ElInput, ElDatePicker } from 'element-plus'
import { Icon } from '@/components/Icon'
import { ContentWrap } from '@/components/ContentWrap'
import { useRouter } from 'vue-router'
import * as echarts from 'echarts'

const router = useRouter()

// 响应式数据
const refreshing = ref(false)
const lastUpdateTime = ref(new Date().toLocaleString())
const orderChartType = ref('count')

// 账户信息
const accountInfo = reactive({
  balance: 125680.50,
  usedCredit: 45000,
  totalCredit: 100000
})

// 计算信用额度使用率
const creditUsageRatio = computed(() => {
  return Math.round((accountInfo.usedCredit / accountInfo.totalCredit) * 100)
})

// 余额状态
const balanceStatus = computed(() => {
  if (accountInfo.balance < accountInfo.totalCredit * 0.1) {
    return { type: 'danger', text: '余额不足' }
  } else if (accountInfo.balance < accountInfo.totalCredit * 0.3) {
    return { type: 'warning', text: '余额偏低' }
  } else {
    return { type: 'success', text: '余额充足' }
  }
})

// 信用预警
const creditAlerts = ref([
  {
    id: 1,
    icon: 'vi-ep:warning',
    color: '#F56C6C',
    message: '信用额度使用已超过40%'
  }
])

// SIM卡信息
const simInfo = reactive({
  totalCards: 15680,
  activationRate: 92.5
})

// 最新订单
const recentOrders = ref([
  {
    orderNo: 'ORD202407300001',
    orderType: '白卡订购',
    amount: 5000,
    status: '已完成',
    createTime: '2024-07-30 14:30:00'
  },
  {
    orderNo: 'ORD202407300002',
    orderType: '流量充值',
    amount: 1200,
    status: '处理中',
    createTime: '2024-07-30 13:45:00'
  },
  {
    orderNo: 'ORD202407300003',
    orderType: '白卡订购',
    amount: 8000,
    status: '已完成',
    createTime: '2024-07-30 12:20:00'
  },
  {
    orderNo: 'ORD202407300004',
    orderType: '套餐变更',
    amount: 300,
    status: '已取消',
    createTime: '2024-07-30 11:15:00'
  },
  {
    orderNo: 'ORD202407300005',
    orderType: '流量充值',
    amount: 2500,
    status: '已完成',
    createTime: '2024-07-30 10:30:00'
  }
])

// 公告数据
const announcements = ref([
  {
    id: 1,
    title: '系统维护通知',
    content: '系统将于本周六凌晨2:00-4:00进行例行维护，期间可能影响部分功能使用。',
    publishTime: '2024-07-30 09:00:00'
  },
  {
    id: 2,
    title: '新功能上线',
    content: 'SIM卡批量管理功能已上线，支持批量激活、停用等操作。',
    publishTime: '2024-07-29 16:30:00'
  },
  {
    id: 3,
    title: '费率调整通知',
    content: '部分国家和地区的流量费率将于下月1日起调整，详情请查看费率表。',
    publishTime: '2024-07-28 14:20:00'
  }
])

// 公告搜索
const announcementSearch = reactive({
  keyword: '',
  dateRange: null
})

// 图表引用
const balanceTrendRef = ref<HTMLDivElement>()
const orderTrendChartRef = ref<HTMLDivElement>()
const orderTypeChartRef = ref<HTMLDivElement>()
const activationGaugeRef = ref<HTMLDivElement>()
const trafficHeatMapRef = ref<HTMLDivElement>()
const packageDistributionRef = ref<HTMLDivElement>()

// 图表实例存储
const chartInstances: echarts.ECharts[] = []

// 工具函数
const formatCurrency = (amount: number) => {
  return amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

const formatNumber = (num: number) => {
  return num.toLocaleString('zh-CN')
}

const getCreditProgressColor = (percentage: number) => {
  if (percentage < 50) return '#67C23A'
  if (percentage < 80) return '#E6A23C'
  return '#F56C6C'
}

const getOrderTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    '白卡订购': 'primary',
    '流量充值': 'success',
    '套餐变更': 'warning'
  }
  return tagMap[type] || 'info'
}

const getOrderStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    '已完成': 'success',
    '处理中': 'warning',
    '已取消': 'danger',
    '待支付': 'info'
  }
  return tagMap[status] || 'info'
}

// 方法
const refreshData = async () => {
  refreshing.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    lastUpdateTime.value = new Date().toLocaleString()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    refreshing.value = false
  }
}

const viewAllOrders = () => {
  router.push('/order/list')
}

const viewOrderDetail = (row: any) => {
  router.push(`/order/detail/${row.orderNo}`)
}

const searchAnnouncements = () => {
  ElMessage.info('搜索功能开发中...')
}

// 图表初始化
const initBalanceTrendChart = () => {
  try {
    if (!balanceTrendRef.value) {
      console.warn('⚠️ [余额趋势图] DOM元素未找到')
      return
    }

    console.log('🎨 [余额趋势图] 开始初始化')
    const chart = echarts.init(balanceTrendRef.value)
    chartInstances.push(chart) // 存储图表实例
    const option = {
      grid: { top: 5, right: 5, bottom: 5, left: 5 },
      xAxis: { type: 'category', show: false, data: ['1', '2', '3', '4', '5', '6', '7'] },
      yAxis: { type: 'value', show: false },
      series: [{
        data: [120000, 125000, 123000, 128000, 126000, 124000, 125680],
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: { color: '#409EFF', width: 2 },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ])
        }
      }]
    }
    chart.setOption(option)
    console.log('✅ [余额趋势图] 初始化完成')
  } catch (error) {
    console.error('❌ [余额趋势图] 初始化失败:', error)
  }
}

const initOrderTrendChart = () => {
  try {
    if (!orderTrendChartRef.value) {
      console.warn('⚠️ [订单趋势图] DOM元素未找到')
      return
    }

    console.log('🎨 [订单趋势图] 开始初始化')
    const chart = echarts.init(orderTrendChartRef.value)
    chartInstances.push(chart)
    const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    const countData = [120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330]
    const amountData = [1200000, 1320000, 1010000, 1340000, 900000, 2300000, 2100000, 1820000, 1910000, 2340000, 2900000, 3300000]

    const option = {
      tooltip: { trigger: 'axis' },
      legend: { data: ['订单数量', '订单金额'] },
      xAxis: { type: 'category', data: months },
      yAxis: [
        { type: 'value', name: '数量', position: 'left' },
        { type: 'value', name: '金额(万元)', position: 'right' }
      ],
      series: [
        {
          name: '订单数量',
          type: 'bar',
          data: countData,
          itemStyle: { color: '#409EFF' }
        },
        {
          name: '订单金额',
          type: 'line',
          yAxisIndex: 1,
          data: amountData.map(v => v / 10000),
          itemStyle: { color: '#67C23A' }
        }
      ]
    }
    chart.setOption(option)
    console.log('✅ [订单趋势图] 初始化完成')
  } catch (error) {
    console.error('❌ [订单趋势图] 初始化失败:', error)
  }
}

const initOrderTypeChart = () => {
  try {
    if (!orderTypeChartRef.value) {
      console.warn('⚠️ [订单类型图] DOM元素未找到')
      return
    }

    console.log('🎨 [订单类型图] 开始初始化')
    const chart = echarts.init(orderTypeChartRef.value)
    chartInstances.push(chart)
    chartInstances.push(chart)
    const option = {
      tooltip: { trigger: 'item' },
      legend: { orient: 'vertical', left: 'left' },
      series: [{
        name: '订单类型',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: '白卡订购' },
          { value: 735, name: '流量充值' },
          { value: 580, name: '套餐变更' },
          { value: 484, name: '其他' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    chart.setOption(option)
    console.log('✅ [订单类型图] 初始化完成')
  } catch (error) {
    console.error('❌ [订单类型图] 初始化失败:', error)
  }
}

const initActivationGauge = () => {
  try {
    if (!activationGaugeRef.value) {
      console.warn('⚠️ [激活率仪表盘] DOM元素未找到')
      return
    }

    console.log('🎨 [激活率仪表盘] 开始初始化')
    const chart = echarts.init(activationGaugeRef.value)
    chartInstances.push(chart)
    const option = {
      series: [{
        name: '激活率',
        type: 'gauge',
        startAngle: 180,
        endAngle: 0,
        min: 0,
        max: 100,
        splitNumber: 10,
        itemStyle: {
          color: '#58D9F9',
          shadowColor: 'rgba(0,138,255,0.45)',
          shadowBlur: 10,
          shadowOffsetX: 2,
          shadowOffsetY: 2
        },
        progress: { show: true, roundCap: true, width: 8 },
        pointer: { show: false },
        axisLine: { roundCap: true, lineStyle: { width: 8 } },
        axisTick: { show: false },
        splitLine: { show: false },
        axisLabel: { show: false },
        title: { show: false },
        detail: {
          backgroundColor: '#fff',
          borderColor: '#999',
          borderWidth: 2,
          width: '60%',
          lineHeight: 40,
          height: 40,
          borderRadius: 8,
          offsetCenter: [0, '-15%'],
          valueAnimation: true,
          formatter: function (value: number) {
            return '{value|' + value.toFixed(1) + '}{unit|%}'
          },
          rich: {
            value: { fontSize: 20, fontWeight: 'bolder', color: '#777' },
            unit: { fontSize: 12, color: '#999', padding: [0, 0, -20, 10] }
          }
        },
        data: [{ value: simInfo.activationRate }]
      }]
    }
    chart.setOption(option)
    console.log('✅ [激活率仪表盘] 初始化完成')
  } catch (error) {
    console.error('❌ [激活率仪表盘] 初始化失败:', error)
  }
}

const initTrafficHeatMap = () => {
  try {
    if (!trafficHeatMapRef.value) {
      console.warn('⚠️ [流量热力图] DOM元素未找到')
      return
    }

    console.log('🎨 [流量热力图] 开始初始化')
    const chart = echarts.init(trafficHeatMapRef.value)
    chartInstances.push(chart)
    const option = {
      tooltip: { trigger: 'item' },
      series: [{
        name: '流量使用',
        type: 'treemap',
        data: [
          { name: '美国', value: 1200, itemStyle: { color: '#409EFF' } },
          { name: '英国', value: 800, itemStyle: { color: '#67C23A' } },
          { name: '德国', value: 600, itemStyle: { color: '#E6A23C' } }
        ]
      }]
    }
    chart.setOption(option)
    console.log('✅ [流量热力图] 初始化完成')
  } catch (error) {
    console.error('❌ [流量热力图] 初始化失败:', error)
  }
}

const initPackageDistribution = () => {
  try {
    if (!packageDistributionRef.value) {
      console.warn('⚠️ [套餐分布图] DOM元素未找到')
      return
    }

    console.log('🎨 [套餐分布图] 开始初始化')
    const chart = echarts.init(packageDistributionRef.value)
    chartInstances.push(chart)
    const option = {
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      legend: { data: ['5GB套餐', '10GB套餐', '20GB套餐', '无限套餐'] },
      xAxis: { type: 'category', data: ['1月', '2月', '3月', '4月', '5月', '6月'] },
      yAxis: { type: 'value' },
      series: [
        {
          name: '5GB套餐',
          type: 'bar',
          stack: 'total',
          data: [320, 302, 301, 334, 390, 330],
          itemStyle: { color: '#409EFF' }
        },
        {
          name: '10GB套餐',
          type: 'bar',
          stack: 'total',
          data: [120, 132, 101, 134, 90, 230],
          itemStyle: { color: '#67C23A' }
        },
        {
          name: '20GB套餐',
          type: 'bar',
          stack: 'total',
          data: [220, 182, 191, 234, 290, 330],
          itemStyle: { color: '#E6A23C' }
        },
        {
          name: '无限套餐',
          type: 'bar',
          stack: 'total',
          data: [150, 212, 201, 154, 190, 330],
          itemStyle: { color: '#F56C6C' }
        }
      ]
    }
    chart.setOption(option)
    console.log('✅ [套餐分布图] 初始化完成')
  } catch (error) {
    console.error('❌ [套餐分布图] 初始化失败:', error)
  }
}

// 窗口resize处理
const handleResize = () => {
  chartInstances.forEach(chart => {
    if (chart && !chart.isDisposed()) {
      chart.resize()
    }
  })
}

// 生命周期
onMounted(async () => {
  await nextTick()

  // 延迟初始化图表，确保DOM完全渲染
  setTimeout(() => {
    try {
      console.log('🎨 [首页图表] 开始初始化图表')
      initBalanceTrendChart()
      initOrderTrendChart()
      initOrderTypeChart()
      initActivationGauge()
      initTrafficHeatMap()
      initPackageDistribution()
      console.log('✅ [首页图表] 图表初始化完成')

      // 添加窗口resize监听
      window.addEventListener('resize', handleResize)
    } catch (error) {
      console.error('❌ [首页图表] 图表初始化失败:', error)
    }
  }, 100)
})

onUnmounted(() => {
  // 移除resize监听
  window.removeEventListener('resize', handleResize)

  // 销毁所有图表实例
  chartInstances.forEach(chart => {
    if (chart && !chart.isDisposed()) {
      chart.dispose()
    }
  })
  chartInstances.length = 0
})
</script>

<style lang="less" scoped>
.cmi-dashboard {
  .dashboard-header {
    margin-bottom: 24px;

    h1 {
      font-size: 28px;
      font-weight: 600;
      color: #1f2937;
      margin: 0 0 8px 0;
    }

    p {
      font-size: 16px;
      color: #6b7280;
      margin: 0 0 16px 0;
    }

    .last-update {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 14px;
      color: #9ca3af;
    }
  }

  .panel-title {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 20px 0;
  }

  .account-panel,
  .order-panel,
  .sim-panel,
  .announcement-panel {
    margin-bottom: 32px;
  }

  .info-card {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    height: 100%;

    .card-header {
      display: flex;
      justify-content: between;
      align-items: center;
      margin-bottom: 16px;

      .card-title {
        font-size: 14px;
        color: #6b7280;
        font-weight: 500;
      }

      .usage-ratio {
        font-size: 14px;
        font-weight: 600;
        color: #1f2937;
      }
    }

    .card-value {
      display: flex;
      align-items: baseline;
      margin-bottom: 12px;

      .currency {
        font-size: 18px;
        color: #6b7280;
        margin-right: 4px;
      }

      .amount {
        font-size: 32px;
        font-weight: 700;
        color: #1f2937;
      }
    }

    .card-trend {
      display: flex;
      align-items: center;
      gap: 8px;

      .trend-chart {
        flex: 1;
        height: 40px;
      }

      .trend-text {
        font-size: 12px;
        color: #9ca3af;
      }
    }

    .card-subtitle {
      font-size: 12px;
      color: #9ca3af;
      text-align: center;
    }

    .credit-progress {
      margin-bottom: 12px;
    }

    .credit-details {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #6b7280;
    }

    .alert-list {
      .no-alerts {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        padding: 20px;
        color: #6b7280;
        font-size: 14px;
      }

      .alert-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 0;
        font-size: 12px;
        color: #6b7280;

        &:not(:last-child) {
          border-bottom: 1px solid #f3f4f6;
        }
      }
    }
  }

  .chart-card {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    height: 100%;

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }

      .chart-controls {
        display: flex;
        gap: 8px;
      }
    }

    .chart-content {
      width: 100%;
    }
  }

  .recent-orders,
  .package-distribution {
    margin-top: 24px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }

      .update-info {
        font-size: 12px;
        color: #9ca3af;
      }
    }
  }

  .announcement-carousel,
  .announcement-search {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    height: 100%;

    .carousel-header,
    .search-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }

      .announcement-count {
        font-size: 12px;
        color: #9ca3af;
      }
    }

    .announcement-item {
      padding: 16px;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .announcement-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 8px;
      }

      .announcement-content {
        font-size: 14px;
        color: #6b7280;
        line-height: 1.5;
        margin-bottom: 12px;
      }

      .announcement-time {
        font-size: 12px;
        color: #9ca3af;
      }
    }

    .search-form {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .search-input,
      .date-picker {
        width: 100%;
      }

      .search-btn {
        width: 100%;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .cmi-dashboard {
    .dashboard-header {
      h1 {
        font-size: 24px;
      }

      .last-update {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }

    .info-card {
      margin-bottom: 16px;
    }

    .chart-card {
      margin-bottom: 16px;

      .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }
    }
  }
}
</style>