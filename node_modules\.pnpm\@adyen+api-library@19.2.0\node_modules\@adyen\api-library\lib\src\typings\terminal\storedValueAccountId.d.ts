/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { IdentificationType } from './identificationType';
import { StoredValueAccountType } from './storedValueAccountType';
export declare class StoredValueAccountId {
    'EntryMode': Array<StoredValueAccountId.EntryModeEnum>;
    'ExpiryDate'?: string;
    'IdentificationType': IdentificationType;
    'OwnerName'?: string;
    'StoredValueAccountType': StoredValueAccountType;
    'StoredValueProvider'?: string;
    'Value'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace StoredValueAccountId {
    enum EntryModeEnum {
        Contactless,
        File,
        Icc,
        Keyed,
        MagStripe,
        Manual,
        Mobile,
        Rfid,
        Scanned,
        SynchronousIcc,
        Tapped
    }
}
