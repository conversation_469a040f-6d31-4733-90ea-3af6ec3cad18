(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6c5d0770"],{"11f0":function(e,t,a){"use strict";a("268f")},"268f":function(e,t,a){},"2a0d":function(e,t,a){"use strict";a.d(t,"i",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"j",(function(){return l})),a.d(t,"b",(function(){return s})),a.d(t,"c",(function(){return c})),a.d(t,"g",(function(){return u})),a.d(t,"d",(function(){return p})),a.d(t,"h",(function(){return d})),a.d(t,"f",(function(){return g})),a.d(t,"e",(function(){return f}));var r=a("66df"),i="/cms",n=function(e){return r["a"].request({url:i+"/cooperation",params:e,method:"get"})},o=function(e){return r["a"].request({url:i+"/cooperation",data:e,method:"post"})},l=function(e){return r["a"].request({url:i+"/cooperation/updateCooperation",data:e,method:"post"})},s=function(e){return r["a"].request({url:i+"/cooperation",params:e,method:"put"})},c=function(e){return r["a"].request({url:i+"/cooperation",data:e,method:"delete"})},u=function(e){return r["a"].request({url:i+"/cooperation/detail",params:e,method:"get"})},p=function(e){return r["a"].request({url:i+"/cooperation/derive",params:e,responseType:"blob",method:"get"})},d=function(e){return r["a"].request({url:"/pms/api/v1/package/getList",data:e,method:"post"})},g=function(e){return r["a"].request({url:i+"/cooperation/getCooperationMoney",params:e,method:"get"})},f=function(e){return r["a"].request({url:"/oms/api/v1/country/queryCounrtyList",method:"get"})}},"30e3":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("Card",{staticStyle:{width:"100%",padding:"16px"}},[t("div",{staticStyle:{width:"80%",margin:"0 auto"}},[t("Form",{ref:"editObj",attrs:{model:e.editObj,"label-width":180,rules:e.ruleEditValidate}},[t("Row",[t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"终端厂商名称",prop:"corpName"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入终端厂商名称"},model:{value:e.editObj.corpName,callback:function(t){e.$set(e.editObj,"corpName",t)},expression:"editObj.corpName"}})],1)],1),t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"EBS Code",prop:"ebsCode"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入EBS Code"},model:{value:e.editObj.ebsCode,callback:function(t){e.$set(e.editObj,"ebsCode",t)},expression:"editObj.ebsCode"}})],1)],1),t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"通知URL",prop:"notifyUrl"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入URL"},model:{value:e.editObj.notifyUrl,callback:function(t){e.$set(e.editObj,"notifyUrl",t)},expression:"editObj.notifyUrl"}})],1)],1)],1),t("Row",[t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"APPkey&APPSecret生成方式",prop:"eopCreateType"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择生成方式",clearable:!0},model:{value:e.editObj.eopCreateType,callback:function(t){e.$set(e.editObj,"eopCreateType",t)},expression:"editObj.eopCreateType"}},e._l(e.appMethodList,(function(a){return t("Option",{key:a.value,attrs:{value:a.value}},[e._v(e._s(a.label))])})),1)],1)],1),"2"===e.editObj.eopCreateType?t("div",[t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"AppKey",prop:"appKey"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入AppKey"},model:{value:e.editObj.appKey,callback:function(t){e.$set(e.editObj,"appKey",t)},expression:"editObj.appKey"}})],1)],1),t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"APPSecret",prop:"appSecret"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入APPSecret"},model:{value:e.editObj.appSecret,callback:function(t){e.$set(e.editObj,"appSecret",t)},expression:"editObj.appSecret"}})],1)],1)],1):e._e()],1),t("Row",[t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"公司名称",prop:"companyName"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入公司名称"},model:{value:e.editObj.companyName,callback:function(t){e.$set(e.editObj,"companyName",t)},expression:"editObj.companyName"}})],1)],1),t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"地址",prop:"address"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入地址"},model:{value:e.editObj.address,callback:function(t){e.$set(e.editObj,"address",t)},expression:"editObj.address"}})],1)],1),t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"内部订单",prop:"internalOrder"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择是否内部订单",clearable:!0},model:{value:e.editObj.internalOrder,callback:function(t){e.$set(e.editObj,"internalOrder",t)},expression:"editObj.internalOrder"}},[t("Option",{attrs:{value:"0"}},[e._v("是")]),t("Option",{attrs:{value:"1"}},[e._v("否")])],1)],1)],1)],1),t("Row",[t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"币种",prop:"currencyCode"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择币种",clearable:!0},model:{value:e.editObj.currencyCode,callback:function(t){e.$set(e.editObj,"currencyCode",t)},expression:"editObj.currencyCode"}},[t("Option",{attrs:{value:"156"}},[e._v("人民币")]),t("Option",{attrs:{value:"344"}},[e._v("港币")]),t("Option",{attrs:{value:"840"}},[e._v("美元")])],1)],1)],1),t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"激活通知",prop:"activeNotifyType"}},[t("i-switch",{attrs:{size:"large"},model:{value:e.editObj.activeNotifyType,callback:function(t){e.$set(e.editObj,"activeNotifyType",t)},expression:"editObj.activeNotifyType"}},[t("span",{attrs:{slot:"open"},slot:"open"},[e._v("开")]),t("span",{attrs:{slot:"close"},slot:"close"},[e._v("关")])])],1)],1),t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"到期通知",prop:"dueNotifyType"}},[t("i-switch",{attrs:{size:"large"},model:{value:e.editObj.dueNotifyType,callback:function(t){e.$set(e.editObj,"dueNotifyType",t)},expression:"editObj.dueNotifyType"}},[t("span",{attrs:{slot:"open"},slot:"open"},[e._v("开")]),t("span",{attrs:{slot:"close"},slot:"close"},[e._v("关")])])],1)],1)],1),t("Row",[t("Col",{attrs:{span:"8"}},[t("FormItem",{attrs:{label:"厂商类型",prop:"type"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择厂商类型",clearable:!0},on:{"on-change":e.changeType},model:{value:e.editObj.type,callback:function(t){e.$set(e.editObj,"type",t)},expression:"editObj.type"}},e._l(e.manufacturerTypeList,(function(a){return t("Option",{key:a.value,attrs:{value:a.value}},[e._v(e._s(a.label))])})),1)],1)],1)],1),t("Row",["8"===e.editObj.type?t("div",[t("Tabs",{staticStyle:{"margin-left":"100px"},attrs:{value:e.checked},on:{"on-click":e.tagChange}},[t("TabPane",{attrs:{label:"套餐计费",name:"1"}},[t("div",[t("Button",{staticStyle:{margin:"10px 0"},attrs:{type:"primary"},on:{click:function(t){return e.ruleAdd("pkge")}}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"md-add"}}),e._v(" 添加套餐")],1)]),t("Table",{attrs:{columns:e.pkgeColumns,data:e.pkgeTableData,loading:e.pkgeTableLoading},scopedSlots:e._u([{key:"action",fn:function(a){var r=a.row;a.index;return[t("Button",{staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small"},on:{click:function(t){return e.ruleUpdate(r,"pkge")}}},[e._v("编辑")]),t("Button",{attrs:{type:"error",size:"small",loading:r.delLoading},on:{click:function(t){return e.deleteItem(r,"pkge")}}},[e._v("删除")])]}}],null,!1,2678796522)}),t("Page",{staticStyle:{margin:"15px 0"},attrs:{total:e.pkgetotal,"page-size":e.pkgepageSize,current:e.pkgepage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.pkgepage=t},"on-change":e.loadByPage}})],1)]),t("TabPane",{attrs:{label:"流量计费",name:"2"}},[t("div",[t("Button",{staticStyle:{margin:"10px 0"},attrs:{type:"primary"},on:{click:function(t){return e.ruleAdd("flow")}}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("Icon",{attrs:{type:"md-add"}}),e._v(" 添加方向")],1)]),t("div",{staticStyle:{"padding-bottom":"20px"}},[t("span",[e._v("套餐名称:"+e._s(e.editObj.corpName)+"流量套餐")])]),t("Table",{attrs:{columns:e.flowColumns,data:e.flowTableData,loading:e.flowTableLoading},scopedSlots:e._u([{key:"action",fn:function(a){var r=a.row;a.index;return[t("Button",{staticStyle:{"margin-right":"5px"},attrs:{type:"success",size:"small"},on:{click:function(t){return e.ruleUpdate(r,"flow")}}},[e._v("编辑")]),t("Button",{attrs:{type:"error",size:"small",loading:r.delLoading},on:{click:function(t){return e.deleteItem(r,"flow")}}},[e._v("删除")])]}}],null,!1,1894490410)}),t("Page",{staticStyle:{margin:"15px 0"},attrs:{total:e.flowtotal,"page-size":e.flowpageSize,current:e.flowpage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.flowpage=t},"on-change":e.flowloadByPage}})],1)])],1)],1):e._e()])],1),t("div",{staticStyle:{"text-align":"center","margin-top":"20px"}},[t("Button",{staticStyle:{"margin-right":"8px"},on:{click:e.back}},[e._v("返回")]),t("Button",{attrs:{type:"primary",loading:e.addLoading},on:{click:function(t){return e.update("editObj")}}},[e._v("提交")])],1)],1),t("Modal",{attrs:{title:e.pkgeTitle,"mask-closable":!1},on:{"on-cancel":function(t){return e.reset("pkgeObj")}},model:{value:e.pkgeEditFlag,callback:function(t){e.pkgeEditFlag=t},expression:"pkgeEditFlag"}},[t("Form",{ref:"pkgeObj",attrs:{model:e.pkgeObj,"label-width":180,rules:e.pkgeValidate}},[t("Row",[t("FormItem",{attrs:{label:"套餐名称",prop:"packageName"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入套餐名称"},model:{value:e.pkgeObj.packageName,callback:function(t){e.$set(e.pkgeObj,"packageName",t)},expression:"pkgeObj.packageName"}})],1)],1),t("Row",[t("FormItem",{attrs:{label:"套餐单价",prop:"price"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入套餐单价"},model:{value:e.pkgeObj.price,callback:function(t){e.$set(e.pkgeObj,"price",t)},expression:"pkgeObj.price"}},[t("span",{attrs:{slot:"append"},slot:"append"},[e._v("元")])])],1)],1)],1),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:function(t){return e.reset("pkgeObj")}}},[e._v("返回")]),"add"===e.pkgeType?t("Button",{attrs:{type:"primary",loading:e.addItemLoading},on:{click:function(t){return e.itemAdd("pkgeObj","pkge")}}},[e._v("确定")]):e._e(),"update"===e.pkgeType?t("Button",{attrs:{type:"primary",loading:e.addItemLoading},on:{click:function(t){return e.updateItem("pkgeObj","pkge")}}},[e._v("确定")]):e._e()],1)],1),t("Modal",{attrs:{title:e.flowTitle,"mask-closable":!1},on:{"on-cancel":function(t){return e.reset("flowObj")}},model:{value:e.flowEditFlag,callback:function(t){e.flowEditFlag=t},expression:"flowEditFlag"}},[t("Form",{ref:"flowObj",attrs:{model:e.flowObj,"label-width":180,rules:e.flowValidate}},[t("Row",[t("FormItem",{attrs:{label:"选择流量方向",prop:"mccList"}},[t("Select",{staticClass:"inputSty",attrs:{filterable:"",placeholder:"请选择流量方向",clearable:!0,multiple:""},model:{value:e.flowObj.mccList,callback:function(t){e.$set(e.flowObj,"mccList",t)},expression:"flowObj.mccList"}},e._l(e.continentList,(function(a){return t("Option",{key:a.id,attrs:{value:a.mcc}},[e._v(e._s(a.countryEn))])})),1)],1)],1),t("Row",[t("FormItem",{attrs:{label:"流量单价",prop:"price"}},[t("Input",{staticClass:"inputSty",attrs:{clearable:!0,placeholder:"请输入流量单价"},model:{value:e.flowObj.price,callback:function(t){e.$set(e.flowObj,"price",t)},expression:"flowObj.price"}},[t("span",{attrs:{slot:"append"},slot:"append"},[e._v("元/GB")])])],1)],1)],1),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"flex-end"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:function(t){return e.reset("flowObj")}}},[e._v("返回")]),"add"===e.flowType?t("Button",{attrs:{type:"primary",loading:e.addItemLoading},on:{click:function(t){return e.itemAdd("flowObj","flow")}}},[e._v("确定")]):e._e(),"update"===e.flowType?t("Button",{attrs:{type:"primary",loading:e.addItemLoading},on:{click:function(t){return e.updateItem("flowObj","flow")}}},[e._v("确定")]):e._e()],1)],1)],1)},i=[],n=(a("14d9"),a("4e82"),a("b64b"),a("d3b7"),a("25f0"),a("f7fa")),o=a("2a0d"),l=a("c70b"),s={components:{},data:function(){return{pkgetotal:0,pkgepageSize:10,pkgepage:1,flowtotal:0,flowpageSize:10,flowpage:1,checked:"1",pkgeType:"add",pkgeTitle:"添加套餐",pkgeEditFlag:!1,flowType:"add",flowTitle:"添加方向",flowEditFlag:!1,currencyOne:"",editObj:{corpId:"",corpName:"",eopCreateType:"",appKey:"",appSecret:"",notifyUrl:"",ebsCode:"",activeNotifyType:!1,dueNotifyType:!1,type:"",settleType:"",packages:[],flowList:[],currencyCode:"",internalOrder:"",address:"",companyName:""},ruleEditValidate:{corpName:[{required:!0,type:"string",message:"终端厂商名称不能为空",trigger:"blur"},{max:50,message:"最长50位"}],eopCreateType:[{required:!0,type:"string",message:"APPkey&APPSecret生成方式不能为空",trigger:"change"}],appKey:[{required:!0,type:"string",message:"APPkey不能为空",trigger:"blur"},{max:255,message:"最长255位"}],appSecret:[{required:!0,type:"string",message:"APPSecret不能为空",trigger:"blur"},{max:255,message:"最长255位"}],companyName:[{required:!0,type:"string",message:"公司名称不能为空",trigger:"blur"},{max:50,message:"最长50位"}],address:[{required:!0,type:"string",message:"地址不能为空",trigger:"blur"},{max:200,message:"最长200位"}],internalOrder:[{required:!0,type:"string",message:"内部订单不能为空",trigger:"change"}],ebsCode:[{required:!0,type:"string",message:"EBS Code不能为空",trigger:"blur"},{max:50,message:"最长50位"}],notifyUrl:[{required:!0,type:"string",message:"URL不能为空",trigger:"blur"},{max:255,message:"最长255位"}],activeNotifyType:[{required:!0,type:"boolean",message:"激活通知开关不能为空",trigger:"blur"}],dueNotifyType:[{required:!0,type:"boolean",message:"到期通知开关不能为空",trigger:"blur"}],type:[{required:!0,type:"string",message:"厂商类型不能为空",trigger:"change"}],settleType:[{required:!0,type:"string",message:"付费模式不能为空",trigger:"change"}],currencyCode:[{required:!0,type:"string",message:"币种不能为空",trigger:"change"}],packages:[{type:"array",required:!0,message:"请选择套餐",trigger:"change"}],flowList:[{type:"array",required:!0,message:"请填写方向",trigger:"change"}]},pkgeObj:{packageName:"",price:""},pkgeValidate:{packageName:[{required:!0,message:"套餐名称不能为空",trigger:"blur"},{max:200,message:"最长200位"}],price:[{required:!0,message:"流量单价不能为空",trigger:"blur"},{pattern:/^(?=([0-9]{1,12}$|[0-9]{1,10}\.))(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/,trigger:"blur",message:"请输入1-12位数字，整数首位非0（可精确到小数点后2位）"}]},flowObj:{packageName:"",mccList:[],mcc:"",price:""},flowValidate:{packageName:[{required:!0,message:"套餐名称不能为空",trigger:"blur"},{max:200,message:"最长200位"}],mccList:[{type:"array",required:!0,message:"流量方向不能为空",trigger:"change"}],mcc:[{type:"array",required:!0,message:"流量方向不能为空",trigger:"change"}],price:[{required:!0,message:"流量单价不能为空",trigger:"blur"},{pattern:/^(?=([0-9]{1,12}$|[0-9]{1,10}\.))(0|[1-9][0-9]*)(\.[0-9]{1,2})?$/,trigger:"blur",message:"请输入1-12位数字，整数首位非0（可精确到小数点后2位）"}]},appMethodList:[{value:"1",label:"自动生成"},{value:"2",label:"手动输入"}],manufacturerTypeList:[{value:"7",label:"线上厂商"},{value:"8",label:"线下厂商"}],paymentModeList:[{value:"1",label:"按套餐"},{value:"2",label:"按流量"}],pkgeTableLoading:!1,pkgeColumns:[{title:"套餐名称",key:"packageName",align:"center",minWidth:120,tooltip:!0,tooltipMaxWidth:2e3},{title:"币种",key:"currencyCode",align:"center",minWidth:100,tooltip:!0,render:function(e,t){var a=t.row,r="156"==a.currencyCode?"人民币":"840"==a.currencyCode?"美元":"344"==a.currencyCode?"港币":"获取失败";return e("label",r)}},{title:"套餐价格",key:"price",align:"center",minWidth:120,tooltip:!0,tooltipMaxWidth:2e3},{title:"操作",slot:"action",minWidth:160,align:"center"}],pkgeTableData:[],flowColumns:[{title:"流量方向",key:"countryName",align:"center",minWidth:120,tooltip:!0,tooltipMaxWidth:2e3},{title:"币种",key:"currencyCode",align:"center",minWidth:100,tooltip:!0,render:function(e,t){var a=t.row,r="156"==a.currencyCode?"人民币":"840"==a.currencyCode?"美元":"344"==a.currencyCode?"港币":"获取失败";return e("label",r)}},{title:"流量单价",key:"price",align:"center",minWidth:120,tooltip:!0,tooltipMaxWidth:2e3},{title:"操作",slot:"action",minWidth:160,align:"center"}],flowTableData:[],flowTableLoading:!1,addLoading:!1,addItemLoading:!1,continentList:[],corpId:"",manufacturer:{}}},methods:{changeSettleType:function(){"2"===this.editObj.settleType&&this.getLocalList(),"1"!==this.editObj.settleType&&"2"!==this.editObj.settleType||this.getRule(1)},changeType:function(){"8"!==this.editObj.type||"1"!==this.editObj.settleType&&"2"!==this.editObj.settleType||this.getRule(1)},getLocalList:function(){var e=this;Object(o["e"])().then((function(t){if(!t||"0000"!=t.code)throw t;var a=t.data;e.continentList=a,e.continentList.sort((function(e,t){return e.countryEn.localeCompare(t.countryEn)}))})).catch((function(e){}))},getRule:function(e){var t=this,a=t.editObj.corpId,r=t.editObj.settleType,i=10,o=e;this.pkgepage=e,this.flowpage=e,Object(n["r"])({corpId:a,settleType:r,pageSize:i,pageNumber:o}).then((function(e){if(!e||"0000"!=e.code)throw e;var a=e.data;"1"===t.editObj.settleType?(t.pkgeTableData=a||[],t.pkgetotal=e.count):(t.flowTableData=a||[],t.flowtotal=e.count)})).catch((function(e){}))},loadByPage:function(e){this.pkgepage=e,this.getRule(e)},flowloadByPage:function(e){this.flowpage=e,this.getRule(e)},update:function(e){var t=this,a=this;console.log(a.editObj.settleType),console.log(a.pkgeTableData),"1"===a.editObj.settleType&&a.pkgeTableData.length<=0?a.$Message.warning("请添加套餐"):"2"===a.editObj.settleType&&a.flowTableData.length<=0?a.$Message.warning("请添加流量"):a.$refs[e].validate((function(e){if(e){a.addLoading=!0;var r={corpName:a.editObj.corpName,eopCreateType:t.editObj.eopCreateType,appKey:a.editObj.appKey,appSecret:a.editObj.appSecret,notifyUrl:a.editObj.notifyUrl,ebsCode:a.editObj.ebsCode,activeNotifyType:!0===a.editObj.activeNotifyType?"1":"2",dueNotifyType:!0===a.editObj.dueNotifyType?"1":"2",type:a.editObj.type,currencyCode:a.editObj.currencyCode,address:a.editObj.address,companyName:a.editObj.companyName,internalOrder:a.editObj.internalOrder};Object(n["u"])(a.editObj.corpId,r).then((function(e){if(!e||"0000"!=e.code)throw e;a.$Notice.success({title:"操作提示",desc:"操作成功"}),a.addLoading=!1,t.$router.push({name:"manufacturerIndex"})})).catch((function(e){a.addLoading=!1}))}}))},ruleAdd:function(e){"pkge"===e&&(this.pkgeType="add",this.pkgeEditFlag=!0,this.pkgeTitle="新增套餐",this.pkgeObj={packageName:"",price:""},this.$refs.pkgeObj.resetFields()),"flow"===e&&(this.flowType="add",this.flowEditFlag=!0,this.flowTitle="新增方向",this.flowObj={packageName:"",mccList:[],mcc:"",price:""},this.$refs.flowObj.resetFields())},ruleUpdate:function(e,t){"pkge"===t&&(this.pkgeType="update",this.pkgeEditFlag=!0,this.pkgeTitle="编辑套餐",this.pkgeObj={id:e.id,packageName:e.packageName,price:e.price},this.pkgeObj.price=this.pkgeObj.price.toString()),"flow"===t&&(this.flowType="update",this.flowEditFlag=!0,this.flowTitle="编辑流量",this.flowObj={ids:e.ids,packageName:e.packageName,mccList:e.mccList,price:e.price},this.flowObj.price=this.flowObj.price.toString())},itemAdd:function(e,t){var a=this;a.$refs[e].validate((function(e){if(e){a.addItemLoading=!0;var r={corpId:a.editObj.corpId,settleType:a.editObj.settleType,currencyCode:a.currencyOne};"pkge"===t?(r.packageName=a.pkgeObj.packageName,r.price=l.multiply(l.bignumber(a.pkgeObj.price),100).toString()):(r.packageName=a.editObj.corpName+"流量套餐",r.price=l.multiply(l.bignumber(a.flowObj.price),100).toString(),r.mccList=a.flowObj.mccList),Object(n["b"])(r).then((function(e){if(!e||"0000"!=e.code)throw e;var t=e.data;a.tableData=t,a.addItemLoading=!1,a.$Notice.success({title:"操作提示",desc:"操作成功"}),a.reset(),a.getRule()})).catch((function(e){a.addItemLoading=!1}))}}))},updateItem:function(e,t){var a=this;a.$refs[e].validate((function(e){if(e){a.addItemLoading=!0;var r={corpId:a.editObj.corpId,settleType:a.editObj.settleType,currencyCode:a.currencyOne};"pkge"===t?(r.packageName=a.pkgeObj.packageName,r.price=l.multiply(l.bignumber(a.pkgeObj.price),100).toString(),r.id=a.pkgeObj.id):(r.packageName=a.flowObj.packageName,r.price=l.multiply(l.bignumber(a.flowObj.price),100).toString(),r.mccList=a.flowObj.mccList,r.ids=a.flowObj.ids),Object(n["v"])(r).then((function(e){if(!e||"0000"!=e.code)throw e;a.$Notice.success({title:"操作提示",desc:"操作成功"}),a.addItemLoading=!1,a.reset(),a.getRule()})).catch((function(e){a.addItemLoading=!1}))}}))},reset:function(){this.pkgeEditFlag=!1,this.flowEditFlag=!1},deleteItem:function(e,t){var a=this,r=e.packageId;Object(n["e"])({packageId:r}).then((function(r){if(!r||"0000"!=r.code)throw r;var i=r.data,n=!0===i?"确认删除?":"删除会造成已绑定的卡池激活失败，请谨慎删除!";a.$Modal.confirm({title:n,onOk:function(){"flow"===t?a.deleteBatch(e.ids):a.delRule(e.id)}})})).catch((function(e){}))},delRule:function(e){var t=this;Object(n["h"])(e).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.getRule(1)})).catch((function(e){}))},deleteBatch:function(e){var t=this;Object(n["g"])(e).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.getRule(1)})).catch((function(e){}))},back:function(){this.$router.push({name:"manufacturerIndex"})},tagChange:function(e){this.pkgepage=1,this.flowpage=1,this.checked=e,this.editObj.settleType=e,this.getRule(1)}},mounted:function(){var e=JSON.parse(decodeURIComponent(this.$route.query.manufacturer));this.manufacturer=e,this.editObj=e,this.editObj.activeNotifyType="1"===this.editObj.activeNotifyType,this.editObj.dueNotifyType="1"===this.editObj.dueNotifyType,this.currencyOne=this.editObj.currencyCode,this.getLocalList(),"8"===this.editObj.type&&this.getRule(1)}},c=s,u=(a("11f0"),a("2877")),p=Object(u["a"])(c,r,i,!1,null,null,null);t["default"]=p.exports},"3f7e":function(e,t,a){"use strict";var r=a("b5db"),i=r.match(/firefox\/(\d+)/i);e.exports=!!i&&+i[1]},"4e82":function(e,t,a){"use strict";var r=a("23e7"),i=a("e330"),n=a("59ed"),o=a("7b0b"),l=a("07fa"),s=a("083a"),c=a("577e"),u=a("d039"),p=a("addb"),d=a("a640"),g=a("3f7e"),f=a("99f4"),m=a("1212"),b=a("ea83"),y=[],h=i(y.sort),k=i(y.push),O=u((function(){y.sort(void 0)})),j=u((function(){y.sort(null)})),v=d("sort"),w=!u((function(){if(m)return m<70;if(!(g&&g>3)){if(f)return!0;if(b)return b<603;var e,t,a,r,i="";for(e=65;e<76;e++){switch(t=String.fromCharCode(e),e){case 66:case 69:case 70:case 72:a=3;break;case 68:case 71:a=4;break;default:a=2}for(r=0;r<47;r++)y.push({k:t+r,v:a})}for(y.sort((function(e,t){return t.v-e.v})),r=0;r<y.length;r++)t=y[r].k.charAt(0),i.charAt(i.length-1)!==t&&(i+=t);return"DGBEFHACIJK"!==i}})),T=O||!j||!v||!w,C=function(e){return function(t,a){return void 0===a?-1:void 0===t?1:void 0!==e?+e(t,a)||0:c(t)>c(a)?1:-1}};r({target:"Array",proto:!0,forced:T},{sort:function(e){void 0!==e&&n(e);var t=o(this);if(w)return void 0===e?h(t):h(t,e);var a,r,i=[],c=l(t);for(r=0;r<c;r++)r in t&&k(i,t[r]);p(i,C(e)),a=l(i),r=0;while(r<a)t[r]=i[r++];while(r<c)s(t,r++);return t}})},"99f4":function(e,t,a){"use strict";var r=a("b5db");e.exports=/MSIE|Trident/.test(r)},addb:function(e,t,a){"use strict";var r=a("f36a"),i=Math.floor,n=function(e,t){var a=e.length;if(a<8){var o,l,s=1;while(s<a){l=s,o=e[s];while(l&&t(e[l-1],o)>0)e[l]=e[--l];l!==s++&&(e[l]=o)}}else{var c=i(a/2),u=n(r(e,0,c),t),p=n(r(e,c),t),d=u.length,g=p.length,f=0,m=0;while(f<d||m<g)e[f+m]=f<d&&m<g?t(u[f],p[m])<=0?u[f++]:p[m++]:f<d?u[f++]:p[m++]}return e};e.exports=n},ea83:function(e,t,a){"use strict";var r=a("b5db"),i=r.match(/AppleWebKit\/(\d+)\./);e.exports=!!i&&+i[1]},f7fa:function(e,t,a){"use strict";a.d(t,"p",(function(){return n})),a.d(t,"r",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"u",(function(){return s})),a.d(t,"d",(function(){return c})),a.d(t,"f",(function(){return u})),a.d(t,"o",(function(){return p})),a.d(t,"i",(function(){return d})),a.d(t,"b",(function(){return g})),a.d(t,"v",(function(){return f})),a.d(t,"e",(function(){return m})),a.d(t,"h",(function(){return b})),a.d(t,"g",(function(){return y})),a.d(t,"s",(function(){return h})),a.d(t,"l",(function(){return k})),a.d(t,"k",(function(){return O})),a.d(t,"t",(function(){return j})),a.d(t,"m",(function(){return v})),a.d(t,"n",(function(){return w})),a.d(t,"j",(function(){return T})),a.d(t,"w",(function(){return C})),a.d(t,"c",(function(){return S})),a.d(t,"q",(function(){return N}));var r=a("66df"),i="/cms/api/v1",n=function(e){return r["a"].request({url:i+"/terminal/pages",params:e,method:"get"})},o=function(e){return r["a"].request({url:i+"/terminal/settleRule/queryList",params:e,method:"get"})},l=function(e){return r["a"].request({url:i+"/terminal",data:e,method:"post"})},s=function(e,t){return r["a"].request({url:i+"/terminal/"+e,data:t,method:"put"})},c=function(e,t){return r["a"].request({url:i+"/terminal/audit/"+e,params:t,method:"put"})},u=function(e){return r["a"].request({url:i+"/terminal",data:e,method:"delete"})},p=function(e){return r["a"].request({url:i+"/terminal/details",params:e,method:"get"})},d=function(e){return r["a"].request({url:i+"/terminal/details/export",params:e,responseType:"blob",method:"get"})},g=function(e){return r["a"].request({url:i+"/terminal/settleRule/add",data:e,method:"post"})},f=function(e){return r["a"].request({url:i+"/terminal/settleRule/update",data:e,method:"put"})},m=function(e){return r["a"].request({url:"/pms/api/v1/cardPool/checkPackage",params:e,method:"get"})},b=function(e){return r["a"].request({url:i+"/terminal/settleRule/delete/"+e,method:"delete"})},y=function(e){return r["a"].request({url:i+"/terminal/settleRule/deleteBatch",data:e,method:"post"})},h=function(e){return r["a"].request({url:"/stat/cdr/flow/get/list",params:e,method:"get"})},k=function(e){return r["a"].request({url:"/stat/cdr/flow/export/details",params:e,method:"get"})},O=function(e){return r["a"].request({url:"/stat/cdr/flow/export/info",params:e,method:"get"})},j=function(e){return r["a"].request({url:"/stat/cdr/flow/get/info",params:e,method:"get"})},v=function(e){return r["a"].request({url:"/stat/cdr/flow/export/list",params:e,method:"get"})},w=function(e){return r["a"].request({url:"/stat/cdr/flow/get/details",params:e,method:"get"})},T=function(e){return r["a"].request({url:"/stat/cdr/flow/export/info/all",params:e,method:"get"})},C=function(e){return r["a"].request({url:i+"/terminal/plmnlist/update",data:e,method:"post",headers:{"Content-Type":"application/json;charset=UTF-8"}})},S=function(e){return r["a"].request({url:i+"/terminal/plmnlist/createByFile",data:e,method:"post",contentType:"multipart/form-data"})},N=function(e){return r["a"].request({url:i+"/terminal/plmnlist/get",params:e,method:"get"})}}}]);