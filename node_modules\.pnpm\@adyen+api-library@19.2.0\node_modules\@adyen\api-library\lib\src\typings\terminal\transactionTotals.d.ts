/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { LoyaltyTotals } from './loyaltyTotals';
import { PaymentInstrumentType } from './paymentInstrumentType';
import { PaymentTotals } from './paymentTotals';
export declare class TransactionTotals {
    'AcquirerID'?: string;
    'CardBrand'?: string;
    'ErrorCondition'?: TransactionTotals.ErrorConditionEnum;
    'HostReconciliationID'?: string;
    'LoyaltyCurrency'?: string;
    'LoyaltyTotals'?: Array<LoyaltyTotals>;
    'LoyaltyUnit'?: TransactionTotals.LoyaltyUnitEnum;
    'OperatorID'?: string;
    'PaymentCurrency'?: string;
    'PaymentInstrumentType': PaymentInstrumentType;
    'PaymentTotals'?: Array<PaymentTotals>;
    'POIID'?: string;
    'SaleID'?: string;
    'ShiftNumber'?: string;
    'TotalsGroupID'?: string;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
export declare namespace TransactionTotals {
    enum ErrorConditionEnum {
        Aborted,
        Busy,
        Cancel,
        DeviceOut,
        InProgress,
        InsertedCard,
        InvalidCard,
        LoggedOut,
        MessageFormat,
        NotAllowed,
        NotFound,
        PaymentRestriction,
        Refusal,
        UnavailableDevice,
        UnavailableService,
        UnreachableHost,
        WrongPin
    }
    enum LoyaltyUnitEnum {
        Monetary,
        Point
    }
}
