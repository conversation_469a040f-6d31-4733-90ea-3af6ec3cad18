<template>
  <ContentWrap>
    <el-card>
      <h2>渠道自服务模块测试页面</h2>
      <p>如果您能看到这个页面，说明基本的组件导入和路由配置是正常的。</p>

      <el-divider />

      <div class="test-section">
        <h3>基础组件测试</h3>
        <el-button type="primary">
          <Icon icon="ep:search" class="mr-5px" />
          测试按钮
        </el-button>
        <el-button type="success" @click="showMessage">显示消息</el-button>
      </div>

      <el-divider />

      <div class="test-section">
        <h3>表格测试</h3>
        <el-table :data="testData" border>
          <el-table-column prop="name" label="名称" />
          <el-table-column prop="value" label="值" />
          <el-table-column prop="status" label="状态">
            <template #default="{ row }">
              <el-tag :type="row.status === 'success' ? 'success' : 'warning'">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-divider />

      <div class="test-section">
        <h3>表单测试</h3>
        <el-form :model="testForm" inline>
          <el-form-item label="测试输入：">
            <el-input v-model="testForm.input" placeholder="请输入测试内容" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSubmit">提交</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-divider />

      <div class="test-section">
        <h3>权限测试</h3>
        <p>权限检查结果：</p>
        <ul>
          <li>search权限: {{ hasPermission('search') ? '✅ 有权限' : '❌ 无权限' }}</li>
          <li>export权限: {{ hasPermission('export') ? '✅ 有权限' : '❌ 无权限' }}</li>
          <li>view权限: {{ hasPermission('view') ? '✅ 有权限' : '❌ 无权限' }}</li>
        </ul>
      </div>

      <el-divider />

      <div class="test-section">
        <h3>导航测试</h3>
        <el-button @click="goToDeposit">跳转到充值管理</el-button>
        <el-button @click="goToStock">跳转到库存管理</el-button>
        <el-button @click="goToOrder">跳转到订单管理</el-button>
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { useRouter } from 'vue-router'

const router = useRouter()

// 权限检查函数
const hasPermission = (permission: string): boolean => {
  console.log(`🔍 [测试页面权限检查] ${permission}: 允许访问`)
  return true
}

// 测试数据
const testData = ref([
  { name: '充值管理', value: '正常', status: 'success' },
  { name: '库存管理', value: '正常', status: 'success' },
  { name: '订单管理', value: '正常', status: 'success' },
  { name: '流量池管理', value: '测试中', status: 'warning' }
])

const testForm = reactive({
  input: ''
})

// 方法
const showMessage = () => {
  ElMessage.success('测试消息显示成功！')
}

const handleSubmit = () => {
  if (testForm.input) {
    ElMessage.success(`提交成功：${testForm.input}`)
  } else {
    ElMessage.warning('请输入测试内容')
  }
}

const goToDeposit = () => {
  router.push('/newcmi/channel/deposit')
}

const goToStock = () => {
  router.push('/newcmi/channel/stock')
}

const goToOrder = () => {
  router.push('/newcmi/channel/order')
}

// 生命周期
onMounted(() => {
  console.log('🎉 [测试页面] 组件已挂载，所有功能正常')
  ElMessage.info('测试页面加载完成')
})
</script>

<style scoped>
.test-section {
  margin: 20px 0;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.test-section h3 {
  margin-top: 0;
  color: #409eff;
}

.test-section ul {
  margin: 10px 0;
  padding-left: 20px;
}

.test-section li {
  margin: 5px 0;
}
</style>
