"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnitOfMeasureType = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var UnitOfMeasureType;
(function (UnitOfMeasureType) {
    UnitOfMeasureType[UnitOfMeasureType["Case"] = 'Case'] = "Case";
    UnitOfMeasureType[UnitOfMeasureType["Centilitre"] = 'Centilitre'] = "Centilitre";
    UnitOfMeasureType[UnitOfMeasureType["Centimetre"] = 'Centimetre'] = "Centimetre";
    UnitOfMeasureType[UnitOfMeasureType["Foot"] = 'Foot'] = "Foot";
    UnitOfMeasureType[UnitOfMeasureType["Gram"] = 'Gram'] = "Gram";
    UnitOfMeasureType[UnitOfMeasureType["Inch"] = 'Inch'] = "Inch";
    UnitOfMeasureType[UnitOfMeasureType["Kilogram"] = 'Kilogram'] = "Kilogram";
    UnitOfMeasureType[UnitOfMeasureType["Kilometre"] = 'Kilometre'] = "Kilometre";
    UnitOfMeasureType[UnitOfMeasureType["Litre"] = 'Litre'] = "Litre";
    UnitOfMeasureType[UnitOfMeasureType["Meter"] = 'Meter'] = "Meter";
    UnitOfMeasureType[UnitOfMeasureType["Mile"] = 'Mile'] = "Mile";
    UnitOfMeasureType[UnitOfMeasureType["Other"] = 'Other'] = "Other";
    UnitOfMeasureType[UnitOfMeasureType["Ounce"] = 'Ounce'] = "Ounce";
    UnitOfMeasureType[UnitOfMeasureType["Pint"] = 'Pint'] = "Pint";
    UnitOfMeasureType[UnitOfMeasureType["Pound"] = 'Pound'] = "Pound";
    UnitOfMeasureType[UnitOfMeasureType["Quart"] = 'Quart'] = "Quart";
    UnitOfMeasureType[UnitOfMeasureType["UkGallon"] = 'UKGallon'] = "UkGallon";
    UnitOfMeasureType[UnitOfMeasureType["UsGallon"] = 'USGallon'] = "UsGallon";
    UnitOfMeasureType[UnitOfMeasureType["Yard"] = 'Yard'] = "Yard";
})(UnitOfMeasureType = exports.UnitOfMeasureType || (exports.UnitOfMeasureType = {}));
//# sourceMappingURL=unitOfMeasureType.js.map