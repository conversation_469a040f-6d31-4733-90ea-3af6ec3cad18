/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { OriginalPOITransaction } from './originalPOITransaction';
import { POIData } from './pOIData';
import { PaymentReceipt } from './paymentReceipt';
import { Response } from './response';
export declare class ReversalResponse {
    'CustomerOrderID'?: string;
    'OriginalPOITransaction'?: OriginalPOITransaction;
    'PaymentReceipt'?: Array<PaymentReceipt>;
    'POIData'?: POIData;
    'Response': Response;
    'ReversedAmount'?: number;
    static discriminator: string | undefined;
    static attributeTypeMap: Array<{
        name: string;
        baseName: string;
        type: string;
    }>;
    static getAttributeTypeMap(): {
        name: string;
        baseName: string;
        type: string;
    }[];
}
