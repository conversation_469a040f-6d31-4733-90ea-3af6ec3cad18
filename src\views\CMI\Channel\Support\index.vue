<template>
  <!-- 服务支持 -->
  <ContentWrap>
    <el-card>
      <div class="search-container">
        <el-form :model="searchForm" inline>
          <el-form-item label="工单号：">
            <el-input 
              v-model="searchForm.ticketNo" 
              placeholder="请输入工单号" 
              clearable
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item label="问题类型：">
            <el-select 
              v-model="searchForm.issueType" 
              placeholder="请选择问题类型"
              clearable
              style="width: 150px;"
            >
              <el-option :value="1" label="技术问题" />
              <el-option :value="2" label="账单问题" />
              <el-option :value="3" label="服务问题" />
              <el-option :value="4" label="其他" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态：">
            <el-select 
              v-model="searchForm.status" 
              placeholder="请选择状态"
              clearable
              style="width: 150px;"
            >
              <el-option :value="1" label="待处理" />
              <el-option :value="2" label="处理中" />
              <el-option :value="3" label="已解决" />
              <el-option :value="4" label="已关闭" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button 
              v-if="hasPermission('search')"
              type="primary" 
              :loading="searchLoading"
              @click="handleSearch"
            >
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button 
              v-if="hasPermission('create')"
              type="success"
              @click="handleCreate"
            >
              <Icon icon="ep:plus" class="mr-5px" />
              创建工单
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 表格 -->
      <div style="margin-top: 20px;">
        <el-table :data="tableData" v-loading="loading" border>
          <el-table-column prop="ticketNo" label="工单号" min-width="150" />
          <el-table-column prop="title" label="标题" min-width="200" />
          <el-table-column prop="issueType" label="问题类型" min-width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="getIssueTypeTag(row.issueType)">
                {{ getIssueTypeText(row.issueType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getPriorityTag(row.priority)">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusTag(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="assignee" label="处理人" min-width="100" />
          <el-table-column prop="createTime" label="创建时间" min-width="160" />
          <el-table-column prop="updateTime" label="更新时间" min-width="160" />
          <el-table-column label="操作" min-width="150" align="center" fixed="right">
            <template #default="{ row }">
              <el-button 
                v-if="hasPermission('view')"
                type="primary" 
                size="small"
                @click="handleView(row)"
              >
                查看
              </el-button>
              <el-button 
                v-if="hasPermission('edit') && row.status !== 4"
                type="warning" 
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right;">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

// 权限检查函数
const hasPermission = (permission: string): boolean => {
  console.log(`🔍 [服务支持权限检查] ${permission}: 允许访问`)
  return true
}

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const searchForm = reactive({
  ticketNo: '',
  issueType: null as number | null,
  status: null as number | null
})

const tableData = ref<any[]>([])

// 方法
const getIssueTypeTag = (type: number): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    1: 'danger',
    2: 'warning',
    3: 'primary',
    4: 'info'
  }
  return typeMap[type] || 'info'
}

const getIssueTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '技术问题',
    2: '账单问题',
    3: '服务问题',
    4: '其他'
  }
  return typeMap[type] || '未知'
}

const getPriorityTag = (priority: number): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const priorityMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    1: 'danger',
    2: 'warning',
    3: 'primary',
    4: 'info'
  }
  return priorityMap[priority] || 'info'
}

const getPriorityText = (priority: number) => {
  const priorityMap: Record<number, string> = {
    1: '紧急',
    2: '高',
    3: '中',
    4: '低'
  }
  return priorityMap[priority] || '未知'
}

const getStatusTag = (status: number): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    1: 'warning',
    2: 'primary',
    3: 'success',
    4: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '待处理',
    2: '处理中',
    3: '已解决',
    4: '已关闭'
  }
  return statusMap[status] || '未知'
}

const handleSearch = () => {
  currentPage.value = 1
  getTableData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getTableData()
}

const handleCreate = () => {
  ElMessage.info('创建工单功能开发中...')
}

const handleView = (row: any) => {
  ElMessage.info(`查看工单: ${row.ticketNo}`)
}

const handleEdit = (row: any) => {
  ElMessage.info(`编辑工单: ${row.ticketNo}`)
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        ticketNo: 'TK20240101001',
        title: '无法连接网络',
        issueType: 1,
        priority: 2,
        status: 2,
        assignee: '技术支持A',
        createTime: '2024-01-01 10:00:00',
        updateTime: '2024-01-01 14:30:00'
      },
      {
        id: 2,
        ticketNo: 'TK20240101002',
        title: '账单金额异常',
        issueType: 2,
        priority: 3,
        status: 1,
        assignee: '客服B',
        createTime: '2024-01-01 11:00:00',
        updateTime: '2024-01-01 11:00:00'
      }
    ]
    total.value = 2
  } catch (error) {
    ElMessage.error('获取工单数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.search-container {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}
</style>
