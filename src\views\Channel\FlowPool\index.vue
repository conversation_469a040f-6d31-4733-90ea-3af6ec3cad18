<template>
  <!-- 流量池管理 -->
  <ContentWrap>
    <el-card>
      <div style="margin-bottom: 20px">
        <span style="font-weight: bold">渠道：</span>
        <span>{{ corpName }}</span>
      </div>

      <div class="search-container">
        <el-form :model="searchForm" inline>
          <el-form-item label="流量池名称：">
            <el-input
              v-model="searchForm.flowpoolname"
              placeholder="请输入流量池名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="使用状态：">
            <el-select
              v-model="searchForm.usestatus"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <el-option :value="1" label="限速" />
              <el-option :value="2" label="停机" />
              <el-option :value="3" label="正常" />
            </el-select>
          </el-form-item>
          <el-form-item label="上架状态：">
            <el-select
              v-model="searchForm.shelfstatus"
              placeholder="请选择"
              clearable
              style="width: 150px"
            >
              <el-option :value="1" label="上线" />
              <el-option :value="2" label="下线" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="hasPermission('search')"
              type="primary"
              :loading="searchLoading"
              @click="handleSearch"
            >
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button
              v-if="hasPermission('export')"
              type="success"
              :loading="exportLoading"
              @click="exportData"
            >
              <Icon icon="ep:download" class="mr-5px" />
              导出
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <div style="margin-top: 20px">
        <el-table :data="tableData" v-loading="loading" border>
          <el-table-column prop="poolName" label="流量池名称" min-width="150" />
          <el-table-column prop="totalFlow" label="总流量(MB)" min-width="120" align="right" />
          <el-table-column prop="usedFlow" label="已用流量(MB)" min-width="120" align="right" />
          <el-table-column
            prop="remainingFlow"
            label="剩余流量(MB)"
            min-width="120"
            align="right"
          />
          <el-table-column prop="cardCount" label="卡片数量" min-width="100" align="center" />
          <el-table-column prop="useStatus" label="使用状态" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getUseStatusTag(row.useStatus)">
                {{ getUseStatusText(row.useStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="shelfStatus" label="上架状态" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getShelfStatusTag(row.shelfStatus)">
                {{ getShelfStatusText(row.shelfStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="160" />
          <el-table-column label="操作" min-width="200" align="center" fixed="right">
            <template #default="{ row }">
              <el-button
                v-if="hasPermission('iccidlist')"
                type="warning"
                size="small"
                plain
                @click="viewICCIDList(row)"
              >
                显示ICCID
              </el-button>
              <el-button
                v-if="hasPermission('reminder')"
                type="primary"
                size="small"
                plain
                @click="setThreshold(row)"
              >
                阈值提醒
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>

    <!-- 阈值提醒弹窗 -->
    <el-dialog
      v-model="thresholdVisible"
      title="阈值提醒设置"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="thresholdForm"
        :rules="thresholdRules"
        ref="thresholdFormRef"
        label-width="120px"
      >
        <el-form-item label="流量池名称：">
          <span>{{ thresholdForm.poolname }}</span>
        </el-form-item>
        <el-form-item label="提醒阈值：" prop="threshold">
          <el-input-number
            v-model="thresholdForm.threshold"
            :min="1"
            :max="100"
            placeholder="请输入阈值百分比"
            style="width: 200px"
          />
          <span style="margin-left: 10px">%</span>
        </el-form-item>
        <el-form-item label="提醒方式：" prop="notifyType">
          <el-checkbox-group v-model="thresholdForm.notifyType">
            <el-checkbox label="email">邮件</el-checkbox>
            <el-checkbox label="sms">短信</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="thresholdVisible = false">取消</el-button>
        <el-button type="primary" @click="saveThreshold" :loading="saveLoading">保存</el-button>
      </template>
    </el-dialog>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { useRouter } from 'vue-router'

// 权限检查函数
const hasPermission = (permission: string): boolean => {
  return true // TODO: 实现权限检查逻辑
}

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const exportLoading = ref(false)
const saveLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const corpName = ref('示例渠道')
const thresholdVisible = ref(false)

const searchForm = reactive({
  flowpoolname: '',
  usestatus: null as number | null,
  shelfstatus: null as number | null
})

const thresholdForm = reactive({
  poolname: '',
  threshold: 80,
  notifyType: [] as string[]
})

const thresholdRules = {
  threshold: [
    { required: true, message: '请输入阈值', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '阈值必须在1-100之间', trigger: 'blur' }
  ],
  notifyType: [{ required: true, message: '请选择提醒方式', trigger: 'change' }]
}

const tableData = ref<any[]>([])
const flowData = ref<any[]>([])
const thresholdFormRef = ref()

// 方法
const getUseStatusTag = (status: number) => {
  const statusMap: Record<number, string> = {
    1: 'warning',
    2: 'danger',
    3: 'success'
  }
  return statusMap[status] || 'info'
}

const getUseStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '限速',
    2: '停机',
    3: '正常'
  }
  return statusMap[status] || '未知'
}

const getShelfStatusTag = (status: number) => {
  const statusMap: Record<number, string> = {
    1: 'success',
    2: 'danger'
  }
  return statusMap[status] || 'info'
}

const getShelfStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '上线',
    2: '下线'
  }
  return statusMap[status] || '未知'
}

const handleSearch = () => {
  currentPage.value = 1
  getTableData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getTableData()
}

const exportData = async () => {
  try {
    exportLoading.value = true
    // TODO: 实现导出功能
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const viewICCIDList = (row: any) => {
  // 跳转到ICCID列表页面
  router.push({
    path: '/newcmi/channel/flowpool/iccid-list',
    query: { poolId: row.id, poolName: row.poolName }
  })
}

const setThreshold = (row: any) => {
  thresholdForm.poolname = row.poolName
  thresholdForm.threshold = row.threshold || 80
  thresholdForm.notifyType = row.notifyType || []
  thresholdVisible.value = true
}

const saveThreshold = async () => {
  try {
    await thresholdFormRef.value?.validate()
    saveLoading.value = true

    // TODO: 实现保存阈值API调用
    ElMessage.success('阈值设置保存成功')
    thresholdVisible.value = false
    getTableData()
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        poolName: '流量池001',
        totalFlow: 10240,
        usedFlow: 3072,
        remainingFlow: 7168,
        cardCount: 100,
        useStatus: 3,
        shelfStatus: 1,
        createTime: '2024-01-01 10:00:00',
        threshold: 80,
        notifyType: ['email']
      }
    ]
    total.value = 1
  } catch (error) {
    ElMessage.error('获取流量池数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.search-container {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}
</style>
