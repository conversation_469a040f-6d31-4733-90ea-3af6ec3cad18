<template>
  <div class="imsi-management">
    <ContentWrap>
      <div class="page-header">
        <h2>IMSI管理</h2>
        <p>管理国际移动用户识别码的分配和使用情况</p>
      </div>

      <!-- 功能开发中提示 -->
      <div class="development-notice">
        <el-alert
          title="功能开发中"
          description="IMSI管理功能正在开发中，敬请期待..."
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 占位内容 -->
      <div class="placeholder-content">
        <el-empty description="IMSI管理功能即将上线">
          <el-button type="primary">返回首页</el-button>
        </el-empty>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
</script>

<style scoped>
.imsi-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.page-header p {
  color: #606266;
  margin: 0;
}

.development-notice {
  margin-bottom: 30px;
}

.placeholder-content {
  background: #fff;
  border-radius: 4px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
