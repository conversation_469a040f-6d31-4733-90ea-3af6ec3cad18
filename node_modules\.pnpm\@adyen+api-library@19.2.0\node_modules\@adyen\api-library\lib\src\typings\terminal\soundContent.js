"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SoundContent = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
class SoundContent {
    static getAttributeTypeMap() {
        return SoundContent.attributeTypeMap;
    }
}
exports.SoundContent = SoundContent;
SoundContent.discriminator = undefined;
SoundContent.attributeTypeMap = [
    {
        "name": "Language",
        "baseName": "Language",
        "type": "string"
    },
    {
        "name": "ReferenceID",
        "baseName": "ReferenceID",
        "type": "string"
    },
    {
        "name": "SoundFormat",
        "baseName": "SoundFormat",
        "type": "SoundContent.SoundFormatEnum"
    },
    {
        "name": "Value",
        "baseName": "Value",
        "type": "string"
    }
];
(function (SoundContent) {
    let SoundFormatEnum;
    (function (SoundFormatEnum) {
        SoundFormatEnum[SoundFormatEnum["MessageRef"] = 'MessageRef'] = "MessageRef";
        SoundFormatEnum[SoundFormatEnum["SoundRef"] = 'SoundRef'] = "SoundRef";
        SoundFormatEnum[SoundFormatEnum["Text"] = 'Text'] = "Text";
    })(SoundFormatEnum = SoundContent.SoundFormatEnum || (SoundContent.SoundFormatEnum = {}));
})(SoundContent = exports.SoundContent || (exports.SoundContent = {}));
//# sourceMappingURL=soundContent.js.map