<template>
	<!-- 套餐管理首页 -->
	<ElCard class="package-card">
		<!-- 搜索条件 -->
		<div class="search-form">
			<ElForm inline>
				<ElFormItem :label="$t('deposit.mealname')" label-width="100px">
					<ElInput
						v-model="searchObj.packageName"
						clearable
						:placeholder="$t('support.input_mealname')"
						style="width: 200px"
					/>
				</ElFormItem>
				<ElFormItem :label="$t('support.pakageId')" label-width="100px">
					<ElInput
						v-model="searchObj.packageId"
						clearable
						:placeholder="$t('support.inputPackageID')"
						style="width: 200px"
					/>
				</ElFormItem>
				<ElFormItem :label="$t('buymeal.Country')" label-width="80px">
					<ElSelect
						v-model="searchObj.country"
						filterable
						style="width: 200px"
						:placeholder="$t('buymeal.selectCountry')"
						clearable
					>
						<ElOption
							v-for="item in countryList"
							:key="item.id"
							:value="item.mcc"
							:label="item.countryEn"
						/>
					</ElSelect>
				</ElFormItem>
			</ElForm>

			<!-- 操作按钮 -->
			<div class="action-buttons">
				<ElButton
					v-if="checkPermission(['search'])"
					:disabled="cooperationMode === '1' || cooperationMode === '3'"
					type="primary"
					:loading="searchLoading"
					@click="search"
					:icon="Search"
				>
					{{ $t('common.search') }}
				</ElButton>
				<ElButton
					v-if="checkPermission(['add'])"
					:disabled="!showFlag || cooperationMode === '1' || cooperationMode === '3'"
					type="success"
					@click="add"
					:icon="Plus"
				>
					{{ $t('support.create') }}
				</ElButton>
				<ElButton
					v-if="checkPermission(['refuel'])"
					type="warning"
					:disabled="cooperationMode === '1' || cooperationMode === '3'"
					@click="fuelPackManagement"
					:icon="Briefcase"
				>
					{{ $t('channelfuelPack_mngr') }}
				</ElButton>
			</div>
		</div>

		<!-- 表格 -->
		<ElTable
			:data="tableData"
			style="width: 100%; margin-top: 40px;"
			v-loading="loading"
			border
			class="package-table"
		>
			<ElTableColumn
				prop="packageName"
				:label="$t('deposit.mealname')"
				min-width="150"
				align="center"
			/>
			<ElTableColumn
				prop="packageId"
				:label="$t('support.pakageId')"
				min-width="120"
				align="center"
			/>
			<ElTableColumn
				prop="country"
				:label="$t('buymeal.Country')"
				min-width="120"
				align="center"
			/>
			<ElTableColumn
				prop="price"
				:label="$t('support.price')"
				min-width="100"
				align="center"
			/>
			<ElTableColumn
				prop="auditStatus"
				:label="$t('support.status')"
				min-width="100"
				align="center"
			>
				<template #default="{ row }">
					<ElTag :type="getStatusType(row.auditStatus)">
						{{ getStatusText(row.auditStatus) }}
					</ElTag>
				</template>
			</ElTableColumn>
			<ElTableColumn
				:label="$t('support.action')"
				min-width="300"
				align="center"
				fixed="right"
			>
				<template #default="{ row }">
					<ElButton
						v-if="checkPermission(['info'])"
						type="warning"
						size="small"
						@click="showDetail(row)"
						style="margin-right: 5px"
					>
						{{ $t('stock.details') }}
					</ElButton>
					<ElButton
						v-if="checkPermission(['update'])"
						:disabled="!showFlag"
						type="primary"
						size="small"
						@click="update(row)"
						style="margin-right: 5px"
					>
						{{ $t('support.edit2') }}
					</ElButton>
					<ElButton
						v-if="checkPermission(['copy'])"
						:disabled="!showFlag"
						type="warning"
						size="small"
						@click="copy(row)"
						style="margin-right: 5px"
					>
						{{ $t('support.copy') }}
					</ElButton>
					<ElButton
						v-if="checkPermission(['delete'])"
						:disabled="row.auditStatus === '4' || !showFlag"
						type="danger"
						size="small"
						@click="deleteItem(row)"
					>
						{{ $t('address.Delete') }}
					</ElButton>
				</template>
			</ElTableColumn>
		</ElTable>

		<!-- 分页 -->
		<div class="pagination-container">
			<ElPagination
				:current-page="currentPage"
				:page-size="pageSize"
				:total="total"
				:page-sizes="[10, 20, 50, 100]"
				layout="total, sizes, prev, pager, next, jumper"
				@current-change="goPage"
				@size-change="handleSizeChange"
			/>
		</div>

		<!-- 新增/编辑弹窗 -->
		<PackageModel
			ref="packageModel"
			@goPageFirst="goPageFirst"
			:title="title"
			:typeFlag="typeFlag"
			:corpId="corpId"
		/>
	</ElCard>
</template>

<script>
	import PackageModel from'../../../components/package/packagemodel.vue'
	import {opsearchAll} from '@/api/operators';
	import {supplier} from '@/api/ResourceSupplier'
	import{getList,batchDelete,judgeChannelCreatePackage}from '@/api/channel/package.js'
	import {searchcorpid} from '@/api/channel.js';
	import {
		getQrCode
	} from '@/api/aqCode';
	export  default{
		components:{
			PackageModel
		},
		data(){
			return{
				cooperationMode: '', //合作模式
				total: 0,
				currentPage: 1,
				page: 0,
				name:'',
				corpId:'',//渠道商id
				loading: false,
				searchloading:false,//查询加载
				title:'',//弹窗标识
				supplierId:'',
				typeFlag:'',//修改/详情/新增标识
				countryList:[],//国家地区列表
				supplierList:[],//资源供应商列表
				searchObj: {
					'packageName': '', //套餐名称
					'packageId':'',//套餐Id
					'country': '', //搜索国家/地区
					'poolId':'',//卡池ID
					'supplierId':'',//供应商ID
				},
				data:[],//表格列表
				columns: [{
					title: this.$t('support.pakageId'),
					key: 'id',
					minWidth: 120,
					align: 'center',
					tooltip: true,
				},{
					title: this.$t('deposit.mealname'),
					key: 'nameCn',
					minWidth: 130,
					align: 'center',
					tooltip: true,
				},{
					title: this.$t('support.DataRestrictionType'),
					key: 'flowLimitType',
					align: 'center',
					tooltip: true,
					minWidth: 135,
					render: (h, params) => {
						const row = params.row;
						const text = row.flowLimitType == '1' ? this.$t('support.DataRestrictionCycle') : row
							.flowLimitType == '2' ? this.$t('support.DataRestrictionSingle') : '';
						return h('label', text)
					}
				},{
					title: this.$t('support.failureReason'),
					key: 'noPassMessage',
					minWidth: 150,
					align: 'center',
					tooltip: true,
					render: (h, params) => {
						const row = params.row;
						let text = row.noPassMessage ? row.noPassMessage : "";
						if (text.length > 10) {
							text = text.substring(0, 10) + "...";
							return h('div', [h('Tooltip', {
									props: {
										placement: 'bottom',
										transfer: true
									},
									style: {
										cursor: 'pointer',
									},
								},
								[
									text,
									h('label', {
											slot: 'content',
											style: {
												whiteSpace: 'normal',
												wordBreak: 'break-all' //超出隐藏
											},
										},
										row.noPassMessage
									)
								])]);
						} else {
							text = text;
							return h('label', text);
						}
					}
				},{
					title: this.$t('support.operate'),
					slot: 'action',
					minWidth: 330,
					align: 'center',
					fixed:'right'
				},{
					title: this.$t('support.approvalStatus'),
					key: 'auditStatus',
					minWidth: 130,
					align: 'center',
					fixed:'right',
					tooltip: true,
					render: (h, params) => {
						const row = params.row
						const color = row.auditStatus == '1' ? '#2b85e4' : row.auditStatus == '2' ? '#19be6b' :
							row.auditStatus == '3' ? '#ff0000' : row.auditStatus == '4' ? '#ffa554' :
							row.auditStatus == '5' ? '#ff0000' : '';
						const text = row.auditStatus == '1' ? this.$t('support.newApproval') : row.auditStatus == '2' ? this.$t('support.approve') :
							row.auditStatus == '3' ? this.$t('support.notApprove') : row.auditStatus == '4' ? this.$t('support.modificationApproval') :
							row.auditStatus == '5' ? this.$t('support.deleteApproval') : ''
						return h('label', {
							style: {
								color: color
							}
						}, text)
					}
				},
				],
				showFalg: false,
			}
		},
		mounted() {
			this.cooperationMode = sessionStorage.getItem("cooperationMode")
			if (!this.cooperationMode || this.cooperationMode == '2') {
				this.searchcorpid()
				this.getLocalList()
				this.getsupplier()
			}
		},
		methods:{
			goPageFirst(page){
				this.loading = true
				var _this = this
				getList({
					page:page,
					pageSize:10,
					groupName:this.groupName,
					corpId:this.corpId,
					packageNameCn:this.searchObj.packageName,
					packageId:this.searchObj.packageId,
					mcc:this.searchObj.country,
					isNeedAuth:true,
					list:true,
					isNeedMcc:true,
					cooperationMode: this.cooperationMode
				}).then(res => {
					if (res.code == '0000') {
						let List=[]
						// 循环遍历data
						res.data.data.map((value, index) => {
							if (value.authObj) {
								List.push(value.authObj)
							}else {
								List.push(value)
							}
						})
						_this.loading = false
						this.searchloading = false
						this.page = page
						this.currentPage = page
						this.total = res.data.total
						this.data =List
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
					this.loading = false
					this.searchloading = false
				})
			},
			search(){
				this.searchloading=true
				this.goPageFirst(1)
			},
			goPage(page){
				this.goPageFirst(page)
			},
			// 弹出新增弹窗
			add(){
				this.title=this.$t('support.createPackage')
				this.typeFlag="add"
				this.$refs.packagemodel.show()
			},
			fuelPackManagement() {
				this.$router.push({
					path: '/fuelPackManagement',
					query: {
						showFalg: this.showFalg,
					}
				})
			},
			// 查看详情
			showdetail(row){
				this.title=this.$t('packageInfo')
				this.typeFlag="info"
				this.$refs.packagemodel.show(JSON.parse(JSON.stringify(row)))
			},
			// 编辑
			update(row){
				this.title=this.$t('packageUpdate')
				this.typeFlag="update"
				let type = 'true'
				this.$refs.packagemodel.show(JSON.parse(JSON.stringify(row)),type)
			},
			// 复制
			copy(row){
				this.title=this.$t('packageCopy')
				this.typeFlag="add"
				this.$refs.packagemodel.show(JSON.parse(JSON.stringify(row)))
			},
			// 删除
			delItem(row){
				this.$Modal.confirm({
					title: this.$t('flow.Confirmdelete'),
					onOk: () => {
						batchDelete({
							packageIdList:[row.id]
						}).then(res => {
							if (res && res.code == '0000') {
								this.$Notice.success({
									title: this.$t("address.Operationreminder"),
									desc: this.$t("common.Successful")
								})
								this.goPageFirst(1)
							} else {
								throw res
							}
						}).catch((err) => {

						})
					}
				});
			},
			/**
			*
			* ----------------------------------初始化信息-----------------------------------
			*
			*/
		    //获取渠道商id
			searchcorpid(){
				searchcorpid({
					userName: this.$store.state.user.userName,
				}).then(res => {
					if (res.code == '0000') {
						this.corpId = res.data
						this.goPageFirst(1)
						this.judgeChannelCreatePackage()
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {})
			},
			judgeChannelCreatePackage() {
				judgeChannelCreatePackage({
					corpId: this.corpId
				}).then(res => {
					if (res.code == '0000') {
						if (res.data === false) {
							this.showFalg = false
						} else {
							this.showFalg = true
						}
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {})
			},
			//国家/地区
			getLocalList() {
				opsearchAll().then(res => {
					if (res && res.code == '0000') {
						var list = res.data;
						this.countryList = list;
						this.countryList.sort(function(str1, str2) {
							return str1.countryEn.localeCompare(str2.countryEn);
						});
						var localMap = new Map();
						list.map((local, index) => {
							localMap.set(local.mcc, local.countryEn);
						});
						this.localMap = localMap;
					} else {
						throw res
					}
				}).catch((err) => {

				}).finally(() => {

				})
			},
			// 获取资源供应商
			getsupplier(){
				supplier({
					pageNum:-1,
					pageSize:-1,
				}).then(res => {
					if (res.code == '0000') {
						this.supplierList = res.data
					}
				}).catch((err) => {
					console.error(err)
				}).finally(() => {
				})
			},
		}
	}
</script>

<style scoped="scoped">
	.search_head_i {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-wrap: wrap;
	}
	.search_box {
		width: 300px;
		padding: 0 5px;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		flex-direction: row;
		margin-bottom: 20px;
	}
	.search_box_label {
		font-weight: bold;
		text-align: center;
		width: 85px;
	}
	.footer_wrap{
		width: 100%;
		display: flex;
		align-items: center;
		justify-content:center;
	}
</style>
