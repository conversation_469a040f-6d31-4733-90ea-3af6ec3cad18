<template>
	<!-- 套餐管理首页 -->
	<ElCard class="package-card">
		<!-- 搜索条件 -->
		<div class="search-form">
			<ElForm inline>
				<ElFormItem label="套餐名称" label-width="100px">
					<ElInput 
						v-model="searchObj.packageName" 
						clearable 
						placeholder="请输入套餐名称" 
						style="width: 200px"
					/>
				</ElFormItem>
				<ElFormItem label="套餐ID" label-width="100px">
					<ElInput 
						v-model="searchObj.packageId" 
						clearable 
						placeholder="请输入套餐ID" 
						style="width: 200px"
					/>
				</ElFormItem>
				<ElFormItem label="国家" label-width="80px">
					<ElSelect 
						v-model="searchObj.country" 
						filterable 
						style="width: 200px" 
						placeholder="请选择国家"
						clearable
					>
						<ElOption 
							v-for="item in countryList" 
							:key="item.id"
							:value="item.mcc" 
							:label="item.countryEn"
						/>
					</ElSelect>
				</ElFormItem>
			</ElForm>
			
			<!-- 操作按钮 -->
			<div class="action-buttons">
				<ElButton 
					v-if="checkPermission(['search'])" 
					:disabled="cooperationMode === '1' || cooperationMode === '3'" 
					type="primary" 
					:loading="searchLoading" 
					@click="search"
					:icon="Search"
				>
					搜索
				</ElButton>
				<ElButton 
					v-if="checkPermission(['add'])" 
					:disabled="!showFlag || cooperationMode === '1' || cooperationMode === '3'" 
					type="success" 
					@click="add"
					:icon="Plus"
				>
					新建
				</ElButton>
				<ElButton 
					v-if="checkPermission(['refuel'])" 
					type="warning" 
					:disabled="cooperationMode === '1' || cooperationMode === '3'" 
					@click="fuelPackManagement"
					:icon="Briefcase"
				>
					流量包管理
				</ElButton>
			</div>
		</div>
		
		<!-- 表格 -->
		<ElTable 
			:data="tableData" 
			style="width: 100%; margin-top: 40px;" 
			v-loading="loading"
			border
			class="package-table"
		>
			<ElTableColumn 
				prop="packageName"
				label="套餐名称"
				min-width="150"
				align="center"
			/>
			<ElTableColumn 
				prop="packageId"
				label="套餐ID"
				min-width="120"
				align="center"
			/>
			<ElTableColumn 
				prop="countryName"
				label="国家"
				min-width="120"
				align="center"
			/>
			<ElTableColumn 
				prop="price"
				label="价格"
				min-width="100"
				align="center"
			>
				<template #default="{ row }">
					¥{{ row.price }}
				</template>
			</ElTableColumn>
			<ElTableColumn 
				prop="auditStatus"
				label="状态"
				min-width="100"
				align="center"
			>
				<template #default="{ row }">
					<ElTag :type="getStatusType(row.auditStatus)">
						{{ getStatusText(row.auditStatus) }}
					</ElTag>
				</template>
			</ElTableColumn>
			<ElTableColumn 
				prop="createTime"
				label="创建时间"
				min-width="160"
				align="center"
			/>
			<ElTableColumn 
				label="操作"
				min-width="300"
				align="center"
				fixed="right"
			>
				<template #default="{ row }">
					<ElButton 
						v-if="checkPermission(['info'])" 
						type="warning" 
						size="small"
						@click="showDetail(row)"
						style="margin-right: 5px"
					>
						详情
					</ElButton>
					<ElButton 
						v-if="checkPermission(['update'])" 
						:disabled="!showFlag" 
						type="primary" 
						size="small"
						@click="update(row)"
						style="margin-right: 5px"
					>
						编辑
					</ElButton>
					<ElButton 
						v-if="checkPermission(['copy'])" 
						:disabled="!showFlag" 
						type="info" 
						size="small"
						@click="copy(row)"
						style="margin-right: 5px"
					>
						复制
					</ElButton>
					<ElButton 
						v-if="checkPermission(['delete'])" 
						:disabled="row.auditStatus === '4' || !showFlag"  
						type="danger" 
						size="small"
						@click="deleteItem(row)"
					>
						删除
					</ElButton>
				</template>
			</ElTableColumn>
		</ElTable>
		
		<!-- 分页 -->
		<div class="pagination-container">
			<ElPagination
				:current-page="currentPage"
				:page-size="pageSize"
				:total="total"
				:page-sizes="[10, 20, 50, 100]"
				layout="total, sizes, prev, pager, next, jumper"
				@current-change="goPage"
				@size-change="handleSizeChange"
			/>
		</div>
		
		<!-- 删除确认弹窗 -->
		<ElDialog 
			v-model="deleteModal" 
			:close-on-click-modal="false"
			width="500px"
			class="delete-dialog"
		>
			<template #header>
				<span>删除确认</span>
			</template>
			
			<div class="delete-content">
				<p class="warning-text">确定要删除此套餐吗？此操作不可撤销。</p>
			</div>

			<template #footer>
				<div class="delete-footer">
					<ElButton @click="cancelDelete" :icon="ArrowLeft">
						取消
					</ElButton>
					<ElButton type="danger" :loading="deleteLoading" @click="confirmDelete">
						确定删除
					</ElButton>
				</div>
			</template>
		</ElDialog>
	</ElCard>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElCard, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElButton, ElTable, ElTableColumn, ElPagination, ElDialog, ElTag } from 'element-plus'
import { Search, Plus, Briefcase, ArrowLeft } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

defineOptions({
  name: 'ChannelPackageManagement'
})

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const deleteLoading = ref(false)
const deleteModal = ref(false)

const cooperationMode = ref('')
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const showFlag = ref(true)
const title = ref('')
const typeFlag = ref('')
const corpId = ref('')

const searchObj = reactive({
  packageName: '',
  packageId: '',
  country: ''
})

const tableData = ref([])
const countryList = ref([])
const selectedRow = ref(null)

// 权限检查函数
const checkPermission = (permissions: string[]): boolean => {
  return true
}

// 状态类型映射
const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': 'info',     // 待审核
    '2': 'warning',  // 审核中
    '3': 'success',  // 已通过
    '4': 'danger',   // 已拒绝
    '5': 'info'      // 草稿
  }
  return statusMap[status] || 'info'
}

// 状态文本映射
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': '待审核',
    '2': '审核中',
    '3': '已通过',
    '4': '已拒绝',
    '5': '草稿'
  }
  return statusMap[status] || '-'
}

// 方法
const search = () => {
  searchLoading.value = true
  currentPage.value = 1
  loadData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

const goPage = (page: number) => {
  currentPage.value = page
  loadData()
}

const goPageFirst = (page: number) => {
  currentPage.value = page
  loadData()
}

const add = () => {
  title.value = '新增套餐'
  typeFlag.value = 'add'
  ElMessage.info('新增套餐功能')
}

const showDetail = (row: any) => {
  title.value = '套餐详情'
  typeFlag.value = 'detail'
  selectedRow.value = row
  ElMessage.info('查看套餐详情')
}

const update = (row: any) => {
  title.value = '编辑套餐'
  typeFlag.value = 'edit'
  selectedRow.value = row
  ElMessage.info('编辑套餐功能')
}

const copy = (row: any) => {
  title.value = '复制套餐'
  typeFlag.value = 'copy'
  selectedRow.value = row
  ElMessage.info('复制套餐功能')
}

const deleteItem = (row: any) => {
  selectedRow.value = row
  deleteModal.value = true
}

const cancelDelete = () => {
  deleteModal.value = false
  selectedRow.value = null
}

const confirmDelete = () => {
  deleteLoading.value = true
  setTimeout(() => {
    deleteLoading.value = false
    deleteModal.value = false
    ElMessage.success('删除成功')
    loadData()
  }, 2000)
}

const fuelPackManagement = () => {
  router.push('/channel/fuelPack')
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    console.log('加载套餐数据')

    // 模拟数据
    tableData.value = [
      {
        packageName: '测试套餐',
        packageId: 'PKG001',
        countryName: '中国',
        price: '99.00',
        auditStatus: '3',
        createTime: '2024-01-15 10:30:00'
      },
      {
        packageName: '国际套餐',
        packageId: 'PKG002',
        countryName: '美国',
        price: '199.00',
        auditStatus: '1',
        createTime: '2024-01-16 14:20:00'
      }
    ]
    total.value = 2

    // 模拟国家列表
    countryList.value = [
      { id: 1, mcc: '460', countryEn: '中国' },
      { id: 2, mcc: '310', countryEn: '美国' },
      { id: 3, mcc: '440', countryEn: '日本' }
    ]
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
    searchLoading.value = false
  }
}

// 组件挂载时初始化
onMounted(() => {
  cooperationMode.value = sessionStorage.getItem('cooperationMode') || ''
  corpId.value = sessionStorage.getItem('corpId') || ''
  loadData()
})
</script>

<style scoped>
.package-card {
  margin: 20px;
}

.search-form {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-form :deep(.el-form-item) {
  margin-bottom: 15px;
  margin-right: 20px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.package-table {
  margin-top: 40px;
}

.pagination-container {
  margin-top: 40px;
  text-align: right;
}

.delete-content {
  text-align: center;
  padding: 20px 0;
}

.warning-text {
  color: #e6a23c;
  font-size: 16px;
  margin-bottom: 20px;
}

.delete-footer {
  display: flex;
  justify-content: center;
  gap: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form :deep(.el-form-item) {
    margin-bottom: 15px;
    margin-right: 0;
  }

  .action-buttons {
    flex-direction: column;
  }

  .pagination-container {
    text-align: center;
  }
}
</style>
