"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionType = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var TransactionType;
(function (TransactionType) {
    TransactionType[TransactionType["Award"] = 'Award'] = "Award";
    TransactionType[TransactionType["CashAdvance"] = 'CashAdvance'] = "CashAdvance";
    TransactionType[TransactionType["CompletedDeffered"] = 'CompletedDeffered'] = "CompletedDeffered";
    TransactionType[TransactionType["CompletedReservation"] = 'CompletedReservation'] = "CompletedReservation";
    TransactionType[TransactionType["Credit"] = 'Credit'] = "Credit";
    TransactionType[TransactionType["Debit"] = 'Debit'] = "Debit";
    TransactionType[TransactionType["Declined"] = 'Declined'] = "Declined";
    TransactionType[TransactionType["Failed"] = 'Failed'] = "Failed";
    TransactionType[TransactionType["FirstReservation"] = 'FirstReservation'] = "FirstReservation";
    TransactionType[TransactionType["IssuerInstalment"] = 'IssuerInstalment'] = "IssuerInstalment";
    TransactionType[TransactionType["OneTimeReservation"] = 'OneTimeReservation'] = "OneTimeReservation";
    TransactionType[TransactionType["Rebate"] = 'Rebate'] = "Rebate";
    TransactionType[TransactionType["Redemption"] = 'Redemption'] = "Redemption";
    TransactionType[TransactionType["ReverseAward"] = 'ReverseAward'] = "ReverseAward";
    TransactionType[TransactionType["ReverseCredit"] = 'ReverseCredit'] = "ReverseCredit";
    TransactionType[TransactionType["ReverseDebit"] = 'ReverseDebit'] = "ReverseDebit";
    TransactionType[TransactionType["ReverseRebate"] = 'ReverseRebate'] = "ReverseRebate";
    TransactionType[TransactionType["ReverseRedemption"] = 'ReverseRedemption'] = "ReverseRedemption";
    TransactionType[TransactionType["UpdateReservation"] = 'UpdateReservation'] = "UpdateReservation";
})(TransactionType = exports.TransactionType || (exports.TransactionType = {}));
//# sourceMappingURL=transactionType.js.map