"use strict";
/*
 *                       ######
 *                       ######
 * ############    ####( ######  #####. ######  ############   ############
 * #############  #####( ######  #####. ######  #############  #############
 *        ######  #####( ######  #####. ######  #####  ######  #####  ######
 * ###### ######  #####( ######  #####. ######  #####  #####   #####  ######
 * ###### ######  #####( ######  #####. ######  #####          #####  ######
 * #############  #############  #############  #############  #####  ######
 *  ############   ############  #############   ############  #####  ######
 *                                      ######
 *                               #############
 *                               ############
 * Adyen NodeJS API Library
 * Copyright (c) 2021 Adyen B.V.
 * This file is open source and available under the MIT license.
 * See the LICENSE file for more info.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionConditions = void 0;
/**
 * Terminal API
 * Definition of Terminal API Schema
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
class TransactionConditions {
    static getAttributeTypeMap() {
        return TransactionConditions.attributeTypeMap;
    }
}
exports.TransactionConditions = TransactionConditions;
TransactionConditions.discriminator = undefined;
TransactionConditions.attributeTypeMap = [
    {
        "name": "AcquirerID",
        "baseName": "AcquirerID",
        "type": "Array<string>"
    },
    {
        "name": "AllowedLoyaltyBrand",
        "baseName": "AllowedLoyaltyBrand",
        "type": "Array<string>"
    },
    {
        "name": "AllowedPaymentBrand",
        "baseName": "AllowedPaymentBrand",
        "type": "Array<string>"
    },
    {
        "name": "CustomerLanguage",
        "baseName": "CustomerLanguage",
        "type": "string"
    },
    {
        "name": "DebitPreferredFlag",
        "baseName": "DebitPreferredFlag",
        "type": "boolean"
    },
    {
        "name": "ForceEntryMode",
        "baseName": "ForceEntryMode",
        "type": "Array<TransactionConditions.ForceEntryModeEnum>"
    },
    {
        "name": "ForceOnlineFlag",
        "baseName": "ForceOnlineFlag",
        "type": "boolean"
    },
    {
        "name": "LoyaltyHandling",
        "baseName": "LoyaltyHandling",
        "type": "TransactionConditions.LoyaltyHandlingEnum"
    },
    {
        "name": "MerchantCategoryCode",
        "baseName": "MerchantCategoryCode",
        "type": "string"
    }
];
(function (TransactionConditions) {
    let ForceEntryModeEnum;
    (function (ForceEntryModeEnum) {
        ForceEntryModeEnum[ForceEntryModeEnum["CheckReader"] = 'CheckReader'] = "CheckReader";
        ForceEntryModeEnum[ForceEntryModeEnum["Contactless"] = 'Contactless'] = "Contactless";
        ForceEntryModeEnum[ForceEntryModeEnum["File"] = 'File'] = "File";
        ForceEntryModeEnum[ForceEntryModeEnum["Icc"] = 'ICC'] = "Icc";
        ForceEntryModeEnum[ForceEntryModeEnum["Keyed"] = 'Keyed'] = "Keyed";
        ForceEntryModeEnum[ForceEntryModeEnum["MagStripe"] = 'MagStripe'] = "MagStripe";
        ForceEntryModeEnum[ForceEntryModeEnum["Manual"] = 'Manual'] = "Manual";
        ForceEntryModeEnum[ForceEntryModeEnum["Rfid"] = 'RFID'] = "Rfid";
        ForceEntryModeEnum[ForceEntryModeEnum["Scanned"] = 'Scanned'] = "Scanned";
        ForceEntryModeEnum[ForceEntryModeEnum["SynchronousIcc"] = 'SynchronousICC'] = "SynchronousIcc";
        ForceEntryModeEnum[ForceEntryModeEnum["Tapped"] = 'Tapped'] = "Tapped";
    })(ForceEntryModeEnum = TransactionConditions.ForceEntryModeEnum || (TransactionConditions.ForceEntryModeEnum = {}));
    let LoyaltyHandlingEnum;
    (function (LoyaltyHandlingEnum) {
        LoyaltyHandlingEnum[LoyaltyHandlingEnum["Allowed"] = 'Allowed'] = "Allowed";
        LoyaltyHandlingEnum[LoyaltyHandlingEnum["Forbidden"] = 'Forbidden'] = "Forbidden";
        LoyaltyHandlingEnum[LoyaltyHandlingEnum["Processed"] = 'Processed'] = "Processed";
        LoyaltyHandlingEnum[LoyaltyHandlingEnum["Proposed"] = 'Proposed'] = "Proposed";
        LoyaltyHandlingEnum[LoyaltyHandlingEnum["Required"] = 'Required'] = "Required";
    })(LoyaltyHandlingEnum = TransactionConditions.LoyaltyHandlingEnum || (TransactionConditions.LoyaltyHandlingEnum = {}));
})(TransactionConditions = exports.TransactionConditions || (exports.TransactionConditions = {}));
//# sourceMappingURL=transactionConditions.js.map