<template>
  <!-- 白卡订单管理 -->
  <ContentWrap>
    <el-card>
      <div class="search-container">
        <el-form :model="searchForm" inline>
          <el-form-item label="订单号：">
            <el-input
              v-model="searchForm.orderNo"
              placeholder="请输入订单号"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="ICCID：">
            <el-input
              v-model="searchForm.iccid"
              placeholder="请输入ICCID"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="订单状态：">
            <el-select
              v-model="searchForm.orderStatus"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <el-option :value="1" label="待处理" />
              <el-option :value="2" label="处理中" />
              <el-option :value="3" label="已完成" />
              <el-option :value="4" label="已取消" />
              <el-option :value="5" label="已退款" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间：">
            <el-date-picker
              v-model="searchForm.createDate"
              type="daterange"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="hasPermission('search')"
              type="primary"
              :loading="searchLoading"
              @click="handleSearch"
            >
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button
              v-if="hasPermission('export')"
              type="success"
              :loading="exportLoading"
              @click="handleExport"
            >
              <Icon icon="ep:download" class="mr-5px" />
              导出
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <div style="margin-top: 20px">
        <el-table :data="tableData" v-loading="loading" border>
          <el-table-column prop="orderNo" label="订单号" min-width="180" />
          <el-table-column prop="iccid" label="ICCID" min-width="200" />
          <el-table-column prop="msisdn" label="MSISDN" min-width="150" />
          <el-table-column prop="cardType" label="卡类型" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getCardTypeTag(row?.cardType)" v-if="row">
                {{ getCardTypeText(row?.cardType) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="packageName" label="套餐名称" min-width="150" />
          <el-table-column prop="orderAmount" label="订单金额" min-width="120" align="right">
            <template #default="{ row }">
              {{ row ? (row.orderAmount || 0).toFixed(2) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="actualAmount" label="实付金额" min-width="120" align="right">
            <template #default="{ row }">
              {{ row ? (row.actualAmount || 0).toFixed(2) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="orderStatus" label="订单状态" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getOrderStatusTag(row?.orderStatus)" v-if="row">
                {{ getOrderStatusText(row?.orderStatus) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="paymentMethod" label="支付方式" min-width="100" align="center">
            <template #default="{ row }">
              {{ getPaymentMethodText(row?.paymentMethod) }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="160" />
          <el-table-column prop="completeTime" label="完成时间" min-width="160" />
          <el-table-column label="操作" min-width="200" align="center" fixed="right">
            <template #default="{ row }">
              <div v-if="row" class="action-buttons">
                <el-button
                  v-if="hasPermission('view')"
                  type="primary"
                  size="small"
                  @click="handleView(row)"
                >
                  查看详情
                </el-button>
                <el-button
                  v-if="hasPermission('process') && row.orderStatus === 1"
                  type="success"
                  size="small"
                  @click="handleProcess(row)"
                >
                  处理
                </el-button>
                <el-button
                  v-if="hasPermission('cancel') && [1, 2].includes(row.orderStatus)"
                  type="warning"
                  size="small"
                  @click="handleCancel(row)"
                >
                  取消
                </el-button>
                <el-button
                  v-if="hasPermission('refund') && row.orderStatus === 3"
                  type="danger"
                  size="small"
                  @click="handleRefund(row)"
                >
                  退款
                </el-button>
                <el-button
                  v-if="hasPermission('resend') && row.orderStatus === 3"
                  type="info"
                  size="small"
                  @click="handleResend(row)"
                >
                  重发
                </el-button>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

// 权限检查函数
const hasPermission = (permission: string): boolean => {
  console.log(`🔍 [白卡订单权限检查] ${permission}: 允许访问`)
  return true
}

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const exportLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const searchForm = reactive({
  orderNo: '',
  iccid: '',
  orderStatus: null as number | null,
  createDate: [] as string[]
})

const tableData = ref<any[]>([])

// 方法
const getCardTypeTag = (type: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    NORMAL: 'primary',
    TEST: 'warning',
    VIP: 'success'
  }
  return typeMap[type] || 'info'
}

const getCardTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    NORMAL: '普通卡',
    TEST: '测试卡',
    VIP: 'VIP卡'
  }
  return typeMap[type] || '未知'
}

const getOrderStatusTag = (
  status: number
): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    1: 'warning',
    2: 'primary',
    3: 'success',
    4: 'info',
    5: 'danger'
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '待处理',
    2: '处理中',
    3: '已完成',
    4: '已取消',
    5: '已退款'
  }
  return statusMap[status] || '未知'
}

const getPaymentMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    ALIPAY: '支付宝',
    WECHAT: '微信支付',
    BANK: '银行转账',
    BALANCE: '余额支付',
    CREDIT: '信用支付'
  }
  return methodMap[method] || '未知'
}

const handleSearch = () => {
  currentPage.value = 1
  getTableData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getTableData()
}

const handleExport = async () => {
  try {
    exportLoading.value = true
    // TODO: 实现导出功能
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const handleView = (row: any) => {
  ElMessage.info(`查看订单详情: ${row.orderNo}`)
}

const handleProcess = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要处理订单 ${row.orderNo} 吗？`, '确认处理', {
      type: 'warning'
    })

    ElMessage.success('订单处理成功')
    getTableData()
  } catch (error) {
    // 用户取消操作
  }
}

const handleCancel = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要取消订单 ${row.orderNo} 吗？`, '确认取消', {
      type: 'warning'
    })

    ElMessage.success('订单取消成功')
    getTableData()
  } catch (error) {
    // 用户取消操作
  }
}

const handleRefund = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要退款订单 ${row.orderNo} 吗？退款金额：${row.actualAmount}元`,
      '确认退款',
      {
        type: 'warning'
      }
    )

    ElMessage.success('退款申请已提交')
    getTableData()
  } catch (error) {
    // 用户取消操作
  }
}

const handleResend = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要重发订单 ${row.orderNo} 吗？`, '确认重发', {
      type: 'warning'
    })

    ElMessage.success('重发成功')
    getTableData()
  } catch (error) {
    // 用户取消操作
  }
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        orderNo: 'WC20240101001',
        iccid: '89860000000000000001',
        msisdn: '13800138000',
        cardType: 'NORMAL',
        packageName: '基础套餐A',
        orderAmount: 100.0,
        actualAmount: 95.0,
        orderStatus: 1,
        paymentMethod: 'ALIPAY',
        createTime: '2024-01-01 10:00:00',
        completeTime: null
      },
      {
        id: 2,
        orderNo: 'WC20240101002',
        iccid: '89860000000000000002',
        msisdn: '13900139000',
        cardType: 'VIP',
        packageName: '高级套餐B',
        orderAmount: 200.0,
        actualAmount: 180.0,
        orderStatus: 3,
        paymentMethod: 'WECHAT',
        createTime: '2024-01-01 09:00:00',
        completeTime: '2024-01-01 10:30:00'
      }
    ]
    total.value = 2
  } catch (error) {
    ElMessage.error('获取白卡订单数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.search-container {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: center;
}

.action-buttons .el-button {
  margin: 2px;
}
</style>
