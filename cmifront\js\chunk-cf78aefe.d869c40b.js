(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cf78aefe"],{3121:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("Card",{staticStyle:{width:"100%",padiing:"16px"}},[t("div",{staticStyle:{padding:"0 16px"}},[t("Form",{ref:"searchObj",staticClass:"box",attrs:{model:e.searchObj}},[t("FormItem",[t("Input",{staticClass:"recordBtnSty",attrs:{placeholder:"请输入ICCID",clearable:""},model:{value:e.searchObj.iccid,callback:function(t){e.$set(e.searchObj,"iccid",t)},expression:"searchObj.iccid"}})],1),t("FormItem",[t("Input",{staticClass:"recordBtnSty",attrs:{placeholder:"请输入订单ID",clearable:""},model:{value:e.searchObj.orderUniqueId,callback:function(t){e.$set(e.searchObj,"orderUniqueId",t)},expression:"searchObj.orderUniqueId"}})],1),t("FormItem",[t("DatePicker",{staticClass:"recordBtnSty",attrs:{type:"month",format:"yyyy-MM",placeholder:"选择月份",clearable:""},on:{"on-change":e.selectTime}})],1),t("FormItem",[t("Select",{staticClass:"recordBtnSty",attrs:{placeholder:"选择订单状态",clearable:""},model:{value:e.searchObj.orderStatus,callback:function(t){e.$set(e.searchObj,"orderStatus",t)},expression:"searchObj.orderStatus"}},e._l(e.orderStatusList,(function(a){return t("Option",{key:a.value,attrs:{value:a.value}},[e._v("\n\t\t\t\t\t\t"+e._s(a.label)+"\n\t\t\t\t\t")])})),1)],1),t("FormItem",[t("Select",{staticClass:"recordBtnSty",attrs:{placeholder:"选择渠道商",filterable:"",clearable:""},on:{"on-change":e.selectChannel},model:{value:e.searchObj.corpId,callback:function(t){e.$set(e.searchObj,"corpId",t)},expression:"searchObj.corpId"}},e._l(e.corpList,(function(a){return t("Option",{key:a.corpId,attrs:{value:a.corpId}},[e._v(e._s(a.corpName)+"\n\t\t\t\t\t")])})),1)],1),t("FormItem",[t("Button",{directives:[{name:"has",rawName:"v-has",value:"search",expression:"'search'"}],attrs:{type:"primary",icon:"md-search",loading:e.searchLoading},on:{click:function(t){return e.getPageFrist(0)}}},[e._v(" 查询")])],1),t("FormItem",[t("Button",{directives:[{name:"has",rawName:"v-has",value:"orderRecord",expression:"'orderRecord'"}],attrs:{type:"success"},on:{click:e.exportOrderRecord}},[e._v(" 导出订单记录")])],1)],1),t("div",{staticStyle:{margin:"20px 0"}},[t("Table",{attrs:{columns:e.Columns,data:e.tableData,ellipsis:!0,loading:e.tableLoading},scopedSlots:e._u([{key:"orderInfo",fn:function(a){var r=a.row;a.index;return[t("a",{staticStyle:{color:"#55aaff"},on:{click:function(t){return e.showOrderInfo(r.orderId)}}},[e._v("查看详情")])]}},{key:"action",fn:function(a){var r=a.row;a.index;return["2"==r.orderStatus&&"2"==r.orderType||("1"==r.orderStatus||"2"==r.orderStatus)&&"3"==r.orderType||"2"==r.orderStatus&&"7"==r.orderType?t("Button",{directives:[{name:"has",rawName:"v-has",value:"unsubscribe",expression:"'unsubscribe'"}],staticClass:"actionstyle",attrs:{size:"small",type:"error"},on:{click:function(t){return e.unsubscribeAll(r)}}},[e._v("退订")]):e._e(),"4"==r.orderStatus?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticClass:"actionstyle",attrs:{type:"info",size:"small"},on:{click:function(t){return e.examine(r.orderId,"2")}}},[e._v("通过")]):e._e(),"4"==r.orderStatus?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticClass:"actionstyle",attrs:{type:"primary",size:"small"},on:{click:function(t){return e.examine(r.orderId,"3")}}},[e._v("不通过")]):e._e()]}}])}),t("Page",{staticStyle:{margin:"15px 0"},attrs:{total:e.total,"page-size":e.pageSize,current:e.page,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.page=t},"on-change":e.getPageFrist}})],1)],1),t("Modal",{attrs:{title:"子订单详情记录","footer-hide":!0,"mask-closable":!1,width:"1200px"},model:{value:e.packageRecordDetailsFlage,callback:function(t){e.packageRecordDetailsFlage=t},expression:"packageRecordDetailsFlage"}},[t("div",{staticStyle:{margin:"20px 0"}},[t("Table",{attrs:{columns:e.packageDetailsColumns,data:e.packageDetailsTableData,ellipsis:!0,loading:e.packageTableDetailsLoading},scopedSlots:e._u([{key:"action1",fn:function(a){var r=a.row;a.index;return["2"==r.orderStatus&&"2"==r.orderType||("1"==r.orderStatus||"2"==r.orderStatus)&&"3"==r.orderType&&"7"!=r.orderType?t("Button",{directives:[{name:"has",rawName:"v-has",value:"unsubscribe",expression:"'unsubscribe'"}],attrs:{size:"small",type:"error"},on:{click:function(t){return e.unsubscribeModal(r.id)}}},[e._v("退订")]):e._e(),"4"==r.orderStatus&&"7"!=r.orderType?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"info",size:"small"},on:{click:function(t){return e.examineModal(r.id,"2")}}},[e._v("通过")]):e._e(),"4"==r.orderStatus&&"7"!=r.orderType?t("Button",{directives:[{name:"has",rawName:"v-has",value:"check",expression:"'check'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.examineModal(r.id,"3")}}},[e._v("不通过")]):e._e()]}}])}),t("Page",{staticStyle:{margin:"15px 0"},attrs:{total:e.packageDetailsTotal,"page-size":e.packageDetailsPageSize,current:e.packageDetailsPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.packageDetailsPage=t},"on-change":e.getPurchaseRecordsDetails}})],1)])],1)},i=[],c=(a("d3b7"),a("3ca3"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("88a7"),a("271a"),a("5494"),a("66df")),n="/cms/channelOrder",s=function(e){return c["a"].request({url:"/cms/channel/getChannelList",params:e,method:"get"})},o=function(e){return c["a"].request({url:n+"/orderPages",params:e,method:"get"})},l=function(e){return c["a"].request({url:n+"/orderPages/export",params:e,method:"GET",responseType:"blob"})},d=function(e){return c["a"].request({url:n+"/unsubscribe/".concat(e),method:"POST"})},u=function(e){return c["a"].request({url:n+"/audit",data:e,method:"post"})},h=function(e){return c["a"].request({url:n+"/orderDetailsByOrderId",params:e,method:"get"})},p=function(e){return c["a"].request({url:n+"/detail/unsubscribe/".concat(e),method:"POST"})},g=function(e){return c["a"].request({url:n+"/detail/audit",data:e,method:"post"})},b={components:{},data:function(){return{tableLoading:!1,searchLoading:!1,packageTableDetailsLoading:!1,packageRecordDetailsFlage:!1,searchObj:{iccid:"",orderUniqueId:"",searchMonth:"",orderStatus:"",corpId:""},orderStatusList:[{label:"待发货",value:"1"},{label:"已完成",value:"2"},{label:"已退订",value:"3"},{label:"激活退订待审批",value:"4"},{label:"部分退订",value:"5"},{label:"部分发货",value:"6"},{label:"已回收",value:"7"},{label:"部分回收",value:"8"},{label:"复合状态",value:"9"}],tableData:[],Columns:[],packageDetailsTableData:[],packageDetailsColumns:[{title:"订单编号",key:"id",align:"center",minWidth:150,tooltip:!0},{title:"ICCID",key:"iccid",align:"center",minWidth:150,tooltip:!0},{title:"购买套餐",key:"nameEn",align:"center",minWidth:150,tooltip:!0},{title:"订单状态",key:"orderStatus",minWidth:150,align:"center",render:function(e,t){var a=t.row,r="";switch(a.orderStatus){case"1":r="待发货";break;case"2":r="已完成";break;case"3":r="已退订/已回滚";break;case"4":r="激活退订待审批";break;case"5":r="已回收";break;default:r="未知状态"}return e("label",r)}},{title:"金额",key:"amount",align:"center",minWidth:100,tooltip:!0,render:function(e,t){return e("span",t.row.amount)}},{title:"币种",key:"currencyCode",align:"center",minWidth:100,tooltip:!0,render:function(e,t){var a=t.row,r="156"==a.currencyCode?"人民币":"840"==a.currencyCode?"美元":"344"==a.currencyCode?"港币":"获取失败";return e("label",r)}},{title:"操作",slot:"action1",width:200,fixed:"right",align:"center"}],corpList:[],total:0,pageSize:10,page:1,packageDetailsTotal:0,packageDetailsPageSize:10,packageDetailsPage:1}},methods:{loadColumns:function(){var e=this.$i18n.locale,t={"zh-CN":"packageName","en-US":"nameEn"},a=[{title:"订单详情",width:100,slot:"orderInfo",align:"center"},{title:"渠道商",key:"user",align:"center",minWidth:150,tooltip:!0},{title:"订单日期",key:"orderDate",align:"center",minWidth:150,tooltip:!0},{title:"订单编号",key:"orderUniqueId",align:"center",minWidth:170,tooltip:!0},{title:"ICCID",key:"iccid",align:"center",minWidth:170,tooltip:!0},{title:"购买套餐",key:t[e],align:"center",minWidth:150,tooltip:!0},{title:"购买渠道",key:"orderChannel",align:"center",minWidth:150,tooltip:!0},{title:"订单状态",key:"orderStatus",align:"center",minWidth:150,tooltip:!0,render:function(e,t){var a=t.row,r="";switch(a.orderStatus){case"1":r="待发货";break;case"2":r="已完成";break;case"3":r="已退订";break;case"4":r="激活退订待审批";break;case"5":r="部分退订";break;case"6":r="部分发货";break;case"7":r="已回收";break;case"8":r="部分回收";break;case"9":r="复合状态";break;default:r="未知状态"}return e("label",r)}},{title:"购买份数",key:"count",align:"center",minWidth:150,tooltip:!0},{title:"金额",key:"amount",align:"center",minWidth:100,tooltip:!0,render:function(e,t){return e("span",t.row.amount)}},{title:"币种",key:"currencyCode",align:"center",minWidth:100,tooltip:!0,render:function(e,t){var a=t.row,r="156"==a.currencyCode?"人民币":"840"==a.currencyCode?"美元":"344"==a.currencyCode?"港币":"--";return e("label",r)}},{title:"操作",slot:"action",width:200,fixed:"right",align:"center"}];this.Columns=a},getPageFrist:function(e){var t=this;if(this.page=e,0===e&&(this.page=1),!(this.searchObj.corpId||this.searchObj.iccid||this.searchObj.orderUniqueId||this.searchObj.orderStatus||this.searchObj.searchMonth))return this.$Notice.warning({title:"操作提示",desc:"至少选择一项搜索条件！"}),this.tableData=[],void(this.total=0);this.tableLoading=!0,this.searchLoading=!0,o({orderUserId:this.searchObj.corpId,pageNumber:e,pageSize:10,iccid:this.searchObj.iccid,orderUniqueId:this.searchObj.orderUniqueId,orderStatus:this.searchObj.orderStatus,startTime:this.searchObj.searchMonth}).then((function(e){if("0000"!==e.code)throw e;t.tableData=e.data.records,t.total=e.data.totalCount,t.searchLoading=!1,t.tableLoading=!1})).catch((function(e){t.searchLoading=!1,t.tableLoading=!1}))},selectTime:function(e){this.searchObj.searchMonth=e},selectChannel:function(){var e=this;s().then((function(t){"0000"==t.code&&(e.corpList=t.data)})).catch((function(e){console.error(e)}))},unsubscribeAll:function(e){var t=this,a=e.orderId;this.$Modal.confirm({title:"确认全部退订？",onOk:function(){d(a).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),t.getPageFrist(0)})).catch((function(e){}))}})},examine:function(e,t){var a=this;this.$Modal.confirm({title:"2"==t?"确认执行通过操作？":"确认执行不通过操作？",onOk:function(){u({id:e,status:t}).then((function(e){if(!e||"0000"!=e.code)throw e;a.$Notice.success({title:"操作提示",desc:"操作成功"}),a.getPageFrist(a.page)})).catch((function(e){}))}})},exportOrderRecord:function(){var e=this;this.searchObj.corpId||this.searchObj.iccid||this.searchObj.orderUniqueId||this.searchObj.orderStatus||this.searchObj.searchMonth?l({startTime:this.searchObj.searchMonth,orderUserId:this.searchObj.corpId,iccid:this.searchObj.iccid,orderUniqueId:this.searchObj.orderUniqueId,orderStatus:this.searchObj.orderStatus,pageNumber:-1,pageSize:-1}).then((function(t){t&&e.fileDownload("订单记录","xlsx",t.data)})):this.$Notice.warning({title:"操作提示",desc:"至少选择一项搜索条件进行导出！"})},fileDownload:function(e,t,a){var r=a,i=e+"."+t;if("download"in document.createElement("a")){var c=document.createElement("a"),n=URL.createObjectURL(r);c.download=i,c.href=n,c.click(),URL.revokeObjectURL(n)}else navigator.msSaveBlob(r,i);this.$Notice.success({title:"操作提示",desc:"操作成功"})},showOrderInfo:function(e){this.packageRecordDetailsFlage=!0,this.recordDetailId=e,this.getPurchaseRecordsDetails(0,e)},getPurchaseRecordsDetails:function(e,t){var a=this;0===e&&(this.packageDetailsPage=1),this.packageTableDetailsLoading=!0,h({orderId:t||this.recordDetailId,pageNumber:e,pageSize:10}).then((function(e){"0000"===e.code&&(a.packageDetailsTableData=e.data.records,a.packageDetailsTotal=e.data.totalCount),a.packageTableDetailsLoading=!1,a.packageRecordDetailsFlage=!0}))},unsubscribeModal:function(e){var t=this;this.$Modal.confirm({title:"确认退订？",onOk:function(){p(e).then((function(e){if(!e||"0000"!=e.code)throw e;t.$Notice.success({title:"操作提示",desc:"操作成功"}),1===t.packageDetailsTableData.length?(t.packageDetailsTableData=[],t.packageRecordDetailsFlage=!1,t.getPageFrist(0)):t.getPurchaseRecordsDetails(0,t.recordDetailId)})).catch((function(e){}))}})},examineModal:function(e,t){var a=this;this.$Modal.confirm({title:"2"==t?"确认执行通过操作？":"确认执行不通过操作？",onOk:function(){g({id:e,status:t}).then((function(e){if(!e||"0000"!=e.code)throw e;a.$Notice.success({title:"操作提示",desc:"操作成功"}),a.getPurchaseRecordsDetails(0)})).catch((function(e){}))}})}},mounted:function(){this.loadColumns(),this.selectChannel()}},m=b,f=(a("887e"),a("2877")),k=Object(f["a"])(m,r,i,!1,null,null,null);t["default"]=k.exports},"887e":function(e,t,a){"use strict";a("b30b")},b30b:function(e,t,a){}}]);