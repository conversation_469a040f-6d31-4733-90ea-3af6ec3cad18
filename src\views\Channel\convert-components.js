#!/usr/bin/env node

/**
 * Element Plus 组件名称转换脚本
 * 将 el- 前缀的小写+连字符格式转换为 El 前缀的大写驼峰格式
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 组件名称映射表
const componentMap = {
  'el-card': 'ElCard',
  'el-form': 'ElForm',
  'el-form-item': 'ElFormItem',
  'el-input': 'ElInput',
  'el-select': 'ElSelect',
  'el-option': 'ElOption',
  'el-button': 'ElButton',
  'el-table': 'ElTable',
  'el-table-column': 'ElTableColumn',
  'el-tag': 'ElTag',
  'el-pagination': 'ElPagination',
  'el-dialog': 'ElDialog',
  'el-date-picker': 'ElDatePicker',
  'el-input-number': 'ElInputNumber',
  'el-divider': 'ElDivider',
  'el-alert': 'ElAlert',
  'el-checkbox': 'ElCheckbox',
  'el-checkbox-group': 'ElCheckboxGroup',
  'el-radio': 'ElRadio',
  'el-radio-group': 'ElRadioGroup',
  'el-switch': 'ElSwitch',
  'el-slider': 'ElSlider',
  'el-time-picker': 'ElTimePicker',
  'el-time-select': 'ElTimeSelect',
  'el-cascader': 'ElCascader',
  'el-color-picker': 'ElColorPicker',
  'el-transfer': 'ElTransfer',
  'el-container': 'ElContainer',
  'el-header': 'ElHeader',
  'el-aside': 'ElAside',
  'el-main': 'ElMain',
  'el-footer': 'ElFooter',
  'el-timeline': 'ElTimeline',
  'el-timeline-item': 'ElTimelineItem',
  'el-link': 'ElLink',
  'el-radio-button': 'ElRadioButton',
  'el-checkbox-button': 'ElCheckboxButton',
  'el-steps': 'ElSteps',
  'el-step': 'ElStep',
  'el-menu': 'ElMenu',
  'el-submenu': 'ElSubmenu',
  'el-menu-item': 'ElMenuItem',
  'el-menu-item-group': 'ElMenuItemGroup',
  'el-tabs': 'ElTabs',
  'el-tab-pane': 'ElTabPane',
  'el-breadcrumb': 'ElBreadcrumb',
  'el-breadcrumb-item': 'ElBreadcrumbItem',
  'el-page-header': 'ElPageHeader',
  'el-dropdown': 'ElDropdown',
  'el-dropdown-menu': 'ElDropdownMenu',
  'el-dropdown-item': 'ElDropdownItem',
  'el-avatar': 'ElAvatar',
  'el-empty': 'ElEmpty',
  'el-descriptions': 'ElDescriptions',
  'el-descriptions-item': 'ElDescriptionsItem',
  'el-result': 'ElResult',
  'el-statistic': 'ElStatistic',
  'el-affix': 'ElAffix',
  'el-anchor': 'ElAnchor',
  'el-anchor-link': 'ElAnchorLink',
  'el-backtop': 'ElBacktop',
  'el-badge': 'ElBadge',
  'el-calendar': 'ElCalendar',
  'el-image': 'ElImage',
  'el-infinite-scroll': 'ElInfiniteScroll',
  'el-progress': 'ElProgress',
  'el-skeleton': 'ElSkeleton',
  'el-skeleton-item': 'ElSkeletonItem',
  'el-tree': 'ElTree',
  'el-tree-select': 'ElTreeSelect',
  'el-upload': 'ElUpload',
  'el-collapse': 'ElCollapse',
  'el-collapse-item': 'ElCollapseItem',
  'el-tooltip': 'ElTooltip',
  'el-popover': 'ElPopover',
  'el-popconfirm': 'ElPopconfirm',
  'el-carousel': 'ElCarousel',
  'el-carousel-item': 'ElCarouselItem',
  'el-row': 'ElRow',
  'el-col': 'ElCol',
  'el-space': 'ElSpace',
  'el-auto-complete': 'ElAutoComplete',
  'el-rate': 'ElRate',
  'el-drawer': 'ElDrawer'
};

/**
 * 转换单个文件
 * @param {string} filePath 文件路径
 */
function convertFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // 转换开始标签和结束标签
    Object.entries(componentMap).forEach(([oldName, newName]) => {
      // 转换开始标签
      const startTagRegex = new RegExp(`<${oldName}(\\s|>)`, 'g');
      if (startTagRegex.test(content)) {
        content = content.replace(startTagRegex, `<${newName}$1`);
        hasChanges = true;
      }

      // 转换结束标签
      const endTagRegex = new RegExp(`</${oldName}>`, 'g');
      if (endTagRegex.test(content)) {
        content = content.replace(endTagRegex, `</${newName}>`);
        hasChanges = true;
      }
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ 已转换: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  无需转换: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 转换失败: ${filePath}`, error.message);
    return false;
  }
}

/**
 * 递归处理目录
 * @param {string} dirPath 目录路径
 */
function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);
  let totalFiles = 0;
  let convertedFiles = 0;

  items.forEach(item => {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      // 递归处理子目录
      const result = processDirectory(fullPath);
      totalFiles += result.total;
      convertedFiles += result.converted;
    } else if (item.endsWith('.vue')) {
      // 处理 Vue 文件
      totalFiles++;
      if (convertFile(fullPath)) {
        convertedFiles++;
      }
    }
  });

  return { total: totalFiles, converted: convertedFiles };
}

// 主函数
function main() {
  const channelDir = path.join(__dirname);
  
  console.log('🚀 开始转换 Element Plus 组件名称...');
  console.log(`📁 目标目录: ${channelDir}`);
  console.log('');

  const result = processDirectory(channelDir);

  console.log('');
  console.log('📊 转换完成统计:');
  console.log(`   总文件数: ${result.total}`);
  console.log(`   已转换: ${result.converted}`);
  console.log(`   无需转换: ${result.total - result.converted}`);
  console.log('');
  console.log('🎉 转换完成！');
}

// 运行脚本
main();

export { convertFile, processDirectory, componentMap };
