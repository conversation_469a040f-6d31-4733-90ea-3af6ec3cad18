<template>
  <div class="password-management">
    <ContentWrap>
      <div class="page-header">
        <h2>账户密码管理</h2>
        <p>管理系统用户密码策略和密码重置</p>
      </div>

      <!-- 搜索区域 -->
      <div class="search-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="用户名">
            <el-input
              v-model="searchForm.username"
              placeholder="请输入用户名"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="密码状态">
            <el-select
              v-model="searchForm.passwordStatus"
              placeholder="请选择密码状态"
              clearable
              style="width: 150px"
            >
              <el-option label="正常" value="normal" />
              <el-option label="即将过期" value="expiring" />
              <el-option label="已过期" value="expired" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <Icon icon="vi-ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button @click="handleReset">
              <Icon icon="vi-ep:refresh" class="mr-5px" />
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <el-button type="primary" @click="handleBatchReset" :disabled="!selectedIds.length">
          <Icon icon="vi-ep:refresh-right" class="mr-5px" />
          批量重置密码
        </el-button>
        <el-button type="warning" @click="handlePasswordPolicy">
          <Icon icon="vi-ep:setting" class="mr-5px" />
          密码策略设置
        </el-button>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <el-table
          v-loading="loading"
          :data="tableData"
          @selection-change="handleSelectionChange"
          stripe
          border
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="username" label="用户名" min-width="120" />
          <el-table-column prop="email" label="邮箱" min-width="180" />
          <el-table-column prop="lastPasswordChange" label="上次密码修改" width="160" />
          <el-table-column prop="passwordExpiry" label="密码过期时间" width="160" />
          <el-table-column prop="passwordStatus" label="密码状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getPasswordStatusType(row.passwordStatus)">
                {{ getPasswordStatusText(row.passwordStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="loginAttempts" label="登录尝试次数" width="120" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleResetPassword(row)">
                重置密码
              </el-button>
              <el-button type="warning" size="small" @click="handleUnlock(row)" v-if="row.isLocked">
                解锁账户
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

// 搜索表单
const searchForm = reactive({
  username: '',
  passwordStatus: ''
})

// 表格数据
const loading = ref(false)
const tableData = ref([
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    lastPasswordChange: '2024-01-01 10:00:00',
    passwordExpiry: '2024-04-01 10:00:00',
    passwordStatus: 'normal',
    loginAttempts: 0,
    isLocked: false
  },
  {
    id: 2,
    username: 'manager',
    email: '<EMAIL>',
    lastPasswordChange: '2023-12-01 10:00:00',
    passwordExpiry: '2024-03-01 10:00:00',
    passwordStatus: 'expiring',
    loginAttempts: 2,
    isLocked: false
  }
])

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 选中的行
const selectedIds = ref<number[]>([])

// 密码状态类型
const getPasswordStatusType = (status: string) => {
  switch (status) {
    case 'normal': return 'success'
    case 'expiring': return 'warning'
    case 'expired': return 'danger'
    default: return 'info'
  }
}

// 密码状态文本
const getPasswordStatusText = (status: string) => {
  switch (status) {
    case 'normal': return '正常'
    case 'expiring': return '即将过期'
    case 'expired': return '已过期'
    default: return '未知'
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadTableData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    passwordStatus: ''
  })
  handleSearch()
}

// 重置密码
const handleResetPassword = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要重置用户 ${row.username} 的密码吗？`, '提示', {
      type: 'warning'
    })
    // 调用重置密码接口
    ElMessage.success('密码重置成功，新密码已发送到用户邮箱')
    loadTableData()
  } catch (error) {
    // 用户取消操作
  }
}

// 批量重置密码
const handleBatchReset = async () => {
  try {
    await ElMessageBox.confirm(`确定要重置选中的 ${selectedIds.value.length} 个用户的密码吗？`, '提示', {
      type: 'warning'
    })
    // 调用批量重置密码接口
    ElMessage.success('批量重置密码成功')
    selectedIds.value = []
    loadTableData()
  } catch (error) {
    // 用户取消操作
  }
}

// 解锁账户
const handleUnlock = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要解锁用户 ${row.username} 的账户吗？`, '提示', {
      type: 'warning'
    })
    // 调用解锁账户接口
    ElMessage.success('账户解锁成功')
    loadTableData()
  } catch (error) {
    // 用户取消操作
  }
}

// 密码策略设置
const handlePasswordPolicy = () => {
  ElMessage.info('密码策略设置功能开发中...')
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedIds.value = selection.map(item => item.id)
}

// 分页变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  loadTableData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadTableData()
}

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    // 调用查询接口
    await new Promise(resolve => setTimeout(resolve, 500)) // 模拟接口调用
    pagination.total = tableData.value.length
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadTableData()
})
</script>

<style scoped>
.password-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.page-header p {
  color: #606266;
  margin: 0;
}

.search-section {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-section {
  margin-bottom: 20px;
}

.table-section {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-section {
  margin-top: 20px;
  text-align: right;
}
</style>
