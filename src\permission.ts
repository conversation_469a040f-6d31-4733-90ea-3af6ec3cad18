import router from './router'
import { useAppStoreWithOut } from '@/store/modules/app'
import type { RouteRecordRaw } from 'vue-router'
import { useTitle } from '@/hooks/web/useTitle'
import { useNProgress } from '@/hooks/web/useNProgress'
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import { usePageLoading } from '@/hooks/web/usePageLoading'
import { NO_REDIRECT_WHITE_LIST } from '@/constants'
import { useUserStoreWithOut } from '@/store/modules/user'

const { start, done } = useNProgress()

const { loadStart, loadDone } = usePageLoading()

router.beforeEach(async (to, from, next) => {
  start()
  loadStart()
  const permissionStore = usePermissionStoreWithOut()
  const appStore = useAppStoreWithOut()
  const userStore = useUserStoreWithOut()
  if (userStore.getUserInfo) {
    if (to.path === '/login') {
      // 已登录用户访问登录页，重定向到首页
      const userPermissions = userStore.getPermissions || []
      if (userPermissions.includes('home')) {
        next({ path: '/dashboard/analysis' })
      } else {
        next({ path: '/personal/personal-center' })
      }
    } else if (to.path === '/') {
      // 处理根路径访问
      const userPermissions = userStore.getPermissions || []
      if (userPermissions.includes('home')) {
        next({ path: '/dashboard/analysis' })
      } else {
        next({ path: '/personal/personal-center' })
      }
    } else {
      if (permissionStore.getIsAddRouters) {
        next()
        return
      }

      // CMI 项目使用服务端动态路由配置
      const roleRouters = userStore.getRoleRouters || []
      console.log('🔍 [路由守卫] 用户路由配置:', roleRouters)
      console.log('🔍 [路由守卫] 是否启用动态路由:', appStore.getDynamicRouter)

      // 是否使用动态路由
      if (appStore.getDynamicRouter) {
        // CMI 项目：roleRouters 包含的是已生成的路由配置，直接使用服务端模式
        console.log('🔍 [路由守卫] 使用服务端动态路由生成')
        await permissionStore.generateRoutes('server', roleRouters as AppCustomRouteRecordRaw[])
      } else {
        console.log('🔍 [路由守卫] 使用静态路由生成')
        await permissionStore.generateRoutes('static')
      }

      const addRouters = permissionStore.getAddRouters
      console.log('🔍 [路由守卫] 生成的路由数量:', addRouters.length)
      console.log('🔍 [路由守卫] 生成的路由列表:', addRouters.map(r => ({ path: r.path, name: r.name })))

      addRouters.forEach((route) => {
        router.addRoute(route as unknown as RouteRecordRaw) // 动态添加可访问路由表
      })
      const redirectPath = from.query.redirect || to.path
      const redirect = decodeURIComponent(redirectPath as string)
      const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect }
      permissionStore.setIsAddRouters(true)
      next(nextData)
    }
  } else {
    if (NO_REDIRECT_WHITE_LIST.indexOf(to.path) !== -1) {
      next()
    } else {
      next(`/login?redirect=${to.path}`) // 否则全部重定向到登录页
    }
  }
})

router.afterEach((to) => {
  useTitle(to?.meta?.title as string)
  done() // 结束Progress
  loadDone()
})
