/**
 * CMI 项目配置文件
 * 从原 cmi-web 项目的 src/config/index.js 迁移而来
 */

export interface CmiConfig {
  /**
   * @description 配置显示在浏览器标签的title
   */
  title: string
  /**
   * @description token在Cookie中存储的天数，默认1 单位：天
   */
  cookieExpires: number
  /**
   * @description 是否使用国际化，默认为false
   *              如果不使用，则需要在路由中给需要在菜单中展示的路由设置meta: {title: 'xxx'}
   *              用来在菜单中显示文字
   */
  useI18n: boolean
  /**
   * @description api请求基础路径
   */
  baseUrl: {
    // 开发环境接口前缀
    dev: string
    // 生产环境接口前缀
    pro: string
  }
  /**
   * @description 默认打开的首页的路由name值，默认为home
   */
  homeName: string
  /**
   * @description 需要加载的插件
   */
  plugin: {
    'error-store': {
      showInHeader: boolean // 设为false后不会在顶部显示错误日志徽标
      developmentOff: boolean // 设为true后在开发环境不会收集错误信息，方便开发中排查错误
    }
  }
  /**
   * @description 客户短信类型配置
   */
  customerSMSType: {
    code: string
    iccid: string
  }
  /**
   * @description HSS开户信息，默认值ClearKey
   */
  keytype: string
}

// 从环境变量获取配置值
const getEnvConfig = () => {
  return {
    title: import.meta.env.VITE_APP_TITLE || 'CMI',
    cookieExpires: Number(import.meta.env.VITE_COOKIE_EXPIRES) || 1,
    useI18n: import.meta.env.VITE_USE_I18N === 'true',
    homeName: import.meta.env.VITE_HOME_NAME || 'home',
    keytype: import.meta.env.VITE_KEY_TYPE || 'ClearKey'
  }
}

export const cmiConfig: CmiConfig = {
  ...getEnvConfig(),
  baseUrl: {
    // 开发环境接口前缀
    dev: '/cmiweb',
    // 生产环境接口前缀
    pro: '/api/'
  },
  plugin: {
    'error-store': {
      showInHeader: true, // 设为false后不会在顶部显示错误日志徽标
      developmentOff: true // 设为true后在开发环境不会收集错误信息，方便开发中排查错误
    }
  },
  customerSMSType: {
    code: '{code}',
    iccid: '{iccid}'
  }
}

export default cmiConfig
