<template>
  <!-- 订单管理 -->
  <ContentWrap>
    <ElCard>
      <div style="margin-top: 20px">
        <div class="search-form">
          <ElForm :model="searchForm" inline label-width="100px">
            <ElFormItem label="套餐名称：">
              <ElInput
                v-model="searchForm.packageName"
                placeholder="请输入套餐名称"
                clearable
                style="width: 200px"
              />
            </ElFormItem>
            <ElFormItem label="号码：">
              <ElInput
                v-model="searchForm.iccid"
                placeholder="请选择号码"
                clearable
                style="width: 200px"
              />
            </ElFormItem>
            <ElFormItem label="时间段：">
              <ElDatePicker
                v-model="searchForm.timeSlot"
                type="daterange"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px"
              />
            </ElFormItem>
            <ElFormItem label="归属渠道：">
              <ElSelect
                v-model="searchForm.attributableChannel"
                placeholder="请选择归属渠道"
                clearable
                style="width: 200px"
              >
                <ElOption
                  v-for="item in channelList"
                  :key="item.corpId"
                  :label="item.corpName"
                  :value="item.corpId"
                />
              </ElSelect>
            </ElFormItem>
          </ElForm>

          <div class="search-buttons">
            <ElButton v-if="hasPermission('view')" type="success" @click="showBill">
              月账单
            </ElButton>
            <ElButton
              v-if="hasPermission('search')"
              type="primary"
              :loading="searchLoading"
              @click="handleSearch"
            >
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </ElButton>
            <ElButton
              v-if="hasPermission('export')"
              type="success"
              :loading="exportLoading"
              @click="exportData"
            >
              <Icon icon="ep:download" class="mr-5px" />
              导出
            </ElButton>
            <ElButton
              v-if="hasPermission('trafficDetails')"
              type="info"
              @click="getTrafficDetails"
            >
              <Icon icon="ep:view" class="mr-5px" />
              流量详情
            </ElButton>
          </div>
        </div>

        <!-- 表格 -->
        <div style="margin-top: 20px">
          <ElTable :data="tableData" v-loading="loading" border>
            <ElTableColumn prop="orderNo" label="订单号" min-width="150" />
            <ElTableColumn prop="packageName" label="套餐名称" min-width="150" />
            <ElTableColumn prop="iccid" label="ICCID" min-width="180" />
            <ElTableColumn prop="msisdn" label="MSISDN" min-width="120" />
            <ElTableColumn prop="orderType" label="订单类型" min-width="100" align="center">
              <template #default="scope">
                <ElTag :type="getOrderTypeTag(scope.row.orderType)">
                  {{ getOrderTypeText(scope.row.orderType) }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="amount" label="金额" min-width="100" align="right" />
            <ElTableColumn prop="status" label="状态" min-width="100" align="center">
              <template #default="scope">
                <ElTag :type="getStatusTag(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="createTime" label="创建时间" min-width="160" />
            <ElTableColumn prop="attributableChannel" label="归属渠道" min-width="120" />
            <ElTableColumn label="操作" min-width="120" align="center" fixed="right">
              <template #default="scope">
                <ElButton
                  v-if="!scope.row.isUsed && scope.row.orderType !== '7'"
                  type="danger"
                  size="small"
                  @click="handleUnsubscribe(scope.row)"
                >
                  退订
                </ElButton>
                <span v-else>-</span>
              </template>
            </ElTableColumn>
          </ElTable>
        </div>

        <!-- 分页 -->
        <div style="margin-top: 20px; text-align: right">
          <ElPagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </ElCard>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { useRouter } from 'vue-router'

// 权限检查函数 - 临时返回true以确保页面正常显示
const hasPermission = (permission: string): boolean => {
  console.log(`🔍 [订单管理权限检查] ${permission}: 允许访问`)
  return true // 临时返回true，后续需要实现真实的权限检查逻辑
}

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const exportLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const searchForm = reactive({
  packageName: '',
  iccid: '',
  timeSlot: [] as string[],
  attributableChannel: ''
})

const tableData = ref<any[]>([])
const channelList = ref<any[]>([])

// 方法
const getOrderTypeTag = (type: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '1': 'primary',
    '2': 'success',
    '3': 'warning',
    '7': 'info'
  }
  return typeMap[type] || 'info'
}

const getOrderTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    '1': '套餐订购',
    '2': '流量充值',
    '3': '套餐变更',
    '7': '其他'
  }
  return typeMap[type] || '未知'
}

const getStatusTag = (status: string): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<string, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    '1': 'success',
    '2': 'warning',
    '3': 'danger',
    '0': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '1': '已完成',
    '2': '处理中',
    '3': '失败',
    '0': '待处理'
  }
  return statusMap[status] || '未知'
}

const handleSearch = () => {
  currentPage.value = 1
  getTableData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getTableData()
}

const showBill = () => {
  // 跳转到月账单页面
  router.push({
    path: '/newcmi/channel/order/bill'
  })
}

const exportData = async () => {
  try {
    exportLoading.value = true
    // TODO: 实现导出功能
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const getTrafficDetails = () => {
  // 跳转到流量详情页面
  router.push({
    path: '/newcmi/channel/order/traffic-details'
  })
}

const handleUnsubscribe = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要退订订单 ${row.orderNo} 吗？`, '确认退订', {
      type: 'warning'
    })

    // TODO: 实现退订API调用
    ElMessage.success('退订成功')
    getTableData()
  } catch (error) {
    // 用户取消操作
  }
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    tableData.value = [
      {
        orderNo: 'ORD20240101001',
        packageName: '基础套餐',
        iccid: '89860000000000000001',
        msisdn: '13800138000',
        orderType: '1',
        amount: 100.0,
        status: '1',
        createTime: '2024-01-01 10:00:00',
        attributableChannel: '渠道A',
        isUsed: false
      }
    ]
    total.value = 1
  } catch (error) {
    ElMessage.error('获取订单数据失败')
  } finally {
    loading.value = false
  }
}

// 获取渠道列表
const getChannelList = async () => {
  try {
    // TODO: 实现API调用
    channelList.value = [
      { corpId: '1', corpName: '渠道A' },
      { corpId: '2', corpName: '渠道B' }
    ]
  } catch (error) {
    ElMessage.error('获取渠道列表失败')
  }
}

// 生命周期
onMounted(() => {
  getTableData()
  getChannelList()
})
</script>

<style scoped>
.search-form {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.search-buttons {
  margin-top: 10px;
}

.search-buttons .el-button {
  margin-right: 10px;
}
</style>
