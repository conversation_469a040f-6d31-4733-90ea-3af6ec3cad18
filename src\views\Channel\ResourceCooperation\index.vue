<template>
  <!-- 资源合作管理 -->
  <ContentWrap>
    <el-card>
      <div class="search-container">
        <el-form :model="searchForm" inline>
          <el-form-item label="合作类型：">
            <el-select
              v-model="searchForm.cooperationType"
              placeholder="请选择合作类型"
              clearable
              style="width: 200px"
            >
              <el-option :value="1" label="资源提供" />
              <el-option :value="2" label="资源采购" />
              <el-option :value="3" label="技术合作" />
            </el-select>
          </el-form-item>
          <el-form-item label="合作状态：">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <el-option :value="1" label="进行中" />
              <el-option :value="2" label="已完成" />
              <el-option :value="3" label="已暂停" />
              <el-option :value="0" label="已终止" />
            </el-select>
          </el-form-item>
          <el-form-item label="合作伙伴：">
            <el-input
              v-model="searchForm.partnerName"
              placeholder="请输入合作伙伴名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="hasPermission('search')"
              type="primary"
              :loading="searchLoading"
              @click="handleSearch"
            >
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </el-button>
            <el-button v-if="hasPermission('add')" type="success" @click="handleAdd">
              <Icon icon="ep:plus" class="mr-5px" />
              新增合作
            </el-button>
            <el-button
              v-if="hasPermission('billingStatistics')"
              type="info"
              @click="handleBillingStatistics"
            >
              <Icon icon="ep:data-analysis" class="mr-5px" />
              计费统计
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <div style="margin-top: 20px">
        <el-table :data="tableData" v-loading="loading" border>
          <el-table-column prop="cooperationNo" label="合作编号" min-width="150" />
          <el-table-column prop="partnerName" label="合作伙伴" min-width="150" />
          <el-table-column prop="cooperationType" label="合作类型" min-width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="getCooperationTypeTag(row?.cooperationType)" v-if="row">
                {{ getCooperationTypeText(row?.cooperationType) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="resourceType" label="资源类型" min-width="120" align="center" />
          <el-table-column prop="totalAmount" label="合作金额" min-width="120" align="right">
            <template #default="{ row }">
              {{ row ? (row.totalAmount || 0).toFixed(2) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="usedAmount" label="已使用金额" min-width="120" align="right">
            <template #default="{ row }">
              {{ row ? (row.usedAmount || 0).toFixed(2) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusTag(row?.status)" v-if="row">
                {{ getStatusText(row?.status) }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="startDate" label="开始日期" min-width="120" />
          <el-table-column prop="endDate" label="结束日期" min-width="120" />
          <el-table-column label="操作" min-width="250" align="center" fixed="right">
            <template #default="{ row }">
              <div v-if="row" class="action-buttons">
                <el-button
                  v-if="hasPermission('view')"
                  type="primary"
                  size="small"
                  @click="handleView(row)"
                >
                  查看
                </el-button>
                <el-button
                  v-if="hasPermission('edit')"
                  type="warning"
                  size="small"
                  @click="handleEdit(row)"
                >
                  编辑
                </el-button>
                <el-button
                  v-if="hasPermission('viewResources')"
                  type="info"
                  size="small"
                  @click="handleViewResources(row)"
                >
                  查看资源
                </el-button>
                <el-button
                  v-if="hasPermission('callOrderDetails')"
                  type="success"
                  size="small"
                  @click="handleCallOrderDetails(row)"
                >
                  通话详情
                </el-button>
                <el-button
                  v-if="hasPermission('delete') && row.status === 0"
                  type="danger"
                  size="small"
                  @click="handleDelete(row)"
                >
                  删除
                </el-button>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div style="margin-top: 20px; text-align: right">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'
import { useRouter } from 'vue-router'

const router = useRouter()

// 权限检查函数
const hasPermission = (permission: string): boolean => {
  console.log(`🔍 [资源合作权限检查] ${permission}: 允许访问`)
  return true
}

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)

const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const searchForm = reactive({
  cooperationType: null as number | null,
  status: null as number | null,
  partnerName: ''
})

const tableData = ref<any[]>([])

// 方法
const getCooperationTypeTag = (
  type: number
): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const typeMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    1: 'primary',
    2: 'success',
    3: 'warning'
  }
  return typeMap[type] || 'info'
}

const getCooperationTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '资源提供',
    2: '资源采购',
    3: '技术合作'
  }
  return typeMap[type] || '未知'
}

const getStatusTag = (status: number): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
  const statusMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
    1: 'primary',
    2: 'success',
    3: 'warning',
    0: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    1: '进行中',
    2: '已完成',
    3: '已暂停',
    0: '已终止'
  }
  return statusMap[status] || '未知'
}

const handleSearch = () => {
  currentPage.value = 1
  getTableData()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  getTableData()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getTableData()
}

const handleAdd = () => {
  ElMessage.info('新增合作功能开发中...')
}

const handleView = (row: any) => {
  ElMessage.info(`查看合作详情: ${row.cooperationNo}`)
}

const handleEdit = (row: any) => {
  ElMessage.info(`编辑合作: ${row.cooperationNo}`)
}

const handleViewResources = (row: any) => {
  router.push({
    path: '/newcmi/channel/resource-cooperation/view-resources',
    query: { cooperationId: row.id }
  })
}

const handleCallOrderDetails = (row: any) => {
  router.push({
    path: '/newcmi/channel/resource-cooperation/call-order-details',
    query: { cooperationId: row.id }
  })
}

const handleBillingStatistics = () => {
  router.push('/newcmi/channel/resource-cooperation/billing-statistics')
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除合作 "${row.cooperationNo}" 吗？`, '确认删除', {
      type: 'warning'
    })

    ElMessage.success('删除成功')
    getTableData()
  } catch (error) {
    // 用户取消操作
  }
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // TODO: 实现API调用
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        cooperationNo: 'COOP20240101001',
        partnerName: '合作伙伴A',
        cooperationType: 1,
        resourceType: '流量资源',
        totalAmount: 100000.0,
        usedAmount: 65000.0,
        status: 1,
        startDate: '2024-01-01',
        endDate: '2024-12-31'
      },
      {
        id: 2,
        cooperationNo: 'COOP20240101002',
        partnerName: '合作伙伴B',
        cooperationType: 2,
        resourceType: '通话资源',
        totalAmount: 50000.0,
        usedAmount: 50000.0,
        status: 2,
        startDate: '2024-01-01',
        endDate: '2024-06-30'
      }
    ]
    total.value = 2
  } catch (error) {
    ElMessage.error('获取资源合作数据失败')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.search-container {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  justify-content: center;
}

.action-buttons .el-button {
  margin: 2px;
}
</style>
