(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8567cddc"],{"3d2b":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e._self._c;return t("Card",[t("div",{staticStyle:{width:"100%","margin-top":"50px",margin:"auto"}},[t("div",{staticStyle:{display:"flex",width:"100%","align-items":"center"}},[t("Button",{directives:[{name:"has",rawName:"v-has",value:"add",expression:"'add'"}],staticStyle:{margin:"0 2px","margin-left":"20px"},attrs:{type:"primary",icon:"md-add"},on:{click:function(t){return e.addSupplier()}}},[e._v("添加供应商")])],1),t("Table",{staticStyle:{width:"100%","margin-top":"50px"},attrs:{columns:e.columns,data:e.data,loading:e.loading},scopedSlots:e._u([{key:"action",fn:function(a){var o=a.row;a.index;return[t("Button",{directives:[{name:"has",rawName:"v-has",value:"update",expression:"'update'"}],staticStyle:{"margin-right":"5px"},attrs:{type:"primary",size:"small"},on:{click:function(t){return e.updateSupplier(o)}}},[e._v("编辑")])]}}])}),t("div",{staticStyle:{"margin-left":"38%","margin-top":"100px","margin-bottom":"160px"}},[t("Page",{attrs:{total:e.total,current:e.currentPage,"show-total":"","show-elevator":""},on:{"update:current":function(t){e.currentPage=t},"on-change":e.goPage}})],1)],1),t("Modal",{attrs:{title:"添加供应商","mask-closable":!1,width:400},on:{"on-cancel":e.cancelModal},model:{value:e.addmodel,callback:function(t){e.addmodel=t},expression:"addmodel"}},[t("Form",{ref:"formmodel",attrs:{model:e.formmodel,rules:e.rules,"label-width":120}},[t("div",[t("FormItem",{attrs:{label:"供应商公司名称",prop:"supplierShortenName"}},[t("Input",{staticStyle:{width:"200px"},attrs:{clearable:""},on:{"on-blur":function(t){return e.changeCode("1",e.formmodel.supplierShortenName)}},model:{value:e.formmodel.supplierShortenName,callback:function(t){e.$set(e.formmodel,"supplierShortenName",t)},expression:"formmodel.supplierShortenName"}})],1),t("FormItem",{attrs:{label:"供应商编码",prop:"supplierShortenCode"}},[t("Input",{staticStyle:{width:"200px"},attrs:{disabled:e.codeDisabled,clearable:""},model:{value:e.formmodel.supplierShortenCode,callback:function(t){e.$set(e.formmodel,"supplierShortenCode",t)},expression:"formmodel.supplierShortenCode"}})],1),t("FormItem",{attrs:{label:"供应商名称",prop:"supplierName"}},[t("Input",{staticStyle:{width:"200px"},attrs:{clearable:""},model:{value:e.formmodel.supplierName,callback:function(t){e.$set(e.formmodel,"supplierName",t)},expression:"formmodel.supplierName"}})],1),t("FormItem",{attrs:{label:"APN(简中)",prop:"apnZh"}},[t("Input",{staticStyle:{width:"200px"},attrs:{clearable:""},model:{value:e.formmodel.apnZh,callback:function(t){e.$set(e.formmodel,"apnZh",t)},expression:"formmodel.apnZh"}})],1),t("FormItem",{attrs:{label:"APN(繁中)",prop:"apnTw"}},[t("Input",{staticStyle:{width:"200px"},attrs:{clearable:""},model:{value:e.formmodel.apnTw,callback:function(t){e.$set(e.formmodel,"apnTw",t)},expression:"formmodel.apnTw"}})],1),t("FormItem",{attrs:{label:"APN(英文)",prop:"apnEn"}},[t("Input",{staticStyle:{width:"200px"},attrs:{clearable:""},model:{value:e.formmodel.apnEn,callback:function(t){e.$set(e.formmodel,"apnEn",t)},expression:"formmodel.apnEn"}})],1),t("FormItem",{attrs:{label:"免实名地区"}},[t("Select",{staticStyle:{width:"200px"},attrs:{multiple:"",filterable:"",clearable:"",placeholder:"请选择免实名地区"},model:{value:e.formmodel.mccList,callback:function(t){e.$set(e.formmodel,"mccList",t)},expression:"formmodel.mccList"}},e._l(e.regionOptions,(function(a){return t("Option",{key:a.mcc,attrs:{value:a.mcc}},[e._v(e._s(a.countryEN))])})),1)],1)],1)]),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:e.cancelModal}},[e._v("返回")]),t("Button",{attrs:{type:"primary",loading:e.addLoading},on:{click:e.addBesure}},[e._v("确定")])],1)],1),t("Modal",{attrs:{title:"修改供应商","mask-closable":!1,width:400},on:{"on-cancel":e.cancelModal},model:{value:e.updateSupplierModel,callback:function(t){e.updateSupplierModel=t},expression:"updateSupplierModel"}},[t("Form",{ref:"updatemodel",attrs:{model:e.updatemodel,rules:e.rules,"label-width":120}},[t("div",[t("FormItem",{attrs:{label:"供应商公司名称",prop:"supplierShortenName"}},[t("Input",{staticStyle:{width:"200px"},attrs:{clearable:""},on:{"on-blur":function(t){return e.changeCode("2",e.updatemodel.supplierShortenName)}},model:{value:e.updatemodel.supplierShortenName,callback:function(t){e.$set(e.updatemodel,"supplierShortenName",t)},expression:"updatemodel.supplierShortenName"}})],1),t("FormItem",{attrs:{label:"供应商编码",prop:"supplierShortenCode"}},[t("Input",{staticStyle:{width:"200px"},attrs:{disabled:e.codeDisabled,clearable:""},model:{value:e.updatemodel.supplierShortenCode,callback:function(t){e.$set(e.updatemodel,"supplierShortenCode",t)},expression:"updatemodel.supplierShortenCode"}})],1),t("FormItem",{attrs:{label:"供应商名称",prop:"supplierName"}},[t("Input",{staticStyle:{width:"200px"},attrs:{readonly:""},model:{value:e.updatemodel.supplierName,callback:function(t){e.$set(e.updatemodel,"supplierName",t)},expression:"updatemodel.supplierName"}})],1),t("FormItem",{attrs:{label:"APN(简中)",prop:"apnZh"}},[t("Input",{staticStyle:{width:"200px"},attrs:{clearable:""},model:{value:e.updatemodel.apnZh,callback:function(t){e.$set(e.updatemodel,"apnZh",t)},expression:"updatemodel.apnZh"}})],1),t("FormItem",{attrs:{label:"APN(繁中)",prop:"apnTw"}},[t("Input",{staticStyle:{width:"200px"},attrs:{clearable:""},model:{value:e.updatemodel.apnTw,callback:function(t){e.$set(e.updatemodel,"apnTw",t)},expression:"updatemodel.apnTw"}})],1),t("FormItem",{attrs:{label:"APN(英文)",prop:"apnEn"}},[t("Input",{staticStyle:{width:"200px"},attrs:{clearable:""},model:{value:e.updatemodel.apnEn,callback:function(t){e.$set(e.updatemodel,"apnEn",t)},expression:"updatemodel.apnEn"}})],1),t("FormItem",{attrs:{label:"免实名地区"}},[t("Select",{staticStyle:{width:"200px"},attrs:{multiple:"",filterable:"",clearable:"",placeholder:"请选择免实名地区"},model:{value:e.updatemodel.mccList,callback:function(t){e.$set(e.updatemodel,"mccList",t)},expression:"updatemodel.mccList"}},e._l(e.regionOptions,(function(a){return t("Option",{key:a.mcc,attrs:{value:a.mcc}},[e._v(e._s(a.countryEN))])})),1)],1)],1)]),t("div",{staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"center"},attrs:{slot:"footer"},slot:"footer"},[t("Button",{on:{click:e.cancelModal}},[e._v("返回")]),t("Button",{attrs:{type:"primary",loading:e.updateLoading},on:{click:e.updateBesure}},[e._v("确定")])],1)],1)],1)},r=[],l=a("5530"),n=(a("a15b"),a("d81d"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("e472")),i={data:function(){return{loading:!1,total:0,currentPage:1,addmodel:!1,updateSupplierModel:!1,addLoading:!1,updateLoading:!1,codeDisabled:!1,supplierShortenList:[],regionOptions:[],columns:[{title:"供应商公司名称",key:"supplierShortenName",align:"center"},{title:"供应商编码",key:"supplierShortenCode",align:"center"},{title:"供应商名称",key:"supplierName",align:"center"},{title:"APN(简中)",key:"apnZh",align:"center"},{title:"APN(繁中)",key:"apnTw",align:"center"},{title:"APN(英文)",key:"apnEn",align:"center"},{title:"免实名地区",key:"mccList",align:"center",ellipsis:!0,tooltip:!0},{title:"操作",slot:"action",align:"center"}],data:[],formmodel:{supplierName:"",apnZh:"",apnTw:"",apnEn:"",supplierShortenName:"",supplierShortenCode:"",mccList:[]},updatemodel:{supplierName:"",apnZh:"",apnTw:"",apnEn:"",supplierId:"",supplierShortenId:"",supplierShortenName:"",supplierShortenCode:"",mccList:[]},rules:{supplierShortenName:[{required:!0,message:"请输入供应商公司名称",trigger:"blur"}],supplierShortenCode:[{required:!0,message:"请输入供应商编码",trigger:"blur"}],supplierName:[{required:!0,message:"请输入供应商名称",trigger:"blur"},{min:0,max:100,message:"输入100位以内字符",trigger:"blur"}],apnZh:[{required:!0,message:"请输入APN(简中)",trigger:"blur"}],apnTw:[{required:!0,message:"请输入APN(繁中)",trigger:"blur"}],apnEn:[{required:!0,message:"请输入APN(英文)",trigger:"blur"}]}}},mounted:function(){this.goPageFirst(1)},methods:{goPageFirst:function(e){var t=this;this.loading=!0;var a=this,o=e,r=10;Object(n["d"])({pageNum:o,pageSize:r}).then((function(o){if("0000"==o.code){a.loading=!1,t.page=e,t.total=Number(o.count);var r=o.data.map((function(e){return Object(l["a"])(Object(l["a"])({},e),{},{mccList:e.countryInfo?e.countryInfo.map((function(e){return e.countryEN})).join(", "):""})}));t.data=r}})).catch((function(e){console.error(e)})).finally((function(){t.loading=!1}))},goPage:function(e){this.goPageFirst(e)},addSupplier:function(){this.getSupplierShortenCode(),this.fetchRegionOptions(),this.addmodel=!0},addBesure:function(){var e=this;this.$refs.formmodel.validate((function(t){if(t){var a=JSON.parse(JSON.stringify(e.formmodel));Object(n["a"])(a).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作提醒",desc:"添加成功！"}),e.goPageFirst(1),e.currentPage=1,e.cancelModal(),e.addLoading=!1,e.addmodel=!1})).catch((function(e){console.log(e)})).finally((function(){e.cancelModal(),e.addLoading=!1}))}}))},updateSupplier:function(e){this.getSupplierShortenCode(),this.fetchRegionOptions(),this.updateSupplierModel=!0,this.updatemodel={supplierName:e.supplierName,apnZh:e.apnZh,apnTw:e.apnTw,apnEn:e.apnEn,supplierId:e.supplierId,supplierShortenId:e.supplierShortenId,supplierShortenName:e.supplierShortenName,supplierShortenCode:e.supplierShortenCode,mccList:e.countryInfo?e.countryInfo.map((function(e){return e.mcc})):[]}},updateBesure:function(){var e=this;this.$refs.updatemodel.validate((function(t){if(t){var a=JSON.parse(JSON.stringify(e.updatemodel));Object(n["e"])(a).then((function(t){if(!t||"0000"!=t.code)throw t;e.$Notice.success({title:"操作提醒",desc:"修改成功！"}),e.goPageFirst(1),e.currentPage=1,e.cancelModal(),e.addLoading=!1,e.addmodel=!1})).catch((function(e){console.log(e)})).finally((function(){e.cancelModal(),e.addLoading=!1}))}}))},cancelModal:function(){this.codeDisabled=!1,this.addmodel=!1,this.updateSupplierModel=!1,this.$refs.formmodel.resetFields(),this.$refs.updatemodel.resetFields(),this.formmodel.mccList=[],this.updatemodel.mccList=[]},changeCode:function(e,t){this.supplierShortenList.hasOwnProperty(t)?"1"==e?(this.codeDisabled=!0,this.formmodel.supplierShortenCode=this.supplierShortenList[t]):(this.codeDisabled=!0,this.updatemodel.supplierShortenCode=this.supplierShortenList[t]):(this.codeDisabled=!1,"1"==e?this.formmodel.supplierShortenCode="":this.updatemodel.supplierShortenCode=this.updatemodel.supplierShortenCode?this.updatemodel.supplierShortenCode:"")},getSupplierShortenCode:function(e,t){var a=this;Object(n["c"])().then((function(e){if(!e||"0000"!=e.code)throw e;a.supplierShortenList=e.data})).catch((function(e){console.log(e)})).finally((function(){}))},fetchRegionOptions:function(){var e=this;Object(n["b"])().then((function(t){t&&"0000"===t.code&&(e.regionOptions=t.data)}))}}},p=i,s=a("2877"),d=Object(s["a"])(p,o,r,!1,null,null,null);t["default"]=d.exports},e472:function(e,t,a){"use strict";a.d(t,"d",(function(){return n})),a.d(t,"a",(function(){return i})),a.d(t,"e",(function(){return p})),a.d(t,"c",(function(){return s})),a.d(t,"b",(function(){return d}));var o=a("66df"),r="/rms/api/v1",l="/pms",n=function(e){return o["a"].request({url:r+"/supplier/selectSupplier",params:e,method:"get"})},i=function(e){return o["a"].request({url:r+"/supplier/saveSupplier",data:e,method:"post"})},p=function(e){return o["a"].request({url:r+"/supplier/updateSupplier",data:e,method:"post"})},s=function(e){return o["a"].request({url:r+"/supplier/queryShorten",data:e,method:"get"})},d=function(e){return o["a"].request({url:l+"/pms-realname/getMccList",data:e,method:"get"})}}}]);