<script setup lang="tsx">
import { reactive, ref, watch, onMounted, unref } from 'vue'
import { Form, FormSchema } from '@/components/Form'
import { useI18n } from '@/hooks/web/useI18n'
import { ElCheckbox, ElLink, ElMessage } from 'element-plus'
import { useForm } from '@/hooks/web/useForm'
import { useAppStore } from '@/store/modules/app'
import { usePermissionStore } from '@/store/modules/permission'
import { useRouter } from 'vue-router'
import { useValidator } from '@/hooks/web/useValidator'
import { Icon } from '@/components/Icon'
import { useUserStore } from '@/store/modules/user'
import { BaseButton } from '@/components/Button'
import { getVerCode, getConfigure, getNeedCode, searchcorpid } from '@/api/cmi/auth'
import type { LoginData } from '@/api/cmi/auth'
import { handleSSOLoginFlow, initSSOCheck } from '@/utils/sso'

const { required } = useValidator()

const emit = defineEmits(['to-register'])

const appStore = useAppStore()
const userStore = useUserStore()
const permissionStore = usePermissionStore()
const { currentRoute, addRoute, push } = useRouter()
const { t } = useI18n()

// 表单状态
const loading = ref(false)
const remember = ref(userStore.getRememberMe)
const captchaImage = ref('')
const captchaId = ref('')
const showCaptcha = ref(false)

// 表单验证规则
const rules = {
  userName: [required()],
  password: [required()],
  captcha: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (showCaptcha.value && !value) {
          callback(new Error('请输入验证码'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 表单配置
const schema = reactive<FormSchema[]>([
  {
    field: 'title',
    colProps: { span: 24 },
    formItemProps: {
      slots: {
        default: () => {
          return <h2 class="text-2xl font-bold text-center w-[100%]">CMI 管理系统</h2>
        }
      }
    }
  },
  {
    field: 'userName',
    label: '用户名',
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入用户名'
    }
  },
  {
    field: 'password',
    label: '密码',
    component: 'InputPassword',
    colProps: { span: 24 },
    componentProps: {
      style: { width: '100%' },
      placeholder: '请输入密码',
      onKeydown: (e: any) => {
        if (e.key === 'Enter') {
          e.stopPropagation()
          signIn()
        }
      }
    }
  }
])

// 动态添加验证码字段
const addCaptchaField = () => {
  const captchaField: FormSchema = {
    field: 'captcha',
    label: '验证码',
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入验证码',
      style: { width: '60%', marginRight: '10px' },
      slots: {
        append: () => (
          <img
            src={captchaImage.value}
            alt="验证码"
            style={{ width: '80px', height: '32px', cursor: 'pointer' }}
            onClick={refreshCaptcha}
          />
        )
      }
    }
  }

  const toolIndex = schema.findIndex((item) => item.field === 'tool')
  if (toolIndex > -1) {
    schema.splice(toolIndex, 0, captchaField)
  } else {
    schema.push(captchaField)
  }
}

// 添加工具栏
schema.push({
  field: 'tool',
  colProps: { span: 24 },
  formItemProps: {
    slots: {
      default: () => (
        <>
          <div class="flex justify-between items-center w-[100%]">
            <ElCheckbox v-model={remember.value} label="记住我" size="small" />
            <ElLink type="primary" underline={false}>
              忘记密码？
            </ElLink>
          </div>
        </>
      )
    }
  }
})

// 添加登录按钮
schema.push({
  field: 'login',
  colProps: { span: 24 },
  formItemProps: {
    slots: {
      default: () => (
        <div class="w-[100%]">
          <BaseButton loading={loading.value} type="primary" class="w-[100%]" onClick={signIn}>
            登录
          </BaseButton>
        </div>
      )
    }
  }
})

const { formRegister, formMethods } = useForm()
const { getFormData, getElFormExpose } = formMethods

// 获取验证码
const refreshCaptcha = async () => {
  try {
    const res = await getVerCode()
    if (res.data) {
      captchaImage.value = res.data.captchaImage
      captchaId.value = res.data.captchaId
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
  }
}

// 检查是否需要验证码
const checkNeedCaptcha = async (userName: string) => {
  try {
    const res = await getNeedCode({ userName })
    if (res.data) {
      const needCaptcha = (res.data as any).needCaptcha
      if (needCaptcha && !showCaptcha.value) {
        showCaptcha.value = true
        addCaptchaField()
        await refreshCaptcha()
      }
    }
  } catch (error) {
    console.error('检查验证码配置失败:', error)
  }
}

// 登录方法
const signIn = async () => {
  const formRef = await getElFormExpose()
  await formRef?.validate(async (isValid) => {
    if (isValid) {
      loading.value = true
      try {
        const formData = await getFormData<LoginData>()

        // 添加验证码信息
        if (showCaptcha.value) {
          formData.captchaId = captchaId.value
        }

        // 添加登录类型
        formData.type = 1

        // 先执行登出清理
        await userStore.cmiLogout()

        // 调用登录API
        const res = await userStore.cmiLogin(formData)

        if (res.data) {
          // 设置记住我
          userStore.setRememberMe(unref(remember))

          // 检查密码是否需要更新
          if (userStore.getIsUpdatePassword === 1) {
            ElMessage.warning('密码已过期，请修改密码')
            push({ name: 'pwd_mngr' })
            return
          }

          // 检查是否为超管
          try {
            const corpRes = await searchcorpid({ userName: userStore.getUserName })
            if ((corpRes as any).code === '0000') {
              // 超管用户
              push({ name: 'home' })
            } else {
              // 普通用户
              push({ name: 'home' })
            }
          } catch (error) {
            console.error('检查用户权限失败:', error)
            push({ name: 'home' })
          }

          ElMessage.success('登录成功')
        }
      } catch (error: any) {
        console.error('登录失败:', error)
        ElMessage.error(error.message || '登录失败')

        // 如果是验证码错误，刷新验证码
        if (showCaptcha.value) {
          await refreshCaptcha()
        }
      } finally {
        loading.value = false
      }
    }
  })
}

// 监听用户名变化，检查是否需要验证码
watch(
  () => remember.value,
  (val) => {
    userStore.setRememberMe(val)
  }
)

onMounted(async () => {
  // 首先检查是否需要 SSO 登录
  try {
    const ssoSuccess = await initSSOCheck(userStore)
    if (ssoSuccess) {
      return // 如果 SSO 登录成功，不需要继续初始化登录表单
    }
  } catch (error) {
    console.error('SSO 检查失败:', error)
  }

  // 初始化时检查验证码配置
  try {
    const configRes = await getConfigure({})
    if (configRes.data && (configRes.data as any).needCaptcha) {
      showCaptcha.value = true
      addCaptchaField()
      await refreshCaptcha()
    }
  } catch (error) {
    console.error('获取配置失败:', error)
  }
})
</script>

<template>
  <Form
    :schema="schema"
    :rules="rules"
    label-position="top"
    hide-required-asterisk
    size="large"
    class="dark:(border-1 border-[var(--el-border-color)] border-solid)"
    @register="formRegister"
  />
</template>
