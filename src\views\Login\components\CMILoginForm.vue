<script setup lang="tsx">
import { reactive, ref, watch, onMounted, unref } from 'vue'
import { Form, FormSchema } from '@/components/Form'
import { ElCheckbox, ElLink, ElMessage, ElButton } from 'element-plus'
import { useForm } from '@/hooks/web/useForm'
import { useRouter } from 'vue-router'
import { useValidator } from '@/hooks/web/useValidator'
import { useUserStore } from '@/store/modules/user'
import { BaseButton } from '@/components/Button'
import { getVerCode, getConfigure, searchcorpid, getSSOSwitchConfig } from '@/api/cmi/auth'
import type { LoginData } from '@/api/cmi/auth'
import { performSSORedirect, defaultSSOConfig } from '@/plugins/ssoRedirect'

const { required } = useValidator()

const emit = defineEmits(['to-register'])

const userStore = useUserStore()
const { push } = useRouter()

// 表单状态
const loading = ref(false)
const remember = ref(userStore.getRememberMe)
const captchaImage = ref('')
const captchaId = ref('')
const showCaptcha = ref(false)
const ssoSwitchConfig = ref(false) // SSO开关配置

// 表单验证规则
const rules = {
  username: [required()],
  password: [required()],
  captcha: [
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (showCaptcha.value && !value) {
          callback(new Error('请输入验证码'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 基础表单配置
const schema = reactive<FormSchema[]>([
  {
    field: 'title',
    colProps: { span: 24 },
    formItemProps: {
      slots: {
        default: () => {
          return <h2 class="text-2xl font-bold text-center w-[100%]">CMI 管理系统</h2>
        }
      }
    }
  },
  {
    field: 'username',
    label: '用户名',
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入用户名'
    }
  },
  {
    field: 'password',
    label: '密码',
    component: 'InputPassword',
    colProps: { span: 24 },
    componentProps: {
      style: { width: '100%' },
      placeholder: '请输入密码',
      onKeydown: (e: any) => {
        if (e.key === 'Enter') {
          e.stopPropagation()
          signIn()
        }
      }
    }
  }
])

// 动态添加验证码字段
const addCaptchaField = () => {
  const captchaField: FormSchema = {
    field: 'captcha',
    label: '验证码',
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入验证码',
      style: { width: '60%', marginRight: '10px' },
      slots: {
        append: () => (
          <img
            src={captchaImage.value}
            alt="验证码"
            style={{ width: '80px', height: '32px', cursor: 'pointer' }}
            onClick={refreshCaptcha}
          />
        )
      }
    }
  }

  const toolIndex = schema.findIndex((item) => item.field === 'tool')
  if (toolIndex > -1) {
    schema.splice(toolIndex, 0, captchaField)
  } else {
    schema.push(captchaField)
  }
}

// 动态添加工具栏
const addToolbar = () => {
  const toolbarField = {
    field: 'tool',
    colProps: { span: 24 },
    formItemProps: {
      slots: {
        default: () => (
          <>
            <div class="flex justify-between items-center w-[100%]">
              <ElCheckbox v-model={remember.value} label="记住我" size="small" />
              <ElLink type="primary" underline={false}>
                忘记密码？
              </ElLink>
              {ssoSwitchConfig.value && (
                <ElButton type="primary" text onClick={handleSSO}>
                  SSO登录
                </ElButton>
              )}
            </div>
          </>
        )
      }
    }
  }

  if (!schema.some((item) => item.field === 'tool')) {
    schema.push(toolbarField)
  }
}

// 动态添加登录按钮
const addLoginButton = () => {
  const loginButtonField = {
    field: 'login',
    colProps: { span: 24 },
    formItemProps: {
      slots: {
        default: () => (
          <div class="w-[100%]">
            <BaseButton loading={loading.value} type="primary" class="w-[100%]" onClick={signIn}>
              登录
            </BaseButton>
          </div>
        )
      }
    }
  }

  if (!schema.some((item) => item.field === 'login')) {
    schema.push(loginButtonField)
  }
}

const { formRegister, formMethods } = useForm()
const { getFormData, getElFormExpose } = formMethods

// 获取验证码
const refreshCaptcha = async () => {
  try {
    const res = await getVerCode()
    if (res.data) {
      captchaImage.value = (res.data as any).captchaImage
      captchaId.value = (res.data as any).captchaId
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
  }
}

// 登录方法 - 按照原项目流程
const signIn = async () => {
  const formRef = await getElFormExpose()
  await formRef?.validate(async (isValid) => {
    if (isValid) {
      loading.value = true
      try {
        const formData = await getFormData<LoginData>()

        // 添加登录类型
        formData.type = 1

        // 先执行登出清理
        await userStore.cmiLogout()

        // 按照原项目流程：先检查验证码配置
        const configRes = await getConfigure(formData)
        if ((configRes.data as any).userDetails.needVerifyCode === '1') {
          // 需要验证码，调用登录接口
          const res = await userStore.cmiLogin(formData)
          if ((res as any).code === '0000') {
            await handleLoginSuccess()
          }
        } else {
          // 不需要验证码，直接登录
          const res = await userStore.cmiLogin(formData)
          if ((res as any).code === '0000') {
            await handleLoginSuccess()
          }
        }
      } catch (error: any) {
        console.error('登录失败:', error)
        ElMessage.error(error.message || '登录失败')

        // 如果是验证码错误，刷新验证码
        if (showCaptcha.value) {
          await refreshCaptcha()
        }
      } finally {
        loading.value = false
      }
    }
  })
}

// 处理登录成功后的逻辑
const handleLoginSuccess = async () => {
  // 设置记住我
  userStore.setRememberMe(unref(remember))
  //获取corpId
  const res = await searchcorpid({ userName: userStore.getUserName })
  if (res.code == '0000') {
    userStore.setCorpId(res.data)
    sessionStorage.setItem("corpId",res.data)
  }
  

  ElMessage.success('登录成功')

  // 等待一小段时间确保动态路由生成完成，然后跳转到首页
  setTimeout(() => {
    // 检查用户是否有首页权限
    const userPermissions = userStore.getPermissions || []
    console.log('🔍 [CMI登录] 用户权限列表:', userPermissions)
    console.log('🔍 [CMI登录] 是否有home权限:', userPermissions.includes('home'))

    if (userPermissions.includes('home')) {
      // 有首页权限，跳转到仪表板
      console.log('✅ [CMI登录] 跳转到仪表板: /newcmi/home')
      push({ path: '/newcmi/home' })
    } else {
      // 没有首页权限，跳转到用户有权限的第一个页面
      const firstPermission = userPermissions[0]
      if (firstPermission) {
        // 根据权限找到对应的路由路径
        const routeMap: Record<string, string> = {
          account_list: '/newcmi/system/account',
          pwd_mngr: '/newcmi/system/password',
          pri_mngr: '/newcmi/system/role',
          login_mngr: '/newcmi/system/login-log',
          msisdn: '/newcmi/resource/msisdn',
          iccid: '/newcmi/resource/iccid',
          imsi: '/newcmi/resource/imsi',
          supplyImsi: '/newcmi/resource/supply-imsi',
          makeCard: '/newcmi/product/make-card',
          masterCard: '/newcmi/product/master-card',
          cardPool: '/newcmi/product/card-pool',
          vimsi: '/newcmi/product/vimsi',
          channelManage: '/newcmi/customer/channel',
          cooperativeManage: '/newcmi/customer/cooperative'
        }
        const targetPath = routeMap[firstPermission] || '/personal/personal-center'
        push({ path: targetPath })
      } else {
        // 没有任何权限，跳转到个人中心
        push({ path: '/personal/personal-center' })
      }
    }
  }, 200) // 增加等待时间确保动态路由生成完成
}

// SSO 登录处理
const handleSSO = () => {
  console.log('开始 SSO 登录')
  performSSORedirect(defaultSSOConfig)
}

// 监听用户名变化，检查是否需要验证码
watch(
  () => remember.value,
  (val) => {
    userStore.setRememberMe(val)
  }
)

// URL 参数解析和 SSO 处理
const parseUrlParams = () => {
  const urlParams = new URLSearchParams(window.location.search)
  const params: Record<string, string> = {}
  for (const [key, value] of urlParams.entries()) {
    params[key] = value.trim()
  }
  return params
}

// SSO 登录处理
const ssoLogin = async (ticket: string) => {
  try {
    const data = {
      ticket: ticket,
      service: getUrlWithoutParams()
    }

    await userStore.cmiLogout()
    const res = await userStore.cmiSSOLogin(data)

    if ((res as any).code === '0000') {
      // SSO 登录成功后的处理
      userStore.setRememberMe(true)

      // 检查密码是否需要更新
      if (userStore.getIsUpdatePassword === 1) {
        ElMessage.warning('密码已过期，请修改密码')
        push({ path: '/personal/personal-center' })
        return
      }

      ElMessage.success('SSO 登录成功')

      // 等待一小段时间确保状态设置完成
      setTimeout(() => {
        push({ path: '/' })
      }, 100)
    }
  } catch (error) {
    console.error('SSO 登录失败:', error)
    ElMessage.error('SSO 登录失败')
  }
}

// 获取不带参数的 URL
const getUrlWithoutParams = () => {
  const url = window.location.href
  const index = url.indexOf('?')
  if (index !== -1) {
    return url.substring(0, index)
  } else {
    return url
  }
}

// 按照原项目的 mounted 初始化顺序
onMounted(async () => {
  // 1. 获取 SSO 开关配置
  try {
    const ssoConfigRes = await getSSOSwitchConfig()
    if ((ssoConfigRes as any).code === '0000') {
      ssoSwitchConfig.value = (ssoConfigRes.data as any).ssoSwitchConfig
    }
  } catch (error) {
    console.error('获取 SSO 配置失败:', error)
  }

  // 2. 解析 URL 参数（包含 ticket 处理）
  const params = parseUrlParams()
  if (params.ticket) {
    await ssoLogin(params.ticket)
    return
  }

  // 3. 添加工具栏和登录按钮
  addToolbar()
  addLoginButton()
})
</script>

<template>
  <Form
    :schema="schema"
    :rules="rules"
    label-position="top"
    hide-required-asterisk
    size="large"
    class="dark:(border-1 border-[var(--el-border-color)] border-solid)"
    @register="formRegister"
  />
</template>
